package com.shuidihuzhu.cf.model.crowdfunding;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Created by niejiangnan on 2017/11/16.
 */
@Data
public class CfHospitalAuditInfo {

    private long id;
    private String infoUuid;
    private String patientName;
    private String hospitalName;
    private String department;
    private String floorNumber;
    private String bedNumber;
    private String hospitalizationNumber;
    private String doctorName;
    private String operatorContent;
    private String departmentTelNumber;

    @ApiModelProperty("审核状态{0:未下发,1:已下发,2:通过,3:驳回,4:已提交}")
    private int auditStatus;

    private int type;
    private int isDelete;

    @ApiModelProperty("下发原因")
    private String reason;

    @ApiModelProperty("下发原因补充")
    private String reasonSupply;

    @ApiModelProperty("方便核实时间")
    private String easyToVerifyTime;

    /**
     * @see com.shuidihuzhu.cf.enums.EasyToVerifyStatusEnum
     */
    @ApiModelProperty("方便核实时间填写状态 {0:未填写,1:知道,2:不知道}")
    private int easyToVerifyTimeStatus;

    @ApiModelProperty("下发人id")
    private int operatorId;

    @ApiModelProperty("下发人组织")
    private String operatorOrg;

    /**
     * 下发的时候是否生成了工单
     * @see com.shuidihuzhu.cf.enums.BooleanEnum
     */
    @ApiModelProperty("是否走工单 {0:老数据无状态,1:是,2:否}")
    private int onWorkOrder;

    @ApiModelProperty("下发时间")
    private Date createTime;

    @ApiModelProperty("用户提交时间")
    private Date submitTime;

    @ApiModelProperty("处理人id")
    private int auditOperatorId;

    @ApiModelProperty("处理时间")
    private Date auditTime;

    @ApiModelProperty("省份id")
    private int provinceId;

    @ApiModelProperty("城市id")
    private int cityId;

    @ApiModelProperty("医院id")
    private int hospitalId;

    @ApiModelProperty("省份")
    private String provinceName;

    @ApiModelProperty("城市")
    private String cityName;

    public CfHospitalAuditInfo() {
    }

    public CfHospitalAuditInfo(long id,
                               String infoUuid,
                               String patientName,
                               String hospitalName,
                               String department,
                               String floorNumber,
                               String bedNumber,
                               String hospitalizationNumber,
                               String doctorName,
                               String departmentTelNumber,
                               String operatorContent,
                               int auditStatus,
                               int type) {
        this.id = id;
        this.infoUuid = infoUuid;
        this.patientName = patientName;
        this.hospitalName = hospitalName;
        this.department = department;
        this.floorNumber = floorNumber;
        this.bedNumber = bedNumber;
        this.hospitalizationNumber = hospitalizationNumber;
        this.doctorName = doctorName;
        this.departmentTelNumber = departmentTelNumber;
        this.auditStatus = auditStatus;
        this.operatorContent = operatorContent;
        this.type = type;
    }

}
