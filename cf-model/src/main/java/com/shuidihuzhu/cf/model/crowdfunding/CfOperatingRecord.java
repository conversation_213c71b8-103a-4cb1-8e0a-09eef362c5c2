package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.common.web.model.AbstractModel;

import java.sql.Timestamp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
public class CfOperatingRecord extends AbstractModel {

	private static final long serialVersionUID = 3695846068131292201L;

	private long id;
	private String infoUuid;
	private int type;
	private int role;
	private long userId;
	private String userName;
	private long bizId;
	private Timestamp dateCreated;
	private Timestamp lastModified;
	private String comment;
	private int refuseCount;
	private String financialComment = "";
	/**
	 * 用户修改图文渠道 0：非自主 1：自主
	 */
	private int twModifyChannel;

	/**
	 * 代录入id标识，案例发起后案例Id和代录入绑定，在消息中透传字段给代录入侧使用的
	 */
	private String reportLinkInfo;

}
