package com.shuidihuzhu.cf.model.crowdfunding;

import org.apache.commons.lang3.builder.ToStringBuilder;

import java.sql.Timestamp;

public class CfDrawCashQueryRecord {

	private Long id;
	private Long drawCashLaunchRecordId;
	private String tradeNo;
	private String thirdTradeNo;
	private int thirdType;
	private Timestamp resTime;
	private String resCode;
	private String resMsg;
	private String tradeStatus;
	private int status;
	private Timestamp dateCreated;
	private Timestamp lastModified;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getDrawCashLaunchRecordId() {
		return drawCashLaunchRecordId;
	}

	public void setDrawCashLaunchRecordId(Long drawCashLaunchRecordId) {
		this.drawCashLaunchRecordId = drawCashLaunchRecordId;
	}

	public String getTradeNo() {
		return tradeNo;
	}

	public void setTradeNo(String tradeNo) {
		this.tradeNo = tradeNo;
	}

	public String getThirdTradeNo() {
		return thirdTradeNo;
	}

	public void setThirdTradeNo(String thirdTradeNo) {
		this.thirdTradeNo = thirdTradeNo;
	}

	public Integer getThirdType() {
		return thirdType;
	}

	public void setThirdType(int thirdType) {
		this.thirdType = thirdType;
	}

	public Timestamp getResTime() {
		return resTime;
	}

	public void setResTime(Timestamp resTime) {
		this.resTime = resTime;
	}

	public String getResCode() {
		return resCode;
	}

	public void setResCode(String resCode) {
		this.resCode = resCode;
	}

	public String getResMsg() {
		return resMsg;
	}

	public void setResMsg(String resMsg) {
		this.resMsg = resMsg;
	}

	public String getTradeStatus() {
		return tradeStatus;
	}

	public void setTradeStatus(String tradeStatus) {
		this.tradeStatus = tradeStatus;
	}

	public Timestamp getDateCreated() {
		return dateCreated;
	}

	public void setDateCreated(Timestamp dateCreated) {
		this.dateCreated = dateCreated;
	}

	public Timestamp getLastModified() {
		return lastModified;
	}

	public void setLastModified(Timestamp lastModified) {
		this.lastModified = lastModified;
	}
	
	public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}

}
