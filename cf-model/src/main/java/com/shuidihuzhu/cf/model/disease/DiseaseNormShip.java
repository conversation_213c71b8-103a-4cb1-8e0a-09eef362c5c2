package com.shuidihuzhu.cf.model.disease;

import lombok.Data;

import java.sql.Timestamp;

/**
 * @Author：liu<PERSON><PERSON>
 * @Date：2021/8/25
 */
@Data
public class DiseaseNormShip {

    private long id;
    private String infoId;
    private int caseId;
    private String diseaseName;
    private String diseaseNormShip;
    private int isDelete;
    private Timestamp createTime;
    private Timestamp updateTime;

}
