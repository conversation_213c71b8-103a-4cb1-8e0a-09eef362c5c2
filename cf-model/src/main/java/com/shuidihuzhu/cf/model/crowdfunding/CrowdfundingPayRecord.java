package com.shuidihuzhu.cf.model.crowdfunding;

import com.shuidihuzhu.common.web.model.AbstractModel;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.sql.Timestamp;
import java.util.Date;

/**
 * Created by lgj on 16/6/21.
 */
public class CrowdfundingPayRecord extends AbstractModel {
	private static final long serialVersionUID = 3142190598947320481L;

	private Integer id;

    private String payUid;

//    private Integer crowdfundingOrderId;
    private Long crowdfundingOrderId;

    private String payPlatform;

    private Integer prePayAmount;

    private Integer realPayAmount;

    private Integer payStatus;

    private Date ctime;

    private Date callbackTime;

    private Integer valid;

    private Integer refundStatus;

    private Timestamp refundTime;

    private Integer refundAmount;

    private String refundReason;

    private Integer payService;

    private Date lastModified;

    public Date getLastModified() {
        return lastModified;
    }

    public void setLastModified(Date lastModified) {
        this.lastModified = lastModified;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getPayUid() {
        return payUid;
    }

    public void setPayUid(String payUid) {
        this.payUid = payUid;
    }

    public Long getCrowdfundingOrderId() {
        return crowdfundingOrderId;
    }

    public void setCrowdfundingOrderId(Long crowdfundingOrderId) {
        this.crowdfundingOrderId = crowdfundingOrderId;
    }


    public String getPayPlatform() {
        return payPlatform;
    }

    public void setPayPlatform(String payPlatform) {
        this.payPlatform = payPlatform;
    }

    public Integer getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(Integer payStatus) {
        this.payStatus = payStatus;
    }

    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    public Date getCallbackTime() {
        return callbackTime;
    }

    public void setCallbackTime(Date callbackTime) {
        this.callbackTime = callbackTime;
    }

    public Integer getValid() {
        return valid;
    }

    public void setValid(Integer valid) {
        this.valid = valid;
    }

    public Integer getRealPayAmount() {
        return realPayAmount;
    }

    public void setRealPayAmount(Integer realPayAmount) {
        this.realPayAmount = realPayAmount;
    }

    public Integer getPrePayAmount() {
        return prePayAmount;
    }

    public void setPrePayAmount(Integer prePayAmount) {
        this.prePayAmount = prePayAmount;
    }

	public Integer getRefundStatus() {
		return refundStatus;
	}

	public void setRefundStatus(Integer refundStatus) {
		this.refundStatus = refundStatus;
	}

	public Timestamp getRefundTime() {
		return refundTime;
	}

	public void setRefundTime(Timestamp refundTime) {
		this.refundTime = refundTime;
	}

	public Integer getRefundAmount() {
		return refundAmount;
	}

	public void setRefundAmount(Integer refundAmount) {
		this.refundAmount = refundAmount;
	}

	public String getRefundReason() {
		return refundReason;
	}

	public void setRefundReason(String refundReason) {
		this.refundReason = refundReason;
	}

	public Integer getPayService() {
		return payService;
	}

	public void setPayService(Integer payService) {
		this.payService = payService;
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this);
	}

	public String getShardingCrowdfundingOrderIdTableNameSuffix(){
        return String.format("%03d", Math.abs(this.crowdfundingOrderId) % 100);
    }
    public String getShardingPayUidTableNameSuffix(){
        return String.format("%03d", Math.abs(this.payUid.hashCode() & 0x7FFFFFFF) % 100);
    }
}
