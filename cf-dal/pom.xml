<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
	<packaging>jar</packaging>
	<artifactId>cf-dal</artifactId>
  <version>3.6.657-SNAPSHOT</version>

	<parent>
		<groupId>com.shuidihuzhu.cf</groupId>
		<artifactId>cf-api-parent</artifactId>
		<version>3.6.657-SNAPSHOT</version>
	</parent>

	<properties>
		<maven.source.skip>false</maven.source.skip>
	</properties>

	<dependencies>
		<dependency>
			<groupId>com.shuidihuzhu.cf</groupId>
			<artifactId>cf-model</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>xstream</artifactId>
					<groupId>com.thoughtworks.xstream</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.shuidihuzhu.cf</groupId>
			<artifactId>cf-finance-common-model</artifactId>
		</dependency>
		<dependency>
			<groupId>com.shuidihuzhu.cf</groupId>
			<artifactId>cf-api-pure-client</artifactId>
		</dependency>
    </dependencies>
</project>
