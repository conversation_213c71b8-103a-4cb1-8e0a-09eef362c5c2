<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.mina.CfMinaQuizAnswerDao">

    <sql id="table_name">
        `cf_mina_quiz_answer`
    </sql>

    <sql id="select_fields">
        `id` as id,
        `mina_quiz_question_id` as minaQuizQuestionId,
        `option_sequence` as optionSequence,
        `answer` as answer,
        `answer_img_url` as answerImgUrl,
        `score` as score
    </sql>

    <sql id="insert_fields">
        `mina_quiz_question_id`,
        `option_sequence`,
        `answer`,
        `answer_img_url`,
        `score`
    </sql>
    
    <insert id="insertBatch" parameterType="com.shuidihuzhu.cf.model.mina.CfMinaQuizAnswer">
        INSERT INTO <include refid="table_name"/>
        (`mina_quiz_question_id`,`answer`,`score`,`option_sequence`) VALUES
        <foreach collection="list" item="it" separator=",">
            (#{it.minaQuizQuestionId},#{it.answer},#{it.score},#{it.optionSequence})
        </foreach>
    </insert>

    <select id="findByQuestionIds" resultType="com.shuidihuzhu.cf.model.mina.CfMinaQuizAnswer">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE is_delete = 0
        AND mina_quiz_question_id in
        <foreach collection="questionIds" item="questionId" open="(" close=")" separator=",">
            #{questionId}
        </foreach>
    </select>

    <select id="findByIds" resultType="com.shuidihuzhu.cf.model.mina.CfMinaQuizAnswer">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE is_delete = 0
        AND id IN
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <update id="updateDeleteByQuestionIds">
        update <include refid="table_name"/>
        SET is_delete = #{isDelete}
        WHERE mina_quiz_question_id in
        <foreach collection="questionIds" item="questionId" open="(" close=")" separator=",">
            #{questionId}
        </foreach>
    </update>
</mapper>