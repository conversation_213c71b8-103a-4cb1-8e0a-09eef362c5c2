<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.mina.CfMinaQuizShareDao">

    <sql id="table_name">
        `cf_mina_quiz_share`
    </sql>

    <sql id="select_fields">
        `id` as id,
        `mina_quiz_id` as minaQuizId,
        `user_id` as userId
    </sql>

    <sql id="insert_fields">
        `mina_quiz_id`,
        `user_id`
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.mina.CfMinaQuizShare">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        INSERT INTO
        <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES (#{minaQuizId},#{userId})
    </insert>

    <select id="getByUserIdAndQuizId" resultType="com.shuidihuzhu.cf.model.mina.CfMinaQuizShare">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE mina_quiz_id = #{minaQuizId}
        AND user_id = #{userId}
    </select>

    <select id="getById" resultType="com.shuidihuzhu.cf.model.mina.CfMinaQuizShare">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE id = #{id}
    </select>
</mapper>