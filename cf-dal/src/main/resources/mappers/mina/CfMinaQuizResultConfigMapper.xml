<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.mina.CfMinaQuizResultConfigDao">

    <sql id="table_name">
        `cf_mina_quiz_result_config`
    </sql>

    <sql id="select_fields">
        `id` as id,
        `mina_quiz_result_id` as minaQuizResultId,
        `button_text` as buttonText,
        `button_style` as buttonStyle,
        `action_type` as actionType,
        `action_url` as actionUrl,
        `app_id` as appId
    </sql>

    <sql id="insert_fields">
        `mina_quiz_result_id`,
        `button_text`,
        `button_style`,
        `action_type`,
        `action_url`,
        `app_id`
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.mina.CfMinaQuizResultConfig">
        INSERT INTO
        <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES (#{minaQuizResultId},#{buttonText},#{buttonStyle},#{actionType},#{actionUrl},#{appId})
    </insert>

    <update id="updateInvalidById">
        UPDATE
        <include refid="table_name"/>
        SET is_delete = 1
        WHERE id = #{id}
    </update>

    <select id="findByQuizResultId" resultType="com.shuidihuzhu.cf.model.mina.CfMinaQuizResultConfig">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE mina_quiz_result_id = #{minaQuizResultId}
        AND is_delete = 0
    </select>

    <select id="findByQuizResultIds" resultType="com.shuidihuzhu.cf.model.mina.CfMinaQuizResultConfig">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE is_delete = 0
        AND mina_quiz_result_id in
        <foreach collection="quizResultIds" item="quizResultId" open="(" separator="," close=")">
            #{quizResultId}
        </foreach>
    </select>

</mapper>
