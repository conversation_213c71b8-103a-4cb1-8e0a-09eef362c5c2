<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.mina.CfMinaMajorDiseaseRegisterDao">
    <sql id="table_name">
        `cf_mina_major_disease_register`
    </sql>

    <sql id="insert_fields">
        `biz_id`,`open_id`,`form_id`,`status`
    </sql>

    <sql id="select_fields">
        `id`,`biz_id`,`open_id`,`form_id`,`status`,`notify_time`
    </sql>

    <insert id="insertOne" parameterType="com.shuidihuzhu.cf.model.mina.CfMinaMajorDiseaseRegister">
        INSERT INTO <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES
        (#{bizId},#{openId},#{formId},#{status})
    </insert>
</mapper>