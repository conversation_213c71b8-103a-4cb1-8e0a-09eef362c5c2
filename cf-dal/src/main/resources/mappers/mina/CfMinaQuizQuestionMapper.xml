<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.mina.CfMinaQuizQuestionDao">

    <sql id="table_name">
        `cf_mina_quiz_question`
    </sql>

    <sql id="select_fields">
        `id` as id,
        `mina_quiz_id` as minaQuizId,
        `question` as question,
        `question_img_url` as questionImgUrl
    </sql>

    <sql id="insert_fields">
        `mina_quiz_id`,
        `question`,
        `question_img_url`
    </sql>


    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.mina.CfMinaQuizQuestion">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        INSERT INTO <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES (#{minaQuizId},#{question},#{questionImgUrl})
    </insert>

    <select id="findByMinaQuizId" resultType="com.shuidihuzhu.cf.model.mina.CfMinaQuizQuestion">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE mina_quiz_id = #{minaQuizId}
        AND is_delete = 0
    </select>

    <update id="updateDeleteByMinaQuizId">
        UPDATE <include refid="table_name"/>
        SET is_delete = #{isDelete}
        WHERE mina_quiz_id = #{minaQuizId}
    </update>
</mapper>