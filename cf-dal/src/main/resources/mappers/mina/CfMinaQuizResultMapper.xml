<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.mina.CfMinaQuizResultDao">

    <sql id="table_name">
        `cf_mina_quiz_result`
    </sql>

    <sql id="select_fields">
        `id` as id,
        `mina_quiz_id` as minaQuizId,
        `begin_score` as beginScore,
        `end_score` as endScore,
        `score_prefix` as scorePrefix,
        `score_suffix` as scoreSuffix,
        `img_url` as imgUrl,
        `result` as result,
        `share_title` as shareTitle,
        `share_img_url` as shareImgUrl
    </sql>

    <sql id="insert_fields">
        `mina_quiz_id`,
        `begin_score`,
        `end_score`,
        `score_prefix`,
        `score_suffix`,
        `img_url`,
        `result`,
        `share_img_url`
    </sql>


    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.mina.CfMinaQuizResult">
        <selectKey keyProperty="id" resultType="java.lang.Integer" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        INSERT INTO <include refid="table_name"/>
        (<include refid="insert_fields"/>) VALUES
        (#{minaQuizId},#{beginScore},#{endScore},#{scorePrefix},#{scoreSuffix},#{imgUrl},#{result},#{shareImgUrl})
    </insert>


    <select id="getByQuizIdAndScore" resultType="com.shuidihuzhu.cf.model.mina.CfMinaQuizResult">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE  mina_quiz_id = #{minaQuizId}
        AND is_delete = 0
        AND begin_score &lt;= #{score}
        AND end_score &gt; #{score}
        LIMIT 1
    </select>

    <select id="findByMinaQuizId" resultType="com.shuidihuzhu.cf.model.mina.CfMinaQuizResult">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE  mina_quiz_id = #{minaQuizId}
        AND is_delete = 0
    </select>

    <update id="updateDeleteByMinaQuizId">
        UPDATE <include refid="table_name"/>
        SET is_delete = #{isDelete}
        WHERE mina_quiz_id = #{minaQuizId}
    </update>
</mapper>