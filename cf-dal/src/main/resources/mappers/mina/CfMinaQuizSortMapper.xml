<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.mina.CfMinaQuizSortDao">

    <sql id="table_name">
        `cf_mina_quiz_sort`
    </sql>

    <sql id="select_fields">
        `id` as id,
        `app_id` as appId,
        `user_id` as userId,
        `sort_value` as sortValue,
        `show_new` as showNew
    </sql>

    <sql id="insert_fields">
        `app_id`,
        `user_id`,
        `sort_value`,
        `show_new`
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.mina.CfMinaQuizSort">
        INSERT INTO 
        <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES 
        (#{appId},#{userId},#{sortValue},#{showNew})
    </insert>
    
    <select id="getByAppIdWithUserId" resultType="com.shuidihuzhu.cf.model.mina.CfMinaQuizSort">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE user_id = #{userId}
        AND app_id = #{appId}
        AND is_delete = 0
        ORDER BY `id` DESC
        LIMIT 1
    </select>

    <select id="getByAppIdWithSelfTag" resultType="com.shuidihuzhu.cf.model.mina.CfMinaQuizSort">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE  `self_tag` = #{selfTag}
        AND app_id = #{appId}
        AND is_delete = 0
        ORDER BY `id` DESC
        LIMIT 1
    </select>
</mapper>