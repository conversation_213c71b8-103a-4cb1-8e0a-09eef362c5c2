<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.mina.CfMinaQuizPushConfigDao">

    <sql id="table_name">
        `cf_mina_quiz_push_config`
    </sql>

    <sql id="select_fields">
        `id` as id,
        `mina_quiz_id` as minaQuizId,
        `push_target` as pushTarget,
        `user_third_type` as userThirdType,
        `business_type` as businessType,
        `sub_business_type` as subBusinessType,
        `template_id` as templateId,
        `template_name` as templateName,
        `template_url` as templateUrl,
        `custom_url` as customUrl,
        `template_first` as templateFirst,
        `template_keyword_one` as templateKeywordOne,
        `template_keyword_two` as templateKeywordTwo,
        `template_keyword_three` as templateKeywordThree,
        `template_keyword_four` as templateKeywordFour,
        `template_keyword_five` as templateKeywordFive,
        `template_keyword_six` as templateKeywordSix,
        `template_remark` as templateRemark,
        `app_emphasis_keyword` as appEmphasisKeyword,
        `mini_program` as miniProgram,
        `visit_times` as visitTimes,
        `update_time` as updateTime,
        `status` as status
    </sql>

    <sql id="insert_fields">
        `mina_quiz_id`,
        `push_target`,
        `user_third_type`,
        `business_type`,
        `sub_business_type`,
        `template_id`,
        `template_name`,
        `template_url`,
        `custom_url`,
        `template_first`,
        `template_keyword_one`,
        `template_keyword_two`,
        `template_keyword_three`,
        `template_keyword_four`,
        `template_keyword_five`,
        `template_keyword_six`,
        `template_remark`,
        `app_emphasis_keyword`,
        `mini_program`,
        `visit_times`,
        `status`
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.mina.CfMinaQuizPushConfig">
        INSERT INTO
        <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES
        (#{minaQuizId},#{pushTarget},#{userThirdType},#{businessType},#{subBusinessType},#{templateId},#{templateName},#{templateUrl},#{customUrl},
        #{templateFirst},#{templateKeywordOne},#{templateKeywordTwo},#{templateKeywordThree},#{templateKeywordFour},#{templateKeywordFive},#{templateKeywordSix},
        #{templateRemark},#{appEmphasisKeyword},#{miniProgram},#{visitTimes},#{status})
    </insert>

    <update id="updateById" parameterType="com.shuidihuzhu.cf.model.mina.CfMinaQuizPushConfig">
        UPDATE
        <include refid="table_name"/>
        SET mina_quiz_id = #{minaQuizId},push_target = #{pushTarget},user_third_type = #{userThirdType},business_type =
        #{businessType},sub_business_type = #{subBusinessType},template_id = #{templateId},template_name =
        #{templateName},template_url = #{templateUrl},custom_url = #{customUrl},
        template_first = #{templateFirst},template_keyword_one = #{templateKeywordOne},template_keyword_two =
        #{templateKeywordTwo},template_keyword_three = #{templateKeywordThree},template_keyword_four =
        #{templateKeywordFour},template_keyword_five = #{templateKeywordFive},template_keyword_six =
        #{templateKeywordSix},
        template_remark = #{templateRemark},app_emphasis_keyword = #{appEmphasisKeyword}
        WHERE id = #{id}
    </update>

    <select id="getByThirdType" resultType="com.shuidihuzhu.cf.model.mina.CfMinaQuizPushConfig">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE user_third_type = #{thirdType}
        AND sub_business_type = #{sbType}
        AND is_delete = 0
    </select>

    <select id="getVisitTimes" resultType="java.lang.Integer">
        SELECT SUM(visit_times) FROM
        <include refid="table_name"/>
        WHERE sub_business_type = #{sbType}
        AND is_delete = 0
    </select>

    <update id="updateVisitTimesById">
        UPDATE
        <include refid="table_name"/>
        SET visit_times = visit_times + 1
        WHERE id = #{id}
    </update>

    <select id="getByMinaQuizIdAndSbType" resultType="com.shuidihuzhu.cf.model.mina.CfMinaQuizPushConfig">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE mina_quiz_id = #{minaQuizId}
        AND user_third_type = #{thirdType}
        AND sub_business_type = #{sbType}
        AND is_delete = 0
    </select>

    <update id="updateStatusById">
        UPDATE
        <include refid="table_name"/>
        SET status = #{status},
        update_time = now()
        WHERE id = #{id}
    </update>

    <select id="findByOffset" resultType="com.shuidihuzhu.cf.model.mina.CfMinaQuizPushConfig">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE is_delete = 0
        ORDER BY create_time DESC
        limit #{offset},#{limit}
    </select>

    <select id="countAll" resultType="java.lang.Integer">
        SELECT count(1) FROM
        <include refid="table_name"/>
        WHERE is_delete = 0
    </select>

    <select id="getById" resultType="com.shuidihuzhu.cf.model.mina.CfMinaQuizPushConfig">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE id = #{id}
    </select>
</mapper>