<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.mina.CfMinaQuizDao">

    <sql id="table_name">
        `cf_mina_quiz`
    </sql>

    <sql id="select_fields">
        `id` as id,
        `app_id` as appId,
        `title` as title,
        `title_img_url` as titleImgUrl,
        `share_title` as shareTitle,
        `share_img_url` as shareImgUrl,
        `times` as times,
        `sort` as sort,
        `is_new` as isNew,
        `show_type` as showType,
        `quiz_type` as quizType,
        `release_time` as releaseTime,
        `result_default_share_img_url` as resultDefaultShareImgUrl,
        `result_default_img_url` as resultDefaultImgUrl,
        `is_delete` as isDelete
    </sql>

    <sql id="insert_fields">
        `app_id`,
        `title`,
        `title_img_url`,
        `share_title`,
        `share_img_url`,
        `quiz_type`,
        `result_default_share_img_url`,
        `result_default_img_url`,
        `is_delete`
    </sql>


    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.mina.CfMinaQuiz">
        <selectKey keyProperty="id" resultType="java.lang.Integer" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        INSERT INTO
        <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES (#{appId},#{title},#{titleImgUrl},#{shareTitle},#{shareImgUrl},#{quizType},
        #{resultDefaultShareImgUrl},#{resultDefaultImgUrl},#{isDelete})
    </insert>

    <select id="findByAppId" resultType="com.shuidihuzhu.cf.model.mina.CfMinaQuiz">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE app_id = #{appId}
        AND is_delete = 0
        ORDER BY `sort`, `id` DESC
        LIMIT #{offset}, #{limit}
    </select>

    <select id="findByAppIdAndType" resultType="com.shuidihuzhu.cf.model.mina.CfMinaQuiz">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE app_id = #{appId}
        AND show_type = #{showType}
        AND is_delete = 0
        ORDER BY `sort`, `id` DESC
        LIMIT #{offset}, #{limit}
    </select>

    <select id="findByShowType" resultType="com.shuidihuzhu.cf.model.mina.CfMinaQuiz">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE show_type = #{showType}
        AND is_delete = 0
        ORDER BY `sort` DESC , `id` DESC
        LIMIT #{offset}, #{limit}
    </select>

    <select id="findByTitle" resultType="com.shuidihuzhu.cf.model.mina.CfMinaQuiz">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        <where>
            <if test="title != null and title != ''">
                AND title like CONCAT('%',#{title},'%' )
            </if>
        </where>
        ORDER BY create_time DESC
        limit #{offset},#{limit}
    </select>

    <select id="countByTitle" resultType="java.lang.Integer">
        SELECT count(1) FROM
        <include refid="table_name"/>
        <where>
            <if test="title != null and title != ''">
                AND title like CONCAT('%',#{title},'%' )
            </if>
        </where>
    </select>

    <select id="countByAppId" resultType="java.lang.Integer">
        SELECT count(1) FROM
        <include refid="table_name"/>
        WHERE app_id = #{appId}
        AND is_delete = 0
    </select>

    <select id="getById" resultType="com.shuidihuzhu.cf.model.mina.CfMinaQuiz">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE id = #{id}
    </select>

    <update id="updateTimesById">
        UPDATE
        <include refid="table_name"/>
        SET times = times + 1
        WHERE id = #{id}
    </update>

    <select id="findByAnchorId" resultType="com.shuidihuzhu.cf.model.mina.CfMinaQuiz">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE id &gt; #{anchorId}
        ORDER BY id
        limit #{limit}
    </select>

    <update id="updateShowTypeById">
        UPDATE <include refid="table_name"/>
        SET show_type = #{showType}
        WHERE id = #{id}
    </update>

    <update id="updateIsNewById">
        UPDATE <include refid="table_name"/>
        SET is_new = #{isNew}
        WHERE id = #{id}
    </update>

    <update id="updateSortById">
        UPDATE <include refid="table_name"/>
        SET sort = #{sort}
        WHERE id = #{id}
    </update>

    <update id="updateReleaseTimeById">
        UPDATE <include refid="table_name"/>
        SET release_time = #{releaseTime}
        WHERE id = #{id}
    </update>

    <update id="updateIsDeleteById">
        UPDATE <include refid="table_name"/>
        SET is_delete = #{isDelete}
        WHERE id = #{id}
    </update>

    <select id="getBySortAndIsNew" resultType="com.shuidihuzhu.cf.model.mina.CfMinaQuiz">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE is_new = #{isNew}
        AND sort = #{sort}
        AND show_type = 1
        limit 1
    </select>

    <update id="updateInfoById" parameterType="com.shuidihuzhu.cf.model.mina.CfMinaQuiz">
        UPDATE <include refid="table_name"/>
        SET title = #{title},
        title_img_url = #{titleImgUrl},
        quiz_type = #{quizType},
        share_title = #{shareTitle},
        result_default_share_img_url = #{resultDefaultShareImgUrl},
        result_default_img_url = #{resultDefaultImgUrl},
        share_img_url = #{shareImgUrl}
        WHERE id = #{id}
    </update>
</mapper>