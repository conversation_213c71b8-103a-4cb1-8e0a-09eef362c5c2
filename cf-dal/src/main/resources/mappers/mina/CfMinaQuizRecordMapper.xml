<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.mina.CfMinaQuizRecordDao">

    <sql id="table_name">
        `cf_mina_quiz_record`
    </sql>

    <sql id="select_fields">
        `id` as id,
        `app_id` as appId,
        `mina_quiz_id` as minaQuizId,
        `user_id` as userId,
        `quiz_answer` as quizAnswer,
        `self_tag` as selfTag,
        `score` as score,
        `mina_quiz_result_id` as minaQuizResultId,
        `times` as times,
        `mina_quiz_share_id` as minaQuizShareId
    </sql>

    <sql id="insert_fields">
        `app_id`,
        `mina_quiz_id`,
        `user_id`,
        `quiz_answer`,
        `score`,
        `self_tag`,
        `mina_quiz_result_id`,
        `times`,
        `mina_quiz_share_id`
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.mina.CfMinaQuizRecord">
        INSERT INTO
        <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES (#{appId},#{minaQuizId},#{userId},#{quizAnswer},#{score}, #{selfTag},#{minaQuizResultId},#{times},#{minaQuizShareId})
    </insert>

    <update id="updateAnswerAndScore" parameterType="com.shuidihuzhu.cf.model.mina.CfMinaQuizRecord">
        UPDATE
        <include refid="table_name"/>
        SET
        `quiz_answer`=#{quizAnswer},
        `score`=#{score},
        `user_id`=#{userId},
        `self_tag`=#{selfTag},
        `app_id`=#{appId},
        `mina_quiz_result_id` = #{minaQuizResultId}
        WHERE
        `id`=#{id}
    </update>

    <select id="listByUserIdAndQuizIdList" resultType="com.shuidihuzhu.cf.model.mina.CfMinaQuizRecord">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE `user_id`=#{userId}
        AND `mina_quiz_id` in
        <foreach collection="quizIdList" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        AND `is_delete`=0
    </select>

    <select id="listBySelfTagAndQuizIdList" resultType="com.shuidihuzhu.cf.model.mina.CfMinaQuizRecord">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE `self_tag`=#{selfTag}
        AND `mina_quiz_id` in
        <foreach collection="quizIdList" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        AND `is_delete`=0
    </select>

    <select id="findByUserIdAndQuizId" resultType="com.shuidihuzhu.cf.model.mina.CfMinaQuizRecord">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE `user_id`=#{userId}
        AND `mina_quiz_id`=#{quizId}
        AND `mina_quiz_share_id` = 0
        AND `is_delete`=0
        ORDER BY `id` DESC
        LIMIT 1
    </select>

    <select id="findBySelfTagAndQuizId" resultType="com.shuidihuzhu.cf.model.mina.CfMinaQuizRecord">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE `self_tag`=#{selfTag}
        AND `mina_quiz_id`=#{quizId}
        AND `mina_quiz_share_id` = 0
        AND `is_delete`=0
        ORDER BY `id` DESC
        LIMIT 1
    </select>

    <select id="findUserIdByQuizIds" resultType="java.lang.Long">
        SELECT distinct user_id
        FROM
        <include refid="table_name"/>
        WHERE `mina_quiz_id` in
        <foreach collection="quizIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        AND `is_delete`=0
        AND id &gt;= #{anchorId}
        limit #{limit}
    </select>

    <select id="findByUserIds" resultType="com.shuidihuzhu.cf.model.mina.CfMinaQuizRecord">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE mina_quiz_id = #{minaQuizId}
        AND user_id in
        <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
            #{userId}
        </foreach>
        AND `is_delete`=0
    </select>

    <select id="getLastQuizByUserId" resultType="com.shuidihuzhu.cf.model.mina.CfMinaQuizRecord">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE user_id = #{userId}
        AND mina_quiz_id = #{minaQuizId}
        AND mina_quiz_share_id = #{minaQuizShareId}
        ORDER BY times DESC
        limit 1
    </select>

    <select id="findByQuizShareId" resultType="com.shuidihuzhu.cf.model.mina.CfMinaQuizRecord">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE mina_quiz_share_id = #{minaQuizShareId}
        ORDER BY create_time DESC
        limit #{offset},#{limit}
    </select>

</mapper>