<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.mina.CfMinaMajorDiseaseInfoDao">
    <sql id="table_name">
        `cf_mina_major_disease_info`
    </sql>

    <sql id="insert_fields">
        `disease_name`,
        `describe`,
        `desc_img_url`
    </sql>

    <sql id="select_fields">
        `id`,
        `disease_name`,
        `describe`,
        `desc_img_url`
    </sql>

    <select id="selectById" resultType="com.shuidihuzhu.cf.model.mina.CfMinaMajorDiseaseInfo">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE id=#{id}
        AND `disable`=0
    </select>

    <insert id="insertOne" parameterType="com.shuidihuzhu.cf.model.mina.CfMinaMajorDiseaseInfo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES
        (#{diseaseName}, #{describe}, #{descImgUrl})
    </insert>

</mapper>