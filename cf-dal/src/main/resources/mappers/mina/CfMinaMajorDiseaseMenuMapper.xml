<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.mina.CfMinaMajorDiseaseMenuDao">
    <sql id="table_name">
        `cf_mina_major_disease_menu`
    </sql>

    <sql id="insert_fields">
        `disease_id`,
        `menu_name`,
        `menu_order`,
        `menu_img_url`,
        `describe`
    </sql>

    <sql id="select_fields">
        `id`,
        `disease_id`,
        `menu_name`,
        `menu_order`,
        `menu_img_url`,
        `describe`
    </sql>

    <select id="selectByDiseaseId" resultType="com.shuidihuzhu.cf.model.mina.CfMinaMajorDiseaseMenu">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `disease_id`=#{diseaseId}
        AND `disable`=0
        ORDER BY `disease_id` ASC,`menu_order` ASC
    </select>

    <insert id="insertOne" parameterType="com.shuidihuzhu.cf.model.mina.CfMinaMajorDiseaseMenu" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES
        (#{diseaseId},#{menuName},#{menuOrder},#{menuImgUrl},#{describe})
    </insert>

    <insert id="insertList" parameterType="com.shuidihuzhu.cf.model.mina.CfMinaMajorDiseaseMenu">
        INSERT INTO
        <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.diseaseId},#{item.menuName},#{item.menuOrder},#{item.menuImgUrl},#{item.describe})
        </foreach>
    </insert>

    <update id="updateOrder">
        UPDATE <include refid="table_name"/>
        SET `menu_order`=#{menuOrder}
        WHERE `id`=#{id}
    </update>

    <select id="selectIdByDiseaseIdAndOrder" resultType="com.shuidihuzhu.cf.model.mina.CfMinaMajorDiseaseMenu">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `disease_id`=#{diseaseId}
        AND `menu_order`=#{menuOrder}
    </select>
</mapper>