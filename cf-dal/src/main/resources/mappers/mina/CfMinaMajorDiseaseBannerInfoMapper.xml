<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.mina.CfMinaMajorDiseaseBannerInfoDao">
    <sql id="table_name">
        `cf_mina_major_disease_banner_info`
    </sql>

    <sql id="insert_fields">
        `disease_id`,
        `banner_img_url`,
        `banner_order`,
        `banner_text`
    </sql>

    <sql id="select_fields">
        `id`,
        `disease_id`,
        `banner_img_url`,
        `banner_order`,
        `banner_text`
    </sql>

    <select id="selectByDiseaseId" resultType="com.shuidihuzhu.cf.model.mina.CfMinaMajorDiseaseBannerInfo">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `disease_id`=#{diseaseId}
        AND `disable`=0
        ORDER BY `banner_order` ASC
    </select>

    <insert id="insertOne" parameterType="com.shuidihuzhu.cf.model.mina.CfMinaMajorDiseaseBannerInfo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES
        (#{diseaseId},#{bannerImgUrl},#{bannerOrder},#{bannerText})
    </insert>

    <select id="selectMaxOrder" resultType="int">
        SELECT MAX(`banner_order`)
        FROM <include refid="table_name"/>
        WHERE `disease_id`=#{diseaseId}
        AND `disable`=0
    </select>

    <update id="updateOrder">
        UPDATE <include refid="table_name"/>
        SET `banner_order`=#{bannerOrder}
        WHERE `id`=#{id}
    </update>

    <insert id="insertList" parameterType="com.shuidihuzhu.cf.model.mina.CfMinaMajorDiseaseBannerInfo">
        INSERT INTO
        <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.diesaseId},#{item.bannerImgUrl},#{item.bannerOrder},#{item.bannerText})
        </foreach>
    </insert>
</mapper>