<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.mina.CfMiniQuizPushDao">

    <sql id="table_name">
        `cf_mina_quiz_push`
    </sql>

    <sql id="select_fields">
        `id` as id,
        `mina_quiz_id` as minaQuizId,
        `user_id` as userId
    </sql>

    <sql id="insert_fields">
        `mina_quiz_id`,
        `user_id`
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.mina.CfMiniQuizPush">
        insert into
        <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        values(#{minaQuizId},#{userId})
    </insert>

    <select id="findByQuizIdWithUserId" resultType="com.shuidihuzhu.cf.model.mina.CfMiniQuizPush">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE mina_quiz_id = #{minaQuizId}
        AND user_id IN
        <foreach collection="userIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        AND is_delete = 0
    </select>
</mapper>