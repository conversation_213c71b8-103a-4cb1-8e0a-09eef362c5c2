<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.mina.CfMinaMajorDiseaseTitleContentDao">
    <sql id="table_name">
        `cf_mina_major_disease_title_content`
    </sql>

    <sql id="insert_fields">
        `menu_id`,
        `title`,
        `title_order`,
        `content`
    </sql>

    <sql id="select_fields">
        `id`,
        `menu_id`,
        `title`,
        `title_order`,
        `content`
    </sql>

    <select id="selectByMenuId" resultType="com.shuidihuzhu.cf.model.mina.CfMinaMajorDiseaseTitleContent">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE <![CDATA[ `id`<#{anchorId} ]]>
        AND `disable`=0
        ORDER BY `id` DESC, `title_order` DESC
        LIMIT #{size}
    </select>

    <select id="selectIdByMenuId" resultType="int">
        SELECT MAX(`id`) FROM <include refid="table_name"/>
        WHERE `menu_id`=#{menuId}
        AND `disable`=0
    </select>

    <select id="selectByMenuIdAndOrder" resultType="java.lang.String">
        SELECT `content`
        FROM <include refid="table_name"/>
        WHERE `menu_id`=#{menuId}
        AND `title_order`=#{titleOrder}
        AND `disable`=0
    </select>
    
    <select id="selectByMenuIdList" resultType="com.shuidihuzhu.cf.model.mina.CfMinaMajorDiseaseTitleContent">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `menu_id`
        IN
        <foreach collection="set" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.mina.CfMinaMajorDiseaseTitleContent" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES
        (#{menuId},#{title},#{titleOrder},#{content})
    </insert>

    <insert id="insertList" parameterType="com.shuidihuzhu.cf.model.mina.CfMinaMajorDiseaseTitleContent">
        INSERT INTO
        <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.menuId},#{item.title},#{item.titleOrder},#{item.content})
        </foreach>
    </insert>
</mapper>