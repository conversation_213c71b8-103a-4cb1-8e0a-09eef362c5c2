<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.bigapp.AppUpdateDao">
	<sql id="tableName">
		cf_app_update
	</sql>

	<select id="getAppUpdateByVersion"  resultType="com.shuidihuzhu.cf.bigapp.AppUpdate">
		SELECT
		`is_update`,`is_force_update`,`descrip`,`update_version`,`update_url`
		FROM
		<include refid="tableName"/>
		WHERE
		`bundle_id` = #{bundleId}
		AND `system_type` = #{systemType}
		AND #{selfVersion} > `start_version`
		AND `end_version` > #{selfVersion}
		AND `is_delete` = '0'
		LIMIT 1
	</select>

</mapper>