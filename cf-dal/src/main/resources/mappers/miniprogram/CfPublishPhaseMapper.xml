<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.miniprogram.CfPublishPhaseDao">
    <sql id="TABLE">
        cf_xcx_publish_phase
    </sql>
    <sql id="FIELDS">
        `id` AS id,
        `keyword_id` AS keywordId,
        `phase_id` AS phaseId,
        `publish_status` AS publishStatus,
        `publish_time` AS publishTime,
        `is_delete` AS isDelete
    </sql>
    
    <insert id="insertOne" parameterType="com.shuidihuzhu.cf.model.miniprogram.CfPublishPhase">
        INSERT INTO <include refid="TABLE"/>
        (`keyword_id`, `phase_id`, `publish_time`)
        VALUES 
        (#{cfPublishPhase.keywordId}, #{cfPublishPhase.phaseId}, #{cfPublishPhase.publishTime})
    </insert>
    
    <update id="deleteById">
        UPDATE <include refid="TABLE"/>
        SET `is_delete`=1
        WHERE `id`=#{id}
    </update>

    <update id="deleteByPhaseId">
        UPDATE <include refid="TABLE"/>
        SET `is_delete`=1
        WHERE `phase_id`=#{phaseId}
    </update>

    <update id="updateByPhaseId" parameterType="com.shuidihuzhu.cf.model.miniprogram.CfPublishPhase">
        UPDATE <include refid="TABLE"/>
        <set>
            <if test="cfPublishPhase.id != null">
                `id` = #{cfPublishPhase.id},
            </if>
            <if test="cfPublishPhase.keywordId != null">
                `keyword_id` = #{cfPublishPhase.keywordId},
            </if>
            <if test="phaseId != null">
                `phase_id` = #{phaseId},
            </if>
            <if test="cfPublishPhase.publishStatus != null">
                `publish_status` = #{cfPublishPhase.publishStatus},
            </if>
            <if test="cfPublishPhase.publishTime != null">
                `publish_time` = #{cfPublishPhase.publishTime},
            </if>
            <if test="cfPublishPhase.isDelete != null">
                `is_delete` = #{cfPublishPhase.isDelete},
            </if>
        </set>
        WHERE `phase_id`=#{phaseId}
        AND `is_delete`=0
    </update>

    <select id="selectByPhaseId" resultType="com.shuidihuzhu.cf.model.miniprogram.CfPublishPhase">
        SELECT <include refid="FIELDS"/>
        FROM <include refid="TABLE"/>
        WHERE `phase_id`=#{phaseId}
        AND `is_delete`=0
    </select>

    <select id="selectNextByTime" resultType="com.shuidihuzhu.cf.model.miniprogram.CfPublishPhase">
        SELECT <include refid="FIELDS"/>
        FROM <include refid="TABLE"/>
        WHERE `publish_time`>#{start}
        AND `publish_time` <![CDATA[ <= ]]> #{end}
        AND `is_delete`=0
        ORDER BY `publish_time` ASC
        limit 1
    </select>

    <select id="getList" resultType="com.shuidihuzhu.cf.model.miniprogram.CfPublishPhase">
        SELECT <include refid="FIELDS"/>
        FROM <include refid="TABLE"/>
        WHERE `is_delete`=0
        ORDER BY `id` DESC
    </select>

    <select id="listByTimeLimit" resultType="com.shuidihuzhu.cf.model.miniprogram.CfPublishPhase">
        SELECT <include refid="FIELDS"/>
        FROM <include refid="TABLE"/>
        WHERE `publish_time`<![CDATA[ <= ]]> #{time}
        AND `is_delete`=0
        ORDER BY `publish_time` DESC
        <if test="limit != 0">
            limit #{limit}
        </if>
    </select>

    <select id="listByPhaseIds" resultType="com.shuidihuzhu.cf.model.miniprogram.CfPublishPhase">
        SELECT <include refid="FIELDS"/>
        FROM <include refid="TABLE"/>
        WHERE `phase_id` IN
        <foreach collection="phaseIds" item="phaseId" open="(" separator="," close=")">
            #{phaseId}
        </foreach>
        AND `is_delete`=0
    </select>
</mapper>