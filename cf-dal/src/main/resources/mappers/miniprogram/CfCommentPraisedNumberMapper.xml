<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.miniprogram.CfCommentPraisedNumberDao">
    <sql id="TABLE">
        cf_xcx_comment_praised_number
    </sql>
    <sql id="FIELDS">
        `id` AS id,
        `user_id` AS userId,
        `praised_number` AS praisedNumber,
        `date` AS date,
        `is_delete` AS isDelete
    </sql>
    
    <insert id="insertOne">
        INSERT INTO <include refid="TABLE"/>
        (`user_id`, `praised_number`, `date`)
        VALUES (#{userId}, #{praisedNumber}, #{date})
    </insert>
    
    <select id="listByUserIdsAndDate" resultType="com.shuidihuzhu.cf.model.miniprogram.CfCommentPraisedNumber">
        SELECT <include refid="FIELDS"/>
        FROM <include refid="TABLE"/>
        WHERE `user_id` IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        AND `date`=#{date}
        AND `is_delete`=0
    </select>

    <insert id="insertList" parameterType="com.shuidihuzhu.cf.model.miniprogram.CfCommentPraisedNumber">
        INSERT INTO <include refid="TABLE"/>
        (`user_id`, `praised_number`, `date`)
        VALUES
        <foreach collection="cfCommentPraisedNumbers" item="item" separator=",">
            (#{item.userId},#{item.praisedNumber},#{item.date})
        </foreach>
    </insert>
</mapper>