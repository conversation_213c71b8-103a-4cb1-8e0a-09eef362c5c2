<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.miniprogram.CfTopicExpectDao">
    <sql id="TABLE">
        cf_xcx_topic_expect
    </sql>
    <sql id="FIELDS">
        `id` AS id,
        `keyword_id` AS keywordId,
        `user_id` AS userId,
        `expect_status` AS status,
        `is_delete` AS isDelete,
        `phase_id` AS phaseId
    </sql>

    <insert id="insertOne" parameterType="com.shuidihuzhu.cf.model.miniprogram.CfTopicExpect">
        INSERT IGNORE INTO <include refid="TABLE"/>
        (`keyword_id`, `user_id`, `expect_status`, `phase_id`)
        VALUES
        (#{cfTopicExpect.keywordId}, #{cfTopicExpect.userId}, #{cfTopicExpect.status}, #{cfTopicExpect.phaseId})
    </insert>

    <select id="selectByPhaseIdAndUserId" resultType="com.shuidihuzhu.cf.model.miniprogram.CfTopicExpect">
        SELECT <include refid="FIELDS"/>
        FROM <include refid="TABLE"/>
        WHERE `user_id`=#{userId}
        AND `phase_id`=#{phaseId}
        AND `is_delete`=0
    </select>

    <update id="updateByPhaseIdAndUserId">
        UPDATE <include refid="TABLE"/>
        SET `expect_status`=#{status}
        WHERE `user_id`=#{userId}
        AND `phase_id`=#{phaseId}
        AND `is_delete`=0
    </update>
</mapper>