<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.miniprogram.CfCommentDynamicDao">
    <sql id="TABLE">
        cf_xcx_comment_dynamic
    </sql>
    <sql id="FIELD">
        `id` AS id,
        `topic_id` AS topicId,
        `comment_id` AS commentId,
        `praise_number` AS praiseNumber,
        `top_status` AS topStatus,
        `is_delete` AS isDelete,
        `is_reply` AS isReply
    </sql>

    <insert id="addOne" parameterType="com.shuidihuzhu.cf.model.miniprogram.CfCommentDynamic">
        INSERT IGNORE INTO <include refid="TABLE"/>
        (`topic_id`, `comment_id`, `praise_number`, `top_status`, `is_reply`)
        VALUES
        (#{cfCommentDynamic.topicId}, #{cfCommentDynamic.commentId},
        #{cfCommentDynamic.praiseNumber}, #{cfCommentDynamic.topStatus},
        #{cfCommentDynamic.isReply})
    </insert>

    <update id="deleteById">
        UPDATE <include refid="TABLE"/>
        SET `is_delete`=1
        WHERE `id`=#{id}
    </update>

    <update id="deleteByCommentId">
        UPDATE <include refid="TABLE"/>
        SET `is_delete`=1
        WHERE `comment_id`=#{commentId}
    </update>

    <update id="updateById" parameterType="com.shuidihuzhu.cf.model.miniprogram.CfCommentDynamic">
        UPDATE <include refid="TABLE"/>
        <set>
            <if test="cfCommentDynamic.id != null">
                `id` = #{cfCommentDynamic.id,jdbcType=INTEGER},
            </if>
            <if test="cfCommentDynamic.topicId != null">
                `topic_id` = #{cfCommentDynamic.topicId,jdbcType=INTEGER},
            </if>
            <if test="cfCommentDynamic.commentId != null">
                `comment_id` = #{cfCommentDynamic.commentId,jdbcType=INTEGER},
            </if>
            <if test="cfCommentDynamic.praiseNumber != null">
                `praise_number` = #{cfCommentDynamic.praiseNumber,jdbcType=INTEGER},
            </if>
            <if test="cfCommentDynamic.topStatus != null">
                `top_status` = #{cfCommentDynamic.topStatus,jdbcType=INTEGER},
            </if>
            <if test="cfCommentDynamic.isDelete != null">
                `is_delete` = #{cfCommentDynamic.isDelete,jdbcType=INTEGER},
            </if>
        </set>
        WHERE `id`=#{id}
        AND `is_delete`=0
    </update>

    <update id="updateByCommentId" parameterType="com.shuidihuzhu.cf.model.miniprogram.CfCommentDynamic">
        UPDATE <include refid="TABLE"/>
        <set>
            <if test="cfCommentDynamic.id != null">
                `id` = #{cfCommentDynamic.id,jdbcType=INTEGER},
            </if>
            <if test="cfCommentDynamic.topicId != null">
                `topic_id` = #{cfCommentDynamic.topicId,jdbcType=INTEGER},
            </if>
            <if test="cfCommentDynamic.commentId != null">
                `comment_id` = #{cfCommentDynamic.commentId,jdbcType=INTEGER},
            </if>
            <if test="cfCommentDynamic.praiseNumber != null">
                `praise_number` = #{cfCommentDynamic.praiseNumber,jdbcType=INTEGER},
            </if>
            <if test="cfCommentDynamic.topStatus != null">
                `top_status` = #{cfCommentDynamic.topStatus,jdbcType=INTEGER},
            </if>
            <if test="cfCommentDynamic.isDelete != null">
                `is_delete` = #{cfCommentDynamic.isDelete,jdbcType=INTEGER},
            </if>
        </set>
        WHERE `comment_id`=#{commentId}
        AND `is_delete`=0
    </update>

    <update id="updatePariseNum">
        UPDATE <include refid="TABLE"/>
        SET `praise_number`=`praise_number`+1
        WHERE `id`=#{id}
        AND `is_delete`=0
    </update>

    <update id="updateUnPariseNum">
        UPDATE <include refid="TABLE"/>
        SET `praise_number`=`praise_number`-1
        WHERE `id`=#{id}
        AND `is_delete`=0
    </update>

    <select id="selectById" resultType="com.shuidihuzhu.cf.model.miniprogram.CfCommentDynamic">
        SELECT <include refid="FIELD"/>
        FROM <include refid="TABLE"/>
        WHERE `id`=#{id}
        AND `is_delete`=0
    </select>

    <select id="selectByCommentId" resultType="com.shuidihuzhu.cf.model.miniprogram.CfCommentDynamic">
        SELECT <include refid="FIELD"/>
        FROM <include refid="TABLE"/>
        WHERE `comment_id`=#{commentId}
        AND `is_delete`=0
        limit 1
    </select>

    <select id="listByCommentIds" resultType="com.shuidihuzhu.cf.model.miniprogram.CfCommentDynamic">
        SELECT <include refid="FIELD"/>
        FROM <include refid="TABLE"/>
        WHERE `comment_id` IN
        <foreach collection="commentIds" item="commentId" open="(" separator="," close=")">
            #{commentId}
        </foreach>
        AND `is_delete`=0
    </select>

    <select id="listByIdAndStatusLimit" resultType="com.shuidihuzhu.cf.model.miniprogram.CfCommentDynamic">
        SELECT <include refid="FIELD"/>
        FROM <include refid="TABLE"/>
        WHERE `topic_id`=#{topicId}
        AND `top_status`=#{topStauts}
        AND `is_delete`=0
        AND `is_reply` = 0
        ORDER BY `id` DESC
        limit #{limit}
    </select>

    <select id="listByIdsAndStatus" resultType="com.shuidihuzhu.cf.model.miniprogram.CfCommentDynamic">
        SELECT <include refid="FIELD"/>
        FROM <include refid="TABLE"/>
        WHERE `topic_id` IN
        <foreach collection="topicIds" item="topicId" open="(" separator="," close=")">
            #{topicId}
        </foreach>
        AND `top_status`=#{topStatus}
        AND `is_reply` = 0
        AND `is_delete`=0
        ORDER BY `id` DESC
    </select>

    <select id="listByIdAndStatus" resultType="com.shuidihuzhu.cf.model.miniprogram.CfCommentDynamic">
        SELECT <include refid="FIELD"/>
        FROM <include refid="TABLE"/>
        WHERE `topic_id`=#{topicId}
        AND `top_status`=#{topStauts}
        AND `is_delete`=0
        AND `is_reply` = 0
        ORDER BY `id` DESC
    </select>
</mapper>