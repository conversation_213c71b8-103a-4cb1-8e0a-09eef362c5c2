<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.miniprogram.CfTopicDao">
    <sql id="TABLE">
        cf_xcx_topic
    </sql>
    <sql id="FIELD">
        `id` AS id,
        `title` AS title,
        `description` AS description,
        `icon` AS icon,
        `is_delete` AS isDelete,
        `phase_id` AS phaseId,
        `img_urls` As imgUrls
    </sql>

    <insert id="addOne" parameterType="com.shuidihuzhu.cf.model.miniprogram.CfTopic">
        INSERT INTO <include refid="TABLE"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="cfTopic.title != null">
                title,
            </if>
            <if test="cfTopic.description != null">
                description,
            </if>
            <if test="cfTopic.icon != null">
                icon,
            </if>
            <if test="cfTopic.phaseId != null">
                phase_id,
            </if>
            <if test="cfTopic.imgUrls != null">
                img_urls,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="cfTopic.title != null">
                #{cfTopic.title,jdbcType=VARCHAR},
            </if>
            <if test="cfTopic.description != null">
                #{cfTopic.description,jdbcType=VARCHAR},
            </if>
            <if test="cfTopic.icon != null">
                #{cfTopic.icon,jdbcType=VARCHAR},
            </if>
            <if test="cfTopic.phaseId != null">
                #{cfTopic.phaseId,jdbcType=INTEGER},
            </if>
            <if test="cfTopic.imgUrls != null">
                #{cfTopic.imgUrls},
            </if>
        </trim>
    </insert>

    <update id="deleteById">
        UPDATE <include refid="TABLE"/>
        SET `is_delete`=1
        WHERE `id`=#{id}
    </update>

    <update id="updateById" parameterType="com.shuidihuzhu.cf.model.miniprogram.CfTopic">
        UPDATE <include refid="TABLE"/>
        <set>
            <if test="cfTopic.id != null">
                `id` = #{cfTopic.id,jdbcType=INTEGER},
            </if>
            <if test="cfTopic.title != null">
                `title` = #{cfTopic.title,jdbcType=VARCHAR},
            </if>
            <if test="cfTopic.description != null">
                `description` = #{cfTopic.description,jdbcType=VARCHAR},
            </if>
            <if test="cfTopic.icon != null">
                `icon` = #{cfTopic.icon,jdbcType=VARCHAR},
            </if>
            <if test="cfTopic.isDelete != null">
                `is_delete` = #{cfTopic.isDelete,jdbcType=INTEGER},
            </if>
            <if test="cfTopic.phaseId != null">
                `phase_id` = #{cfTopic.phaseId,jdbcType=INTEGER},
            </if>
            <if test="cfTopic.imgUrls != null">
                 `img_urls` = #{cfTopic.imgUrls},
            </if>
        </set>
        WHERE `id`=#{id}
        AND `is_delete`=0
    </update>

    <select id="selectById" resultType="com.shuidihuzhu.cf.model.miniprogram.CfTopic">
        SELECT <include refid="FIELD"/>
        FROM <include refid="TABLE"/>
        WHERE `id`=#{id}
        AND `is_delete`=0
    </select>

    <select id="selectByPhaseId" resultType="com.shuidihuzhu.cf.model.miniprogram.CfTopic">
        SELECT <include refid="FIELD"/>
        FROM <include refid="TABLE"/>
        WHERE `phase_id`=#{phaseId}
        AND `is_delete`=0
    </select>

    <select id="listByPhaseIds" resultType="com.shuidihuzhu.cf.model.miniprogram.CfTopic">
        SELECT <include refid="FIELD"/>
        FROM <include refid="TABLE"/>
        WHERE `phase_id` IN
        <foreach collection="phaseIds" item="phaseId" open="(" separator="," close=")">
            #{phaseId}
        </foreach>
        AND `is_delete`=0
        order by field(phase_id,
        <foreach collection="phaseIds" separator="," close=")" item="phaseId">
            #{phaseId}
        </foreach>
    </select>

    <select id="listByPhaseId" resultType="com.shuidihuzhu.cf.model.miniprogram.CfTopic">
        SELECT <include refid="FIELD"/>
        FROM <include refid="TABLE"/>
        WHERE `phase_id` <![CDATA[ <= ]]> #{phaseId}
        AND `is_delete`=0
        ORDER BY `phase_id` DESC
    </select>

    <select id="listByPhaseIdLimit" resultType="com.shuidihuzhu.cf.model.miniprogram.CfTopic">
        SELECT <include refid="FIELD"/>
        FROM <include refid="TABLE"/>
        WHERE `phase_id` IN
        <foreach collection="phaseIds" item="phaseId" open="(" separator="," close=")">
            #{phaseId}
        </foreach>
        AND `is_delete`=0
        ORDER BY `phase_id` DESC
        LIMIT #{limit}
    </select>

    <select id="listByIds" resultType="com.shuidihuzhu.cf.model.miniprogram.CfTopic">
        SELECT <include refid="FIELD"/>
        FROM <include refid="TABLE"/>
        WHERE `id` IN
        <foreach collection="topicIds" item="topicId" open="(" separator="," close=")">
            #{topicId}
        </foreach>
        AND `is_delete`=0
    </select>
</mapper>