<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.miniprogram.CfCommentPriseDao">
    <sql id="TABLE">
        cf_xcx_comment_praise
    </sql>
    <sql id="FIELDS">
        `id` AS id,
        `topic_id` AS topicId,
        `user_id` AS userId,
        `comment_id` AS commentId,
        `praise_status` AS status,
        `is_delete` AS isDelete
    </sql>

    <insert id="add">
        INSERT INTO
        <include refid="TABLE"/>
        (`topic_id`, `user_id`, `comment_id`, `praise_status`)
        VALUES (#{topicId}, #{userId}, #{commentId}, 1)
    </insert>

    <select id="selectByUserAndComment" resultType="com.shuidihuzhu.cf.model.miniprogram.CfCommentPrise">
        SELECT <include refid="FIELDS"/>
        FROM <include refid="TABLE"/>
        WHERE `user_id`=#{userId}
        AND `comment_id`=#{commentId}
        AND `is_delete`=0
    </select>

    <update id="updateByUserAndComment">
        UPDATE <include refid="TABLE"/>
        SET `praise_status`=#{status}
        WHERE `user_id`=#{userId}
        AND `comment_id`=#{commentId}
        AND `is_delete`=0
    </update>

    <select id="listByUserAndCommentIds" resultType="com.shuidihuzhu.cf.model.miniprogram.CfCommentPrise">
        SELECT <include refid="FIELDS"/>
        FROM <include refid="TABLE"/>
        WHERE `user_id`=#{userId}
        AND `comment_id` IN
        <foreach collection="commentIds" item="commentId" open="(" separator="," close=")">
            #{commentId}
        </foreach>
        AND `is_delete`=0
    </select>
</mapper>