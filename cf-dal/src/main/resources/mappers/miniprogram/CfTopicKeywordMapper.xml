<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.miniprogram.CfTopicKeywordDao">
    <sql id="TABLE">
        cf_xcx_topic_keyword
    </sql>

    <sql id="FIELD">
        `id` AS id,
        `keyword` AS keyword,
        `is_delete` AS isDelete
    </sql>

    <insert id="addOne" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="TABLE"/> (`keyword`)
        VALUES #{cfTopicKeyword.keyword}
    </insert>

    <update id="deleteById">
        UPDATE <include refid="TABLE"/>
        SET `is_delete`=1
        WHERE `id`=#{id}
    </update>

    <update id="updateById">
        UPDATE <include refid="TABLE"/>
        SET `keyword`=#{keyword}
        WHERE `id`=#{id}
        AND `is_delete`=0
    </update>

    <select id="selectById" resultType="com.shuidihuzhu.cf.model.miniprogram.CfTopicKeyword">
        SELECT <include refid="FIELD"/>
        FROM <include refid="TABLE"/>
        WHERE `id`=#{id}
        AND `is_delete`=0
    </select>

    <select id="listByIds" resultType="com.shuidihuzhu.cf.model.miniprogram.CfTopicKeyword">
        SELECT <include refid="FIELD"/>
        FROM <include refid="TABLE"/>
        WHERE `id` IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND `is_delete`=0
    </select>

    <select id="selectByKeyword" resultType="com.shuidihuzhu.cf.model.miniprogram.CfTopicKeyword">
        SELECT <include refid="FIELD"/>
        FROM <include refid="TABLE"/>
        WHERE `keyword`=#{keyword}
        AND `is_delete`=0
    </select>

</mapper>