<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.miniprogram.CfTopicCommentDao">
    <sql id="TABLE">
        cf_xcx_topic_comment
    </sql>

    <sql id="FIELD">
    `id` AS id,
    `topic_id` AS topicId,
    `user_id` AS userId,
    `nickname` AS nickname,
    `head_url` AS headUrl,
    `content` AS content,
    `sensitive` AS isSensitive,
    `create_time` AS createTime,
    `update_time` AS updateTime,
    `parent_id`,
    `comment_group_id`,
    `is_delete` AS isDelete
    </sql>

    <insert id="addOne" parameterType="com.shuidihuzhu.cf.model.miniprogram.CfTopicComment" useGeneratedKeys="true" keyProperty="cfTopicComment.id">
        INSERT INTO <include refid="TABLE"/>
        (`topic_id`, `user_id`, `nickname`, `head_url`, `content`, `sensitive`,`parent_id`,`comment_group_id`)
        VALUES
        (#{cfTopicComment.topicId}, #{cfTopicComment.userId}, #{cfTopicComment.nickname},
        #{cfTopicComment.headUrl}, #{cfTopicComment.content}, #{cfTopicComment.isSensitive},
        #{cfTopicComment.parentId}, #{cfTopicComment.commentGroupId})
    </insert>

    <update id="deleteById">
        UPDATE <include refid="TABLE"/>
        SET `is_delete`=1
        WHERE `id`=#{id}
    </update>

    <select id="selectById" resultType="com.shuidihuzhu.cf.model.miniprogram.CfTopicComment">
        SELECT <include refid="FIELD"/>
        FROM <include refid="TABLE"/>
        WHERE `id`=#{id}
        AND `is_delete`=0
    </select>

    <select id="selectByIds" resultType="com.shuidihuzhu.cf.model.miniprogram.CfTopicComment">
        SELECT <include refid="FIELD"/>
        FROM <include refid="TABLE"/>
        WHERE `id` IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND `is_delete`=0
        ORDER BY `id` DESC
    </select>

    <select id="selectByUserId" resultType="com.shuidihuzhu.cf.model.miniprogram.CfTopicComment">
        SELECT <include refid="FIELD"/>
        FROM <include refid="TABLE"/>
        WHERE `user_id`=#{userId}
        AND `is_delete`=0
        ORDER BY `id` DESC
    </select>

    <select id="listByTopicId" resultType="com.shuidihuzhu.cf.model.miniprogram.CfTopicComment">
        SELECT <include refid="FIELD"/>
        FROM <include refid="TABLE"/>
        WHERE `topic_id`=#{topicId}
        AND `is_delete`=0
        ORDER BY `id` DESC
    </select>

    <select id="listByUserIds" resultType="com.shuidihuzhu.cf.model.miniprogram.CfTopicComment">
        SELECT <include refid="FIELD"/>
        FROM <include refid="TABLE"/>
        WHERE `user_id` IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        AND `is_delete`=0
        ORDER BY `user_id`
    </select>

    <select id="listByTimeAndSensitive" resultType="com.shuidihuzhu.cf.model.miniprogram.CfTopicComment">
        SELECT <include refid="FIELD"/>
        FROM <include refid="TABLE"/>
        WHERE `create_time` <![CDATA[ >= ]]> #{begin}
        AND `create_time` <![CDATA[ < ]]> #{end}
        AND `sensitive`=#{sensitive}
        AND `is_delete`=0
    </select>

    <select id="listByCommentParentIds" resultType="com.shuidihuzhu.cf.model.miniprogram.CfTopicComment">
        SELECT <include refid="FIELD"/>
        FROM <include refid="TABLE"/>
        WHERE `parent_id` IN
        <foreach collection="parentIds" item="parentId" open="(" separator="," close=")">
            #{parentId}
        </foreach>
        AND `is_delete`=0
    </select>


    <select id="listByCommentGroupId" resultType="com.shuidihuzhu.cf.model.miniprogram.CfTopicComment">
        SELECT <include refid="FIELD"/>
        FROM <include refid="TABLE"/>
        WHERE `comment_group_id` = #{commentGroupId}
        AND `id` > #{anchorId}
        AND `parent_id` != 0
        AND `is_delete` = 0
        limit #{limit}
    </select>

</mapper>