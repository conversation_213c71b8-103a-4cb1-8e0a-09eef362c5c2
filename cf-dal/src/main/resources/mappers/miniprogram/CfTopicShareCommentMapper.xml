<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.miniprogram.CfTopicShareCommentDao">
    <sql id="TABLE">
        cf_xcx_topic_count
    </sql>
    <sql id="FIELDS">
        `id` AS id,
        `topic_id` AS topicId,
        `comment_count` AS commentCount,
        `share_count` AS shareCount,
        `praise_count` AS praiseCount
    </sql>

    <insert id="insertOne" parameterType="com.shuidihuzhu.cf.model.miniprogram.CfTopicShareCommentCount">
        INSERT INTO <include refid="TABLE"/>
        (`topic_id`,`comment_count`,`share_count`,`praise_count`)
        VALUES
        (#{cfTopicShareCommentCount.topicId}, #{cfTopicShareCommentCount.commentCount},
        #{cfTopicShareCommentCount.shareCount}, #{cfTopicShareCommentCount.praiseCount})
    </insert>

    <select id="selectByTopicId" resultType="com.shuidihuzhu.cf.model.miniprogram.CfTopicShareCommentCount">
        SELECT <include refid="FIELDS"/>
        FROM <include refid="TABLE"/>
        WHERE `topic_id`=#{topicId}
        AND `is_delete`=0
    </select>

    <select id="listByTopicIds" resultType="com.shuidihuzhu.cf.model.miniprogram.CfTopicShareCommentCount">
        SELECT <include refid="FIELDS"/>
        FROM <include refid="TABLE"/>
        WHERE `topic_id` IN
        <foreach collection="topicIds" item="topicId" open="(" separator="," close=")">
            #{topicId}
        </foreach>
        AND `is_delete`=0
    </select>

    <update id="updateShareNumByTopicId">
        UPDATE <include refid="TABLE"/>
        SET `share_count`=`share_count`+1
        WHERE `topic_id`=#{topicId}
        AND `is_delete`=0
    </update>

    <update id="updateCommentNumByTopicId">
        UPDATE <include refid="TABLE"/>
        SET `comment_count`=`comment_count`+1
        WHERE `topic_id`=#{topicId}
        AND `is_delete`=0
    </update>

    <update id="incPraiseNum">
        UPDATE <include refid="TABLE"/>
        SET `praise_count`=`praise_count`+1
        WHERE `topic_id`=#{topicId}
        AND `is_delete`=0
    </update>

    <update id="decPraiseNum">
        UPDATE <include refid="TABLE"/>
        SET `praise_count`=`praise_count`-1
        WHERE `topic_id`=#{topicId}
        AND `is_delete`=0
    </update>

    <update id="updateByTopicId" parameterType="com.shuidihuzhu.cf.model.miniprogram.CfTopicShareCommentCount">
        UPDATE <include refid="TABLE"/>
        <set>
            <if test="cfTopicShareCommentCount.commentCount != null">
                `comment_count` = #{cfTopicShareCommentCount.commentCount,jdbcType=INTEGER},
            </if>
            <if test="cfTopicShareCommentCount.shareCount != null">
                `share_count` = #{cfTopicShareCommentCount.shareCount,jdbcType=INTEGER},
            </if>
            <if test="cfTopicShareCommentCount.praiseCount != null">
                `praise_count` = #{cfTopicShareCommentCount.praiseCount,jdbcType=INTEGER},
            </if>
        </set>
        where `topic_id` = #{topicId}
        AND `is_delete`=0
    </update>
</mapper>