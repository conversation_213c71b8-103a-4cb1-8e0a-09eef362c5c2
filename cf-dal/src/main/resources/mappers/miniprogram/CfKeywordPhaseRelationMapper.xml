<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.miniprogram.CfKeywordPhaseRelationDao">
    <sql id="TABLE">
        cf_xcx_keyword_phase_relation
    </sql>
    <sql id="FIELD">
        `id` AS id,
        `keyword_id` AS keywordId,
        `phase_id` AS phaseId,
        `expect_number` AS expectNumber,
        `not_expect_number` AS notExpectNumber,
        `is_delete` AS isDelete
    </sql>

    <insert id="addOne" parameterType="com.shuidihuzhu.cf.model.miniprogram.CfKeywordPhaseRelation" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="TABLE"/>
        (`keyword_id`, `phase_id`, `expect_number`, `not_expect_number`)
        VALUES (#{cfKeywordPhaseRelation.keywordId}, #{cfKeywordPhaseRelation.phaseId},
        #{cfKeywordPhaseRelation.expectNumber}, #{cfKeywordPhaseRelation.notExpectNumber})
    </insert>

    <update id="updateById" parameterType="com.shuidihuzhu.cf.model.miniprogram.CfKeywordPhaseRelation">
        UPDATE <include refid="TABLE"/>
        <set>
            <if test="cfKeywordPhaseRelation.id != null">
                `id` = #{cfKeywordPhaseRelation.id,jdbcType=INTEGER},
            </if>
            <if test="cfKeywordPhaseRelation.keywordId != null">
                `keyword_id` = #{cfKeywordPhaseRelation.keywordId,jdbcType=INTEGER},
            </if>
            <if test="cfKeywordPhaseRelation.phaseId != null">
                `phase_id` = #{cfKeywordPhaseRelation.phaseId,jdbcType=INTEGER},
            </if>
            <if test="cfKeywordPhaseRelation.expectNumber != null">
                `expect_number` = #{cfKeywordPhaseRelation.expectNumber,jdbcType=INTEGER},
            </if>
            <if test="cfKeywordPhaseRelation.notExpectNumber != null">
                `not_expect_number` = #{cfKeywordPhaseRelation.notExpectNumber,jdbcType=INTEGER},
            </if>
            <if test="cfKeywordPhaseRelation.isDelete != null">
                `is_delete` = #{cfKeywordPhaseRelation.isDelete,jdbcType=INTEGER},
            </if>
        </set>
        WHERE `id`=#{id}
        AND `is_delete`=0
    </update>

    <update id="updateByPhaseId" parameterType="com.shuidihuzhu.cf.model.miniprogram.CfKeywordPhaseRelation">
        UPDATE <include refid="TABLE"/>
        <set>
            <if test="cfKeywordPhaseRelation.id != null">
                `id` = #{cfKeywordPhaseRelation.id,jdbcType=INTEGER},
            </if>
            <if test="cfKeywordPhaseRelation.keywordId != null">
                `keyword_id` = #{cfKeywordPhaseRelation.keywordId,jdbcType=INTEGER},
            </if>
            <if test="phaseId != null">
                `phase_id` = #{phaseId},
            </if>
            <if test="cfKeywordPhaseRelation.expectNumber != null">
                `expect_number` = #{cfKeywordPhaseRelation.expectNumber,jdbcType=INTEGER},
            </if>
            <if test="cfKeywordPhaseRelation.notExpectNumber != null">
                `not_expect_number` = #{cfKeywordPhaseRelation.notExpectNumber,jdbcType=INTEGER},
            </if>
            <if test="cfKeywordPhaseRelation.isDelete != null">
                `is_delete` = #{cfKeywordPhaseRelation.isDelete,jdbcType=INTEGER},
            </if>
        </set>
        WHERE `phase_id`=#{phaseId}
        AND `is_delete`=0
    </update>

    <update id="addExpectByPhaseId">
        UPDATE <include refid="TABLE"/>
        SET `expect_number`=`expect_number`+1, `not_expect_number`=`not_expect_number`-1
        WHERE `phase_id`=#{phaseId}
        AND `is_delete`=0
    </update>

    <update id="addNotExpectByPhaseId">
        UPDATE <include refid="TABLE"/>
        SET `not_expect_number`=`not_expect_number`+1, `expect_number`=`expect_number`-1
        WHERE `phase_id`=#{phaseId}
        AND `is_delete`=0
    </update>

    <update id="addExpectByPhaseIdOnly">
        UPDATE <include refid="TABLE"/>
        SET `expect_number`=`expect_number`+1
        WHERE `phase_id`=#{phaseId}
        AND `is_delete`=0
    </update>

    <update id="addNotExpectByPhaseIdOnly">
        UPDATE <include refid="TABLE"/>
        SET `not_expect_number`=`not_expect_number`+1
        WHERE `phase_id`=#{phaseId}
        AND `is_delete`=0
    </update>

    <select id="selectByPhaseId" resultType="com.shuidihuzhu.cf.model.miniprogram.CfKeywordPhaseRelation">
        SELECT <include refid="FIELD"/>
        FROM <include refid="TABLE"/>
        WHERE `phase_id`=#{phaseId}
        AND `is_delete`=0
        ORDER BY `id` DESC
        limit 1
    </select>
</mapper>