<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.miniprogram.CfMiniProgramDao">


   <insert id="save" parameterType="com.shuidihuzhu.cf.model.miniprogram.CfMiniStoreModel" useGeneratedKeys="false" keyProperty="id">
        replace into cf_mini_store (user_id,mini_key,mini_value)
        values (#{userId},#{miniKey},#{miniValue})
    </insert>


    <select id="getByUserId" resultType="com.shuidihuzhu.cf.model.miniprogram.CfMiniStoreModel">
        SELECT *
        FROM cf_mini_store
        WHERE `user_id`=#{userId} and mini_key=#{key}
        AND `is_delete`=0
    </select>


</mapper>