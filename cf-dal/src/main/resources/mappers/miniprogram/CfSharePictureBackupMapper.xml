<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.miniprogram.CfSharePictureBackupDao">
    <insert id="insert" parameterType="com.shuidihuzhu.cf.domain.miniprogram.CfSharePictureBackupDO">
        INSERT INTO `cf_share_picture_backup` (`user_id`, `case_id`, `type`, `uuid`, `url`)
        VALUES (#{userId}, #{caseId}, #{type}, #{uuid}, #{url})
        ON DUPLICATE KEY UPDATE `uuid` = values(`uuid`),
                                `url`  = values(`url`)
    </insert>

    <update id="update">
        UPDATE `cf_share_picture_backup`
        SET `url`=#{url}
        WHERE `user_id` = #{userId}
          AND `case_id` = #{caseId}
          AND `type` = #{type}
    </update>

    <select id="getByUserIdAndCaseIdAndTypeAndUuid"
            resultType="com.shuidihuzhu.cf.domain.miniprogram.CfSharePictureBackupDO">
        SELECT `user_id` AS userId,
               `case_id` AS caseId,
               `type`    AS type,
               `uuid`    AS uuid,
               `url`     AS url
        FROM `cf_share_picture_backup`
        WHERE `user_id` = #{userId}
          AND `case_id` = #{caseId}
          AND `type` = #{type}
        order by id desc
        limit 1
    </select>
</mapper>