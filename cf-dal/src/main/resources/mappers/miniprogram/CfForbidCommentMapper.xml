<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.miniprogram.CfForbidCommentDao">
    <sql id="TABLE">
        cf_xcx_forbid_comment
    </sql>
    <sql id="FIELDS">
        `id` AS id,
        `topic_id` AS topicId,
        `user_id` AS userId,
        `content` AS content,
        `is_delete` AS idDelete
    </sql>

    <insert id="addOne" parameterType="com.shuidihuzhu.cf.model.miniprogram.CfForbidComment">
        INSERT INTO <include refid="TABLE"/>
        (`topic_id`, `user_id`, `content`)
        VALUES (#{cfForbidComment.topicId}, #{cfForbidComment.userId}, #{cfForbidComment.content})
    </insert>

    <update id="deleteById">
        UPDATE <include refid="TABLE"/>
        SET `is_delete`=1
        WHERE `id`=#{id}
    </update>

    <select id="selectById" resultType="com.shuidihuzhu.cf.model.miniprogram.CfForbidComment">
        SELECT <include refid="FIELDS"/>
        FROM <include refid="TABLE"/>
        WHERE `id`=#{id}
        AND `is_delete`=0
    </select>

    <select id="listByUserId" resultType="com.shuidihuzhu.cf.model.miniprogram.CfForbidComment">
        SELECT <include refid="FIELDS"/>
        FROM <include refid="TABLE"/>
        WHERE `user_id`=#{userId}
        AND `is_delete`=0
        ORDER BY `id` DESC
    </select>

    <select id="listByTopicId" resultType="com.shuidihuzhu.cf.model.miniprogram.CfForbidComment">
        SELECT <include refid="FIELDS"/>
        FROM <include refid="TABLE"/>
        WHERE `topic_id`=#{topicId}
        AND `is_delete`=0
        ORDER BY `id` DESC
    </select>
</mapper>