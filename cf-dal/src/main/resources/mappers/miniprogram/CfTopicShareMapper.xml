<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.miniprogram.CfTopicShareDao">
    <sql id="TABLE">
        cf_xcx_topic_share
    </sql>
    <sql id="FIELDS">
        `id` AS id,
        `topic_id` AS topicId,
        `user_id` AS userId,
        `uuid` AS uuid,
        `user_source_id` AS userSourceId,
        `source` AS source,
        `wx_source` AS wxSource,
        `to_wx_source` AS toWxSource,
        `is_delete` AS isDelete
    </sql>

    <insert id="addOne" parameterType="com.shuidihuzhu.cf.model.miniprogram.CfTopicShare">
        INSERT INTO <include refid="TABLE"/>
        (`topic_id`, `user_id`, `uuid`, `user_source_id`, `source`, `wx_source`, `to_wx_source`)
        VALUES (#{cfTopicShare.topicId}, #{cfTopicShare.userId}, #{cfTopicShare.uuid}, #{cfTopicShare.userSourceId},
        #{cfTopicShare.source}, #{cfTopicShare.wxSource}, #{cfTopicShare.toWxSource})
    </insert>

    <select id="listByTopicId" resultType="com.shuidihuzhu.cf.model.miniprogram.CfTopicShare">
        SELECT <include refid="TABLE"/>
        FROM <include refid="TABLE"/>
        WHERE `topic_id`=#{topicId}
        AND `is_delete`=0
        ORDER BY `id` DESC
    </select>
</mapper>