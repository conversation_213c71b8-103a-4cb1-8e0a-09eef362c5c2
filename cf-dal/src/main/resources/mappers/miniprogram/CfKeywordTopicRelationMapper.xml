<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.miniprogram.CfKeywordTopicRelationDao">
    <sql id="TABLE">
        cf_xcx_keyword_topic_relation
    </sql>
    <sql id="FIELD">
        `id` AS id,
        `keyword_id` AS keywordId,
        `show_date` AS showDate,
        `expect_number` AS expectNumber,
        `not_expect_number` AS notExpectNumber,
        `is_delete` AS isDelete
    </sql>

    <insert id="addOne" parameterType="com.shuidihuzhu.cf.model.miniprogram.CfKeywordTopicRelation" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="TABLE"/>
        (`keyword_id`, `show_date`, `expect_number`, `not_expect_number`)
        VALUES (#{cfKeywordTopicRelation.keywordId}, #{cfKeywordTopicRelation.showDate},
        #{cfKeywordTopicRelation.expectNumber}, #{cfKeywordTopicRelation.notExpectNumber})
    </insert>

    <!--<update id="deleteById">-->
        <!--UPDATE <include refid="TABLE"/>-->
        <!--SET `is_delete`=1-->
        <!--WHERE `id`=#{id}-->
    <!--</update>-->

    <update id="addExpectByDate">
        UPDATE <include refid="TABLE"/>
        SET `expect_number`=`expect_number`+1, `not_expect_number`=`not_expect_number`-1
        WHERE `show_date`=#{date}
        AND `is_delete`=0
    </update>

    <update id="addNotExpectByDate">
        UPDATE <include refid="TABLE"/>
        SET `not_expect_number`=`not_expect_number`+1, `expect_number`=`expect_number`-1
        WHERE `show_date`=#{date}
        AND `is_delete`=0
    </update>

    <update id="addExpectByDateOnly">
        UPDATE <include refid="TABLE"/>
        SET `expect_number`=`expect_number`+1
        WHERE `show_date`=#{date}
        AND `is_delete`=0
    </update>

    <update id="addNotExpectByDateOnly">
        UPDATE <include refid="TABLE"/>
        SET `not_expect_number`=`not_expect_number`+1
        WHERE `show_date`=#{date}
        AND `is_delete`=0
    </update>

    <update id="updateById" parameterType="com.shuidihuzhu.cf.model.miniprogram.CfKeywordTopicRelation">
        UPDATE <include refid="TABLE"/>
        <set>
            <if test="cfKeywordTopicRelation.id != null">
                `id` = #{cfKeywordTopicRelation.id,jdbcType=INTEGER},
            </if>
            <if test="cfKeywordTopicRelation.keywordId != null">
                `keyword_id` = #{cfKeywordTopicRelation.keywordId,jdbcType=INTEGER},
            </if>
            <if test="cfKeywordTopicRelation.showDate != null">
                `show_date` = #{cfKeywordTopicRelation.showDate,jdbcType=TIMESTAMP},
            </if>
            <if test="cfKeywordTopicRelation.expectNumber != null">
                `expect_number` = #{cfKeywordTopicRelation.expectNumber,jdbcType=INTEGER},
            </if>
            <if test="cfKeywordTopicRelation.notExpectNumber != null">
                `not_expect_number` = #{cfKeywordTopicRelation.notExpectNumber,jdbcType=INTEGER},
            </if>
            <if test="cfKeywordTopicRelation.isDelete != null">
                `is_delete` = #{cfKeywordTopicRelation.isDelete,jdbcType=INTEGER},
            </if>
        </set>
        WHERE `id`=#{id}
        AND `is_delete`=0
    </update>

    <update id="updateByDate" parameterType="com.shuidihuzhu.cf.model.miniprogram.CfKeywordTopicRelation">
        UPDATE <include refid="TABLE"/>
        <set>
            <if test="cfKeywordTopicRelation.id != null">
                `id` = #{cfKeywordTopicRelation.id,jdbcType=INTEGER},
            </if>
            <if test="cfKeywordTopicRelation.keywordId != null">
                `keyword_id` = #{cfKeywordTopicRelation.keywordId,jdbcType=INTEGER},
            </if>
            <if test="date != null">
                `show_date` = #{date},
            </if>
            <if test="cfKeywordTopicRelation.expectNumber != null">
                `expect_number` = #{cfKeywordTopicRelation.expectNumber,jdbcType=INTEGER},
            </if>
            <if test="cfKeywordTopicRelation.notExpectNumber != null">
                `not_expect_number` = #{cfKeywordTopicRelation.notExpectNumber,jdbcType=INTEGER},
            </if>
            <if test="cfKeywordTopicRelation.isDelete != null">
                `is_delete` = #{cfKeywordTopicRelation.isDelete,jdbcType=INTEGER},
            </if>
        </set>
        WHERE `show_date`=#{date}
        AND `is_delete`=0
    </update>

    <select id="selectByDate" resultType="com.shuidihuzhu.cf.model.miniprogram.CfKeywordTopicRelation">
        SELECT <include refid="FIELD"/>
        FROM <include refid="TABLE"/>
        WHERE `show_date`=#{date}
        AND `is_delete`=0
        ORDER BY `id` DESC
        limit 1
    </select>

    <!--<select id="listByDate" resultType="com.shuidihuzhu.model.miniprogram.CfKeywordTopicRelation">
        SELECT <include refid="FIELD"/>
        FROM <include refid="TABLE"/>
        WHERE `show_date` <![CDATA[ >= ]]> #{start}
        AND `show_date` <![CDATA[ <= ]]> #{end}
        AND `is_delete`=0
        ORDER BY `id` DESC
    </select>

    <select id="listByKeywordId" resultType="com.shuidihuzhu.model.miniprogram.CfKeywordTopicRelation">
        SELECT <include refid="FIELD"/>
        FROM <include refid="TABLE"/>
        WHERE `keyword_id`=#{keywordId}
        AND `is_delete`=0
        ORDER BY `id` DESC
    </select>-->
</mapper>