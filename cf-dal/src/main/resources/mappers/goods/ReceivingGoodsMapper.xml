<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.goods.ReceivingGoodsDao">
	<sql id="tableName">
		receiving_goods_info
	</sql>

	<sql id="insertColumn">
		`user_id`,
        `receiving_name`,
        `receiving_phone`,
        `receiving_address`,
		`certificate_nickname`,
		`registration_type`
	</sql>

	<sql id="selectColumn">
		`id`,
		`user_id`,
        `receiving_name`,
        `receiving_phone`,
        `receiving_address`,
		`certificate_nickname`,
		`registration_type`,
		`create_time`
	</sql>

	<insert id="saveReceivingGoods" parameterType="com.shuidihuzhu.client.model.ReceivingGoodsDto">
		INSERT INTO <include refid="tableName" />
		(<include refid="insertColumn"/>)
		VALUES
		(#{userId}, #{receivingName}, #{receivingPhone}, #{receivingAddress}, #{certificateNickname}, #{registrationType})
	</insert>

	<select id="queryTotalCount" resultType="int">
		select count(1)
		from <include refid="tableName"/>
		where is_delete = 0
	</select>

	<select id="queryReceivingByPhoneAndUserId" resultType="com.shuidihuzhu.client.model.ReceivingGoodsDto">
		select <include refid="selectColumn"/>
		from <include refid="tableName" />
		where (user_id = #{userId} or receiving_phone = #{receivingPhone})
		and is_delete = 0
	</select>

	<select id="queryReceivingGoods" resultType="com.shuidihuzhu.client.model.ReceivingGoodsDto">
		select <include refid="selectColumn"/>
		from <include refid="tableName" />
		<where>
			<if test="receivingName != null and receivingName != ''">
				AND receiving_name like CONCAT('%',#{receivingName},'%' )
			</if>
			<if test="receivingPhone != null and receivingPhone != ''">
				AND receiving_phone like CONCAT('%',#{receivingPhone},'%' )
			</if>
			<if test="receivingAddress != null and receivingAddress != ''">
				AND receiving_address like CONCAT('%',#{receivingAddress},'%' )
			</if>
			<if test="beginTime != null and endTime != null">
				AND create_time between #{beginTime} and #{endTime}
			</if>
		</where>
		order by id desc
		limit #{offset}, 10
	</select>

	<select id="queryReceivingGoodsCount" resultType="int">
		select count(*)
		from <include refid="tableName" />
		<where>
			<if test="receivingName != null and receivingName != ''">
				AND receiving_name like CONCAT('%',#{receivingName},'%' )
			</if>
			<if test="receivingPhone != null and receivingPhone != ''">
				AND receiving_phone like CONCAT('%',#{receivingPhone},'%' )
			</if>
			<if test="receivingAddress != null and receivingAddress != ''">
				AND receiving_address like CONCAT('%',#{receivingAddress},'%' )
			</if>
		</where>
	</select>

	<select id="queryReceivingGoodsByTime" resultType="com.shuidihuzhu.client.model.ReceivingGoodsDto">
		select <include refid="selectColumn"/>
		from <include refid="tableName"/>
		where create_time between #{beginTime} and #{endTime}
		and is_delete = 0
		order by id desc
	</select>

</mapper>