<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.goods.GoodsDetailDao">
	<sql id="tableName">
		goods_detail
	</sql>

	<insert id="save" parameterType="com.shuidihuzhu.cf.model.goods.GoodsDetail" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO <include refid="tableName" />
		(`info_uuid`,`title`,`name`,`story`,`detail`,`intro`)
		VALUES
		(#{infoUuid},#{title},#{name},#{story},#{detail},#{intro})
	</insert>

	<select id="getByInfoUuid" resultType="com.shuidihuzhu.cf.model.goods.GoodsDetail">
		SELECT *
		FROM <include refid="tableName" />
		WHERE `info_uuid`=#{infoUuid}
	</select>
	
	<select id="getListByInfoUuids" resultType="com.shuidihuzhu.cf.model.goods.GoodsDetail">
		SELECT *
		FROM <include refid="tableName" />
		WHERE `info_uuid` in <foreach collection="infoUuids" open="(" separator="," close=")" item="infoUuid">#{infoUuid}</foreach>
	</select>

    <update id="update" parameterType="com.shuidihuzhu.cf.model.goods.GoodsDetail">
        UPDATE <include refid="tableName" />
        SET `title`=#{title},`name`=#{name},`story`=#{story},`detail`=#{detail},`intro`=#{intro},`purpose`=#{purpose}
        WHERE `info_uuid`=#{infoUuid}
    </update>
</mapper>