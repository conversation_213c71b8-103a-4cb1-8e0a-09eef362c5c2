<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.goods.GoodsGearDao">
	<sql id="tableName">
		goods_gear
	</sql>

	<insert id="save" parameterType="com.shuidihuzhu.cf.model.goods.GoodsGear" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO <include refid="tableName" />
		(`info_uuid`,`amount`,`target_num`,`num`,`title`,`detail`,`need_address`,`need_email`,`has_limit`)
		VALUES
		(#{infoUuid},#{amount},#{targetNum},#{num},#{title},#{detail},#{needAddress},#{needEmail},#{hasLimit})
	</insert>

	<select id="getByInfoUuid" resultType="com.shuidihuzhu.cf.model.goods.GoodsGear">
		SELECT *
		FROM <include refid="tableName" />
		WHERE `info_uuid`=#{infoUuid} AND `valid`=1
	</select>
	
	<select id="getById" resultType="com.shuidihuzhu.cf.model.goods.GoodsGear">
		SELECT *
		FROM <include refid="tableName" />
		WHERE `id`=#{id}
	</select>
	
	<select id="getListByIds" resultType="com.shuidihuzhu.cf.model.goods.GoodsGear">
		SELECT *
		FROM <include refid="tableName" />
		WHERE `id` in <foreach collection="ids" open="(" separator="," close=")" item="id">#{id}</foreach>
	</select>

	<update id="updateNum">
		UPDATE <include refid="tableName" />
		SET `num`=`num`+ #{goodsCount}
		WHERE <![CDATA[ `id`=#{id} AND `num`+ #{goodsCount} <=`target_num` ]]>
	</update>

    <update id="updateValidByInfoUuid">
        UPDATE <include refid="tableName"/>
        SET `valid`=#{valid}
        WHERE `info_uuid`=#{infoUuid}
    </update>

    <update id="updateByGoodsGear" parameterType="com.shuidihuzhu.cf.model.goods.GoodsGear">
        UPDATE <include refid="tableName"/>
        SET `target_num`=#{targetNum},`title`=#{title},`has_limit`=#{hasLimit},
        	`detail`=#{detail},`need_address`=#{needAddress},`need_email`=#{needEmail}
        WHERE `id`=#{id} AND `info_uuid`=#{infoUuid}
    </update>
</mapper>