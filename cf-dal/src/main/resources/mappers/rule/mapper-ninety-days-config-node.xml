<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.rule.INinetyConfigNodeDAO" >
    <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.model.rule.NinetyDaysConfigNode">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="data_key" property="datakey" jdbcType="VARCHAR"/>
        <result column="data_value" property="dataValue" jdbcType="INTEGER"/>
        <result column="source_node_id" property="sourceNodeId" jdbcType="INTEGER"/>
        <result column="leaf_node" property="leafNode" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,data_key,data_value,source_node_id,leaf_node
    </sql>

    <select id="getAll" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from ninety_days_config_node where
        is_delete = 0;
    </select>
</mapper>