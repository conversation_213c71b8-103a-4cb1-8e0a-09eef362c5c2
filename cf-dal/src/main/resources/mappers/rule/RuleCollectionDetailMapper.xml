<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.rule.IRuleCollectionDetailDAO" >
    <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.model.rule.RuleCollectionDetail">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="admin_user_id" property="adminUserId" jdbcType="INTEGER"/>
        <result column="owner_name" property="ownerName" jdbcType="VARCHAR"/>
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="fail_mode" property="failMode" jdbcType="INTEGER"/>
        <result column="root_node_id" property="rootNodeId" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,admin_user_id,owner_name,start_time,end_time,status,fail_mode,root_node_id,create_time,update_time
    </sql>

    <sql id="Insert_Column_List">
        admin_user_id,owner_name,start_time,end_time,status,fail_mode,root_node_id
    </sql>

    <select id="getById" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from rule_collection_detail where
        id = #{id,jdbcType=INTEGER}
    </select>

    <select id="getByOwner" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from rule_collection_detail where
        admin_user_id = #{adminUserId,jdbcType=INTEGER}
    </select>

    <update id="enableRuleCollection" >
        update rule_collection_detail
        set
        status = 1
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateTime">
        update rule_collection_detail
        SET
        start_time = #{startTime, jdbcType=TIMESTAMP},
        end_time = #{endTime, jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateFailModel">
        update rule_collection_detail
        SET
        fail_mode = #{failModel, jdbcType=INTEGER}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.rule.RuleCollectionDetail">
        <selectKey order="AFTER" resultType="int" keyProperty="id">
            select last_insert_id() as id
        </selectKey>
        insert into rule_collection_detail(<include refid="Insert_Column_List" />)
        values(
        #{adminUserId,jdbcType=INTEGER},
        #{ownerName,jdbcType=VARCHAR},
        #{startTime,jdbcType=TIMESTAMP},
        #{endTime,jdbcType=TIMESTAMP},
        #{status,jdbcType=INTEGER},
        #{failMode,jdbcType=INTEGER},
        #{rootNodeId,jdbcType=INTEGER}
        )
    </insert>

    <update id="update" parameterType="com.shuidihuzhu.cf.model.rule.RuleCollectionDetail">
        update rule_collection_detail
        <set>
            `admin_user_id` = #{adminUserId},
            `owner_name` = #{ownerName},
            `start_time` = #{startTime},
            `end_time` = #{endTime},
            `status` = #{status},
            `fail_mode` = #{failMode},
        </set>
        where `id` = #{id}
        and `is_delete` = 0
    </update>
</mapper>