<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.rule.IRuleConditionDAO" >
    <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.model.rule.RuleCondition">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="collection_id" property="collectionId" jdbcType="INTEGER"/>
        <result column="field_name" property="fieldName" jdbcType="VARCHAR"/>
        <result column="operator" property="operator" jdbcType="INTEGER"/>
        <result column="target_value" property="targetValue" jdbcType="VARCHAR"/>
        <result column="extern_data_source" property="externDataSource" jdbcType="VARCHAR"/>
        <result column="priority" property="priority" jdbcType="INTEGER"/>
        <result column="result" property="result" jdbcType="VARCHAR"/>
        <result column="node_type" property="nodeTypeEnum" jdbcType="INTEGER"
                javaType="com.shuidihuzhu.cf.enums.rule.RuleNodeTypeEnum"
                typeHandler="com.shuidihuzhu.cf.dao.rule.RuleNodeTypeEnumTypeHandler"/>
        <result column="source_node_id" property="sourceNodeId" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,collection_id,field_name,operator,target_value,extern_data_source,priority,result,node_type,source_node_id,create_time,update_time
    </sql>

    <sql id="Insert_Column_List">
        collection_id,field_name,operator,target_value,extern_data_source,priority,result,node_type,source_node_id
    </sql>

    <select id="getByCollectionId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from rule_condition where
        collection_id = #{collectionId,jdbcType=INTEGER}
    </select>

    <select id="getById" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from rule_condition
        where id = #{id,jdbcType=INTEGER}
        and `is_delete` = 0
    </select>

    <update id="updateRule">
        update rule_condition
        set
        field_name = #{fieldName, jdbcType=VARCHAR},
        operator = #{operator, jdbcType=INTEGER},
        target_value = #{targetValue, jdbcType=VARCHAR},
        extern_data_source = ""
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateRuleExtDataSouce">
        update rule_condition
        set
        field_name = #{fieldName, jdbcType=VARCHAR},
        operator = #{operator, jdbcType=INTEGER},
        target_value = "",
        extern_data_source = #{externDataSource, jdbcType=VARCHAR}

        where id = #{id,jdbcType=INTEGER}
    </update>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.rule.RuleCondition">
        <selectKey order="AFTER" resultType="long" keyProperty="id">
            select last_insert_id() as id
        </selectKey>
        insert into rule_condition(<include refid="Insert_Column_List" />)
        values(
        #{collectionId,jdbcType=INTEGER},
        #{fieldName,jdbcType=VARCHAR},
        #{operator,jdbcType=INTEGER},
        #{targetValue,jdbcType=VARCHAR},
        #{externDataSource,jdbcType=VARCHAR},
        #{priority,jdbcType=INTEGER},
        #{result,jdbcType=VARCHAR},
        #{nodeTypeEnum,jdbcType=INTEGER,javaType=com.shuidihuzhu.cf.enums.rule.RuleNodeTypeEnum,typeHandler=com.shuidihuzhu.cf.dao.rule.RuleNodeTypeEnumTypeHandler},
        #{sourceNodeId,jdbcType=INTEGER}
        )
    </insert>

    <update id="update" parameterType="com.shuidihuzhu.cf.model.rule.RuleCondition">
        update rule_condition
        <set>
            collection_id = #{collectionId},
            field_name = #{fieldName},
            operator = #{operator},
            target_value = #{targetValue},
            extern_data_source = #{externDataSource},
            priority = #{priority},
            result = #{result}
        </set>
        where `id` = #{id}
        and `is_delete` = 0
    </update>

</mapper>