<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.starrocks.CfCaseFriendShareDao">

    <sql id="tableName">
        shuidi_rpt.rpt_cf_case_friend_share_full_h_dupl
    </sql>


    <sql id="selectFields">
        info_id as infoId,
        friend_user_id as friendUserId,
        update_time as updateTime,
        share_donate_ant as shareDonateAnt,
        share_donate_amt as shareDonateAmt,
        dt
    </sql>

    <!--
info_id BIGINT  NULL COMMENT '案例id',
friend_user_id BIGINT  NULL COMMENT '好友用户id',
update_time string null COMMENT '更新时间',
share_donate_ant BIGINT NULL COMMENT '转发带来的捐单量',
share_donate_amt DOUBLE  NULL COMMENT '转发带来的捐单金额,单位：元',
dt date comment ''
     -->

    <select id="listByInfoId" statementType="STATEMENT" resultType="com.shuidihuzhu.cf.model.starrocks.RptCfCaseFriendShareDo">
        select <include refid="selectFields"/>
        from <include refid = "tableName"/>
        where info_id  = ${infoId}
    </select>

<!--    <select id="aggregateByTimeBetween" statementType="STATEMENT" resultType="com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientrecruit.RecruitBoardModel">-->
<!--        select sum(is_first_window) as firstWindowNum, sum(is_first_submit) as firstSubmitNum,-->
<!--        sum(is_first_submit_through) as firstSubmitThroughNum, sum(is_first_crc_succeed) as firstCrcSucNum,-->
<!--        sum(is_first_obtain) as firstObtainNum, sum(is_first_random) as firstRandomNum,-->
<!--        sum(case when clew_source in (10) then is_clew_submit else 0 end) as manualClewNum,-->
<!--        sum(case when clew_source in (11, 12) then is_clew_submit else 0 end) as autoFlowClewNum,-->
<!--        sum(is_clew_submit) as allClewNum-->
<!--        from sdhz_rpt.dws_cro_user_patient_trans_full_d_dupl-->
<!--        where stat_date >= '${startTime}' and stat_date &lt;= '${endTime}'-->
<!--    </select>-->


<!--    <select id="groupByUniqueCode" statementType="STATEMENT" resultType="com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientrecruit.RecruitThoughSuccessModel">-->
<!--        select clew_unique_code as uniqueCode,-->
<!--        sum(is_first_submit_through) as firstSubmitThroughNum-->
<!--        from sdhz_rpt.dws_cro_user_patient_trans_full_d_dupl-->
<!--        where stat_date >= '${startTime}' group by clew_unique_code-->
<!--    </select>-->

</mapper>

