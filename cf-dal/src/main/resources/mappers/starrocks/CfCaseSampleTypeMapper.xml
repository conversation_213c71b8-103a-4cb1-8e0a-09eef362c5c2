<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.starrocks.CfCaseSampleTypeDao">

    <sql id="tableName">
        shuidi_rpt.rpt_cf_case_sample_type_full_h_dupl
    </sql>


    <sql id="selectFields">
        info_id as infoId,
        sample_type_info_id as sampleTypeInfoId,
        type_flag as typeFlag,
        donate_amt as donateAmt,
        donate_ant as donateAnt,
        share_cnt as shareCnt,
        ckr_share_cnt as ckrShareCnt,
        dt
    </sql>

    <!--
info_id BIGINT  NULL COMMENT '案例id',
sample_type_info_id BIGINT  NULL COMMENT '相似案例id',
type_flag int null comment '类型,满足兜底则为2，不是兜底的为1'
donate_amt DOUBLE  NULL COMMENT '总筹款金额,单位：元',
donate_ant BIGINT NULL COMMENT '总捐款次数',
share_cnt BIGINT NULL COMMENT '总转发次数',
ckr_share_cnt BIGINT NULL COMMENT '筹款人总转发次数',
dt date comment ''
     -->

    <select id="listByInfoId" statementType="STATEMENT" resultType="com.shuidihuzhu.cf.model.starrocks.RptCfCaseSampleTypeDo">
        select <include refid="selectFields"/>
        from <include refid = "tableName"/>
        where info_id  = ${infoId}
    </select>

<!--    <select id="aggregateByTimeBetween" statementType="STATEMENT" resultType="com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientrecruit.RecruitBoardModel">-->
<!--        select sum(is_first_window) as firstWindowNum, sum(is_first_submit) as firstSubmitNum,-->
<!--        sum(is_first_submit_through) as firstSubmitThroughNum, sum(is_first_crc_succeed) as firstCrcSucNum,-->
<!--        sum(is_first_obtain) as firstObtainNum, sum(is_first_random) as firstRandomNum,-->
<!--        sum(case when clew_source in (10) then is_clew_submit else 0 end) as manualClewNum,-->
<!--        sum(case when clew_source in (11, 12) then is_clew_submit else 0 end) as autoFlowClewNum,-->
<!--        sum(is_clew_submit) as allClewNum-->
<!--        from sdhz_rpt.dws_cro_user_patient_trans_full_d_dupl-->
<!--        where stat_date >= '${startTime}' and stat_date &lt;= '${endTime}'-->
<!--    </select>-->


<!--    <select id="groupByUniqueCode" statementType="STATEMENT" resultType="com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientrecruit.RecruitThoughSuccessModel">-->
<!--        select clew_unique_code as uniqueCode,-->
<!--        sum(is_first_submit_through) as firstSubmitThroughNum-->
<!--        from sdhz_rpt.dws_cro_user_patient_trans_full_d_dupl-->
<!--        where stat_date >= '${startTime}' group by clew_unique_code-->
<!--    </select>-->

</mapper>

