<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.starrocks.CfCaseFeaturesDao">

    <sql id="tableName">
        shuidi_rpt.rpt_cf_case_features_full_h_dupl
    </sql>


    <sql id="selectFields">
        info_id as infoId,
        volunteer_code as volunteerCode,
        age_range as ageRange,
        patient_treatment_methods as patientTreatmentMethods,
        normalized_disease as normalizedDisease,
        is_big_space_donate as isBigSpaceDonate,
        update_time as updateTime,
        friend_donate_cnt as friendDonateCnt,
        donate_amt as donateAmt,
        donate_ant as donateAnt,
        friend_contributions as friendContributions,
        share_cnt as shareCnt,
        ckr_share_cnt as ckrShareCnt,
        dt
    </sql>

    <!--
     info_id BIGINT  NULL COMMENT '案例id',
     volunteer_code String 顾问code,
age_range STRING  NULL COMMENT '患者年龄区间',
patient_treatment_methods STRING  NULL COMMENT '患者治疗方式',
normalized_disease STRING  NULL COMMENT '疾病名称',
is_big_space_donate INT NULL COMMENT '是否大空间捐单,是：1，否：0',
update_time string null COMMENT '更新时间',
friend_donate_cnt BIGINT NULL COMMENT '好友捐单次数',
donate_amt DOUBLE  NULL COMMENT '总筹款金额,单位：元',
donate_ant BIGINT NULL COMMENT '总捐款次数',
friend_contributions int NULL COMMENT '好友贡献占比',
share_cnt BIGINT NULL COMMENT '总转发次数',
ckr_share_cnt BIGINT NULL COMMENT '筹款人总转发次数',
dt date comment ''
     -->

    <select id="getByInfoId" statementType="STATEMENT" resultType="com.shuidihuzhu.cf.model.starrocks.RptCfCaseFeaturesDo">
        select <include refid="selectFields"/>
        from <include refid = "tableName"/>
        where info_id = ${infoId}
    </select>

    <select id="listByInfoIds" statementType="STATEMENT" resultType="com.shuidihuzhu.cf.model.starrocks.RptCfCaseFeaturesDo">
        select <include refid="selectFields"/>
        from <include refid = "tableName"/>
        where info_id in
        <foreach collection="infoIds" item="infoId" open="(" separator="," close=")">
            ${infoId}
        </foreach>
    </select>

    <select id="listBigSpaceDonateByVolunteerCode" statementType="STATEMENT" resultType="com.shuidihuzhu.cf.model.starrocks.RptCfCaseFeaturesDo">
        select <include refid="selectFields"/>
        from <include refid = "tableName"/>
        where volunteer_code = '${volunteerCode}'
        and is_big_space_donate = 1
    </select>


<!--    <select id="aggregateByTimeBetween" statementType="STATEMENT" resultType="com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientrecruit.RecruitBoardModel">-->
<!--        select sum(is_first_window) as firstWindowNum, sum(is_first_submit) as firstSubmitNum,-->
<!--        sum(is_first_submit_through) as firstSubmitThroughNum, sum(is_first_crc_succeed) as firstCrcSucNum,-->
<!--        sum(is_first_obtain) as firstObtainNum, sum(is_first_random) as firstRandomNum,-->
<!--        sum(case when clew_source in (10) then is_clew_submit else 0 end) as manualClewNum,-->
<!--        sum(case when clew_source in (11, 12) then is_clew_submit else 0 end) as autoFlowClewNum,-->
<!--        sum(is_clew_submit) as allClewNum-->
<!--        from sdhz_rpt.dws_cro_user_patient_trans_full_d_dupl-->
<!--        where stat_date >= '${startTime}' and stat_date &lt;= '${endTime}'-->
<!--    </select>-->


<!--    <select id="groupByUniqueCode" statementType="STATEMENT" resultType="com.shuidihuzhu.cf.cfgrowthtoolapi.model.patientrecruit.RecruitThoughSuccessModel">-->
<!--        select clew_unique_code as uniqueCode,-->
<!--        sum(is_first_submit_through) as firstSubmitThroughNum-->
<!--        from sdhz_rpt.dws_cro_user_patient_trans_full_d_dupl-->
<!--        where stat_date >= '${startTime}' group by clew_unique_code-->
<!--    </select>-->

</mapper>

