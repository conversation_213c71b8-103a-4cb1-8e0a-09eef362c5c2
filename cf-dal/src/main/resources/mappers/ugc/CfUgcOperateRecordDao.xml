<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.ugc.CfUgcOperateRecordDao">
  <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.domain.ugc.CfUgcOperateRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="case_id" jdbcType="BIGINT" property="caseId" />
    <result column="biz_id" jdbcType="BIGINT" property="bizId" />
    <result column="biz_type" jdbcType="INTEGER" property="bizType" />
    <result column="operate_type" jdbcType="INTEGER" property="operateType" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="operator_id" jdbcType="INTEGER" property="operatorId" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="department" jdbcType="VARCHAR" property="department" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, case_id, biz_id, biz_type, operate_type, content, operator_id, `operator`, department, 
    is_delete, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from cf_ugc_operate_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.shuidihuzhu.cf.domain.ugc.CfUgcOperateRecord" useGeneratedKeys="true">
    insert into cf_ugc_operate_record (case_id, biz_id, biz_type, 
      operate_type, content, operator_id, 
      `operator`, department, is_delete, 
      create_time, update_time)
    values (#{caseId,jdbcType=BIGINT}, #{bizId,jdbcType=BIGINT}, #{bizType,jdbcType=INTEGER}, 
      #{operateType,jdbcType=INTEGER}, #{content,jdbcType=VARCHAR}, #{operatorId,jdbcType=INTEGER}, 
      #{operator,jdbcType=VARCHAR}, #{department,jdbcType=VARCHAR}, #{isDelete,jdbcType=TINYINT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.shuidihuzhu.cf.domain.ugc.CfUgcOperateRecord" useGeneratedKeys="true">
    insert into cf_ugc_operate_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="caseId != null">
        case_id,
      </if>
      <if test="bizId != null">
        biz_id,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="operateType != null">
        operate_type,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="operator != null">
        `operator`,
      </if>
      <if test="department != null">
        department,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="caseId != null">
        #{caseId,jdbcType=BIGINT},
      </if>
      <if test="bizId != null">
        #{bizId,jdbcType=BIGINT},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=INTEGER},
      </if>
      <if test="operateType != null">
        #{operateType,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=INTEGER},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="department != null">
        #{department,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shuidihuzhu.cf.domain.ugc.CfUgcOperateRecord">
    update cf_ugc_operate_record
    <set>
      <if test="caseId != null">
        case_id = #{caseId,jdbcType=BIGINT},
      </if>
      <if test="bizId != null">
        biz_id = #{bizId,jdbcType=BIGINT},
      </if>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=INTEGER},
      </if>
      <if test="operateType != null">
        operate_type = #{operateType,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=INTEGER},
      </if>
      <if test="operator != null">
        `operator` = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="department != null">
        department = #{department,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shuidihuzhu.cf.domain.ugc.CfUgcOperateRecord">
    update cf_ugc_operate_record
    set case_id = #{caseId,jdbcType=BIGINT},
      biz_id = #{bizId,jdbcType=BIGINT},
      biz_type = #{bizType,jdbcType=INTEGER},
      operate_type = #{operateType,jdbcType=INTEGER},
      content = #{content,jdbcType=VARCHAR},
      operator_id = #{operatorId,jdbcType=INTEGER},
      `operator` = #{operator,jdbcType=VARCHAR},
      department = #{department,jdbcType=VARCHAR},
      is_delete = #{isDelete,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="getByCaseIdAndBizIdAndBizType" resultType="com.shuidihuzhu.cf.domain.ugc.CfUgcOperateRecord">
    select
    <include refid="Base_Column_List" />
    from cf_ugc_operate_record
    where case_id = #{caseId}
    and biz_id = #{bizId}
    and biz_type = #{bizType}
    and is_delete = 0
    order by create_time desc
    limit 1
  </select>
</mapper>