<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.inventory.CfInventoryDao">
	<sql id="tableName">
		app_user_device_info_collection
	</sql>

	<sql id="insertField">
		`user_id`,
		`identification`,
		`content`
	</sql>

	<sql id="selectField">
		`user_id`,
		`identification`,
		`content`,
		`create_time`
	</sql>

	<insert id="insertDeviceInfoList">
		INSERT INTO <include refid="tableName"/>
		(<include refid="insertField"/>)
		VALUES
		<foreach collection="userDeviceInfos" item="item" index="index" separator=",">
			(#{item.userId}, #{item.identification}, #{item.content})
		</foreach>
	</insert>

	<select id="getOneDeviceInfoByUserId" resultType="com.shuidihuzhu.cf.domain.UserDeviceInfo">
		select <include refid="selectField"/>
		from <include refid="tableName"/>
		where user_id = #{userId}
		and identification = #{identification}
		and is_delete = 0
		order by id desc
		limit 1
	</select>

	<select id="getDeviceInfoCount" resultType="int">
		select count(*)
		from <include refid="tableName"/>
		where user_id = #{userId}
		and identification = #{identification}
		and create_time > #{time}
		and is_delete = 0
	</select>

</mapper>