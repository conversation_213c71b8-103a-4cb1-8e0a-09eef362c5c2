<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.dedicated.CfToufangInvitorVisitDao">
    <insert id="add" parameterType="com.shuidihuzhu.cf.domain.dedicated.CfToufangInvitorVisitDO">
        insert into cf_toufang_invitor_visit_record
        (create_time,source_user_id,invitor_user_id,channel)
        value
        (#{createTime},#{sourceUserId},#{invitorUserId},#{channel})
    </insert>
    <select id="getCfToufangInvitorVisitDOByUserId"
            resultType="com.shuidihuzhu.cf.domain.dedicated.CfToufangInvitorVisitDO">
        select id,create_time,update_time,source_user_id,invitor_user_id,channel
        from cf_toufang_invitor_visit_record
        where invitor_user_id=#{userId} and channel=#{channel}
        order by id desc limit 1
    </select>
    <select id="getCfToufangInvitorVisitDOCountByUserId" resultType="java.lang.Integer">
        select count(distinct invitor_user_id)
        from cf_toufang_invitor_visit_record
        where source_user_id=#{sourceUserId} and channel=#{inviteChannel}
        and create_time between #{startTime} and #{endTime}
    </select>
</mapper>