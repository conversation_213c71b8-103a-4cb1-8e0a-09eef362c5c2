<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.dedicated.CrowdfundingVolunteerInviteUserRecordDao">
    <sql id="table_name">
        crowdfunding_volunteer_invite_user_record
    </sql>
    <sql id="fileds">
      `id`,
      `channel`,
      `unique_code`,
      `phone`,
      `user_id`,
      `create_time`,
      `update_time`,
      `is_delete`
    </sql>
    <insert id="insert" parameterType="com.shuidihuzhu.cf.domain.dedicated.CrowdfundingVolunteerInviteUserRecordDO"
            useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="table_name"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id!=null">`id`,</if>
            <if test="channel!=null and channel!=''">`channel`,</if>
            <if test="uniqueCode!=null and uniqueCode!=''">`unique_code`,</if>
            <if test="phone!=null and phone!=''">`phone`,</if>
            <if test="userId!=null">`user_id`,</if>
            <if test="isDelete!=null">`is_delete`</if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id!=null">#{id},</if>
            <if test="channel!=null and channel!=''">#{channel},</if>
            <if test="uniqueCode!=null and uniqueCode!=''">#{uniqueCode},</if>
            <if test="phone!=null and phone!=''">#{phone},</if>
            <if test="userId!=null">#{userId},</if>
            <if test="isDelete!=null">#{isDelete}</if>
        </trim>
    </insert>
</mapper>
