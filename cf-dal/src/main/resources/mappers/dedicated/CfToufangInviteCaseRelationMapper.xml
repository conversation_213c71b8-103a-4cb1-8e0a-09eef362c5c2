<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.dedicated.CfToufangInviteCaseRelationDao">
    <sql id="tableName">
        cf_toufang_invite_case_relation
    </sql>
    <sql id="fields">
        id,
        create_time,
        update_time,
        invitor_unique_code,
        info_uuid,
        complete_flag,
        task_flag,
        invitor_case_id
    </sql>
    <insert id="addInviteRelation" parameterType="com.shuidihuzhu.cf.domain.dedicated.CfToufangInviteCaseRelationDO">
        insert into <include refid="tableName"/>
        (create_time,invitor_unique_code,info_uuid, invitor_type, invitor_case_id)
        values
        (#{createTime},#{invitorUniqueCode},#{infoUuid}, #{invitorType}, #{invitorCaseId})
    </insert>
    <select id="getInviteCaseRelation"
            resultType="com.shuidihuzhu.cf.domain.dedicated.CfToufangInviteCaseRelationDO">
        select <include refid="fields"/>
        from <include refid="tableName"/>
        where invitor_unique_code=#{invitorUniqueCode}
    </select>
</mapper>