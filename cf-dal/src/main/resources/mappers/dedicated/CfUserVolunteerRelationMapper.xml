<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.dedicated.CfUserVolunteerRelationDao">
    <sql id="tableName">cf_user_volunteer_relation</sql>
    <sql id="fields">
       id,
    create_time,
    update_time,
    open_id,
    volunteer_phone
    </sql>
    <insert id="addUserVolunteerRelationDO" parameterType="com.shuidihuzhu.cf.domain.dedicated.CfUserVolunteerRelationDO">
        insert into <include refid="tableName"/>
        (create_time,update_time,open_id,volunteer_phone)
        value
        (#{createTime},#{updateTime},#{openId},#{volunteerPhone})
    </insert>

    <select id="getAccountUserAndVolunteerRelation"
            resultType="com.shuidihuzhu.cf.domain.dedicated.CfUserVolunteerRelationDO">
        select <include refid="fields"/>
        from <include refid="tableName"/>
        where
        1=1
        <if test="openId!=null">
          and open_id=#{openId}
        </if>
        <if test="phone!=null">
            and volunteer_phone=#{phone}
        </if>
    </select>
</mapper>