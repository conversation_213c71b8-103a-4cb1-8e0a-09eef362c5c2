<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.publictrust.CfPublicTrustPromotionStatisticsDao">
  <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.model.publictrust.CfPublicTrustPromotionStatisticsDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="info_id" jdbcType="VARCHAR" property="infoId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="is_click" jdbcType="TINYINT" property="isClick" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="is_available" jdbcType="TINYINT" property="isAvailable" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, info_id, user_id, is_click, `type`, is_delete, is_available, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from cf_public_trust_promotion_statistics
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.shuidihuzhu.cf.model.publictrust.CfPublicTrustPromotionStatisticsDO">
    insert into cf_public_trust_promotion_statistics (id, info_id, user_id, 
      is_click, `type`, is_delete, 
      is_available, create_time, update_time
      )
    values (#{id,jdbcType=BIGINT}, #{infoId,jdbcType=VARCHAR}, #{userId,jdbcType=BIGINT}, 
      #{isClick,jdbcType=TINYINT}, #{type,jdbcType=TINYINT}, #{isDelete,jdbcType=TINYINT}, 
      #{isAvailable,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.shuidihuzhu.cf.model.publictrust.CfPublicTrustPromotionStatisticsDO">
    insert into cf_public_trust_promotion_statistics
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="infoId != null">
        info_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="isClick != null">
        is_click,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="isAvailable != null">
        is_available,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="infoId != null">
        #{infoId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="isClick != null">
        #{isClick,jdbcType=TINYINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="isAvailable != null">
        #{isAvailable,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shuidihuzhu.cf.model.publictrust.CfPublicTrustPromotionStatisticsDO">
    update cf_public_trust_promotion_statistics
    <set>
      <if test="infoId != null">
        info_id = #{infoId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="isClick != null">
        is_click = #{isClick,jdbcType=TINYINT},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=TINYINT},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="isAvailable != null">
        is_available = #{isAvailable,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shuidihuzhu.cf.model.publictrust.CfPublicTrustPromotionStatisticsDO">
    update cf_public_trust_promotion_statistics
    set info_id = #{infoId,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=BIGINT},
      is_click = #{isClick,jdbcType=TINYINT},
      `type` = #{type,jdbcType=TINYINT},
      is_delete = #{isDelete,jdbcType=TINYINT},
      is_available = #{isAvailable,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getByUserIdAndIsClickAndType"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cf_public_trust_promotion_statistics
    where
    is_delete = 0
    and is_available = 1
    and user_id = #{userId}
    and is_click = #{isClick}
    and `type` = #{type}
    limit 1
  </select>
</mapper>