<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.mask.CfImageMaskDao">
	<sql id="tableName">
		cf_image_ai_mask
	</sql>

	<sql id="insertField">
		`case_id`,
		`biz_id`,
		`biz_type`
	</sql>

	<sql id="selectField">
		`case_id`,
		`biz_id`,
		`biz_type`,
		`image_url_ai`,
		`image_handle_status`,
		`finish_ai_time`,
		`create_time`,
		`is_delete`
	</sql>

	<insert id="insertImageRecord">
		INSERT INTO <include refid="tableName"/>
		(<include refid="insertField"/>)
		VALUES
		<foreach collection="cfImageAiMaskDOS" item="item" index="index" separator=",">
			(#{item.caseId},#{item.bizId},#{item.bizType})
		</foreach>
	</insert>

	<select id="selectByBizId" resultType="com.shuidihuzhu.cf.domain.CfImageAiMaskDO">
		select <include refid="selectField"/>
		from <include refid="tableName"/>
		where `biz_id` = #{bizId}
		and `is_delete` = 0
		order by id desc
		limit 1
	</select>

	<update id="updateMaskImage">
		update <include refid="tableName"/>
		set `image_url_ai` = #{imageUrlAi}
		    , `image_handle_status` = #{imageHandleStatus}
		    , `finish_ai_time` = #{finishAiTime}
		where `biz_id` = #{bizId} and `image_url_ai` = ''
		and `is_delete` = 0
		limit 1
	</update>

	<select id="selectByCaseIdAndType" resultType="com.shuidihuzhu.cf.domain.CfImageAiMaskDO">
		SELECT
		<include refid="selectField"/>
		FROM
		<include refid="tableName"/>
		WHERE
		`case_id` = #{caseId}
		and `biz_type` IN
			<foreach collection="bizType" open="("  close=")" separator="," item="item">
				#{item}
			</foreach>
		and `is_delete` = 0
	</select>

	<select id="selectDeleteByBizIdsAndType" resultType="com.shuidihuzhu.cf.domain.CfImageAiMaskDO">
		SELECT
		<include refid="selectField"/>
		FROM
		<include refid="tableName"/>
		WHERE
		`biz_id` IN
		<foreach collection="bizIds" open="("  close=")" separator="," item="item">
			#{item}
		</foreach>
		and `biz_type` IN
		<foreach collection="bizType" open="("  close=")" separator="," item="item">
			#{item}
		</foreach>
	</select>

	<select id="selectByBizIdAndType" resultType="com.shuidihuzhu.cf.domain.CfImageAiMaskDO">
		SELECT
		<include refid="selectField"/>
		FROM
		<include refid="tableName"/>
		WHERE
		`biz_id` = #{bizId}
		and `biz_type` = #{bizType}
		and `is_delete` = 0
	</select>

	<update id="deleteMaskImageByBiz">
		update <include refid="tableName"/>
		set `is_delete` = 1
		where `biz_id` IN
		<foreach collection="bizIds" open="("  close=")" separator="," item="item">
			#{item}
		</foreach>
		and `biz_type` IN
		<foreach collection="bizType" open="("  close=")" separator="," item="item">
			#{item}
		</foreach>
	</update>

	<update id="deleteMaskProgressImage">
		update <include refid="tableName"/>
		set `is_delete` = 1
		where `biz_id` IN
		<foreach collection="bizIds" open="("  close=")" separator="," item="item">
			#{item}
		</foreach>
		and `biz_type` IN
		<foreach collection="bizType" open="("  close=")" separator="," item="item">
			#{item}
		</foreach>
		and `image_url_ai` = #{imageUrl}
	</update>

	<update id="deleteMaskImageByBizType">
		update <include refid="tableName"/>
		set `is_delete` = 1
		where `case_id` = #{caseId} and `biz_type` = #{bizType}
	</update>

</mapper>