<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.bigdata.CaseFeaturesDao">

    <sql id="tableName">
        shuidi_rpt.rpt_cf_case_features_full_h_dupl
    </sql>


    <sql id="selectFields">
        info_id as infoId,
        volunteer_code as volunteerCode,
        age_range as ageRange,
        patient_treatment_methods as patientTreatmentMethods,
        normalized_disease as normalizedDisease,
        is_big_space_donate as isBigSpaceDonate,
        friend_donate_cnt as friendDonateCnt,
        donate_amt as donateAmt,
        donate_ant as donateAnt,
        friend_contributions as friendContributions,
        share_cnt as shareCnt,
        ckr_share_cnt as ckrShareCnt,
        dt
    </sql>

    <!--
     info_id BIGINT  NULL COMMENT '案例id',
     volunteer_code String 顾问code,
age_range STRING  NULL COMMENT '患者年龄区间',
patient_treatment_methods STRING  NULL COMMENT '患者治疗方式',
normalized_disease STRING  NULL COMMENT '疾病名称',
is_big_space_donate INT NULL COMMENT '是否大空间捐单,是：1，否：0',
update_time string null COMMENT '更新时间',
friend_donate_cnt BIGINT NULL COMMENT '好友捐单次数',
donate_amt DOUBLE  NULL COMMENT '总筹款金额,单位：元',
donate_ant BIGINT NULL COMMENT '总捐款次数',
friend_contributions int NULL COMMENT '好友贡献占比',
share_cnt BIGINT NULL COMMENT '总转发次数',
ckr_share_cnt BIGINT NULL COMMENT '筹款人总转发次数',
dt date comment ''
     -->

    <select id="getByInfoId" resultType="com.shuidihuzhu.cf.model.bigdata.CfCaseFeaturesDo">
        select <include refid="selectFields"/>
        from <include refid = "tableName"/>
        where info_id = #{infoId}
        and dt = #{dt}
    </select>

    <select id="listByInfoIds" resultType="com.shuidihuzhu.cf.model.bigdata.CfCaseFeaturesDo">
        select <include refid="selectFields"/>
        from <include refid = "tableName"/>
        where info_id in
        <foreach collection="infoIds" item="infoId" open="(" separator="," close=")">
            #{infoId}
        </foreach>
        and dt = #{dt}
    </select>

    <select id="listBigSpaceDonateByVolunteerCode" resultType="com.shuidihuzhu.cf.model.bigdata.CfCaseFeaturesDo">
        select <include refid="selectFields"/>
        from <include refid = "tableName"/>
        where volunteer_code = #{volunteerCode}
        and is_big_space_donate = 1
        and dt = #{dt}
    </select>

</mapper>

