<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.bigdata.CaseFriendShareDao">

    <sql id="tableName">
        shuidi_rpt.rpt_cf_case_friend_share_full_h_dupl
    </sql>


    <sql id="selectFields">
        info_id as infoId,
        friend_user_id as friendUserId,
        share_donate_ant as shareDonateAnt,
        share_donate_amt as shareDonateAmt,
        dt
    </sql>

    <!--
info_id BIGINT  NULL COMMENT '案例id',
friend_user_id BIGINT  NULL COMMENT '好友用户id',
update_time string null COMMENT '更新时间',
share_donate_ant BIGINT NULL COMMENT '转发带来的捐单量',
share_donate_amt DOUBLE  NULL COMMENT '转发带来的捐单金额,单位：元',
dt date comment ''
     -->

    <select id="listByInfoId" resultType="com.shuidihuzhu.cf.model.bigdata.CfCaseFriendShareDo">
        select <include refid="selectFields"/>
        from <include refid = "tableName"/>
        where info_id  = #{infoId}
        and dt = #{dt}
    </select>

</mapper>

