<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.bigdata.CaseSampleTypeDao">

    <sql id="tableName">
        shuidi_rpt.rpt_cf_case_sample_type_full_h_dupl
    </sql>


    <sql id="selectFields">
        info_id as infoId,
        sample_type_info_id as sampleTypeInfoId,
        type_flag as typeFlag,
        donate_amt as donateAmt,
        donate_ant as donateAnt,
        share_cnt as shareCnt,
        ckr_share_cnt as ckrShareCnt,
        dt
    </sql>

    <!--
info_id BIGINT  NULL COMMENT '案例id',
sample_type_info_id BIGINT  NULL COMMENT '相似案例id',
type_flag int null comment '类型,满足兜底则为2，不是兜底的为1'
donate_amt DOUBLE  NULL COMMENT '总筹款金额,单位：元',
donate_ant BIGINT NULL COMMENT '总捐款次数',
share_cnt BIGINT NULL COMMENT '总转发次数',
ckr_share_cnt BIGINT NULL COMMENT '筹款人总转发次数',
dt date comment ''
     -->

    <select id="listByInfoId" resultType="com.shuidihuzhu.cf.model.bigdata.CfCaseSampleTypeDo">
        select <include refid="selectFields"/>
        from <include refid = "tableName"/>
        where info_id  = #{infoId}
        and dt = #{dt}
    </select>

</mapper>

