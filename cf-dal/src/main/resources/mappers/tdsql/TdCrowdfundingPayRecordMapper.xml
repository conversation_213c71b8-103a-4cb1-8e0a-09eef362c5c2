<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.tdsql.TdCrowdfundingPayRecordDao">
    <sql id="TABLE">
        crowdfunding_pay_record_orderid_sharding
    </sql>
    <sql id="FIELDS">
        `id`,`pay_uid`,`crowdfunding_order_id`,`pre_pay_amount`,`real_pay_amount`,`pay_platform`,`pay_status`,`ctime`,`callback_time`,`valid`,
        `refund_status`,`refund_time`,`refund_amount`,`refund_reason`
    </sql>

    <select id="getAllPaySuccessByIdAndTime" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord">
        SELECT <include refid="FIELDS"/>
        FROM <include refid="TABLE"/>
        WHERE pay_status = 1
        AND  callback_time between  #{beginTime} and #{endTime}
        and  <![CDATA[ `id` > #{id}]]>
        limit #{size}
    </select>

    <select id="selectByOrderId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord">
        SELECT *
        FROM <include refid="TABLE"/>
        WHERE `crowdfunding_order_id` = #{orderId}
        limit 1
    </select>
</mapper>
