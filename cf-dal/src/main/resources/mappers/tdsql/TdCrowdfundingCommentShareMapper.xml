<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.tdsql.TdCrowdfundingCommentShareDao">

    <sql id="fields">
        `id`, `crowdfunding_id`,`parent_id`,`user_id`,`user_third_id`,`user_third_type`,`comment_id`,`content`,`type`,`is_deleted`,`create_time`
    </sql>

    <select id="getReplyByParentList" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingComment">
        SELECT <include refid="fields"/>
        FROM td_shuidi_crowdfunding.crowdfunding_comment_new
        WHERE
        `parent_id` in
        <foreach item="orderId" collection="orderIdList" open="(" close=")" separator=",">
            #{orderId}
        </foreach>
        and type = #{type}
        order by `create_time` desc
        limit #{limit}
    </select>

</mapper>
