<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.tdsql.TdCrowdfundingOrderDao">
    <sql id="TABLE">
        crowdfunding_order_crowdfundingid_sharding
    </sql>

    <sql id="FIELDS">
        `id`,`code`,`user_id`,`crowdfunding_id`,`amount`,`comment`,`pay_status`,
        `ctime`,`pay_time`,`valid`,`ip`,`os_type`,`channel`,`from`,`self_tag`,`user_third_id`,`user_third_type`,`anonymous`
    </sql>

    <select id="selectCountByMin" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.CfCountSumVo">
        SELECT COUNT(*) AS counts,IFNULL(SUM(amount),0) AS sum
        FROM <include refid="TABLE"/>
        WHERE <![CDATA[ `pay_time`>=#{begin} ]]> AND <![CDATA[ `pay_time`<#{end} ]]>
        <if test="payStatus!=null">
            AND `pay_status`=#{payStatus}
        </if>
    </select>

    <select id="getByOffset" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
        SELECT
        <include refid="FIELDS"/>
        FROM
        <include refid="TABLE"/>
        WHERE `id`>#{offset}
        AND `pay_status`=1
        AND `valid`=1
        AND `user_id` IS NOT NULL
        LIMIT #{limit};
    </select>

    <select id="getListBetweenTime" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
        SELECT
        <include refid="FIELDS"/>
        FROM
        <include refid="TABLE"/>
        WHERE
        `pay_time`>=#{start} and
        <![CDATA[ `pay_time`<#{end} ]]> AND
        `pay_status`=1 AND
        `valid`=1
        limit #{offset},#{limit}
    </select>


    <select id="getOrderCountBetweenTime" resultType="java.lang.Integer">
        SELECT count(id)
        from <include refid="TABLE"/>
        where valid = 1
        AND pay_status = 1
        <if test="start != null">
            AND <![CDATA[   `pay_time`>=#{start} ]]>
        </if>
        <if test="end != null">
            AND <![CDATA[   `pay_time`<#{end} ]]>
        </if>
    </select>

    <select id="getLatestOrderTime" resultType="java.sql.Timestamp">
        SELECT ctime FROM
        <include refid="TABLE"/>
        ORDER BY id DESC LIMIT 1
    </select>



    <select id="selectByPayTimeLimit" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
        SELECT `id`,`crowdfunding_id`,`amount`,`user_third_type`
        FROM <include refid="TABLE"/>
        WHERE `pay_status`=1
        AND <![CDATA[ `pay_time`>=#{begin} ]]> AND <![CDATA[ `pay_time`<#{end} ]]>
        LIMIT #{start},#{size}
    </select>





    <select id="getValidPayedSuccessListByPayTimeLimit" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
        SELECT `id`,`crowdfunding_id`,`amount`,`user_third_type`
        FROM <include refid="TABLE"/>
        WHERE `pay_status`=1 AND `valid` = 1
        AND <![CDATA[ `pay_time`>=#{begin} ]]> AND <![CDATA[ `pay_time`<#{end} ]]>
        AND `id` > #{id}
        LIMIT #{size}
    </select>

    <select id="selectByUserIdAndThirdType" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
        SELECT <include refid="FIELDS"/>
        FROM <include refid="TABLE"/>
        <where>
            <if test="userId != null and userId > 0">
                `user_id`=#{userId}
            </if>
            <if test="caseId != null">
                AND `crowdfunding_id`=#{caseId}
            </if>
            <if test="thirdType != null">
                AND `user_third_type`=#{thirdType}
            </if>
        </where>
        AND `pay_status` = 1 AND `valid` = 1
    </select>

    <select id="findCrowdfundingOrder" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
        select
        id,
        code,
        user_id,
        crowdfunding_id,
        amount,
        comment,
        pay_status,
        ctime,
        pay_time,
        valid,
        ip,
        os_type,
        channel,
        `from`,
        self_tag,
        user_third_id,
        user_third_type,
        last_modified,
        anonymous
        from <include refid="TABLE"/>
        <trim prefix="WHERE" prefixOverrides="AND|OR" suffixOverrides="AND|OR">
            <if test="userId!=null">
                AND user_id = #{userId}
            </if>
            <if test="startTime!=null and startTime!=''">
                AND <![CDATA[ `pay_time` >= #{startTime} ]]>
            </if>
            <if test="endTime!=null and endTime!=''">
                AND <![CDATA[ `pay_time` < #{endTime} ]]>
            </if>
        </trim>
    </select>

    <select id="getCountByTime" resultType="java.util.Map">
        SELECT crowdfunding_id as infoId, sum(amount) as amount, count(*) as num
        FROM <include refid="TABLE"/>
        WHERE <![CDATA[ `pay_time` >= #{startTime} AND `pay_time` < #{endTime} ]]>
        GROUP BY crowdfunding_id
    </select>


    <select id="getCountByTimeNew" resultType="com.shuidihuzhu.cf.model.crowdfunding.OrderStatisticsPo">
        SELECT crowdfunding_id as infoId, sum(amount) as amount, count(*) as num
        FROM <include refid="TABLE"/>
        WHERE <![CDATA[ `pay_time` >= #{startTime} AND `pay_time` < #{endTime} ]]>
        GROUP BY crowdfunding_id
    </select>

    <select id="getNoPayListByDate" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
        SELECT
        <include refid="FIELDS"/>
        FROM
        <include refid="TABLE"/>
        WHERE last_modified between #{startDate} and #{endDate}
        and id > #{id}
        and pay_status = 0 and valid = 1
        LIMIT #{limit}
    </select>

    <select id="getCrowdfundingOrderByCode" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
        SELECT <include refid="FIELDS"/>
        FROM  <include refid="TABLE"/>
        WHERE  code=#{code}
    </select>

    <select id="getCrowdfundingOrderById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
        SELECT <include refid="FIELDS"/>
        FROM <include refid="TABLE"/>
        WHERE  id=#{id}
    </select>

    <select id="getCrowdfundingOrderByActivity" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
        SELECT
        <include refid="FIELDS"/>
        FROM
        <include refid="TABLE"/>
        WHERE crowdfunding_id in
        <foreach collection="caseIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND amount > 0
        AND pay_status = 1
        AND valid = 1
        AND activity_id = #{activityId}
    </select>
</mapper>
