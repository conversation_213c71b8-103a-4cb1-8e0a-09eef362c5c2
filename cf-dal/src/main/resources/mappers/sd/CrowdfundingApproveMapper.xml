<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.sd.admin.CrowdfundingApproveDao">
    <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingApprove">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="crowdfunding_id" property="crowdfundingId" jdbcType="INTEGER"/>
        <result column="oprid" property="oprid" jdbcType="INTEGER"/>
        <result column="oprtime" property="oprtime" jdbcType="TIMESTAMP"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="comment" property="comment" jdbcType="VARCHAR"/>
        <result column="organization" property="organization" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, crowdfunding_id, oprid, oprtime, status, comment, organization
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        <include refid="Base_Column_List"/>
        from admin_crowdfunding_approve
        where id = #{id,jdbcType=INTEGER}
    </select>

    <select id="getListByCrowdfundingId" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        <include refid="Base_Column_List"/>
        from admin_crowdfunding_approve
        where crowdfunding_id = #{crowdfundingId,jdbcType=INTEGER}
    </select>

    <select id="getLastWithCommentByCrowdfundingId"  resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        <include refid="Base_Column_List"/>
        from admin_crowdfunding_approve
        where crowdfunding_id = #{crowdfundingId,jdbcType=INTEGER} and comment is not null and comment != ' '
	    order by id desc limit 1
    </select>

    <select id="getCrowdfundingApprovesByCfIds"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingApprove">
        select
        <include refid="Base_Column_List"/>
        from admin_crowdfunding_approve
        where crowdfunding_id in
        <foreach collection="cfIds" open="(" close=")" separator="," item="infoId">
          #{infoId}
        </foreach>
    </select>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingApprove">
    insert into admin_crowdfunding_approve (id, crowdfunding_id, oprid, 
      oprtime, status, comment, organization
      )
    values (#{id,jdbcType=INTEGER}, #{crowdfundingId,jdbcType=INTEGER}, #{oprid,jdbcType=INTEGER}, 
      #{oprtime,jdbcType=TIMESTAMP}, #{status,jdbcType=INTEGER}, #{comment,jdbcType=VARCHAR}, #{organization}
      )
  </insert>
    <insert id="insertSelective" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingApprove">
        insert into admin_crowdfunding_approve
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="crowdfundingId != null">
                crowdfunding_id,
            </if>
            <if test="oprid != null">
                oprid,
            </if>
            <if test="oprtime != null">
                oprtime,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="comment != null">
                comment,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="crowdfundingId != null">
                #{crowdfundingId,jdbcType=INTEGER},
            </if>
            <if test="oprid != null">
                #{oprid,jdbcType=INTEGER},
            </if>
            <if test="oprtime != null">
                #{oprtime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="comment != null">
                #{comment,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingApprove">
        update admin_crowdfunding_approve
        <set>
            <if test="crowdfundingId != null">
                crowdfunding_id = #{crowdfundingId,jdbcType=INTEGER},
            </if>
            <if test="oprid != null">
                oprid = #{oprid,jdbcType=INTEGER},
            </if>
            <if test="oprtime != null">
                oprtime = #{oprtime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="comment != null">
                comment = #{comment,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingApprove">
    update admin_crowdfunding_approve
    set crowdfunding_id = #{crowdfundingId,jdbcType=INTEGER},
      oprid = #{oprid,jdbcType=INTEGER},
      oprtime = #{oprtime,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=INTEGER},
      comment = #{comment,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>