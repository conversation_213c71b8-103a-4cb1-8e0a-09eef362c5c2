<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.activity.ActivityRedPocketNodeDAO" >
    <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.domain.activity.ActivityRedPocketDO">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="case_id" property="caseId" jdbcType="INTEGER"/>
        <result column="money" property="money" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER"
                javaType="com.shuidihuzhu.cf.enums.activity.ActivityRedPocketStatusEnum"
                typeHandler="com.shuidihuzhu.cf.dao.type.handler.ActivityRedPocketStatusEnumTypeHandler"
                />
        <result column="need_alert" property="needAlert" jdbcType="INTEGER"/>
        <result column="send_result" property="sendResult" jdbcType="INTEGER"/>
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="current_value" property="currentValue" jdbcType="INTEGER"/>

    </resultMap>

    <sql id="INSERT_Column_List">
        id,case_id,money,status,need_alert,send_result,current_value,start_time,end_time
    </sql>

    <sql id="Base_Column_List">
        id,case_id,money,create_time,update_time,is_delete,status,need_alert,send_result,current_value,start_time,end_time
    </sql>

    <insert id="insertList" parameterType="com.shuidihuzhu.cf.domain.activity.ActivityRedPocketDO"
            useGeneratedKeys="true" keyProperty="id">
<!--        <selectKey order="AFTER" resultType="long" keyProperty="id">-->
<!--            select last_insert_id() as id-->
<!--        </selectKey>-->
        insert into activity_red_pocket_node(<include refid="INSERT_Column_List" />)
        values
        <foreach collection ="list" item="item" index= "index" separator =",">
            (
            #{item.id,jdbcType=INTEGER},
            #{item.caseId,jdbcType=INTEGER},
            #{item.money,jdbcType=INTEGER},
            #{item.status,jdbcType=INTEGER,
                        javaType=com.shuidihuzhu.cf.enums.activity.ActivityRedPocketStatusEnum,
                        typeHandler=com.shuidihuzhu.cf.dao.type.handler.ActivityRedPocketStatusEnumTypeHandler},
            #{item.needAlert,jdbcType=INTEGER},
            #{item.sendResult,jdbcType=INTEGER},
            #{item.currentValue,jdbcType=INTEGER},
            #{item.startTime,jdbcType=TIMESTAMP},
            #{item.endTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>

    <select id="getByCaseId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from activity_red_pocket_node
        where case_id = #{caseId} and is_delete = 0
    </select>

    <select id="getByNodeId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from activity_red_pocket_node
        where case_id = #{caseId} and id = #{id} and is_delete = 0
    </select>

    <update id="updateSendResult">

        update activity_red_pocket_node
        set send_result = 1
        where case_id = #{caseId} and id = #{id}

    </update>

    <update id="updateNeedAlert">
        update activity_red_pocket_node
        set need_alert = 0
        where case_id = #{caseId} and id = #{id}
    </update>

    <update id="addMoney">
        update activity_red_pocket_node
        set current_value = current_value + #{money}
        where case_id = #{caseId} and id = #{id}
    </update>

    <update id="updateStatus">
        update activity_red_pocket_node
        set status = #{status,jdbcType=INTEGER,
                        javaType=com.shuidihuzhu.cf.enums.activity.ActivityRedPocketStatusEnum,
                        typeHandler=com.shuidihuzhu.cf.dao.type.handler.ActivityRedPocketStatusEnumTypeHandler}
        where case_id = #{caseId} and id = #{id}
    </update>

    <update id="updateStatusStartTime">
        update activity_red_pocket_node
        set status = #{status,jdbcType=INTEGER,
                        javaType=com.shuidihuzhu.cf.enums.activity.ActivityRedPocketStatusEnum,
                        typeHandler=com.shuidihuzhu.cf.dao.type.handler.ActivityRedPocketStatusEnumTypeHandler}
        ,
            start_time = #{startTime}
        where case_id = #{caseId} and id = #{id}
    </update>

    <update id="updateStatusStartTimeEndTime">
        update activity_red_pocket_node
        set status = #{status,jdbcType=INTEGER,
                        javaType=com.shuidihuzhu.cf.enums.activity.ActivityRedPocketStatusEnum,
                        typeHandler=com.shuidihuzhu.cf.dao.type.handler.ActivityRedPocketStatusEnumTypeHandler}
        ,
            start_time = #{startTime}
        ,
            end_time = #{endTime}
        where case_id = #{caseId} and id = #{id}
    </update>

    <update id="updateStatusEndTime">
        update activity_red_pocket_node
        set status = #{status,jdbcType=INTEGER,
                        javaType=com.shuidihuzhu.cf.enums.activity.ActivityRedPocketStatusEnum,
                        typeHandler=com.shuidihuzhu.cf.dao.type.handler.ActivityRedPocketStatusEnumTypeHandler}
        ,
            end_time = #{endTime}
        where case_id = #{caseId} and id = #{id}
    </update>

</mapper>