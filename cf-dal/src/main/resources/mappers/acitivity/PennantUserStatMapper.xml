<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.activity.PennantUserStatDao">
	<sql id="tableName">
		pennant_user_stat
	</sql>

	<sql id="fields">
		`id` as id,
		`user_id` as userId,
		`total_num` as totalNum,
		`unreceived_num` as unreceivedNum,
		`pennant_id_str` as pennantIdStr
	</sql>

	<insert id="insert" parameterType="com.shuidihuzhu.cf.model.activity.PennantUserStat">
		insert into <include refid="tableName"/>
		(`user_id`,`total_num`,`unreceived_num`) values (#{userId},#{totalNum},#{unreceivedNum})
	</insert>

	<select id="getByUserId" resultType="com.shuidihuzhu.cf.model.activity.PennantUserStat">
		select <include refid="fields"/> from <include refid="tableName"/>
		where user_id = #{userId}
		and is_delete = 0
		limit 1
	</select>

	<update id="updateAwardById">
		update <include refid="tableName"/>
		set `total_num` = `total_num` + 1,
		`unreceived_num` = `unreceived_num` + 1,
		`pennant_id_str` = #{pennantIdStr}
		where id = #{id}
	</update>
</mapper>