<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.activity.ActivityDonatorRedPocketHistoryDAO" >
    <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.domain.activity.ActivityDonatorRedPocketHistory">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="case_id" property="caseId" jdbcType="INTEGER"/>
        <result column="user_id" property="userId" jdbcType="INTEGER"/>
        <result column="pocket_id" property="pocketId" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,case_id,user_id,pocket_id,create_time,update_time
    </sql>

    <sql id="Insert_Column_List">
        id,case_id,user_id,pocket_id
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.domain.activity.ActivityDonatorRedPocketHistory">
        <selectKey order="AFTER" resultType="long" keyProperty="id">
            select last_insert_id() as id
        </selectKey>
        insert ignore into activity_donator_red_pocket_history(<include refid="Insert_Column_List" />)
        values(
        #{id,jdbcType=INTEGER},
        #{caseId,jdbcType=INTEGER},
        #{userId,jdbcType=INTEGER},
        #{pocketId,jdbcType=INTEGER}
        )
    </insert>

    <select id="getByCaseIdUserId"  resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from activity_donator_red_pocket_history where
        case_id = #{caseId, jdbcType=INTEGER}
        and
        user_id = #{userId, jdbcType=INTEGER}
        and is_delete = 0
    </select>

    <select id="getByCaseId"  resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from activity_donator_red_pocket_history where
        case_id = #{caseId, jdbcType=INTEGER}
        and is_delete = 0
    </select>

    <select id="getByCaseIdLastThree"  resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from activity_donator_red_pocket_history where
        case_id = #{caseId, jdbcType=INTEGER}
        order by id desc limit 3
        and is_delete = 0
    </select>

    <select id="listUserIdsByCaseIdAndPocketId" resultType="java.lang.Long">
        select user_id
        from activity_donator_red_pocket_history
        where case_id = #{caseId, jdbcType=INTEGER}
        and pocket_id = #{pocketId}
        and is_delete = 0
    </select>

    <select id="listUserIdsByCaseIdAndPocketIdList" resultType="java.lang.Long">
        select user_id
        from activity_donator_red_pocket_history
        where case_id = #{caseId, jdbcType=INTEGER}
        and pocket_id in
        <foreach collection="pocketIdList" item="pocketId" open="(" separator="," close=")">
            #{pocketId}
        </foreach>
        and is_delete = 0
    </select>

    <update id="cleanUserSubsidyBackdoor">
        update activity_donator_red_pocket_history
        set is_delete = 1
        where case_id = #{caseId}
        and user_id = #{userId}
        and is_delete = 0
        order by id desc
        limit 1
    </update>

</mapper>