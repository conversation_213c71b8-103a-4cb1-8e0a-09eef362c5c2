<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.activity.Cf111WxEventRecordDao" >
    <sql id="TABLE">
      cf_111_wx_event_record
    </sql>

    <sql id="SELECT_FIELD">
        `id`, `from_user_name`, `third_type`, `event_type`, `event_key`, `is_unsubscribed`, `is_delete`, `create_time`, `update_time`
    </sql>

    <insert id="saveCf111WxEventRecord">
      insert into <include refid="TABLE"/> (
        `from_user_name`, `third_type`, `event_type`, `event_key`
        ) values (
          #{fromUserName}, #{thirdType}, #{eventType}, #{eventKey}
        )
    </insert>

    <update id="unsubscribeShuiDiChou">
        update <include refid="TABLE"/>
        set `is_unsubscribed` = 1
        where `from_user_name`=#{fromUserName}
        and `third_type` = 3
    </update>

    <update id="recoverNon111State">
        update <include refid="TABLE"/>
        set `is_delete` = 1
        where `from_user_name`=#{fromUserName}
        and `third_type` = 3
    </update>

    <select id="get111StateEvents" resultType="com.shuidihuzhu.cf.model.wx.Cf111WxEventRecord">
        select <include refid="SELECT_FIELD"/>
        from <include refid="TABLE"/>
        where is_delete = 0
        and event_type in (1, 3)
        and `from_user_name` in
        <foreach collection="fromUserNames" item="fromUserName" open="(" close=")" separator=",">
            #{fromUserName}
        </foreach>
    </select>

    <select id="getSubscribedBy111Souvenir" resultType="com.shuidihuzhu.cf.model.wx.Cf111WxEventRecord">
        select <include refid="SELECT_FIELD"/>
        from <include refid="TABLE"/>
        where is_delete = 0
        and `is_unsubscribed` = 0
        and `from_user_name` = #{fromUserName}
        and event_type in (1, 3)
    </select>
</mapper>