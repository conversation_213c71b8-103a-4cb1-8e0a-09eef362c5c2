<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.activity.PennantInfoDao">
	<sql id="tableName">
		pennant_info
	</sql>

	<sql id="fields">
		`id` as id,
		`content` as content,
		`content_img` as contentImg
	</sql>

	<select id="findAll" resultType="com.shuidihuzhu.cf.model.activity.PennantInfo">
		select <include refid="fields"/>
		from <include refid="tableName"/>
		where is_delete = 0
	</select>
</mapper>