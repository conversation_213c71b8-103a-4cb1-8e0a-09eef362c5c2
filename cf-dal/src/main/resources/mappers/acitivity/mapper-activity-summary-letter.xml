<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.activity.ActivitySummaryLetterDAO">

    <sql id="table_name">
		cf_activity_summary_letter
	</sql>

    <sql id="insert_column_list">
      `case_id`,
      `content`,
      `area` ,
      `image_url`,
      `name` ,
      `head_image_url`,
      `head_case` ,
      `apply_finish_time`,
      `apply_amount`,
      `letter_type`
	</sql>

    <sql id="base_column_list">
        `id`,
        <include refid="insert_column_list"/>
    </sql>

    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into
        <include refid="table_name"/>
        (<include refid="insert_column_list"/>)
        values (
        #{caseId} ,
        #{content} ,
        #{area} ,
        #{imageUrl} ,
        #{name} ,
        #{headImageUrl} ,
        #{headCase} ,
        #{applyFinishTime} ,
        #{applyAmount},
        #{letterType}
        )
    </insert>

    <select id="listByAnchor" resultType="com.shuidihuzhu.cf.domain.activity.SummaryLetterDO">
        select
        <include refid="base_column_list"/>
        from
        <include refid="table_name"/>
        <where>
            `id` > #{anchor}
            and `is_delete` = 0
        </where>
        limit #{size}
    </select>

    <select id="listById" resultType="com.shuidihuzhu.cf.domain.activity.SummaryLetterDO">
        select
        <include refid="base_column_list"/>
        from
        <include refid="table_name"/>
        <where>
            `id` in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            and `is_delete` = 0
        </where>

    </select>

    <select id="listHeadByArea" resultType="com.shuidihuzhu.cf.domain.activity.SummaryLetterDO">
        select
        <include refid="base_column_list"/>
        from
        <include refid="table_name"/>
        <where>
            `head_case` = true
            and area = #{area}
            and `is_delete` = 0
        </where>
        limit #{size}
    </select>

    <select id="listByCaseIds" resultType="com.shuidihuzhu.cf.domain.activity.SummaryLetterDO">
        select
        <include refid="base_column_list"/>
        from
        <include refid="table_name"/>
        <where>
            `case_id` in
            <foreach collection="caseIds" item="caseId" open="(" separator="," close=")">
                #{caseId}
            </foreach>
            and `is_delete` = 0
        </where>
        order by `letter_type` asc , `case_id` desc
        limit #{size}
    </select>

    <select id="getByCaseId" resultType="com.shuidihuzhu.cf.domain.activity.SummaryLetterDO">
        select
        <include refid="base_column_list"/>
        from
        <include refid="table_name"/>
        <where>
            `case_id` = #{caseId}
            and `is_delete` = 0
        </where>
    </select>

    <update id="updateById">
    update <include refid="table_name"/>
    <set>
        content = #{content} ,
        area = #{area} ,
        image_url = #{imageUrl} ,
        `name` = #{name} ,
        head_image_url = #{headImageUrl} ,
        head_case = #{headCase} ,
        apply_finish_time = #{applyFinishTime} ,
        apply_amount = #{applyAmount},
        letter_type = #{letterType}
    </set>
        where `id` = #{id}
    </update>

</mapper>
