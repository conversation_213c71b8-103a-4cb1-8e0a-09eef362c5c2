<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.activity.ActivityUserProfileDAO">

    <sql id="table_name">
		cf_activity_user_profile
	</sql>

    <sql id="insert_column_list">
        `user_id`,
        `profile_type`,
        `profile_value`
	</sql>

    <sql id="base_column_list">
        `id`,
        `create_time`,
        `update_time`,
        <include refid="insert_column_list"/>
    </sql>

    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into <include refid="table_name"/>
        (<include refid="insert_column_list"/>)
        values (
        #{userId} ,
        #{profileType} ,
        #{profileValue}
        )
    </insert>

    <select id="getByUserIdAndType" resultType="com.shuidihuzhu.cf.domain.activity.ActivityUserProfileDO">
        select <include refid="base_column_list"/>
        from <include refid="table_name"/>
        <where>
            `user_id` = #{userId}
            and `profile_type` = #{profileType}
            and `is_delete` = 0
        </where>
        order by id desc
        limit 1
    </select>

</mapper>
