<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.activity.ActivityVenueCaseDao">
    <sql id="tableName">
		activity_venue_case
	</sql>

    <sql id="fields">
		`id` as id,
		`valid`,
		`total_donate` as totalDonate,
		`venue_donate` as venueDonate,
		`venue_donate_out` as venueDonateOut,
		`cooperate_donate` as cooperateDonate,
		`activity_id` as activityId,
		`case_id` as caseId,
		`info_id` as infoId,
		`title` as title,
		`content` as content,
		`title_img` as titleImg,
		`case_desc` as `desc`,
		`case_order` as `order`,
		`case_type` as `type`,
		`pool_type` as poolType,
		`valid` as valid
	</sql>

    <sql id="insert_fields">
		`activity_id`,
		`case_id`,
		`info_id`,
		`title`,
		`content`,
		`title_img`,
		`case_desc`,
		`case_order`,
		`case_type`,
		`pool_type`
	</sql>

	<select id="findByPoolType" resultType="com.shuidihuzhu.cf.model.activity.ActivityVenueCaseModel">
		select <include refid="fields"/>
		from <include refid="tableName"/>
		where activity_id = #{activityId}
		and pool_type = #{poolType}
		and is_delete = 0 and valid = 1
	</select>

	<select id="findByPoolTypes" resultType="com.shuidihuzhu.cf.model.activity.ActivityVenueCaseModel">
		select <include refid="fields"/>
		from <include refid="tableName"/>
		where activity_id = #{activityId}
		and pool_type in
		<foreach collection="poolTypes" item="poolType" open="(" close=")" separator=",">
			#{poolType}
		</foreach>
		and is_delete = 0
	</select>

	<select id="listByCaseId" resultType="com.shuidihuzhu.cf.model.activity.ActivityVenueCaseModel">
		select <include refid="fields"/>
		from <include refid="tableName"/>
		where case_id = #{caseId}
		and is_delete = 0 and valid = 1
	</select>

	<update id="updateVenueDonateByCaseId">
		update <include refid="tableName"/>
		set `total_donate` = `total_donate` + #{venueDonate},
		`venue_donate` = `venue_donate` + #{venueDonate}
		where case_id = #{caseId}
		and activity_id = #{activityId}
	</update>

	<update id="updateVenueDonateOutByCaseId">
		update <include refid="tableName"/>
		set `total_donate` = `total_donate` + #{venueDonateOut},
		`venue_donate_out` = `venue_donate_out` + #{venueDonateOut}
		where case_id = #{caseId}
		and activity_id = #{activityId}
	</update>

	<update id="updateCooperateDonateByCaseId">
		update <include refid="tableName"/>
		set `total_donate` = `total_donate` + #{cooperateDonate},
		`cooperate_donate` = `cooperate_donate` + #{cooperateDonate}
		where case_id = #{caseId}
		and activity_id = #{activityId}
	</update>

	<select id="getByCaseId" resultType="com.shuidihuzhu.cf.model.activity.ActivityVenueCaseModel">
		select <include refid="fields"/>
		from <include refid="tableName"/>
		where case_id = #{caseId}
		and activity_id = #{activityId}
		and is_delete = 0
		and valid = 1
		order by id desc
		limit 1;
	</select>

	<update id="updateById">
        update <include refid="tableName"/>
		<set>
			`title` = #{title},
			`content` = #{content},
			`title_img` = #{titleImg},
			`case_desc` = #{desc},
			`case_order` = #{order},
			`case_type` = #{type},
			`pool_type` = #{poolType},
			`valid` = #{valid},
			<if test="comment != null">
				`comment` = #{comment}
			</if>
		</set>
        where id = #{id}
    </update>

    <update id="deleteById">
        update
        <include refid="tableName"/>
        <set>
            is_delete = 1
        </set>
        where id = #{id}
    </update>

    <insert id="insert">
        insert into
        <include refid="tableName"/>
        (<include refid="insert_fields"/>)
        values
        (
        #{activityId},
        #{caseId},
        #{infoId},
        #{title},
        #{content},
        #{titleImg},
        #{desc},
        #{order},
        #{type},
        #{poolType}
        )
    </insert>

    <select id="getMinVenueDonate" resultType="com.shuidihuzhu.cf.model.activity.ActivityVenueCaseModel">
        select
        <include refid="fields"/>
        from
        <include refid="tableName"/>
        where activity_id = #{activityId}
        and `valid` = 1
        and is_delete = 0
        order by venue_donate
        limit 1
    </select>

    <select id="listByCaseTypes" resultType="com.shuidihuzhu.cf.model.activity.ActivityVenueCaseModel">
        select
        <include refid="fields"/>
        from
        <include refid="tableName"/>
        where activity_id = #{activityId}
        <if test="caseTypes.size() > 0">
            and case_type in
            <foreach collection="caseTypes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        and is_delete = 0 and valid = 1
        order by case_type, case_order
        limit #{current},#{pageSize}
    </select>

    <select id="findByCaseId" resultType="com.shuidihuzhu.cf.model.activity.ActivityVenueCaseModel">
        select
        <include refid="fields"/>
        from
        <include refid="tableName"/>
        where case_id = #{caseId}
        and is_delete = 0
    </select>

    <select id="getByInfoId" resultType="com.shuidihuzhu.cf.model.activity.ActivityVenueCaseModel">
        select
        <include refid="fields"/>
        from
        <include refid="tableName"/>
        where info_id = #{infoId}
        and is_delete = 0
        limit 1
    </select>
</mapper>