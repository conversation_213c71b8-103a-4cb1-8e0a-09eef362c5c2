<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.activity.ActivitySubsidyUserCaseShareCountDAO">

    <sql id="table_name">
		cf_activity_subsidy_user_case_share_count
	</sql>

    <sql id="insert_column_list">
        `user_id`,
        `case_id`,
        `share_count`
	</sql>

    <sql id="base_column_list">
        `id`,
        `create_time`,
        `update_time`,
        <include refid="insert_column_list"/>
    </sql>

    <update id="save">
        insert into <include refid="table_name"/>
        (<include refid="insert_column_list"/>)
        values (
        #{userId} ,
        #{caseId} ,
        #{shareCount}
        )
        on duplicate key update
        `share_count`=values(share_count)
    </update>

    <select id="getCount" resultType="int">
        select `share_count`
        from <include refid="table_name"/>
        where `user_id` = #{userId}
        and `case_id` = #{caseId}
    </select>

</mapper>
