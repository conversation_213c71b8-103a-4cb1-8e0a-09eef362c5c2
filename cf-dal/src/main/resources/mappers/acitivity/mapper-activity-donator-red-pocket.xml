<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.activity.ActivityDonatorRedPocketDAO" >
    <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.domain.activity.ActivityDonatorRedPocketDO">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="case_id" property="caseId" jdbcType="INTEGER"/>
        <result column="total_value" property="totalValue" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="need_user_count" property="needUserCount" jdbcType="INTEGER"/>
        <result column="remain_user_count" property="remainUserCount" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,case_id,`total_value`,create_time,update_time,status,need_user_count,remain_user_count
    </sql>

    <sql id="Insert_Column_List">
        id,case_id,`total_value`,status,need_user_count,remain_user_count
    </sql>

    <select id="getAllByCaseId"  resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from activity_donator_red_pocket_node where
        case_id = #{caseId, jdbcType=INTEGER}
    </select>

    <select id="getDoingByCaseId"  resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from activity_donator_red_pocket_node where
        case_id = #{caseId, jdbcType=INTEGER}
        and status = 1 order by id desc limit 1
    </select>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.domain.activity.ActivityDonatorRedPocketDO">
        <selectKey order="AFTER" resultType="long" keyProperty="id">
            select last_insert_id() as id
        </selectKey>
        insert ignore into activity_donator_red_pocket_node(<include refid="Insert_Column_List" />)
        values(
        #{id,jdbcType=INTEGER},
        #{caseId,jdbcType=INTEGER},
        #{totalValue,jdbcType=INTEGER},
        #{status,jdbcType=INTEGER},
        #{needUserCount,jdbcType=INTEGER},
        #{remainUserCount,jdbcType=INTEGER}
        )
    </insert>

    <update id="updateStatus">
        update activity_donator_red_pocket_node
        set
        status = #{updateStatus}
        where case_id = #{caseId} and id = #{pocketId} and status = #{oldStatus}

    </update>

    <update id="reduceCount">
        update activity_donator_red_pocket_node
        set  remain_user_count = remain_user_count - #{reduceCount}
        where case_id = #{caseId} and id = #{pocketId}
    </update>

    <select id="listDonePocketIdByCaseId" resultType="java.lang.Long">
        select id
        from activity_donator_red_pocket_node where
        case_id = #{caseId, jdbcType=INTEGER}
        and status = 0
    </select>

</mapper>