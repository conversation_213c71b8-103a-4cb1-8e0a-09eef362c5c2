<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.activity.ActivityWaveDAO">

    <sql id="table_name">
        cf_activity_wave
	</sql>

    <sql id="insert_column_list">
        `activity_id`,
        `wave_type`,
        `start_time`,
        `end_time`,
        `threshold`,
        `donate_money_threshold`,
        `donate_count_threshold`,
        `case_hard_amount_limit`,
        `city_id`,
        `max_place`,
        `current_place`,
        `task_group_id`,
        `join_type`,
        `scope`,
        `monthly_place`,
        `hospital_code`,
        `hospital_name`,
        `case_soft_amount_limit`
    </sql>

    <sql id="base_column_list">
        `id`,
        `create_time`,
        `update_time`,
        <include refid="insert_column_list"/>
    </sql>

    <insert id="insert">
        insert into <include refid="table_name"/>
        (<include refid="insert_column_list"/>)
        values (
        #{activityId},
        #{waveType},
        #{startTime},
        #{endTime},
        #{threshold},
        #{donateMoneyThreshold},
        #{donateCountThreshold},
        #{caseHardAmountLimit},
        #{cityId},
        #{maxPlace},
        #{currentPlace},
        #{taskGroupId},
        #{joinType},
        #{scope},
        #{monthlyPlace},
        #{hospitalCode},
        #{hospitalName},
        #{caseSoftAmountLimit}
        )
    </insert>

    <select id="getWaveByTypeAndNowTime" resultType="com.shuidihuzhu.cf.domain.activity.ActivityWaveDO">
        select <include refid="base_column_list"/>
        from <include refid="table_name"/>
        where `wave_type` = #{waveType}
        and current_timestamp <![CDATA[ >= ]]> start_time
        and current_timestamp <![CDATA[ < ]]>  end_time
        and is_delete = 0
        order by id desc
        limit 1
    </select>

    <select id="getWaveByTypeAndNowTimeAndCity" resultType="com.shuidihuzhu.cf.domain.activity.ActivityWaveDO">
        select <include refid="base_column_list"/>
        from <include refid="table_name"/>
        where `wave_type` = #{waveType}
        and current_timestamp <![CDATA[ >= ]]> start_time
        and current_timestamp <![CDATA[ < ]]>  end_time
        and city_id = #{cityId}
        and is_delete = 0
        order by id desc
        limit 1
    </select>

    <select id="getWaveByCondition" resultType="com.shuidihuzhu.cf.domain.activity.ActivityWaveDO">
        select <include refid="base_column_list"/>
        from <include refid="table_name"/>
        where `wave_type` = #{waveType}
        and current_timestamp <![CDATA[ >= ]]> start_time
        and current_timestamp <![CDATA[ < ]]>  end_time
        and join_type = #{joinType}
        and scope = #{scope}
        and city_id = #{cityId}
        and is_delete = 0
        <if test="activityId != null">
            and activity_id = #{activityId}
        </if>
        order by id desc
        limit 1
    </select>

    <select id="getWaveByHospital" resultType="com.shuidihuzhu.cf.domain.activity.ActivityWaveDO">
        select <include refid="base_column_list"/>
        from <include refid="table_name"/>
        where `wave_type` = #{waveType}
        and current_timestamp <![CDATA[ >= ]]> start_time
        and current_timestamp <![CDATA[ < ]]>  end_time
        and join_type = #{joinType}
        and scope = #{scope}
        and hospital_code = #{hospitalCode}
        and is_delete = 0
        <if test="activityId != null">
            and activity_id = #{activityId}
        </if>
        order by id desc
        limit 1
    </select>

    <select id="getWaveById" resultType="com.shuidihuzhu.cf.domain.activity.ActivityWaveDO">
        select <include refid="base_column_list"/>
        from <include refid="table_name"/>
        where `id` = #{id}
        limit 1
    </select>

    <select id="getListByCondition" resultType="com.shuidihuzhu.cf.domain.activity.ActivityWaveDO">
        select <include refid="base_column_list"/>
        from <include refid="table_name"/>
        <where>
            <if test="status != null">
                <if test="status == true">
                    and current_timestamp <![CDATA[ >= ]]> start_time
                    and current_timestamp <![CDATA[ < ]]>  end_time
                </if>
                <if test="status == false">
                    and current_timestamp <![CDATA[ < ]]> start_time
                    or current_timestamp <![CDATA[ >= ]]>  end_time
                </if>
            </if>
            and is_delete = 0
        </where>
        order by `end_time` desc
        limit #{current}, #{pageSize}
    </select>

    <select id="count" resultType="int">
        select count(*)
        from <include refid="table_name"/>
        <where>
            <if test="status != null">
                <if test="status == true">
                    and current_timestamp <![CDATA[ >= ]]> start_time
                    and current_timestamp <![CDATA[ < ]]>  end_time
                </if>
                <if test="status == false">
                    and current_timestamp <![CDATA[ < ]]> start_time
                    or current_timestamp <![CDATA[ >= ]]>  end_time
                </if>
            </if>
            and is_delete = 0
        </where>
    </select>

    <update id="updatePlace">
        update <include refid="table_name"/>
        set current_place = current_place + 1
        where current_place = #{currentPlace}
        and id = #{id}
    </update>


</mapper>
