<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.activity.PennantUserRelationDao">
    <sql id="tableName">
		pennant_user_relation
	</sql>

    <sql id="fields">
		`id` as id,
		`user_id` as userId,
		`pennant_id` as pennantId,
		`award_type` as awardType,
		`info_id` as infoId,
		`pay_uid` as payUid,
		`receive_status` as receiveStatus,
		`create_time` as createTime
	</sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.activity.PennantUserRelation">
        insert into
        <include refid="tableName"/>
        (`user_id`,`pennant_id`,`award_type`,`info_id`,`pay_uid`,`receive_status`)
        values (#{userId},#{pennantId},#{awardType},#{infoId},#{payUid},#{receiveStatus})
    </insert>

    <select id="findByInfoIdAndAwardType" resultType="com.shuidihuzhu.cf.model.activity.PennantUserRelation">
        select
        <include refid="fields"/>
        from
        <include refid="tableName"/>
        where user_id = #{userId}
        and info_id = #{infoId}
        and award_type = #{awardType}
        and is_delete = 0
    </select>

    <select id="findByUserId" resultType="com.shuidihuzhu.cf.model.activity.PennantUserRelation">
        select <include refid="fields"/>
        from <include refid="tableName"/>
        where user_id = #{userId}
        and is_delete = 0
        order by id desc
    </select>

    <select id="getByUserIdAndPayUid" resultType="com.shuidihuzhu.cf.model.activity.PennantUserRelation">
        select <include refid="fields"/>
        from <include refid="tableName"/>
        where user_id = #{userId}
        and pay_uid = #{payUid}
        and is_delete = 0
        limit 1
    </select>
</mapper>