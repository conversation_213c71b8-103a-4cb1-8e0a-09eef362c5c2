<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.activity.ActivityKeyAreaSubsidyCaseDAO">

    <sql id="table_name">
		activity_key_area_subsidy_case
	</sql>

    <sql id="insert_column_list">
        `activity_id`,
        `case_id`,
        `city_id`,
        `subsidy_type`,
        `reached_threshold`,
        `case_soft_amount_limit`,
        `wave_id`,
        `rule_type`
	</sql>

    <sql id="base_column_list">
        `id`,
        `create_time`,
        `update_time`,
        `subsidy_count`,
        <include refid="insert_column_list"/>
    </sql>


    <insert id="insert">
        insert into <include refid="table_name"/>
        (<include refid="insert_column_list"/>)
        values (
        #{activityId},
        #{caseId} ,
        #{cityId} ,
        #{subsidyType},
        #{reachedThreshold},
        #{caseSoftAmountLimit},
        #{waveId},
        #{ruleType}
        )
    </insert>

    <select id="getSameWaveCaseListOfRandomExceptSelf" resultType="java.lang.Integer">
        select distinct case_id
        from <include refid="table_name"/>
        where wave_id = #{waveId}
        and case_id != #{exceptCaseId}
        and `reached_threshold` = 1
        order by rand()
        limit #{size}
    </select>

    <select id="getByCaseId" resultType="com.shuidihuzhu.cf.domain.activity.ActivityKeyAreaDO">
        select <include refid="base_column_list"/>
        from <include refid="table_name"/>
        where case_id = #{caseId}
        order by id desc
        limit 1;
    </select>

    <update id="addSubsidyCount">
        update <include refid="table_name"/>
        set subsidy_count = subsidy_count + 1
        where case_id = #{caseId}
        and activity_id = #{activityId}
        order by id desc
        limit 1;
    </update>

    <update id="setReachedThreshold">
        update <include refid="table_name"/>
        set reached_threshold = 1
        where case_id = #{caseId}
        and subsidy_type = #{subsidyType}
    </update>

    <select id="getCurrentCountInThisMonth" resultType="int">
        select count(distinct case_id) from <include refid="table_name"/>
        where wave_id = #{waveId}
        and create_time > date_add(curdate(), interval -day(curdate()) + 1 day)
        and is_delete = 0
        ;
    </select>

</mapper>
