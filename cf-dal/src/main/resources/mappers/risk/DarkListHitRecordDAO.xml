<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.risk.DarkListHitRecordDAO">
    <sql id="table_name">
        cf_dark_list_hit_record
    </sql>

    <sql id="insert_column_list">
        `user_id`,
        `action_type`,
        `hit_result_json`
    </sql>

    <sql id="base_column_list">
        `id`,
        `create_time`,
        `update_time`,
        <include refid="insert_column_list"/>
    </sql>

    <insert id="insert">
        insert into
        <include refid="table_name"/>
        (<include refid="insert_column_list"/>)
        values (
        #{userId},
        #{actionType},
        #{hitResultJson}
        )
    </insert>
</mapper>