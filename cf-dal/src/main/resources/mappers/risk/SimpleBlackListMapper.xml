<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.risk.SimpleBlacklistDAO">
    <sql id="table_name">
        simple_blacklist
    </sql>

    <sql id="insert_column_list">
        `case_id`,
        `action_type`
    </sql>

    <sql id="base_column_list">
        `id`,
        `create_time`,
        `update_time`,
        <include refid="insert_column_list"/>
    </sql>

    <insert id="insert">
        insert into
        <include refid="table_name"/>
        (<include refid="insert_column_list"/>)
        values (
        #{caseId},
        #{actionType}
        )
    </insert>

    <select id="getByCaseId" resultType="com.shuidihuzhu.cf.model.risk.SimpleBlacklistDO">
        select <include refid="base_column_list"/>
        from <include refid="table_name"/>
        where case_id = #{caseId}
        and action_type = #{actionType}
        and is_delete = 0
    </select>

    <update id="removeByCaseId">
        update <include refid="table_name"/>
        set is_delete = 1
        where action_type = #{actionType}
        and case_id = #{caseId}
    </update>

    <update id="removeById">
        update <include refid="table_name"/>
        set is_delete = 1
        where id = #{id}
    </update>
</mapper>