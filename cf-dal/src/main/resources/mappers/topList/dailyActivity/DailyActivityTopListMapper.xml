<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.topList.dailyActivity.DailyActivityTopListDao">


    <sql id="table_name">
        daily_activity_top_list_summary
    </sql>

    <select id="selectByType" resultType="com.shuidihuzhu.cf.model.topList.DailyActivityTopList$TopListSummary">
        SELECT *
        FROM <include refid="table_name"/>
        WHERE activity_period = #{activityPeriod} AND is_delete = 0
    </select>

    <insert id="insertOrUpdate">
        INSERT INTO
        <include refid="table_name"/>
        ( `activity_period`, `donate_count_top_info`, `share_bring_visit_top_info`, `last_update_rank_time`)
        VALUES
        ( #{activityPeriod}, #{donateCountTopInfo}, #{shareBringVisitTopInfo}, #{lastUpdateRankTime})
        ON DUPLICATE KEY UPDATE
        `donate_count_top_info`= #{donateCountTopInfo},
        `share_bring_visit_top_info`= #{shareBringVisitTopInfo},
        `last_update_rank_time` = #{lastUpdateRankTime}
    </insert>






</mapper>