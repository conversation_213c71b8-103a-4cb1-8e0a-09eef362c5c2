<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.topList.dailyActivity.DailyActivityVisitRecordDao">

    <sql id="bring_visit_record_table_name">
        daily_activity_bring_visit_record
    </sql>

    <insert id = "insertVisitRecord">
        INSERT INTO
        <include refid="bring_visit_record_table_name"/>
        (user_id, visit_user_id, activity_period)
        VALUES(#{userId}, #{visitUserId}, #{activityPeriod})
    </insert>

    <select id = "selectVisitRecord" resultType="com.shuidihuzhu.cf.model.topList.DailyActivityTopList$BringVisitRecord">
        SELECT * FROM
        <include refid="bring_visit_record_table_name"/>
        WHERE user_id = #{userId}
        AND visit_user_id = #{visitUserId}
        AND activity_period = #{activityPeriod}
    </select>


</mapper>