<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.topList.dailyActivity.DailyActivityUserInfoDao">

    <sql id="user_info_summary_table_name">
        daily_activity_user_info_summary
    </sql>

    <select id = "selectTopDonateCountList" resultType="com.shuidihuzhu.cf.model.topList.DailyActivityTopList$UserInfoSummary">
        SELECT * FROM
        daily_activity_user_info_summary_${index}
        where activity_period = #{activityPeriod}
        ORDER BY
        donate_count DESC, last_donate_time DESC
        LIMIT #{size}
    </select>

    <select id = "selectTopBringVisitCountList" resultType="com.shuidihuzhu.cf.model.topList.DailyActivityTopList$UserInfoSummary">
        SELECT * FROM
        daily_activity_user_info_summary_${index}
        where activity_period = #{activityPeriod}
        ORDER BY
        share_bring_visit DESC, last_share_bring_visit_time DESC
        LIMIT #{size}
    </select>

    <select id = "selectUserSummaryFromSlave" resultType="com.shuidihuzhu.cf.model.topList.DailyActivityTopList$UserInfoSummary">
        SELECT * FROM
        <include refid="user_info_summary_table_name"/>
        WHERE user_id = #{userId}
        and activity_period = #{activityPeriod}
    </select>


    <select id = "selectUserSummary" resultType="com.shuidihuzhu.cf.model.topList.DailyActivityTopList$UserInfoSummary">
        SELECT * FROM
        <include refid="user_info_summary_table_name"/>
        WHERE user_id = #{userId}
        and activity_period = #{activityPeriod}
    </select>

    <insert id = "insertOrUpdateDonateCount">
        INSERT INTO
        <include refid="user_info_summary_table_name"/>
        (`user_id`, `donate_count`, `last_donate_time`, `share_bring_visit`, `last_share_bring_visit_time`, `activity_period`)
        VALUES
        (#{userId}, #{donateCount}, #{lastDonateTime}, #{shareBringVisit}, #{lastShareBringVisitTime}, #{activityPeriod})
        ON DUPLICATE KEY UPDATE
        `donate_count`= #{donateCount},
        `last_donate_time`=#{lastDonateTime}
    </insert>

    <insert id = "insertOrUpdateBringVisit">
        INSERT INTO
        <include refid="user_info_summary_table_name"/>
        (`user_id`, `donate_count`, `last_donate_time`, `share_bring_visit`, `last_share_bring_visit_time`, `activity_period`)
        VALUES
        (#{userId}, #{donateCount}, #{lastDonateTime}, #{shareBringVisit}, #{lastShareBringVisitTime}, #{activityPeriod})
        ON DUPLICATE KEY UPDATE
        `share_bring_visit`= #{shareBringVisit},
        last_share_bring_visit_time = #{lastShareBringVisitTime}
    </insert>

    <insert id = "insertOrUpdateVisitType">
        INSERT INTO
        <include refid="user_info_summary_table_name"/>
        (`user_id`, `visit_venue_type`, `source_bring_visit`, `activity_period`)
        VALUES
        (#{userId}, #{visitVenueType}, #{sourceBringVisit}, #{activityPeriod})
        ON DUPLICATE KEY UPDATE
        `visit_venue_type` = #{visitVenueType},
        `source_bring_visit` = #{sourceBringVisit}
    </insert>

</mapper>