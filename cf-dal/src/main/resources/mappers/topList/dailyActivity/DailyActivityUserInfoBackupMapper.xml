<?xml version="1.0" encoding="UTF-8" ?>
        <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.topList.dailyActivity.DailyActivityUserInfoBackupDao">


    <select id = "selectAllByLimit" resultType="com.shuidihuzhu.cf.model.topList.DailyActivityTopList$UserInfoSummary">
        SELECT *
        FROM
        daily_activity_user_info_summary_${index}
        WHERE id > #{id}
        order by id
        limit #{limit}
    </select>

    <insert id = "insertBackUpHistory">
        INSERT INTO
        daily_activity_user_info_summary_backup
        (`user_id`, `donate_count`, `last_donate_time`, `share_bring_visit`, `last_share_bring_visit_time`, `activity_period` )
        VALUES
        <foreach collection="list" item = "item" separator=",">
            ( #{item.userId}, #{item.donateCount}, #{item.lastDonateTime},
            #{item.shareBringVisit}, #{item.lastShareBringVisitTime}, #{item.activityPeriod} )
        </foreach>
    </insert>



</mapper>