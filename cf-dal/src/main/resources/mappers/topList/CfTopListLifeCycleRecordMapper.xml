<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.shuidihuzhu.cf.dao.topList.CfTopListLifeCycleRecordDAO">

    <sql id="table_name">
        cf_top_list_life_cycle_record_${tableIndex}
    </sql>

    <insert id="insertLifeCycleRecordList" parameterType="com.shuidihuzhu.cf.model.topList.CfTopListLifeCycleRecord">
        INSERT INTO
        <include refid="table_name"/>
        (`case_id`, `join_prize`, `prize_begin_time`, `prize_end_time`,

        `today_time`,

        `top_list_type_id`, `rank_change_time`, `top_list_change_time`,

        `user_id`, `rank`, `share_bring_visit`, `total_contribute_donate`, `own_donate`, `share_bring_donate`,

        `if_today_last_top_list`,

        `if_thanks`, `thanks_time`, `if_invite`, `invite_time`, `if_prize`, `if_has_prize`, `user_prize_time`, `prize_channel`)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.caseId}, #{item.joinPrize}, #{item.prizeBeginTime}, #{item.prizeEndTime},

            #{item.todayTime},

            #{item.topListTypeId}, #{item.rankChangeTime}, #{item.topListChangeTime},

            #{item.userId}, #{item.rank}, #{item.shareBringVisit}, #{item.totalContributeDonate}, #{item.ownDonate},
            #{item.shareBringDonate},

            #{item.ifTodayLastTopList},

            #{item.ifThanks}, #{item.thanksTime}, #{item.ifInvite}, #{item.inviteTime},

            #{item.ifPrize}, #{item.ifHasPrize}, #{item.userPrizeTime}, #{item.prizeChannel})
        </foreach>
    </insert>

    <update id="updateThanksTime">
        UPDATE
        <include refid="table_name"/>
        SET
        if_thanks = 1,
        thanks_time = #{thanksTime}
        WHERE id in
        <foreach collection="ids" item="id" open="(" separator="," close=")" >
            #{id}
        </foreach>

    </update>

    <update id="updateInviteTime">
        UPDATE
        <include refid="table_name"/>
        SET
        if_invite = 1,
        invite_time = #{inviteTime}
        WHERE id in
        <foreach collection="ids" item="id" open="(" separator="," close=")" >
            #{id}
        </foreach>
    </update>

    <update id="updatePrizeTime">
        UPDATE
        <include refid="table_name"/>
        SET
        if_has_prize = 1,
        user_prize_time = #{userPrizeTime},
        prize_channel = #{prizeChannel}
        WHERE id in
        <foreach collection="ids" item="id" open="(" separator="," close=")" >
            #{id}
        </foreach>
    </update>

    <update id="updateIfPrize">
        UPDATE
        <include refid="table_name"/>
        SET
        if_prize = 1
        WHERE id in
        <foreach collection="ids" item="id" open="(" separator="," close=")" >
            #{id}
        </foreach>
    </update>

    <select id = "selectLastByCaseIdAndUserId" resultType="com.shuidihuzhu.cf.model.topList.CfTopListLifeCycleRecord">
        SELECT *
        FROM
        <include refid="table_name"/>
        WHERE case_id = #{caseId}
        AND user_id = #{userId}
        AND top_list_type_id = #{topListId}
        ORDER BY id desc LIMIT 1
    </select>

    <select id = "selectRankHistoryByCaseId" resultType="com.shuidihuzhu.cf.model.topList.CfTopListLifeCycleRecord">
        SELECT *
        FROM
        <include refid="table_name"/>
        WHERE case_id = #{caseId}
        AND top_list_type_id = #{topListId}
    </select>

    <select id = "selectRankHistoryByCaseIdAndUserId" resultType="com.shuidihuzhu.cf.model.topList.CfTopListLifeCycleRecord">
        SELECT *
        FROM
        <include refid="table_name"/>
        WHERE case_id = #{caseId}
        AND user_id = #{userId}
        AND top_list_type_id = #{topListId}
    </select>


</mapper>