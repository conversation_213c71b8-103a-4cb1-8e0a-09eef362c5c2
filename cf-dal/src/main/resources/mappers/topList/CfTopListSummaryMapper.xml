<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.shuidihuzhu.cf.dao.topList.CfTopListSummaryDAO">

    <sql id="table_name">
        cf_top_list_summary
    </sql>

    <select id="selectSummaryByCaseId" resultType="com.shuidihuzhu.cf.model.topList.CfTopListSummary">
        SELECT * FROM
        <include refid="table_name"/>
        WHERE case_id = #{caseId}
    </select>

    <select id="selectSummaryByCaseIds" resultType="com.shuidihuzhu.cf.model.topList.CfTopListSummary">
        SELECT * FROM
        <include refid="table_name"/>
        WHERE case_id IN
       <foreach collection="caseIds" item = "caseId" open="(" separator="," close=")">
           #{caseId}
       </foreach>
    </select>

    <select id="selectSummaryByInfoUuid" resultType="com.shuidihuzhu.cf.model.topList.CfTopListSummary">
        SELECT * FROM
        <include refid="table_name"/>
        WHERE info_uuid = #{infoUuid}
    </select>

    <insert id="insertSummary" parameterType="com.shuidihuzhu.cf.model.topList.CfTopListSummary">
        INSERT INTO
        <include refid="table_name"/>
        (`case_id`, `info_uuid`, `all_share_top_info`, `friend_share_top_info`,

        `all_donate_top_info`, `friend_donate_top_info`,

        `activity_begin_time`, `activity_end_time`, `can_prize_users`, `last_update_rank_time`)
        VALUES
        (#{caseId}, #{infoUuid}, #{allShareTopInfo}, #{friendShareTopInfo},

        #{allDonateTopInfo}, #{friendDonateTopInfo},

          #{activityBeginTime}, #{activityEndTime}, #{canPrizeUsers}, #{lastUpdateRankTime})

        ON DUPLICATE KEY UPDATE
        `all_share_top_info` = #{allShareTopInfo},
        `friend_share_top_info` = #{friendShareTopInfo},
        `all_donate_top_info` = #{allDonateTopInfo},
        `friend_donate_top_info` = #{friendDonateTopInfo},
        `activity_begin_time` = #{activityBeginTime},
        `activity_end_time` = #{activityEndTime},
        `can_prize_users` =  #{canPrizeUsers},
        `last_update_rank_time` = #{lastUpdateRankTime}
    </insert>

    <update id="updateAllAndFriendTopList" parameterType="com.shuidihuzhu.cf.model.topList.CfTopListSummary">
        UPDATE
        <include refid="table_name"/>
        SET

        `all_share_top_info` = #{allShareTopInfo},
        `friend_share_top_info` = #{friendShareTopInfo},

        `all_donate_top_info` = #{allDonateTopInfo},
        `friend_donate_top_info` = #{friendDonateTopInfo},

        `last_update_rank_time` = #{lastUpdateRankTime}
        WHERE case_id = #{caseId}
    </update>

    <update id="updatePrizeUserIds" parameterType="com.shuidihuzhu.cf.model.topList.CfTopListSummary">
        UPDATE
        <include refid="table_name"/>
        SET
        `can_prize_users` = #{canPrizeUsers}
        WHERE case_id = #{caseId}
    </update>

    <select id = "selectFirstInitialPassTime" resultType="java.util.Date">
        SELECT create_time
        FROM cf_material_verity_history
        WHERE
        `case_id` = #{caseId}
        AND
        `material_id` = #{materialId}
        AND
        `handle_type` = #{handleType}
        ORDER BY id LIMIT 1
    </select>

</mapper>