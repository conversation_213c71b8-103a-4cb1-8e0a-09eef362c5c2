<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.shuidihuzhu.cf.dao.topList.CfTopListOperateRecordDAO">


    <sql id="table_name">
      cf_top_list_operate_record_${tableIndex}
    </sql>

    <insert id="insertRecordList" parameterType="com.shuidihuzhu.cf.model.topList.CfTopListOperateRecord">
        INSERT INTO
        <include refid="table_name"/>
        (`case_id`, `user_id`, `operate_type`, `operate_detail`, `operate_time`)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.caseId}, #{item.userId}, #{item.operateType}, #{item.operateDetail},  #{item.operateTime})
        </foreach>
    </insert>

    <select id="selectByTimeAndType" resultType="com.shuidihuzhu.cf.model.topList.CfTopListOperateRecord">
        SELECT *
        FROM
        <include refid="table_name"/>
        WHERE case_id = #{caseId}
        <if test="userIds != null and userIds.size() > 0">
            AND user_id IN
            <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        <if test="operateTime != null and operateTime != ''">
            AND operate_time = #{operateTime}
        </if>
        <if test="operateType != null and operateType != 0">
            AND operate_type = #{operateType}
        </if>
    </select>




</mapper>