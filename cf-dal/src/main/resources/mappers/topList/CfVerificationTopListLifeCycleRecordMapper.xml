<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.topList.CfVerificationTopListLifeCycleRecordDao">

    <sql id="table_name">
        cf_verification_top_list_life_cycle_record
    </sql>

    <insert id="insertLifeCycleRecordList" parameterType="com.shuidihuzhu.cf.model.topList.CfTopListLifeCycleRecord">
    INSERT INTO
    <include refid="table_name"/>
        (`case_id`, `prize_begin_time`, `prize_end_time`,

        `today_time`,

         `rank_change_time`, `top_list_change_time`,

        `user_id`, `rank`,  `total_contribute_donate`, `own_donate`, `share_bring_donate`,

        `if_today_last_top_list`,

         `if_prize`, `if_has_prize`, `user_prize_time`, `prize_channel` )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.caseId}, #{item.prizeBeginTime}, #{item.prizeEndTime},

            #{item.todayTime},

            #{item.rankChangeTime}, #{item.topListChangeTime},

            #{item.userId}, #{item.rank}, #{item.totalContributeDonate}, #{item.ownDonate},#{item.shareBringDonate},

            #{item.ifTodayLastTopList},

            #{item.ifPrize}, #{item.ifHasPrize}, #{item.userPrizeTime}, #{item.prizeChannel})
        </foreach>

    </insert>

    <select id = "selectLastByCaseIdAndUserId" resultType="com.shuidihuzhu.cf.model.topList.CfTopListLifeCycleRecord">
        SELECT *
        FROM
        <include refid="table_name"/>
        WHERE case_id = #{caseId}
        AND user_id = #{userId}
        ORDER BY id desc LIMIT 1
    </select>

    <update id="updateIfPrize">
        UPDATE
        <include refid="table_name"/>
        SET
        if_prize = 1
        WHERE id in
        <foreach collection="ids" item="id" open="(" separator="," close=")" >
            #{id}
        </foreach>
    </update>

    <update id="updatePrizeTime">
        UPDATE
        <include refid="table_name"/>
        SET
        if_has_prize = 1,
        user_prize_time = #{userPrizeTime},
        prize_channel = #{prizeChannel}
        WHERE id in
        <foreach collection="ids" item="id" open="(" separator="," close=")" >
            #{id}
        </foreach>
    </update>
</mapper>
