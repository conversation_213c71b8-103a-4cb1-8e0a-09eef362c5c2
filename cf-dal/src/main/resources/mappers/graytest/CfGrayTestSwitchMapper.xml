<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.graytest.CfGrayTestSwitchDao">
	<sql id="tableName">
		cf_gray_test_switch
	</sql>

	<sql id="selectFields">
		`id` as id,
		`biz_type` as bizType,
		`code` as code,
		`case_count` as caseCount,
		`case_percentage` as casePercentage,
		`if_record_result` as ifRecordResult,
		`description` as description
	</sql>


	<select id="getByCode" resultType="com.shuidihuzhu.cf.model.graytest.CfGrayTestSwitch">
		SELECT
			<include refid="selectFields"/>
		FROM
			<include refid="tableName"/>
		WHERE
			`code`=#{code}
		AND
			`valid` = 1
	</select>

	<insert id="addItem">
		INSERT INTO
		<include refid="tableName"/>
		(`biz_type`, `code`, `case_percentage`, `description`, `if_record_result`)
		VALUES (#{bizType}, #{code}, #{casePercentage}, #{description}, #{isRecordResult})
	</insert>

	<update id="updateIfRecordResult">
		UPDATE
		<include refid="tableName"/>
		SET `if_record_result`=#{isRecordResult} WHERE `code`=#{code} AND `valid`=1
	</update>

	<update id="delete">
		UPDATE
		<include refid="tableName"/>
		SET `valid`=0 WHERE `code`=#{code} AND `valid`=1
	</update>

	<select id="getByPage" resultType="com.shuidihuzhu.cf.model.graytest.CfGrayTestSwitch">
		SELECT
		<include refid="selectFields"/>
		FROM
		<include refid="tableName"/>
		WHERE
		`valid` = 1
		ORDER BY `id`
		limit #{start},#{size}
	</select>

	<select id="total" resultType="Integer">
		SELECT count(*) FROM
		<include refid="tableName"/>
		WHERE `valid`=1
	</select>

	<select id="getLikeCode" resultType="com.shuidihuzhu.cf.model.graytest.CfGrayTestSwitch">
		SELECT
		<include refid="selectFields"></include>
		FROM
		<include refid="tableName"></include>
		WHERE
		`code` LIKE CONCAT('%',#{code},'%')
		AND `valid`=1
	</select>

</mapper>