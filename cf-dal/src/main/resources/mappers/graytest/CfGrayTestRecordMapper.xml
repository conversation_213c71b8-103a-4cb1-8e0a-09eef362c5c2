<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.graytest.CfGrayTestRecordDao">
	<sql id="TABLE_NAME">
		cf_gray_test_record
	</sql>

	<sql id="INSERT_FIELDS">
		`gray_test_id`, `user_id`, `open_id`, `self_tag`, `gray_test_result`
	</sql>
	
	<insert id="add">
		INSERT INTO
		<include refid="TABLE_NAME"/>
		(<include refid="INSERT_FIELDS" />)
		VALUES (#{grayTestId}, #{userId}, #{openId}, #{selfTag}, #{grayTestResult})
	</insert>
</mapper>