<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.audit.CfAuditInfoSupplementDao">
    <sql id="tableName">
        cf_audit_info_supplement
    </sql>

    <sql id="insertField">
        `case_id`,
		`supplement_type`,
		`content`
    </sql>

    <sql id="selectField">
        `case_id`,
		`supplement_type`,
		`content`
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.client.apipure.model.audit.CfAuditInfoSupplement">
        INSERT INTO <include refid="tableName"/>
        (<include refid="insertField"/>)
        VALUES
        (#{caseId}, #{supplementType}, #{content})
    </insert>

    <update id="updateContentByCaseIdAndType" parameterType="com.shuidihuzhu.cf.client.apipure.model.audit.CfAuditInfoSupplement">
        UPDATE <include refid="tableName"/>
        SET  `content` = #{content}
        WHERE `case_id` = #{caseId} AND `supplement_type` = #{supplementType}
    </update>

    <select id="selectByCaseId" resultType="com.shuidihuzhu.cf.client.apipure.model.audit.CfAuditInfoSupplement">
        select <include refid="selectField"/>
        from <include refid="tableName"/>
        where `case_id` = #{caseId}
        and `is_delete` = 0
        order by id desc
    </select>

    <select id="selectByCaseIdAndType" resultType="com.shuidihuzhu.cf.client.apipure.model.audit.CfAuditInfoSupplement">
        select <include refid="selectField"/>
        from <include refid="tableName"/>
        where `case_id` = #{caseId} and `supplement_type` = #{supplementType}
        and `is_delete` = 0
        order by id desc
        limit 1
    </select>

</mapper>