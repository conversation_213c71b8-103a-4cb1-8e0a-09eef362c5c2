<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.audit.CfPublicAuditDao">
	<sql id="tableName">
		cf_audit_public_info
	</sql>

	<sql id="insertField">
		`case_id`,
		`work_order_id`,
		`operator_id`,
		`operator_name`,
		`audit_type`
	</sql>

	<sql id="selectField">
		`case_id`,
		`work_order_id`,
		`operator_id`,
		`operator_name`,
		`audit_type`,
		`create_time`
	</sql>

	<insert id="insertPublicAudit" parameterType="com.shuidihuzhu.client.model.CfPublicAuditAction">
		INSERT INTO <include refid="tableName"/>
		(<include refid="insertField"/>)
		VALUES
		(#{caseId}, #{workOrderId}, #{operatorId}, #{operatorName}, #{auditType})
	</insert>

	<insert id="insertPublicAudits">
		INSERT INTO <include refid="tableName"/>
		(<include refid="insertField"/>)
		VALUES
		<foreach collection="cfPublicAuditActions" item="item" index="index" separator=",">
			(#{item.caseId},#{item.workOrderId},#{item.operatorId},#{item.operatorName},#{item.auditType})
		</foreach>
	</insert>

	<select id="selectByCaseId" resultType="com.shuidihuzhu.client.model.CfPublicAuditAction">
		select <include refid="selectField"/>
		from <include refid="tableName"/>
		where `case_id` = #{caseId}
		and `audit_type` = #{auditType}
		and `is_delete` = 0
		order by id desc
		limit 1
	</select>

	<select id="selectAllByCaseId" resultType="com.shuidihuzhu.client.model.CfPublicAuditAction">
		select <include refid="selectField"/>
		from <include refid="tableName"/>
		where `case_id` = #{caseId} and `is_delete` = 0
	</select>

</mapper>