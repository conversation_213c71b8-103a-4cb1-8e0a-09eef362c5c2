<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.message.UserAddressDao">

    <sql id="TABLE">
		user_address
	</sql>

	<insert id="save" parameterType="com.shuidihuzhu.common.web.model.UserAddress" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO <include refid="TABLE" />
		(`user_id`,`name`,`mobile`,`province`,`city`,`county`,`address`,`email`)
		VALUES
		(#{userId},#{name},#{mobile},#{province},#{city},#{county},#{address},#{email})
	</insert>
	
	<update id="updateForClearDefault">
		UPDATE <include refid="TABLE" />
		SET `is_default`=0
		WHERE `user_id`=#{userId} AND `id`!=#{id} AND `is_default`=1
	</update>
	
	<select id="getById" resultType="com.shuidihuzhu.common.web.model.UserAddress">
		SELECT * 
		FROM <include refid="TABLE" />
		WHERE `id`=#{id}
	</select>
	
	<select id="getDefaultByUserId" resultType="com.shuidihuzhu.common.web.model.UserAddress">
		SELECT * 
		FROM <include refid="TABLE" />
		WHERE `user_id`=#{userId} AND `is_default`=1 AND `valid`=1
		ORDER BY id DESC 
		LIMIT 1
	</select>

</mapper>