<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.message.MobileClientDao">

	<sql id="mainTable">`shuidi_dev`.`mobile_client`</sql>

	<sql id="mobileClientField">
		id,
		user_id as userId,
		client_id as clientId,
		event_status as eventStatus,
		create_time as createTime,
		update_time as updateTime,
		biz_type as bizType
	</sql>


	<select id="get" resultType="com.shuidihuzhu.common.web.model.mobile.MobileClient">
		SELECT <include refid="mobileClientField" />
		FROM <include refid="mainTable" />
		WHERE user_id=#{userId}
		<if test="clientId!=null">
			AND client_id=#{clientId}
		</if>
		<if test="eventStatus!=null">
			AND event_status=#{eventStatus}
		</if>
		<if test="bizType!=null">
			AND biz_type=#{bizType}
		</if>
	</select>

	<select id="getByUserIdList" resultType="com.shuidihuzhu.common.web.model.mobile.MobileClient">
		SELECT <include refid="mobileClientField" />
		FROM <include refid="mainTable" />
		WHERE user_id in
		<foreach collection="userIdList" item="userId" open="(" separator="," close=")">
			#{userId}
		</foreach>
		<if test="eventStatus!=null">
			AND event_status=#{eventStatus}
		</if>
		<if test="bizType!=null">
			AND biz_type=#{bizType}
		</if>
	</select>

	<select id="getByRegStatus" resultType="com.shuidihuzhu.common.web.model.mobile.MobileClient">
		SELECT <include refid="mobileClientField" />
		FROM <include refid="mainTable" />
		WHERE event_status=#{eventStatus} AND user_id in
		<foreach collection="userIdList" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<insert id="save" parameterType="com.shuidihuzhu.common.web.model.mobile.MobileClient" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO <include refid="mainTable" />
			(`user_id`,`client_id`,`event_status`,`biz_type`)
		VALUES
			(#{userId},#{clientId},#{eventStatus},#{bizType})
	</insert>

	<update id="updateRegStatusById" >
		UPDATE <include refid="mainTable" />
		SET event_status=#{eventStatus}
		WHERE id=#{id}
	</update>

	<update id="updateRegStatusByIdList" >
		UPDATE <include refid="mainTable" />
		SET event_status=#{eventStatus}
		WHERE id in
		<foreach collection="idList" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</update>

	<update id="updateRegStatusByUserId" >
		UPDATE <include refid="mainTable" />
		SET event_status=#{eventStatus}
		WHERE user_id=#{userId} AND biz_type=#{bizType}
	</update>

	<update id="updateRegStatusByUserIdAndClientId" >
		UPDATE <include refid="mainTable" />
		SET event_status=#{eventStatus}
		WHERE user_id=#{userId} AND client_id=#{clientId}
	</update>

</mapper>
