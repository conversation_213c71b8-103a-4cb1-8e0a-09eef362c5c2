<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.ai.CfAiAdGenerateDao">

    <sql id="tableName">
        ai_ad_generate_record
    </sql>

    <sql id="insert_Column_List">
        `ad_id`,
        `case_id`,
        `profile_key`,
        `ad_content`,
        `prompt_type`,
        `is_compliant`,
        `no_compliant_reason`
    </sql>

    <sql id="selectField">
        `ad_id`,
        `case_id`,
        `profile_key`,
        `ad_content`,
        `prompt_type`,
        `is_compliant`,
        `no_compliant_reason`
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.client.cf.api.model.AdGenerationResult">
        insert into
        <include refid="tableName"/>
        (<include refid="insert_Column_List"/>)
        values (
        #{adId}, #{caseId}, #{profileKey}, #{adContent},
        #{promptType}, #{isCompliant}, #{noCompliantReason}
        )
    </insert>

    <update id="deleteAdByCaseId">
        update <include refid="tableName"/>
        set is_delete = 1
        where case_id = #{caseId}
    </update>

    <update id="deleteAdByAdId">
        update <include refid="tableName"/>
        set is_delete = 1
        where ad_id = #{adId}
    </update>

    <select id="queryAiAd" resultType="com.shuidihuzhu.client.cf.api.model.AdGenerationResult">
        select
        <include refid="selectField"/>
        from
        <include refid="tableName"/>
        where `case_id` = #{caseId}
        and `profile_key` = #{profileKey}
        and `is_delete` = 0
        limit 1
    </select>

</mapper>
