<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.ai.CfAiIdCardResultDAO">

    <sql id="tableName">
        cf_ai_id_card_result
    </sql>

    <sql id="insert_Column_List">
        `case_id`,
        `single_id_card_result`,
        `group_id_card_result`,
        `use_scene`
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.ai.CfAiIdCardResult">
        insert into
        <include refid="tableName"/>
        (<include refid="insert_Column_List"/>)
        values (
        #{caseId},
        #{singleIdCardResult},
        #{groupIdCardResult},
        #{useScene}
        )
    </insert>

    <select id="getLastByCaseId" resultType="com.shuidihuzhu.cf.model.ai.CfAiIdCardResult">
        select
        *
        from
        <include refid="tableName"/>
        where `case_id` = #{caseId}
        and `is_delete` = 0
    </select>

</mapper>
