<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.user.UserMobileMapper">

    <sql id="table_name">
		user_mobile
	</sql>

    <sql id="insert_column_list">
        `user_id`,
        `encrypt_mobile`
	</sql>

    <sql id="base_column_list">
        `id`,
        `create_time`,
        `update_time`,
        <include refid="insert_column_list"/>
    </sql>

    <update id="save">
        insert into <include refid="table_name"/>
        (<include refid="insert_column_list"/>)
        values (
        #{userId} ,
        #{encryptMobile}
        )
        on duplicate key update
        `encrypt_mobile`=values(encrypt_mobile)
    </update>

    <select id="getCount" resultType="int">
    </select>

    <select id="getEncryptMobileByUserId" resultType="java.lang.String">
        select `encrypt_mobile`
        from <include refid="table_name"/>
        where `user_id` = #{userId}
    </select>

</mapper>
