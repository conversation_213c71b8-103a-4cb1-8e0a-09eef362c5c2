<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingFundUseAuditDao">
    <sql id="table_name">
        new_crowdfunding_fund_use_progress
    </sql>


    <sql id="addFields">
        (`user_id`,`crowdfunding_id`,`type`,`title`,`content`,`image_urls`,`draw_finish_time_str`)
    </sql>

    <insert id="insertFundUseAuditProgress" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingProgress"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="table_name"/>
        <include refid="addFields"/>
        VALUES
        (#{userId},#{activityId},#{type},#{title},#{content},#{imageUrls},#{drawFinishTimeStr});
    </insert>

    <select id = "selectByCaseIdAndStatus" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingProgress">
        SELECT *
        FROM crowdfunding_fund_use_progress
        WHERE crowdfunding_id = #{caseId}
        AND status = #{status}
        AND is_delete = 0
        order by id DESC
    </select>


    <select id = "selectByCaseIdAndStatusNew" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingProgress">
        SELECT *
        FROM new_crowdfunding_fund_use_progress
        WHERE crowdfunding_id = #{caseId}
        AND status = #{status}
        AND is_delete = 0
        order by id DESC
    </select>

    <select id = "selectById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingProgress">
        SELECT *
        FROM new_crowdfunding_fund_use_progress
        WHERE id = #{id}
          AND is_delete = 0
        order by id DESC
    </select>



</mapper>