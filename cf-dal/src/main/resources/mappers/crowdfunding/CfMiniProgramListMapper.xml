<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfMiniProgramListModelDao">

    <sql id="tableName">
        `cf_mini_program_list`
    </sql>

    <sql id="insert_fields">
        `info_uuid`,
        `date`
    </sql>

    <insert id="insert" parameterType="java.util.List">
        INSERT INTO <include refid="tableName"/>
        (<include refid="insert_fields"/>)
        VALUES
        <foreach collection="list" item="item" separator=",">
        (
          #{item.infoUuid},
          #{item.date}
        )
        </foreach>
    </insert>

    <select id="selectByPage" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfMiniProgramListModel">
        SELECT *
        FROM <include refid="tableName"/>
        WHERE `date`=#{date}
        AND <![CDATA[ `id`<#{anchorId} ]]>
        ORDER BY`date`,`id` DESC
        LIMIT #{size}
    </select>
</mapper>