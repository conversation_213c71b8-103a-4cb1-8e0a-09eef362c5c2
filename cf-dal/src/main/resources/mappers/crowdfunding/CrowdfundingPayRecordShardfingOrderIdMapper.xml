<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingPayRecordShardingOrderIdDao">
    <sql id="TABLE">
        crowdfunding_pay_record_orderid_sharding
    </sql>
    <sql id="SHARDING_TABLE">
        crowdfunding_pay_record_orderid_sharding_${sharding}
    </sql>
    <sql id="FIELDS">
        `id`,`pay_uid`,`crowdfunding_order_id`,`pre_pay_amount`,`real_pay_amount`,`pay_platform`,`pay_status`,`ctime`,`callback_time`,`valid`,
        `refund_status`,`refund_time`,`refund_amount`,`refund_reason`,`last_modified`
    </sql>
    <sql id="INSERT_FIELDS">
        `crowdfunding_order_id`,`pay_uid`,`pre_pay_amount`,`real_pay_amount`,`pay_platform`,`pay_status`,`ctime`,`valid`
    </sql>

    <insert id="addPayRecord" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord">
        INSERT INTO
        <include refid="TABLE"/>
        (<include refid="INSERT_FIELDS"/>)
        VALUES(#{crowdfundingOrderId},#{payUid},#{prePayAmount},#{realPayAmount},#{payPlatform},#{payStatus},#{ctime},#{valid});
    </insert>


    <update id="updatePayStatus">
        UPDATE
        <include refid="TABLE"/>
        SET `pay_status`=#{payStatus},`callback_time`=#{callbackTime},`real_pay_amount`=#{realPayAmount}
        WHERE crowdfunding_order_id=#{crowdfundingOrderId} and `pay_uid`=#{payUid};
    </update>

	<update id="updateRefundStatus">
		UPDATE
		<include refid="TABLE"/>
		SET `refund_status`=#{refundStatus},
		`refund_time`=#{refundTime},
		`refund_amount`=#{refundAmount},
		`refund_reason`=#{refundReason}
		WHERE crowdfunding_order_id=#{crowdfundingOrderId} and `pay_uid`=#{payUid};
	</update>


    <update id="updateRefundStatusByOrderIds">
        UPDATE
        <include refid="TABLE"/>
        SET `refund_status`=#{refundStatus}
        WHERE crowdfunding_order_id in <foreach collection="crowdfundingOrderIds" item="orderId" open="(" separator="," close=")" >#{orderId}</foreach>
    </update>
    <select id="getByOrderId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord">
        SELECT *
        FROM <include refid="TABLE"/>
        WHERE crowdfunding_order_id=#{crowdfundingOrderId}
        limit 1;
    </select>
    <select id="getByOrderIdWithPayUidAndSuffixTableName" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord">
        SELECT <include refid="FIELDS"/>
        FROM <include refid="SHARDING_TABLE"/>
        WHERE crowdfunding_order_id=#{crowdfundingOrderId} and pay_uid=#{payUid}
        limit 1;
    </select>

    <select id="listSuccessByOrderIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord">
        SELECT <include refid="FIELDS"/>
        FROM <include refid="TABLE"/>
        WHERE crowdfunding_order_id in <foreach collection="crowdfundingOrderIds" item="st" open="(" separator="," close=")" >#{st}</foreach>
        AND  pay_status = 1 AND valid = 1 AND refund_status in (0,3)
    </select>
    <select id="getPaySuccessByPayUidsAndOrderIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord">
        SELECT
        <include refid="FIELDS"/>
        FROM
        <include refid="TABLE"/>
        WHERE
        crowdfunding_order_id in <foreach collection="crowdfundingOrderIds" item="st" open="(" separator="," close=")" >#{st}</foreach>
        and `pay_uid` in <foreach collection="payUids" item="payUid" open="(" separator="," close=")" >#{payUid}</foreach>
        AND pay_status = 1 AND valid = 1
    </select>
    <select id="getPaySuccessByOrderIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord">
        SELECT <include refid="FIELDS"/>
        FROM <include refid="TABLE"/>
        WHERE crowdfunding_order_id in <foreach collection="orderIds" item="orderId" open="(" separator="," close=")" >#{orderId}</foreach>
        AND pay_status = 1
        <if test="valid!=null">
            AND valid = #{valid}
        </if>
    </select>
    <select id="getPaySuccessByOrderIdsWithSuffixTableName" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord">
        SELECT <include refid="FIELDS"/>
        FROM <include refid="SHARDING_TABLE"/>
        WHERE crowdfunding_order_id in <foreach collection="orderIds" item="orderId" open="(" separator="," close=")" >#{orderId}</foreach>
        AND pay_status = 1
        <if test="valid!=null">
            AND valid = #{valid}
        </if>
    </select>
    <select id="selectByOrderIdList" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord">
        SELECT `crowdfunding_order_id`,`pay_uid`
        FROM <include refid="TABLE"/>
        WHERE `crowdfunding_order_id` IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        LIMIT #{start},#{size}
    </select>
    <select id="getPaySuccessByOrderIdsAndRefundStatus" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord">
        SELECT <include refid="FIELDS"/>
        FROM <include refid="TABLE"/>
        WHERE crowdfunding_order_id in <foreach collection="orderIds" item="orderId" open="(" separator="," close=")" >#{orderId}</foreach>
        AND pay_status = 1 AND valid = 1
        AND refund_status in <foreach collection="refundStatusList" open="(" separator="," close=")" item="refundStatus"> #{refundStatus}</foreach>
    </select>
    <select id="getPaySuccessByOrderIdsAndRefundStatusWithSuffixTableName" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord">
        SELECT <include refid="FIELDS"/>
        FROM <include refid="SHARDING_TABLE"/>
        WHERE crowdfunding_order_id in <foreach collection="orderIds" item="orderId" open="(" separator="," close=")" >#{orderId}</foreach>
        AND pay_status = 1 AND valid = 1
        AND refund_status in <foreach collection="refundStatusList" open="(" separator="," close=")" item="refundStatus"> #{refundStatus}</foreach>
    </select>
    <select id="getByOrderIdsAndPayUids" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord">
        SELECT <include refid="FIELDS"/>
        FROM <include refid="TABLE"/>
        WHERE crowdfunding_order_id in <foreach collection="orderIds" item="orderId" open="(" separator="," close=")" >#{orderId}</foreach>
        and `pay_uid` in <foreach collection="payUids" item="payUid" open="(" separator="," close=")" >#{payUid}</foreach>
    </select>
    <select id="getValidByOrderIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord">
        SELECT <include refid="FIELDS"/>
        FROM <include refid="TABLE"/>
        WHERE crowdfunding_order_id in <foreach collection="orderIds" item="orderId" open="(" separator="," close=")" >#{orderId}</foreach>
        AND valid = 1
    </select>

    <select id="getByOrderIdsWithSuffixTableName" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord">
        SELECT <include refid="FIELDS"/>
        FROM <include refid="SHARDING_TABLE"/>
        WHERE crowdfunding_order_id in <foreach collection="orderIds" item="orderId" open="(" separator="," close=")" >#{orderId}</foreach>
    </select>
</mapper>
