<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.slave.CrowdFundingVerificationSlaveDao">

    <sql id="tableName">
        `crowd_funding_verification`
    </sql>

    <select id="selectCountByMin" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM <include refid="tableName"/>
        WHERE <![CDATA[ `create_time`>=#{begin} ]]> AND <![CDATA[ `create_time`<#{end} ]]>
    </select>
</mapper>

