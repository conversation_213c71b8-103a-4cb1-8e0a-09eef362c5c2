<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.slave.CrowdFundingProgressSlaveDao">
    <sql id="tableName">
        crowdfunding_progress
    </sql>

    <resultMap id="selectResult" type="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingProgress">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="activityId" column="crowdfunding_id"/>
        <result property="type" column="type"/>
        <result property="title" column="title"/>
        <result property="content" column="content"/>
        <result property="imageUrls" column="image_urls"/>
        <result property="postDate" column="create_time"/>
        <result property="updateDate" column="update_time"/>
    </resultMap>
    <select id="selectCountByMin" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM <include refid="tableName"/>
        WHERE <![CDATA[ `create_time`>=#{begin} ]]> AND <![CDATA[ `create_time`<#{end} ]]>
    </select>

    <select id="selectById" resultMap="selectResult">
        select id, crowdfunding_id
        from <include refid="tableName"/>
        where id in
        <foreach item="item" index="index" collection="ids"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>