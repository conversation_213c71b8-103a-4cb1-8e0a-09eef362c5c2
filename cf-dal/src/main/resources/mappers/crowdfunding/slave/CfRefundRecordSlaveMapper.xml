<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.slave.CfRefundRecordSlaveDao">
    <sql id="TABLE">
        cf_refund_record
    </sql>

    <sql id="QUERY_FIELD">
    	`id`,
    	`info_uuid` as infoUuid,
        `app_id` as appId,
        `mht_order_no` as mhtOrderNo,
        `mht_refund_no` as mhtRefundNo,
        `pay_type` as payType,
        `refund_amount` as refundAmount,
        `status`,
        `response_code` as responseCode,
        `trade_status` as tradeStatus,
        `now_pay_order_no` as nowPayOrderNo,
        `date_created` as dateCreated,
		`last_modified` as lastModified,
		`refund_type` as refundType
    </sql>

    <select id="getByInfoUuidAndAnchorId" resultType="com.shuidihuzhu.cf.model.crowdfunding.NewCfRefundRecord">
        SELECT *
        FROM <include refid="TABLE" />
        WHERE `info_uuid`=#{infoUuid} AND `status`=2
        <if test="anchorId != null">
            AND <![CDATA[ id<#{anchorId}]]>
        </if>
        ORDER by `id` DESC
        LIMIT #{limit}
    </select>

</mapper>
