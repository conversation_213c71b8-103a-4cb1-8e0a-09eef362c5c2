<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.slave.UserRealInfoSlaveDao">
	<sql id="tableName">
        user_real_info
    </sql>

    <select id="countByMin" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM <include refid="tableName"/>
        WHERE <![CDATA[ `date_created`>=#{begin} ]]> AND <![CDATA[ `date_created`<#{end} ]]> AND is_delete = 0
    </select>

    <select id="countSuccess" resultType="int">
        select IFNULL((select count(*) from <include refid="tableName"/> where <![CDATA[ `date_created`>=#{begin} ]]> AND <![CDATA[ `date_created`<#{end} ]]> and idcard_verify_status =1)/count(*) * 100,0) from <include refid="tableName"/>
        WHERE <![CDATA[ `date_created`>=#{begin} ]]> AND <![CDATA[ `date_created`<#{end} ]]>  AND is_delete = 0
    </select>
</mapper>
