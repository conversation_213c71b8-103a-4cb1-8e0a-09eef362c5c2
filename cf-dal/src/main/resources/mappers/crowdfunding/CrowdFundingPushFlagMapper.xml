<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CrowdFundingPushFlagDao">
    <sql id="tableName">
        crowdfunding_push_flag
    </sql>

    <sql id="addFields">
        (`user_id`,`crowdfunding_id`,`push_flag`)
    </sql>

    <sql id="selectFields">
        `user_id`,`crowdfunding_id`,`push_flag`,`create_time`,`update_time`
    </sql>

    <resultMap id="selectResult" type="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingPushFlag">
        <result property="userId" column="user_id"/>
        <result property="crowdFundingId" column="crowdfunding_id"/>
        <result property="pushFlag" column="push_flag"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="queryPushFlag" resultMap="selectResult">
        SELECT
          <include refid="selectFields"/>
        FROM
          <include refid="tableName"/>
        WHERE
          crowdfunding_id=#{crowdFundingId} and user_id=#{userId}
    </select>

    <update id="update" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingPushFlag">
        UPDATE
        <include refid="tableName"/>
        SET push_flag=#{pushFlag} WHERE user_id=#{userId} and crowdfunding_id=#{crowdFundingId}
    </update>

    <insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingPushFlag">
        INSERT INTO
        <include refid="tableName"/>
        <include refid="addFields"/>
        VALUES (#{userId},#{crowdFundingId},#{pushFlag});
    </insert>
</mapper>