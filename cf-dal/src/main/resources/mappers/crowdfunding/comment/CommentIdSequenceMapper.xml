<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.comment.CommentIdSequenceDao">
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.shuidihuzhu.cf.domain.comment.CommentIdSequence"
            useGeneratedKeys="true">
        insert into comment_id_sequence (is_delete)
        values ( #{isDelete})
    </insert>

</mapper>