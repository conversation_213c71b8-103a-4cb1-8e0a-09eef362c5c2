<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.comment.CrowdfundingCommentIdToCrowdfundingIdDao">
    <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.domain.comment.CrowdfundingCommentIdToCrowdfundingIdDO">

    </resultMap>
    <sql id="Base_Column_List">
        id, crowdfunding_id
    </sql>

    <sql id="TABLE">
        crowdfunding_comment_id_to_crowdfunding_id
    </sql>

    <select id="selectByCommentId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="TABLE"/>
        where id = #{commentId}
    </select>

    <select id="selectByCommentIdsNoCareDeleted"  resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="TABLE"/>
        WHERE `id` in
        <foreach collection="commentIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


    <update id="deleteByCommentId" parameterType="java.lang.Long">
        UPDATE
        <include refid="TABLE"/>
        SET `is_deleted`=TRUE
        WHERE `id`=#{id}
    </update>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.domain.comment.CrowdfundingCommentIdToCrowdfundingIdDO">
        insert into
        <include refid="TABLE"/>
        (id, crowdfunding_id)
        values (#{id},#{crowdfundingId})
    </insert>

</mapper>