<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.comment.CrowdfundingCommentShareDao">

    <sql id="tableName">
        crowdfunding_comment_share
    </sql>

    <sql id="fields">
        `id`, `crowdfunding_id`,`parent_id`,`user_id`,`user_third_id`,`user_third_type`,`comment_id`,`content`,`type`,`is_deleted`,`create_time`
    </sql>

    <sql id="insertFields">
        `id`, `crowdfunding_id`,`parent_id`,`comment_id`,`user_id`,`user_third_id`,`user_third_type`,`content`,`type`
    </sql>


    <insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingComment">
        INSERT INTO
        <include refid="tableName"/>
        (<include refid="insertFields"/>)
        VALUES
        (#{id},#{crowdfundingId},#{parentId},#{commentId},#{userId},#{userThirdId},#{userThirdType},#{content},#{type});
    </insert>

    <update id="removeByCrowdfundingIdAndCommentId">
        UPDATE
        <include refid="tableName"/>
        SET `is_deleted`= 1
        WHERE crowdfunding_id = #{crowdfundingId} AND `id`=#{commentId}
    </update>





    <select id="getByPageNoCareCommentIdList" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingComment">
        SELECT <include refid="fields"/>
        FROM <include refid="tableName"/>
        WHERE `crowdfunding_id` in
        <foreach collection="crowdfundingIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>

        AND

        `parent_id` in
        <foreach collection="parentIdList" item="parentId" open="(" separator="," close=")">
            #{parentId}
        </foreach>

        AND `is_deleted`=FALSE
        AND `type`=#{type}
        ORDER BY `create_time` ASC
        LIMIT #{offset}, #{limit}
    </select>


    <select id="getByParentIdList" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingComment">
        SELECT <include refid="fields"/>
        FROM <include refid="tableName"/>
        WHERE `crowdfunding_id` in
        <foreach collection="crowdfundingIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>

        AND
        `parent_id` in
        <foreach collection="parentIdList" item="parentId" open="(" separator="," close=")">
            #{parentId}
        </foreach>

        AND `is_deleted`=FALSE
        AND `type`=#{type}
        AND `user_id`=#{userId}
        GROUP BY `parent_id`
        LIMIT #{offset}, #{limit}
    </select>


    <select id="getByIdNoCareDeleted" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingComment">
        SELECT <include refid="fields"/>
        FROM <include refid="tableName"/>
        WHERE `crowdfunding_id`=#{crowdfundingId} AND `id`=#{id}
    </select>

    <select id="getListByIdNoCareDeleted" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingComment">
        SELECT <include refid="fields"/>
        FROM <include refid="tableName"/>
        WHERE `crowdfunding_id` in
        <foreach collection="crowdfundingIds" item="crowdfundingId" open="(" separator="," close=")">
            #{crowdfundingId}
        </foreach>
       AND
        `id` in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


    <select id="getByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingComment">
        SELECT <include refid="fields"/>
        FROM <include refid="tableName"/>
        where `crowdfunding_id` = #{crowdfundingId}
        and `parent_id` = #{parentId}
        and `user_id` = #{userId}
        and `type` = #{type}
        and `is_deleted` = 0
        limit 1
    </select>

    <select id="getListByIdNoCareDeletedV1" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingComment">
        SELECT <include refid="fields"/>
        FROM <include refid="tableName"/>
        WHERE `crowdfunding_id` in
        <foreach collection="crowdfundingIds" item="crowdfundingId" open="(" separator="," close=")">
            #{crowdfundingId}
        </foreach>
        AND
        `id` in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>
