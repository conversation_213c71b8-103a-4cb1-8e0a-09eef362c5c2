<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.EvaluateLabelDao">

    <sql id="tableName">
        evaluate_label
    </sql>
    <sql id="fields">
        `id`,
		`character_id`,
		`age_min`,
		`age_max`,
		`label_name`,
        `label_priority`
    </sql>
    <select id="getSingleByAge" resultType="com.shuidihuzhu.cf.model.crowdfunding.EvaluateLabelDO">
        SELECT
        <include refid="fields"/>
        FROM
        <include refid="tableName"/>
        WHERE
        `age_max` = #{age}
    </select>

    <select id="getLabelId" resultType="java.lang.Integer">
        select `id` from <include refid="tableName"/>
        where
        `label_name` = #{label}
        and `is_delete` = 0
    </select>
    <select id="getByLabelId" resultType="com.shuidihuzhu.cf.model.crowdfunding.EvaluateLabelDO">
        select * from <include refid="tableName"/>
        where
        `id` = #{labelId}
        and `is_delete` = 0;
    </select>
    <select id="getListByAge" resultType="com.shuidihuzhu.cf.model.crowdfunding.EvaluateLabelDO">
        SELECT
        <include refid="fields"/>
        FROM
        <include refid="tableName"/>
        WHERE
        `age_max` = #{age}
    </select>
    <select id="getByPriority" resultType="com.shuidihuzhu.cf.model.crowdfunding.EvaluateLabelDO">
        SELECT
        <include refid="fields"/>
        FROM
        <include refid="tableName"/>
        WHERE
        `label_priority` = #{priority}
    </select>
    <select id="getByCharacterId" resultType="com.shuidihuzhu.cf.model.crowdfunding.EvaluateLabelDO">
        SELECT
        <include refid="fields"/>
        FROM
        <include refid="tableName"/>
        WHERE
        `character_id`= #{characterId}
    </select>
    <select id="getAll" resultType="com.shuidihuzhu.cf.model.crowdfunding.EvaluateLabelDO">
        SELECT
        <include refid="fields"/>
        FROM
        <include refid="tableName"/>
        WHERE
        `is_delete`= 0
    </select>
    <select id="getByCharacterId1" resultType="com.shuidihuzhu.cf.model.crowdfunding.EvaluateLabelDO">
        SELECT
        <include refid="fields"/>
        FROM
        <include refid="tableName"/>
        WHERE
        `character_id`= #{characterId}
    </select>

</mapper>

