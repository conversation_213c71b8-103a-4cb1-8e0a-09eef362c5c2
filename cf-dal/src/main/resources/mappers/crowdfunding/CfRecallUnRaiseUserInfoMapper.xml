<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfRecallUnRaiseUserInfoDao">

    <sql id="tableName">
        `cf_recall_unraise_user_info`
    </sql>

    <sql id="insert_fields">
        `user_id`,
        `open_id`,
        `subscribe_event_time`,
        `is_send`
    </sql>

    <insert id="insertList" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfRecallUnRaiseUserInfo">
        INSERT INTO <include refid="tableName"/>
        (<include refid="insert_fields"/>)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.userId},#{item.openId},#{item.subscribeEventTime},#{item.isSend})
        </foreach>

    </insert>
</mapper>