<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
CREATE TABLE `crowdfunding_attachment` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '附件ID',
  `parent_id` int(11) NOT NULL COMMENT 'crowdfunding_info的Id',
  `url` varchar(1024) DEFAULT NULL COMMENT '附件URL',
  `type` smallint(11) DEFAULT '0' COMMENT '附件类型：0-基本情况介绍, 1-身份证明，2-医疗证明，3-补充材料',
  `sequence` int(11) DEFAULT '0' COMMENT '图片顺序',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6923 DEFAULT CHARSET=utf8 COMMENT='筹款附件表';

todo V3 上线前请在正式库上执行:
UPDATE crowdfunding_attachment SET `type` = 0 WHERE `type` IS NULL;
UPDATE crowdfunding_attachment SET `sequence` = 0 WHERE `sequence` IS NULL;

ALTER TABLE crowdfunding_attachment
MODIFY COLUMN `type` SMALLINT NOT NULL DEFAULT 0 COMMENT '附件类型：0-基本情况介绍, 1-身份证明，2-医疗证明，3-补充材料',
MODIFY COLUMN `sequence` SMALLINT NOT NULL DEFAULT 0 COMMENT '图片顺序',
ADD COLUMN `create_time` TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
ADD COLUMN `last_modified` TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '最后修改时间',
ADD INDEX ix_parent_id_type (parent_id, type);
-->

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingAttachmentDao">
	<sql id="tableName">
		crowdfunding_attachment
	</sql>
	
	<sql id="QUERY_FIELDS">
		id,
		parent_id,
		url,
		type,
		sequence,
		create_time,
		last_modified,
		is_delete
	</sql>

	<select id="getAttachmentsByType" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment">
		SELECT IFNULL(url,'') as url, `type`
		FROM
		<include refid="tableName"/>
		WHERE parent_id = #{parentId}
		AND `is_delete` = 0
		AND type =
		#{type, typeHandler=org.apache.ibatis.type.EnumOrdinalTypeHandler, javaType=com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum}
		ORDER BY id
	</select>

	<select id="getAttachmentsByTypeToSea" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment">
		SELECT IFNULL(url,'') as url,id,parent_id, `type`
		FROM
		<include refid="tableName"/>
		WHERE parent_id = #{parentId}
		AND `is_delete` = 0
		AND type =
		#{type, typeHandler=org.apache.ibatis.type.EnumOrdinalTypeHandler, javaType=com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum}
		ORDER BY id
	</select>

	<select id="getAttachmentsByTypeToSeaAndTime" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment">
		SELECT <include refid="QUERY_FIELDS"/>
		FROM
		<include refid="tableName"/>
		WHERE
		parent_id = #{parentId} and `type`=#{type} and create_time <![CDATA[ <= ]]> #{firstApproveTime}
		AND `is_delete` = 0
	</select>

	<select id="getAttachmentById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment">
		SELECT <include refid="QUERY_FIELDS"/>
		FROM
		<include refid="tableName"/>
		WHERE id = #{id}
	</select>
	
	<select id="getListByInfoIdListAndType" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment">
		SELECT <include refid="QUERY_FIELDS"/>
		FROM <include refid="tableName"/>
		WHERE parent_id in
			<foreach collection="parentIdList" item="parentId" open="(" separator="," close=")">
				#{parentId}
			</foreach>
		AND type =
			#{type, typeHandler=org.apache.ibatis.type.EnumOrdinalTypeHandler, javaType=com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum}
		AND `is_delete` = 0
		ORDER BY id
	</select>

	<select id="getFundingAttachment" parameterType="int" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment">
		SELECT id,url, type, sequence
		FROM
		<include refid="tableName"/>
		WHERE
		parent_id = #{parentId}
		AND `is_delete` = 0
	</select>

	<select id="queryByParentId" parameterType="int" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment">
		SELECT <include refid="QUERY_FIELDS"/>
		FROM <include refid="tableName"/>
		WHERE parent_id = #{parentId}
	</select>


	<update id="deleteByParentId" parameterType="int">
		UPDATE <include refid="tableName"/>
		SET `is_delete`=1
		WHERE
		parent_id = #{parentId}
	</update>

	<insert id="add" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO
		<include refid="tableName"/>
		(parent_id,url,type,sequence) VALUES
		<foreach collection="list" item="element" index="index" separator=",">
			(
			#{element.parentId},
			#{element.url},
			#{element.type, typeHandler=org.apache.ibatis.type.EnumOrdinalTypeHandler, javaType=com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum},
			#{element.sequence}
			)
		</foreach>
	</insert>
	
	<insert id="addOne" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO
		<include refid="tableName"/>
		(parent_id,url,type,sequence)
		 VALUES
		(
			#{parentId},
			#{url},
			#{type, typeHandler=org.apache.ibatis.type.EnumOrdinalTypeHandler, javaType=com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum},
			#{sequence}
		)
	</insert>

	<insert id="saveOrUpdate">
		insert into crowdfunding_attachment(id,parent_id, url, type) values
		<foreach collection="list" item="element" index="index" separator=",">
			(
			#{element.id},
			#{element.parentId},
			#{element.url},
			#{element.type, typeHandler=org.apache.ibatis.type.EnumOrdinalTypeHandler, javaType=com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum}
			)
		</foreach>
		ON DUPLICATE KEY UPDATE url=values(url)
	</insert>
	
	<update id="deleteByParentIdAndType" parameterType="int">
		UPDATE <include refid="tableName"/>
		SET `is_delete`=1
		WHERE parent_id = #{parentId}
		AND type in 
		<foreach collection="typeList" item="type" index="index" open="(" separator="," close=")">
			#{type, typeHandler=org.apache.ibatis.type.EnumOrdinalTypeHandler, javaType=com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum}
		</foreach>
	</update>

	<insert id="addForAdmin" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO
		<include refid="tableName"/>
		(parent_id,url,type,sequence) VALUES
		<foreach collection="list" item="element" index="index" separator=",">
			(
			#{element.parentId},
			#{element.url},
			#{element.type, typeHandler=org.apache.ibatis.type.EnumOrdinalTypeHandler, javaType=com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum},
			#{element.sequence}
			)
		</foreach>
	</insert>

	<select id="getAttachmentsByTypes" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment">
		SELECT <include refid="QUERY_FIELDS"/>
		FROM
		<include refid="tableName"/>
		WHERE parent_id = #{parentId}
		AND `is_delete` = 0
		AND `type` in
		<foreach collection="types" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

    <select id="getAttachmentsByParentIdAndType" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment">
        SELECT IFNULL(url,'') as url,id,parent_id,`type`
        FROM
        <include refid="tableName"/>
        WHERE parent_id = #{parentId}
        AND `is_delete` = 0
        AND type = #{type}
        ORDER BY id
    </select>

	<update id="deleteByIds" >
		UPDATE <include refid="tableName"/>
		SET `is_delete`=1
		WHERE parent_id = #{parentId}
		AND
		`id` IN
		<foreach collection="ids" item="id" open="(" separator="," close=")">
			#{id}
		</foreach>
	</update>

	<select id="getAttachmentsByCaseIdsWithDelete" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment">
		SELECT <include refid="QUERY_FIELDS"/>
		FROM
		<include refid="tableName"/>
		WHERE parent_id in
		<foreach collection="parentIdList" item="parentId" open="(" separator="," close=")">
			#{parentId}
		</foreach>
		AND type in
		<foreach collection="types" item="type" index="index" open="(" separator="," close=")">
			#{type, typeHandler=org.apache.ibatis.type.EnumOrdinalTypeHandler, javaType=com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum}
		</foreach>
	</select>

	<select id="getAttachmentsByIdListToSea" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment">
		SELECT <include refid="QUERY_FIELDS"/>
		FROM
		<include refid="tableName"/>
		WHERE parent_id = #{parentId}
		and id in
		<foreach collection="idList" item="id" open="(" separator="," close=")">
			#{id}
		</foreach>
	</select>
</mapper>