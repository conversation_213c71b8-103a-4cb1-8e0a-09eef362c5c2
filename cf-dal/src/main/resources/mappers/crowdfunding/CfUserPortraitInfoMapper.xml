<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfUserPortraitInfoDao">

    <sql id="TABLE">
        cf_user_portrait_info
    </sql>

    <sql id="Base_Field">
        `user_portrait_id` as userPortraitId,
        `on_off_switch` as onOffSwitch,
        `sort`
    </sql>

    <select id="getAll" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfUserPortraitInfo">
        SELECT
        <include refid="Base_Field"/>
        FROM
        <include refid="TABLE"/>
        WHERE
        is_delete = 0
    </select>

    <select id="getFirst" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfUserPortraitInfo">
        SELECT
        <include refid="Base_Field"/>
        FROM
        <include refid="TABLE"/>
        WHERE
        is_delete = 0
        and on_off_switch = 1
        order by sort asc limit 1
    </select>

</mapper>
