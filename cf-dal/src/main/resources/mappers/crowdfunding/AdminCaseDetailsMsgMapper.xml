<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCaseDetailsMsgDao">

    <sql id="TABLE">
        case_details_msg
    </sql>

    <sql id="Base_Field">
        `id` as id,
        `case_id` as caseId,
        `info_uuid` as infoUuid,
        `head_picture_url` as headPictureUrl,
        `carousel_text` as carouselText,
        `case_label` as caseLabel,
        `case_label_sort` as caseLabelSort
    </sql>

    <select id="getByInfoUuid" resultType="com.shuidihuzhu.client.cf.admin.model.AdminCaseDetailsMsg">
        SELECT
        <include refid="Base_Field"/>
        FROM
        <include refid="TABLE"/>
        WHERE
        info_uuid =#{infoUuid}
        and is_delete = 0
    </select>

    <select id="getByCaseId" resultType="com.shuidihuzhu.client.cf.admin.model.AdminCaseDetailsMsg">
        SELECT
        <include refid="Base_Field"/>
        FROM
        <include refid="TABLE"/>
        WHERE
        case_id =#{caseId}
        and is_delete = 0
    </select>

    <select id="getCaseDetailsMsgByInfoUuid" resultType="com.shuidihuzhu.client.cf.admin.model.AdminCaseDetailsMsg">
        SELECT
        <include refid="Base_Field"/>
        FROM
        <include refid="TABLE"/>
        WHERE
        info_uuid =#{infoUuid}
        and is_delete = 0
    </select>

    <update id="updateDiseaseName">
        UPDATE
        <include refid="TABLE"/>
        SET
        case_label = #{caseLabel}
        WHERE id = #{id}
    </update>

</mapper>
