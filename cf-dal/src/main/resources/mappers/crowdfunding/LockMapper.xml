<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.LockMapper">
    <sql id="TABLE">
		cf_lock
	</sql>
    <sql id="INSERT_FIELDS">
		`user_key`,`sys_key`,`key_type`,`expiry_time`
	</sql>

    <insert id="tryLock" useGeneratedKeys="true">
        insert ignore into
        <include refid="TABLE"/>
        (<include refid="INSERT_FIELDS"/>)
        VALUES
        (
        #{userKey}, #{sysKey}, #{keyType}, #{expiryTime}
        )
    </insert>

    <select id="checkLock" resultType="integer">
        select count(*)
        from
        <include refid="TABLE"/>
        where `user_key` = #{userKey}
        and `is_delete` = 0
    </select>

</mapper>