<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfOrderTransferHistoryDao">

	<sql id="TABLE">
		cf_property_transfer_history
	</sql>

	<sql id="SELECT_FIELDS">
		`id`, `from_user_id`, `to_user_id`, `biz_id`, `biz_type`, `biz_table_name`, `create_time`, `update_time`
	</sql>

	<sql id="INSERT_FIELDS">
		`from_user_id`, `to_user_id`, `biz_id`, `biz_type`, `biz_table_name`
	</sql>

	<insert id="addBatch" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfPropertyTransferHistory">
		INSERT INTO <include refid="TABLE"/>
		(<include refid="INSERT_FIELDS" />)
		VALUES
		<foreach collection="historyList" item="item" separator=",">
			(#{item.fromUserId}, #{item.toUserId}, #{item.bizId}, #{item.bizType}, #{item.bizTableName})
		</foreach>
	</insert>

	<select id="get" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfPropertyTransferHistory">
		SELECT
		<include refid="SELECT_FIELDS" />
		FROM
		<include refid="TABLE"/>
		WHERE `from_user_id`=#{fromUserId} AND
		`to_user_id`=#{toUserId} AND
		`biz_id`=#{bizId} AND
		`biz_type`=#{bizType} AND
		`is_delete`=0
	</select>
	<select id="getByToUserIdAndBizId"
			resultType="com.shuidihuzhu.cf.model.crowdfunding.CfPropertyTransferHistory">
		SELECT
		<include refid="SELECT_FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE `to_user_id`=#{toUserId}
		and `is_delete`=0
		<if test="bizId != null and bizId!=''">
		AND `biz_id`=#{bizId}
		</if>
	</select>


	<select id="getListByBizTypeAndBizId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfPropertyTransferHistory">
		SELECT
		<include refid="SELECT_FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE
		`biz_type`=#{bizType} AND
		`biz_id`=#{bizId} AND
		`is_delete`=0
	</select>

</mapper>