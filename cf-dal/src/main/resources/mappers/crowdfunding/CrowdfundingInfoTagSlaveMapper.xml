<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingInfoTagSlaveDao">
	<sql id="tableName">
		crowdfunding_info_tag
	</sql>

	<sql id="fields">
		`id`, `info_id`, `tags`, `create_time`, `update_time`, `is_delete`
	</sql>

	<select id="getTagsByInfoId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoTag">
		SELECT
		<include refid="fields"/>
		FROM
		<include refid="tableName"/>
		WHERE
		`info_id`=#{infoId}
		and is_delete = 0
	</select>
	<select id="getAllCaseTags" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoTag">
		SELECT
		<include refid="fields"/>
		FROM
		<include refid="tableName"/>
		where is_delete = 0
	</select>
</mapper>
