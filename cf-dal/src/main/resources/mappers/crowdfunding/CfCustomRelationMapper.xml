<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfCustomRelationDao">
    <sql id="tableName">
		cf_custom_relation
	</sql>

    <sql id="fields">
		`id` as id,
		`case_id` as caseId,
		`show_type` as showType,
		`relation_type` as relationType,
		`relation_desc` as relationDesc,
		`remark` as remark,
		`operate_user_id` as operateUserId,
		`create_time` as createTime
	</sql>

	<select id="getLast" resultType="com.shuidihuzhu.cf.model.CfCustomRelation">
		select <include refid="fields"/>
		from <include refid="tableName"/>
		where case_id = #{caseId}
		and is_delete = 0
		order by id desc
		limit 1
	</select>

	<insert id="insert" parameterType="com.shuidihuzhu.cf.model.CfCustomRelation">
		insert into <include refid="tableName"/>
		(`case_id`,`show_type`,`relation_type`,`relation_desc`,`remark`,`operate_user_id`)
		values(#{caseId},#{showType},#{relationType},#{relationDesc},#{remark},#{operateUserId})
	</insert>

	<select id="findByCaseId" resultType="com.shuidihuzhu.cf.model.CfCustomRelation">
		select <include refid="fields"/>
		from <include refid="tableName"/>
		where case_id = #{caseId}
		and is_delete = 0
	</select>
</mapper>
