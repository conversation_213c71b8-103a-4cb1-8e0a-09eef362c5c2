<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingInfoSlaveDao">
	<sql id="tableName">
		crowdfunding_info
	</sql>

	<sql id="fields">
		`id`, `info_id`, `user_id`,
		`relation`, `applicant_name`, `applicant_qq`, `applicant_mail`, `relation_type`, `channel_type`,`channel`,
		`payee_name`, `payee_id_card`, `payee_mobile`, `payee_bank_name`, `payee_bank_branch_name`, `payee_bank_card`,
		`bank_card_verify_status`,
		`bank_card_verify_message`, `bank_card_verify_message2`, `title`, `title_img`, `content`,
		`target_amount`, `amount`, `donation_count`, `status`, `create_time`, `begin_time`, `end_time`, `from`, `use`,`data_status`,
		`type`, `content_type`, `material_plan_id`, `content_image`, `content_image_status`
	</sql>

	<select id="getCrownfundingSummary" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingSummary">
		SELECT
		sum(`amount`) as totalAmount, sum(`donation_count`) as totalCount
		FROM
		<include refid="tableName"/>
		WHERE
		 	`type` = #{type}
	</select>

    <select id="getListBetween" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
        SELECT
			<include refid="fields"/>
        FROM <include refid="tableName"/>
		WHERE
			`create_time`
		BETWEEN
			#{start}
		AND
			#{end}
    </select>

	<select id="listUserIdBetween" resultType="java.lang.Long">
		SELECT
			`user_id`
		FROM
			<include refid="tableName"/>
		WHERE
			`create_time`
		BETWEEN
			#{start} AND  #{end}
	</select>

    <select id="getFirstByUserId"  resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
        SELECT <include refid="fields"/>
        FROM <include refid="tableName"/>
        WHERE `user_id`=#{userId}
        ORDER BY `create_time` ASC
        LIMIT 1
    </select>

    <select id="getLastByUserId"  resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
        SELECT <include refid="fields"/>
        FROM <include refid="tableName"/>
        WHERE `user_id`=#{userId}
        ORDER BY `create_time` DESC
        LIMIT 1
    </select>

    <select id="selectLastByUserIdAnChannel" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
        SELECT <include refid="fields"/>
        FROM <include refid="tableName"/>
        WHERE `user_id`=#{userId}
        AND `channel`=#{channel}
        AND <![CDATA[ `end_time`>now() ]]>
        ORDER BY `create_time` DESC
        LIMIT 1
    </select>

    <select id="selectWithChannelNotFinishLimit" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
        SELECT <include refid="fields"/>
        FROM <include refid="tableName"/>
        WHERE `channel`=#{channel}
        AND <![CDATA[ `create_time`>#{createTime} ]]>
        AND <![CDATA[ `end_time`>#{endTime} ]]>
        LIMIT #{start},#{size}
    </select>

	<select id="getNotEnd" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		SELECT <include refid="fields"/>
		FROM <include refid="tableName"/>
		WHERE `type`=#{type} AND
		`end_time`>now()
		LIMIT #{offset},#{limit}
	</select>

    <select id="getNotEndByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
        SELECT <include refid="fields"/>
        FROM <include refid="tableName"/>
        WHERE `end_time` > now()
        AND `user_id` = #{userId}
        LIMIT 1
    </select>

    <select id="getNotEndListByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
        SELECT <include refid="fields"/>
        FROM <include refid="tableName"/>
        WHERE `end_time` > now()
        AND `user_id` = #{userId}
    </select>

    <select id="getLastNotEndByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
        SELECT <include refid="fields"/>
        FROM <include refid="tableName"/>
        WHERE `user_id` = #{userId}
        AND `end_time` > now()
        order by id desc
        LIMIT 1
    </select>

    <select id="selectByTimeFindUserId" resultType="java.lang.Long">
        SELECT `user_id`
        FROM <include refid="tableName"/>
        WHERE <![CDATA[ `create_time`>#{begin} ]]>
        AND <![CDATA[ `create_time`<#{end} ]]>
        LIMIT #{start},#{size}
    </select>

    <select id="getByInfoUuidPrefix" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
        SELECT <include refid="fields"/>
        FROM <include refid="tableName"/>
        WHERE `info_id` like CONCAT(#{infoUuidPrefix}, '%')
    </select>

    <select id="selectLatestByUserId" resultType="java.lang.String">
        SELECT `info_id`
        FROM <include refid="tableName"/>
        WHERE `user_id`=#{userId}
        AND <![CDATA[ `end_time`>now() ]]>
        ORDER BY `id` DESC
        LIMIT 1
    </select>

    <select id="selectInfoUuidAndUserIdByTime" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
        SELECT `info_id`,`user_id`,`create_time`
        FROM <include refid="tableName"/>
        WHERE <![CDATA[ `create_time`>#{begin} ]]>
        AND <![CDATA[ `create_time`<#{end} ]]>
        LIMIT #{start},#{size}
    </select>
    
    <select id="selectByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
        SELECT <include refid="fields"/>
        FROM <include refid="tableName"/>
        WHERE `user_id`=#{userId}
    </select>

    <select id="selectByUserIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
        SELECT <include refid="fields"/>
        FROM <include refid="tableName"/>
        WHERE `user_id` in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>

    <select id="selectUserIdWhenFinishCaseByTime" resultType="java.lang.Long">
        SELECT `user_id`
        FROM <include refid="tableName"/>
        WHERE <![CDATA[ `end_time`<now() ]]>
        AND <![CDATA[ `create_time`>#{begin} ]]>
        AND <![CDATA[ `create_time`<#{end} ]]>
        LIMIT #{start},#{size}
    </select>
    
    <select id="selectUserIdByThirdTypeWhenNotFinishCase" resultType="java.lang.Long">
        SELECT `user_id`
        FROM (SELECT `user_id`,`info_id` FROM <include refid="tableName"/> WHERE <![CDATA[ `end_time`>now() ]]>) ci
        LEFT JOIN `cf_info_ext` cie ON ci.`info_id`=cie.`info_uuid`
        WHERE cie.`user_third_type`=#{thirdType}
        LIMIT #{start},#{size}
    </select>

    <select id="getFundingInfo" parameterType="String" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
        SELECT
        <include refid="fields"/>
        FROM
        <include refid="tableName"/>
        WHERE info_id = #{infoId}
    </select>

    <select id="getFundingInfoById" parameterType="int" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
        SELECT
        <include refid="fields"/>
        FROM
        <include refid="tableName"/>
        WHERE id = #{id}
    </select>

    <select id="selectIdAndUuidAndTypeByInfoUuid" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
        SELECT `id`,`info_id`,`type`
        FROM <include refid="tableName"/>
        WHERE `info_id` IN
        <foreach collection="set" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getCrowdfundingInfoByUserIdAndChannel"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
        select <include refid="fields"/>
        from <include refid="tableName"/>
        where user_id = #{userId}
        <if test="channels != null and channels.size() > 0">
            and `channel` in
            <foreach collection="channels" item="channel" open="(" separator="," close=")">
                #{channel}
            </foreach>
        </if>
    </select>
    <select id="getLastCrowdfundedByUserId"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
        SELECT <include refid="fields"/>
        FROM <include refid="tableName"/>
        WHERE `user_id`=#{userId} and amount <![CDATA[ > ]]> 0
        ORDER BY `create_time` DESC
        LIMIT 1
    </select>

    <select id="getIdByUserIdAndCreateTime" resultType="java.lang.Integer">
        SELECT id
        FROM <include refid="tableName"/>
        WHERE `user_id` = #{userId} AND `create_time` &gt;= #{begin} AND `create_time` &lt; #{end}
        LIMIT 1
    </select>

    <select id="getIdsByEndTime" resultType="java.lang.Integer">
        SELECT id
        FROM <include refid="tableName"/>
        WHERE
        id > #{id}
        AND
        end_time > #{endTime}
        ORDER BY id LIMIT #{limit}
    </select>

    <select id="listNotEndCaseIdOfUser" resultType="java.lang.Integer">
        SELECT `id`
        FROM
        <include refid="tableName"/>
        WHERE `user_id` = #{userId} AND `end_time` > now()
    </select>
</mapper>