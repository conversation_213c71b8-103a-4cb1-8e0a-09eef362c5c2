<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.combine.CfContributeOrderDao">

    <sql id="table_name">cf_contribute_order</sql>

    <insert id="addContributeOrder" keyColumn="id" keyProperty="id"
            parameterType="com.shuidihuzhu.cf.model.contribute.CfContributeOrder"
            useGeneratedKeys="true">
        insert into
        <include refid="table_name"/>
        (`case_id`, `user_id`, `anonymous`, `pre_pay_amount`, `pay_uid`, `contribute_channel`, `contribute_source_id`,
         `pay_platform`, `ip`, `os_type`, `self_tag`, `user_third_type`, `product_name`, client_id)
        values (#{caseId}, #{userId}, #{anonymous}, #{prePayAmount}, #{payUid}, #{contributeChannel}, #{contributeSourceId},
         #{payPlatform}, #{ip}, #{osType}, #{selfTag}, #{userThirdType}, #{productName}, #{clientId})
    </insert>

    <update id="updatePaySuccess">
        update
        <include refid="table_name"/>
        set third_pay_uid = #{thirdPayUid},
        callback_time = #{callbackTime},
        real_pay_amount = #{realPayAmount},
        order_status = #{orderStatus},
        fee_amount = #{feeAmount}
        where pay_uid = #{payUid}
    </update>

    <select id = "selectByPayUids" resultType="com.shuidihuzhu.cf.model.contribute.CfContributeOrder">
        select *
        from <include refid="table_name"/>
        where pay_uid in
        <foreach collection="payUids" item="payUid" open="(" close=")" separator=",">
            #{payUid}
        </foreach>
        and is_delete = 0
    </select>

    <select id = "selectByThirdPayUids" resultType="com.shuidihuzhu.cf.model.contribute.CfContributeOrder">
        select *
        from <include refid="table_name"/>
        where third_pay_uid in
        <foreach collection="thirdPayUids" item="thirdPayUid" open="(" close=")" separator=",">
            #{thirdPayUid}
        </foreach>
        and is_delete = 0
    </select>

    <select id = "selectByUserIds" resultType="com.shuidihuzhu.cf.model.contribute.CfContributeOrder">
        select *
        from <include refid="table_name"/>
        where user_id in
        <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
            #{userId}
        </foreach>
        <if test="statusList != null and statusList.size() > 0">
            and order_status in
            <foreach collection="statusList" item="status" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        and is_delete = 0
    </select>

    <select id = "selectByCaseId" resultType="com.shuidihuzhu.cf.model.contribute.CfContributeOrder">
        select *
        from <include refid="table_name"/>
        where case_id = #{caseId}
        and is_delete = 0
    </select>

    <select id = "selectByCaseIdTimeRange" resultType="com.shuidihuzhu.cf.model.contribute.CfContributeOrder">
        select *
        from <include refid="table_name"/>
        FORCE index(idx_case_id)
        where case_id = #{caseId}
        and callback_time > #{callBackBeginTime}
        and <![CDATA[ `id`<#{callBackEndTime} ]]>
        and order_status != 0
        and is_delete = 0
    </select>

    <select id = "selectTotalInfoByCaseIdTimeRange" resultType="com.shuidihuzhu.cf.model.contribute.CfContributeTotalInfo">
        select count(1) as total, sum(real_pay_amount) as totalAmount, sum(fee_amount) as totalFeeAmount
        from <include refid="table_name"/>
        FORCE index(idx_case_id)
        where case_id = #{caseId}
        and callback_time > #{callBackBeginTime}
        and <![CDATA[ `id`<#{callBackEndTime} ]]>
        and order_status != 0
        and is_delete = 0
    </select>

    <update id = "updateOrderStatus">
        update
        <include refid="table_name"/>
        set order_status = #{toStatus}
        where pay_uid = #{payUid}
    </update>

    <select id = "selectByIds" resultType="com.shuidihuzhu.cf.model.contribute.CfContributeOrder">
        select *
        from <include refid="table_name"/>
        where id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        and is_delete = 0
    </select>


    <select id = "selectByUserIdLimit" resultType="com.shuidihuzhu.cf.model.contribute.CfContributeOrder">
        select *
        from <include refid="table_name"/>
        FORCE index(idx_user_id)
        where user_id = #{userId}
        and <![CDATA[ `id`<#{anchorId} ]]>
        and order_status != 0
        and is_delete = 0
        order by id DESC
        limit #{limit}
    </select>
    <select id="getList" resultType="com.shuidihuzhu.cf.model.contribute.CfContributeOrder">
        select *
        from <include refid="table_name"/>
        where is_delete = 0
        <if test="caseId >= 0">
            and case_id = #{caseId}
        </if>
        <if test="donateOrderId >= 0">
            and contribute_source_id = #{donateOrderId}
        </if>
        <if test="callbackEnd != null and callbackStart != null">
            and callback_time between #{callbackStart} and #{callbackEnd}
        </if>
        <if test="contributeType != null and contributeType != ''">
            and contribute_channel = #{contributeType}
        </if>
        <if test="orderStatus > -1">
            and order_status = #{orderStatus}
        </if>
        <if test="payAmount >= 0">
            and real_pay_amount = #{payAmount}
        </if>
        <if test="thirdPayUid != null and thirdPayUid != ''">
            and third_pay_uid = #{thirdPayUid}
        </if>

        <choose>
            <when test="pageType != null and pageType != '' and id > 0">
                <choose>
                    <when test="pageType == 'next'">
                        and id &lt; #{id}
                        order by id desc
                    </when>
                    <otherwise>
                        and id &gt; #{id}
                        order by id
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                order by id desc
            </otherwise>
        </choose>
        limit #{pageSize}
    </select>


    <select id = "selectBySourceChannel" resultType="com.shuidihuzhu.cf.model.contribute.CfContributeOrder">
        select *
        from <include refid="table_name"/>
        where
        `contribute_channel` = #{sourceChannel}
        and
         `contribute_source_id` in
        <foreach collection="sourceIds" item="sourceId" open="(" close=")" separator=",">
            #{sourceId}
        </foreach>
        and is_delete = 0
    </select>


    <update id = "updateUserByIds">
        update
        <include refid="table_name"/>
        set user_id = #{toUserId}
        where id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>

    </update>


    <select id = "selectContributeUserId" resultType="java.lang.Long">
         select distinct user_id
         from cf_contribute_order
         FORCE index(PRIMARY)
         where  order_status = #{orderStatus}
         <if test="excludeUserIds != null and excludeUserIds.size() > 0">
             and user_id not in
             <foreach collection="excludeUserIds" open="(" close=")" item="userId" separator=",">
                 #{userId}
             </foreach>
         </if>
         order by id desc limit #{limit};
    </select>

</mapper>