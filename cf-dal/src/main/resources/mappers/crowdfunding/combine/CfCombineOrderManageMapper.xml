<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.combine.CfCombineOrderManageDao">

    <sql id="table_name">cf_combine_order_manage</sql>

    <insert id="addCombineOrderList" parameterType="com.shuidihuzhu.cf.model.contribute.CfCombineOrderManage">
        insert into
        <include refid="table_name"/>
        (`parent_pay_uid`, `sub_pay_uid`, `order_type`, case_id, order_id, main, biz_type)
        values
        <foreach collection="manageList" item="item" separator=",">
            (#{item.parentPayUid}, #{item.subPayUid}, #{item.orderType}, #{item.caseId}, #{item.orderId}, #{item.main}, #{item.bizType})
        </foreach>

    </insert>

    <update id="updatePayStatus">
        update
        <include refid="table_name"/>
        set parent_third_pay_uid = #{parentThirdUid},
        `sub_third_pay_uid` = #{subThirdPayUid}
        where sub_pay_uid = #{subPayUid}
    </update>

    <select id="selectByParentUids" resultType="com.shuidihuzhu.cf.model.contribute.CfCombineOrderManage">
        select *
        from <include refid="table_name"/>
        where  parent_pay_uid in
        <foreach collection="parentPayUids" item="parentPayUid" open="(" close=")" separator=",">
            #{parentPayUid}
        </foreach>
        and is_delete = 0
    </select>

    <select id="selectBySubUids" resultType="com.shuidihuzhu.cf.model.contribute.CfCombineOrderManage">
        select *
        from <include refid="table_name"/>
        where  sub_pay_uid in
        <foreach collection="subPayUids" item="subPayUid" open="(" close=")" separator=",">
            #{subPayUid}
        </foreach>
        and is_delete = 0
    </select>
    <select id="getByParentThirdPayUidAndOrderType"
            resultType="com.shuidihuzhu.cf.model.contribute.CfCombineOrderManage">
        select *
        from <include refid="table_name"/>
        where parent_third_pay_uid = #{parentThirdPayUid} and order_type = #{orderType}
        and is_delete = 0
        limit 1
    </select>

    <select id="selectBySubThirdPayUid" resultType="com.shuidihuzhu.cf.model.contribute.CfCombineOrderManage">
        select *
        from <include refid="table_name"/>
        where  sub_third_pay_uid in
        <foreach collection="subThirdPayUids" item="subThirdPayUid" open="(" close=")" separator=",">
            #{subThirdPayUid}
        </foreach>
        and is_delete = 0
    </select>
    <select id="getByOrderId" resultType="com.shuidihuzhu.cf.model.contribute.CfCombineOrderManage">
        select *
        from <include refid="table_name"/>
        where order_id = #{orderId}
        and is_delete = 0
    </select>

</mapper>