<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.UserRealInfoDao">
	<sql id="tableName">
		user_real_info
	</sql>

	<insert id="save" parameterType="com.shuidihuzhu.cf.model.crowdfunding.UserRealInfo" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO <include refid="tableName" />
		(`user_id`,`name`,`crypto_id_card`,`idcard_verify_status`,`order_id`)
		VALUES
		(#{userId},#{name},#{cryptoIdCard},#{idcardVerifyStatus},#{orderId})
	</insert>

	<select id="getCountByCreateTime" resultType="java.lang.Long">
		SELECT COUNT(*)
		FROM <include refid="tableName" />
		WHERE `user_id`=#{userId} AND <![CDATA[ date_created>=#{startTime} and date_created<#{endTime} ]]> AND is_delete = 0
	</select>
	
	<select id="getByVerifyStatus" resultType="com.shuidihuzhu.cf.model.crowdfunding.UserRealInfo">
		SELECT *
		FROM <include refid="tableName" />
		WHERE `user_id`=#{userId} 
		AND `idcard_verify_status` in 
		<foreach collection="idcardVerifyStatusList" open="(" separator="," close=")" item="idcardVerifyStatus">
			#{idcardVerifyStatus}
		</foreach>
		AND is_delete = 0
		ORDER BY id DESC
	</select>
	
	<select id="getLastOne" resultType="com.shuidihuzhu.cf.model.crowdfunding.UserRealInfo">
		SELECT *
		FROM <include refid="tableName" />
		WHERE `user_id`=#{userId}
		AND is_delete = 0
		ORDER BY id DESC
		LIMIT 1
	</select>
	
	<select id="getById" resultType="com.shuidihuzhu.cf.model.crowdfunding.UserRealInfo">
		SELECT *
		FROM <include refid="tableName" />
		WHERE `id`=#{id}
	</select>

	<select id="getByCryptoIdCard" resultType="com.shuidihuzhu.cf.model.crowdfunding.UserRealInfo">
		SELECT *
		FROM <include refid="tableName" />
		WHERE `crypto_id_card`=#{cryptoIdCard} AND is_delete = 0 limit 1
	</select>

	<select id="getSuccessByUserIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.UserRealInfo">
		SELECT *
		FROM <include refid="tableName" />
		WHERE `user_id` in <foreach collection="userIds" open="(" separator="," close=")" item="userId">#{userId}</foreach>
		AND `idcard_verify_status`=2
		AND is_delete = 0
	</select>

	<update id="updateIdcardVerifyStatus">
		UPDATE <include refid="tableName" />
		SET `idcard_verify_status`=#{idcardVerifyStatus},`result_code`=#{resultCode},`result_msg`=#{resultMsg}
		WHERE `id`=#{id}
	</update>
	
	<select id="getHandlingInfo" resultType="com.shuidihuzhu.cf.model.crowdfunding.UserRealInfo">
		SELECT *
		FROM <include refid="tableName" />
		WHERE `idcard_verify_status`=1
		AND is_delete = 0
		LIMIT #{from},#{limit}
	</select>

	<select id="getByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.UserRealInfo">
		SELECT *
		FROM <include refid="tableName" />
		where user_id = #{userId}
		and is_delete = 0
	</select>

	<update id="updateMedicalStatus" >
		UPDATE <include refid="tableName" />
		SET `medical_status`=#{medicalStatus}
		where user_id = #{userId}
		and idcard_verify_status = 2
		and is_delete = 0
	</update>

	<update id="updateMedicalImageUrl">
		UPDATE <include refid="tableName" />
		SET `medical_image_url`=#{medicalImageUrl}
		where user_id = #{userId}
		and idcard_verify_status = 2
		and is_delete = 0
	</update>

	<update id="updateIsMedicalById" >
		UPDATE <include refid="tableName" />
		SET `medical_status`=#{medicalStatus}
		where id = #{id}
		and is_delete = 0
	</update>

	<update id ="deleteById">
		UPDATE
		<include refid="tableName" />
		SET
		is_delete = 1
		WHERE id = #{id}
	</update>

	<select id="getOnceAllSuccess" resultType="com.shuidihuzhu.cf.model.crowdfunding.UserRealInfo">
		SELECT *
		FROM <include refid="tableName" />
		WHERE `user_id` = #{userId}
		AND `idcard_verify_status`=2
		ORDER BY id desc
	</select>


	<select id="getByUserIdAndIdCard" resultType="com.shuidihuzhu.cf.model.crowdfunding.UserRealInfo">
		SELECT *
		FROM <include refid="tableName" />
		WHERE
		is_delete = 0
		<if test="userId > 0">
		    AND `user_id` = #{userId}
		</if>
		<if test="cryptoIdCard != null and cryptoIdCard != ''">
			AND `crypto_id_card` = #{cryptoIdCard}
		</if>
		AND `idcard_verify_status`=2
		ORDER BY id desc
	</select>

</mapper>