<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.ICfUserLabelDAO">

    <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.model.crowdfunding.CfUserLabelDO">
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="case_num" property="caseNum" jdbcType="INTEGER"/>
        <result column="draft" property="draft" jdbcType="TINYINT"/>
        <result column="toufang_channel" property="toufangChannel" jdbcType="TINYINT"/>
        <result column="channel_str" property="channelStr" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="is_delete" property="delete" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="TABLE_NAME">
        cf_user_label
    </sql>

    <sql id="Base_Column_List">
		id,user_id,case_num,draft,toufang_channel,channel_str,create_time,update_time,is_delete
	</sql>

    <update id="insertLabel" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfUserLabelDO" keyProperty="id" useGeneratedKeys="true">
        insert into <include refid="TABLE_NAME"/>
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="userId != null">
                user_id,
            </if>
            <if test="caseNum != null">
                case_num,
            </if>
            <if test="draft != null">
                draft,
            </if>
            <if test="toufangChannel != null">
                toufang_channel,
            </if>
            <if test="channelStr != null">
                channel_str,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="userId != null">
                #{userId, jdbcType=BIGINT},
            </if>
            <if test="caseNum != null">
                #{caseNum, jdbcType=INTEGER},
            </if>
            <if test="draft != null">
                #{draft, jdbcType=TINYINT},
            </if>
            <if test="toufangChannel != null">
                #{toufangChannel, jdbcType=INTEGER},
            </if>
            <if test="channelStr != null">
                #{channelStr, jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime, jdbcType=TIMESTAMP}
            </if>
        </trim>
        on duplicate key update
        <trim prefix="" suffix="" suffixOverrides="," >
            <if test="userId != null">
                user_id = values(user_id),
            </if>
            <if test="caseNum != null">
                case_num = values(case_num),
            </if>
            <if test="draft != null">
                draft = values(draft),
            </if>
            <if test="toufangChannel != null">
                toufang_channel = values(toufang_channel),
            </if>
            <if test="channelStr != null">
                channel_str = values(channel_str),
            </if>
            <if test="updateTime != null">
                update_time = values(update_time),
            </if>
        </trim>
    </update>

    <update id="incrCaseNum">
        update <include refid="TABLE_NAME"/>
        set case_num = case_num + 1
        where user_id = #{userId}
    </update>

    <update id="updateDraft">
        update <include refid="TABLE_NAME"/>
        set draft = #{draft}
        where user_id = #{userId}
    </update>

    <select id="queryLabelByUser" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="TABLE_NAME" />
        WHERE user_id = #{userId}
    </select>
</mapper>