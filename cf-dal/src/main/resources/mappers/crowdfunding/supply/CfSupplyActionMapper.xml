<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.supply.CfSupplyActionDao">

    <sql id="table_name">
        cf_info_supply_action
    </sql>

    <sql id="select_fields">
        `id`,
        `case_id` as caseId,
        `action_type` as actionType,
        `handle_status` as handleStatus,
        `supply_user_id` as supplyUserId,
        `supply_org_id` as supplyOrgId,
        `supply_org_name` as supplyOrgName,
        `supply_reason` as supplyReason,
        `comment` as comment,
        `create_time` as createTime,
        `update_time` as updateTime
    </sql>


    <select id="getById" resultType="com.shuidihuzhu.cf.model.CfInfoSupplyAction">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE `id` = #{id}
    </select>

</mapper>