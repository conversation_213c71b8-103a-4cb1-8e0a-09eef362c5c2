<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.supply.CfSupplyProgressDao">

    <sql id="table_name">
        cf_info_supply_progress
    </sql>


    <sql id="select_fields">
        `id`,
        `user_id`,
        `case_id`,
        `info_uuid`,
        `progress_action_id`,
        `progress_id`,
        `content`,
        `img_urls`,
        `content_status`,
        `img_status`,
        `create_time`,
        `update_time`,
        `is_delete`
    </sql>


    <select id="getById" resultType="com.shuidihuzhu.cf.model.CfInfoSupplyProgress">
        SELECT *
        FROM
        <include refid="table_name"/>
        WHERE `id` = #{id}
    </select>

    <select id="getByActionId" resultType="com.shuidihuzhu.cf.model.CfInfoSupplyProgress">
        SELECT *
        FROM
        <include refid="table_name"/>
        WHERE `progress_action_id` = #{progressActionId}
    </select>

</mapper>