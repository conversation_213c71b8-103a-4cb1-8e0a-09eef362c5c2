<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
CREATE TABLE `crowdfunding_author` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(32) NOT NULL COMMENT '筹款申请人姓名',
  `crypto_id_card` varchar(50) DEFAULT NULL COMMENT '加密身份证',
  `crypto_phone` varchar(50) DEFAULT NULL COMMENT '加密手机号',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `mofify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最近一次修改时间',
  `status` smallint(11) DEFAULT NULL COMMENT '状态：1-有效，2-无效',
  `id_type` smallint(11) DEFAULT '0' COMMENT '身份证件类型：0-身份证，1-出生证',
  `mail` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `qq` varchar(20) DEFAULT NULL COMMENT 'qq',
  `crowdfunding_id` int(11) NOT NULL COMMENT 'crowdfunding_info的Id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=451 DEFAULT CHARSET=utf8mb4 COMMENT='筹款发起人基本信息表';

todo V3 上线前请在正式库上执行:

UPDATE crowdfunding_author SET status = 0 WHERE status IS NULL;
UPDATE crowdfunding_author SET crypto_id_card = '' WHERE crypto_id_card IS NULL;

ALTER TABLE crowdfunding_author
MODIFY COLUMN `crypto_id_card` varchar(50) NOT NULL DEFAULT '' COMMENT '加密身份证',
MODIFY COLUMN `status` SMALLINT NOT NULL DEFAULT 0 COMMENT '(已不再使用)状态：1-有效，2-无效',
MODIFY COLUMN `id_type` SMALLINT NOT NULL DEFAULT 0 COMMENT '身份证件类型：0-身份证，1-出生证',
MODIFY COLUMN `create_time` TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
DROP COLUMN `mofify_time`,
ADD COLUMN `last_modified` TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '最后修改时间',
ADD INDEX ix_crowdfunding_id (crowdfunding_id);

-->
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingAuthorDao">
	<sql id="tableName">
		`crowdfunding_author`
	</sql>

	<sql id="selectFields">
		`id`,`crowdfunding_id`,`name`,`crypto_id_card`,`crypto_phone`,`id_type`,`mail`,`qq`,`health_insurance`,`commercial_insurance`,
		`birth_year`, `birth_month`, `birth_day`, `province_code`, `city_code`, `district_code`, `gender`, `relation`,
		`create_time`,`last_modified`,face_id_result
	</sql>

	<select id="get" parameterType="int" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor">
		SELECT
		<include refid="selectFields"/>
		FROM
		<include refid="tableName"/>
		WHERE
		crowdfunding_id=#{crowdfundingId}
	</select>
	
	<select id="getByInfoIdList" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor">
		SELECT
		<include refid="selectFields"/>
		FROM
		<include refid="tableName"/>
		WHERE
		crowdfunding_id in
		<foreach collection="infoIdList" item="crowdfundingId" open="(" separator="," close=")">
			#{crowdfundingId}
		</foreach>
	</select>

	<update id="update" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor">
		UPDATE <include refid="tableName"/>
		SET `name`=#{name},
		`crypto_id_card`=#{cryptoIdCard},
		`crypto_phone`=#{cryptoPhone},
		`status`=#{status},
		`id_type`=#{idType, typeHandler=org.apache.ibatis.type.EnumOrdinalTypeHandler, javaType=com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType},
		`mail`=#{mail},
		`qq`=#{qq},
		`health_insurance`=#{healthInsurance},
		`commercial_insurance`=#{commercialInsurance},
		`birth_year`=#{birthYear},
		`birth_month`=#{birthMonth},
		`birth_day`=#{birthDay},
		`province_code`=#{provinceCode},
		`city_code`=#{cityCode},
		`district_code`=#{districtCode},
		`gender`=#{gender},
		`relation`=#{relation}
		,face_id_result=#{faceIdResult}
		WHERE
		`crowdfunding_id`=#{crowdfundingId}
	</update>

	<select id="getByName" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor">
		SELECT <include refid="selectFields"/>
		FROM
		<include refid="tableName"/>
		WHERE
		name = #{name}
	</select>

	<insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO
		<include refid="tableName"/>
		(`name`,
			`crypto_id_card`,
			`crypto_phone`,
			`status`,
			`id_type`,
			`mail`,
			`qq`,
			`crowdfunding_id`,
			`health_insurance`,
			`commercial_insurance`,
			`birth_year`,
			`birth_month`,
			`birth_day`,
			`province_code`,
			`city_code`,
			`district_code`,
			`gender`,
			`relation`,
			face_id_result
		) VALUES (
			#{name},
			#{cryptoIdCard},
			#{cryptoPhone},
			#{status},
			#{idType, typeHandler=org.apache.ibatis.type.EnumOrdinalTypeHandler, javaType=com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType},
			#{mail},
			#{qq},
			#{crowdfundingId},
			#{healthInsurance},
			#{commercialInsurance},
			#{birthYear},
			#{birthMonth},
			#{birthDay},
			#{provinceCode},
			#{cityCode},
			#{districtCode},
			#{gender},
			#{relation},
			#{faceIdResult}
		)
	</insert>

	<update id="updateIdentity" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor">
		UPDATE <include refid="tableName"/>
		SET `name`=#{name},
		`crypto_id_card`=#{cryptoIdCard},
		`crypto_phone`=#{cryptoPhone},
		`status`=#{status},
		`id_type`=#{idType, typeHandler=org.apache.ibatis.type.EnumOrdinalTypeHandler, javaType=com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType},
		`mail`=#{mail},
		`qq`=#{qq},
		`birth_year`=#{birthYear},
		`birth_month`=#{birthMonth},
		`birth_day`=#{birthDay},
		`province_code`=#{provinceCode},
		`city_code`=#{cityCode},
		`district_code`=#{districtCode},
		`gender`=#{gender},
		`relation`=#{relation}
		,face_id_result=#{faceIdResult}
		WHERE
		`crowdfunding_id`=#{crowdfundingId}
	</update>

	<select id="getApplyCount" resultType="int">
		SELECT count(*)
		FROM <include refid="tableName"/>
		WHERE `name` = #{name} AND (`create_time` BETWEEN #{startTime} AND #{endTime})
	</select>

	<update id="updateInsurance" >
		UPDATE <include refid="tableName"/>
		SET
		`health_insurance`=#{healthInsurance},
		`commercial_insurance`=#{commercialInsurance}
		WHERE
		`crowdfunding_id`= #{caseId}
	</update>

	<select id="getByCryptoIdCards" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor">
		SELECT <include refid="selectFields"/>
		FROM
		<include refid="tableName"/>
		WHERE
		crypto_id_card
		IN
		<foreach collection="list" item="item" open="(" close=")" separator=",">
			#{item}
		</foreach>
	</select>


	<select id="getCaseIdByCryptoIdCards" resultType="java.lang.Integer">
		SELECT crowdfunding_id
		FROM
		<include refid="tableName"/>
		WHERE
		crypto_id_card
		IN
		<foreach collection="list" item="item" open="(" close=")" separator=",">
			#{item}
		</foreach>
	</select>

	<select id="getByNameAndCreateTime" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor">
		SELECT <include refid="selectFields"/>
		FROM
		<include refid="tableName"/>
		WHERE
		name = #{name} and create_time > #{createTime}
	</select>

</mapper>
