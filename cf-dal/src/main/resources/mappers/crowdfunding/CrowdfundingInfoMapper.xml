<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingInfoDao">
	<sql id="tableName">
		crowdfunding_info
	</sql>

    <sql id="fields">
		`id`, `info_id`, `user_id`,
		`relation`, `applicant_name`, `applicant_qq`, `applicant_mail`, `relation_type`, `channel_type`,`channel`,
		`payee_name`, `payee_id_card`, `payee_mobile`, `payee_bank_name`, `payee_bank_branch_name`, `payee_bank_card`,
		`bank_card_verify_status`,
		`bank_card_verify_message`, `bank_card_verify_message2`, `title`, `title_img`, `content`, `encrypt_content`,
		`target_amount`, `amount`, `donation_count`, `status`, `create_time`, `begin_time`, `end_time`, `from`, `use`,`data_status`,
		`type`, `content_type`, `material_plan_id`, `content_image`, `content_image_status`
	</sql>

	<sql id="simple_fields">
		`id`, `info_id`, `user_id`, `target_amount`, `amount`, `create_time`, `begin_time`, `end_time`
	</sql>

	<select id="getApproveList" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		SELECT
		<include refid="fields"/>
		FROM
		<include refid="tableName"/>
		WHERE
		status = #{status}
		<if test="applicantName != null and applicantName != ''">
			<bind name="applicantNamePattern" value="'%' + applicantName + '%'"/>
			AND applicant_name LIKE #{applicantNamePattern}
		</if>
		<if test="title != null and title != ''">
			<bind name="titlePattern" value="'%' + title + '%'"/>
			AND title LIKE #{titlePattern}
		</if>
		ORDER BY create_time DESC
		LIMIT #{size} OFFSET #{offset}
	</select>

	<select id="getFundingInfo" parameterType="String"
	        resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		SELECT
		<include refid="fields"/>
		FROM
		<include refid="tableName"/>
		WHERE
		info_id = #{infoId}
	</select>

	<select id="getCountByUserId" resultType="java.lang.Integer">
		SELECT COUNT(*) FROM crowdfunding_info WHERE `user_id`=#{userId}
	</select>

	<select id="getFundingInfoById" parameterType="int" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		SELECT
		<include refid="fields"/>
		FROM
		<include refid="tableName"/>
		WHERE id = #{id}
	</select>

	<select id="getFundingInfoByIdFromSlave" parameterType="int" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		SELECT
		<include refid="fields"/>
		FROM
		<include refid="tableName"/>
		WHERE id = #{caseId}
	</select>

	<select id="getByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		SELECT
		<include refid="fields"/>
		FROM
		<include refid="tableName"/>
		WHERE user_id = #{userId}
		ORDER BY id desc
	</select>

	<update id="addAmount">
		UPDATE
		<include refid="tableName"/>
		SET
		amount = amount + #{amount},
		donation_count = donation_count + 1
		WHERE id = #{id}
	</update>

	<update id="subtractAmount">
		UPDATE
		<include refid="tableName"/>
		SET
		amount = amount - #{amount}
		WHERE id = #{id}
	</update>

	<insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO
		<include refid="tableName"/>
			(`user_id`,`info_id`,`relation`,`relation_type`,`channel_type`,`channel`,`payee_name`,`payee_id_card`,`payee_mobile`,
			`payee_bank_name`,`payee_bank_branch_name`,`payee_bank_card`,`bank_card_verify_status`,`applicant_name`,`applicant_qq`,
			`applicant_mail`,`title`,`title_img`,`content`,`encrypt_content`,`type`,`target_amount`,`status`,`end_time`,`from`,`use`,`data_status`,
			`content_type`, `material_plan_id`, `create_time`)
		VALUES
			(#{userId},#{infoId},#{relation},
			#{relationType, typeHandler=org.apache.ibatis.type.EnumOrdinalTypeHandler, javaType=com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingRelationType},
			#{channelType, typeHandler=org.apache.ibatis.type.EnumTypeHandler},
			#{channel},#{payeeName},#{payeeIdCard},#{payeeMobile},#{payeeBankName},#{payeeBankBranchName},#{payeeBankCard},
			#{bankCardVerifyStatus},#{applicantName},#{applicantQq},#{applicantMail},#{title},#{titleImg},#{content},#{encryptContent},#{type},
			#{targetAmount},#{status},#{endTime},#{from},#{use},#{dataStatus},#{contentType}, #{materialPlanId}, #{createTime})
	</insert>

	<update id="update" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo" useGeneratedKeys="true" keyProperty="id">
		UPDATE
		<include refid="tableName"/>
		SET
		`user_id`=#{userId},
		`relation`=#{relation},
		`relation_type`=#{relationType, typeHandler=org.apache.ibatis.type.EnumOrdinalTypeHandler, javaType=com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingRelationType},
		`channel_type`=#{channelType, typeHandler=org.apache.ibatis.type.EnumTypeHandler},
		`channel`=#{channel},
		`payee_name`=#{payeeName},
		`payee_id_card`=#{payeeIdCard},
		`payee_mobile`=#{payeeMobile},
		`payee_bank_name`=#{payeeBankName},
		`payee_bank_branch_name`=#{payeeBankBranchName},
		`payee_bank_card`=#{payeeBankCard},
		`bank_card_verify_status`=#{bankCardVerifyStatus},
		`applicant_name`=#{applicantName},
		`applicant_qq`=#{applicantQq},
		`applicant_mail`=#{applicantMail},
		`title`=#{title},
		`title_img`=#{titleImg},
		`content`=#{content},
		`encrypt_content`=#{encryptContent},
		`target_amount`=#{targetAmount},
		`amount`=#{amount},
		`donation_count`=#{donationCount},
		`status`=#{status},
		`from`=#{from},
		`use`=#{use},
		`content_type`=#{contentType}
		WHERE info_id = #{infoId}
	</update>

	<update id="updatePayeeInfo" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo" useGeneratedKeys="true" keyProperty="id">
		UPDATE
		<include refid="tableName"/>
		SET
		`payee_name` = #{payeeName},
		`payee_id_card` = #{payeeIdCard},
		`payee_bank_name` = #{payeeBankName},
		`payee_bank_branch_name` = #{payeeBankBranchName},
		`payee_bank_card` = #{payeeBankCard},
		`payee_mobile` = #{payeeMobile},
		`relation` = #{relation},
		`relation_type` = #{relationType, typeHandler=org.apache.ibatis.type.EnumOrdinalTypeHandler, javaType=com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingRelationType}
		WHERE info_id = #{infoId}
	</update>
	
	<update id="updateTitle" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		UPDATE
		<include refid="tableName"/>
		SET
		`title` = #{title}
		WHERE info_id = #{infoId}
	</update>

	<update id="updateBaseInfo" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		UPDATE
		<include refid="tableName"/>
		SET
		`payee_name` = #{payeeName},
		`title_img` = #{titleImg}
		WHERE info_id = #{infoId}
	</update>

	<update id="updateRelationType" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		UPDATE
		<include refid="tableName"/>
		SET
		`relation_type` = #{relationType, typeHandler=org.apache.ibatis.type.EnumOrdinalTypeHandler, javaType=com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingRelationType}
		WHERE info_id = #{infoId}
	</update>

	<update id="updateUserId" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		UPDATE
		<include refid="tableName"/>
		SET
		`user_id` = #{userId}
		WHERE info_id = #{infoId}
	</update>

	<update id="updateBaseInfoV2" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		UPDATE
		<include refid="tableName"/>
		SET
		`user_id` = #{userId},
		`channel_type` = #{channelType, typeHandler=org.apache.ibatis.type.EnumTypeHandler},
		`channel` = #{channel},
		`target_amount`= #{targetAmount}
		WHERE info_id = #{infoId}
	</update>

	<update id="updateFromStageInfo" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		UPDATE
		<include refid="tableName"/>
		SET
		`title` = #{title},
		`content` = #{content},
		`encrypt_content` = #{encryptContent},
		`title_img`= #{titleImg}
		WHERE info_id = #{infoId}
	</update>

	<update id="adjustAmountFromOrders">
		UPDATE
		<include refid="tableName"/>
		SET
		`amount`=#{amount}
		WHERE id = #{id}
	</update>

	<update id="doApprove">
		UPDATE
		<include refid="tableName"/>
		SET status = #{status}, begin_time = #{beginTime}
		WHERE id = #{crowdfundingId}
	</update>

	<select id="getFundingInfoList" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		SELECT <include refid="fields"/>
		FROM <include refid="tableName"/>
		where <![CDATA[ amount>=3000000 and `end_time`<now() ]]>
		order by begin_time desc limit 30
	</select>

	<update id="updateEndTime">
		UPDATE
		<include refid="tableName"/>
		SET `end_time`=#{endTime}
		WHERE id = #{id}
	</update>

	<update id="updateDataStatus">
		UPDATE
		<include refid="tableName"/>
		SET `data_status`=#{dataStatus}
		WHERE id = #{id}
	</update>

	<update id="updateType">
		UPDATE
		<include refid="tableName"/>
		SET `type`=#{type}
		WHERE id = #{id}
	</update>

	<update id="updateStatus">
		UPDATE
		<include refid="tableName"/>
		SET `status`=#{newStatus}
		WHERE id=#{id} AND `status`=#{oldStatus}
	</update>

	<update id="updateVerifyStatus">
		UPDATE
		<include refid="tableName"/>
		SET `bank_card_verify_status`=#{verifyStatus},
		`bank_card_verify_message`=#{bankCardVerifyMessage},
		`bank_card_verify_message2`=#{bankCardVerifyMessage2}
		WHERE
		`id`=#{id}
	</update>

	<select id="getFundingInfoByIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		SELECT <include refid="fields"/>
		FROM <include refid="tableName"/>
		WHERE 1=1 and id in
		<foreach item="item" index="index" collection="ids"
				 open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<select id="getFundingInfoByInfoIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		SELECT <include refid="fields"/>
		FROM <include refid="tableName"/>
		WHERE 1=1 and info_id in
		<foreach item="item" index="index" collection="infoIds"
				 open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<select id="getByCreateTime" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		SELECT <include refid="fields"/>
		FROM <include refid="tableName"/>
		WHERE <![CDATA[ `create_time`>=#{startTime} and `create_time`<#{endTime} ]]>
        LIMIT #{start},#{size}
	</select>

	<select id="getApplyCount" resultType="int">
		SELECT count(1)
		FROM
		<include refid="tableName"/>
		WHERE user_id = #{userId} AND (create_time BETWEEN #{startTime} AND #{endTime})
	</select>

	<insert id="insertIntoFromExcel" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo" useGeneratedKeys="true" keyProperty="id" >
		INSERT INTO
		<include refid="tableName"/>
		(
		`info_id`,
		`user_id`,
		`relation`,
		`relation_type`,
		`channel_type`,
		`channel`,
		`payee_name`,
		`payee_mobile`,
		`title`,
		`title_img`,
		`content`,
		`encrypt_content`,
		`content_type`,
		`target_amount`,
		`create_time`,
		`begin_time`,
		`end_time`,
		`data_status`,
		`type`,
		`material_plan_id`
		)
		VALUES
		(
		#{infoId},
		#{userId},
		#{relation},
		#{relationType, typeHandler=org.apache.ibatis.type.EnumOrdinalTypeHandler, javaType=com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingRelationType},
		#{channelType},
		#{channel},
		#{payeeName},
		#{payeeMobile},
		#{title},
		#{titleImg},
		#{content},
		#{encryptContent},
		#{contentType}
		#{targetAmount},
		#{createTime},
		#{beginTime},
		#{endTime},
		#{dataStatus},
		#{type},
		#{materialPlanId}
		)
	</insert>

	<update id="updateFromByInfoUuid">
		UPDATE <include refid="tableName"/>
		SET `from` = #{from}
		WHERE `info_id`=#{infoUuid}
		LIMIT 1
	</update>

	<select id="getSuccessCaseIds" resultType="java.lang.String">
		SELECT
		info_id as infoId
		FROM
		<include refid="tableName"/>
		WHERE
		`info_id` in
		<foreach item="item" index="index" collection="endInfoIds"
				 open="(" separator="," close=")">
			#{item}
		</foreach>
		AND
		`target_amount`>#{targetAmount}
		AND
		`amount`>`target_amount`*0.7
		ORDER BY id DESC
		<if test="limit!=-1">
			LIMIT #{limit}
		</if>
	</select>

	<select id="getInfoIdsByCreateTime" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		SELECT
		`id`,info_id as infoId
		FROM
		<include refid="tableName"/>
		WHERE
		<![CDATA[ `create_time`>=#{startTime} and `create_time`<#{endTime} ]]>
		AND <![CDATA[id<#{id}]]> ORDER BY id DESC  limit 3000
	</select>

	<select id="getId" resultType="java.lang.Integer">
		SELECT `id`
		FROM
		<include refid="tableName"/>
		WHERE
		<![CDATA[ `create_time`>=#{startTime} and `create_time`<#{endTime} ]]>
	    ORDER BY id DESC  limit 1
	</select>

	<select id="findByAnchorId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		SELECT <include refid="fields"/>
		FROM <include refid="tableName" />
		WHERE
		`id` <![CDATA[ <= ]]> #{anchorId}
		ORDER BY `id` DESC
	  	LIMIT #{limit}
	</select>

	<select id="findByEndTime" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		SELECT <include refid="fields"/>
		FROM <include refid="tableName"/>
		WHERE
	  	`end_time` BETWEEN #{start} AND #{end}
		LIMIT #{offset}, #{limit}
	</select>

	<select id="getInfoIdsInEndTime" resultType="java.lang.Integer" >
		SELECT `id`
		FROM <include refid="tableName"></include>
		WHERE `id` >= #{startId} AND `end_time` >= #{endTime}
		LIMIT #{offset},#{limit}
	</select>

    <select id="selectLatestInfoByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
        SELECT <include refid="fields"/>
        FROM <include refid="tableName"/>
        WHERE `user_id`=#{userId}
        AND <![CDATA[ `end_time`>now() ]]>
        ORDER BY `id` DESC
        LIMIT 1
    </select>

	<select id="getIdsByCreateTime" resultType="java.lang.Integer">
		SELECT `id`
		FROM
		<include refid="tableName"/>
		WHERE
		<![CDATA[ `create_time`>=#{startTime} and `create_time`<#{endTime} ]]>
		ORDER BY id DESC  LIMIT #{offset},#{limit}
	</select>

	<update id="updateBeginAndEndTime">
		UPDATE
		<include refid="tableName"/>
		SET `end_time`=#{endTime},
		`begin_time` = #{beginTime}
		WHERE id = #{id}
	</update>

    <select id="selectIdAndUuidAndTypeById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
        SELECT `id`,`info_id`,`type`
        , end_time
        FROM <include refid="tableName"/>
        WHERE `id` IN
        <foreach collection="set" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

	<select id="queryById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		select <include refid="fields"/>
		from <include refid="tableName"/>
		where id > #{id}
	    limit 100
	</select>

	<update id="updateTitleImgById">
		UPDATE
		<include refid="tableName"/>
		SET `title_img`=#{titleImg}
		WHERE id = #{id}
	</update>

	<select id="listByPayeeMobile" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		SELECT <include refid="fields"/>
		FROM <include refid="tableName"/>
		WHERE `payee_mobile`=#{cryptoMobile}
		ORDER BY `id` DESC
	</select>

	<update id="updatePayeeRelation">
		UPDATE <include refid="tableName" />
		SET
		`relation_type`=#{relationType, typeHandler=org.apache.ibatis.type.EnumOrdinalTypeHandler, javaType=com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingRelationType},
		`relation` = #{relation}
		WHERE `id` = #{id}
	</update>


	<update id="setMaterialsVersion">
		UPDATE <include refid="tableName" />
		SET
		`material_plan_id`= #{mId}

		WHERE `id` = #{caseId}
	</update>

	<select id="getSimpleInfo" parameterType="String"
			resultType="com.shuidihuzhu.cf.model.CfInfoSimpleModelV2">
		SELECT
		<include refid="simple_fields"/>
		FROM
		<include refid="tableName"/>
		WHERE
		info_id = #{infoId}
	</select>

	<select id="getSimpleInfoById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		SELECT `id`,`info_id`,`title`,`amount`, `end_time`,status
		FROM <include refid="tableName"/>
		WHERE `id` = #{id}
	</select>
	
	<select id="getCrowdfundingInfoListByAmount" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo">
		select `id`, `amount`, `user_id`
		from <include refid="tableName"/>
		where create_time between #{startTime} and #{endTime}
		<if test="minAmount != null">
		    and amount > #{minAmount}
		</if>
		<if test="maxAmount != null">
		    and amount <![CDATA[ <= ]]> #{maxAmount}
		</if>
		order by id desc
		limit #{limit}
	</select>

</mapper>
