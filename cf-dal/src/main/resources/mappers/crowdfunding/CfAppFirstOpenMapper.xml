<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfAppFirstOpenDao">
	<sql id="tableName">
		cf_app_first_open
	</sql>

	<sql id="fields">
		`id` as id,
		`app_uniq_id` as appUniqId,
		`user_id` as userId,
		`create_time` as createTime
	</sql>

	<select id="findByCreateTime" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfAppFirstOpen">
		SELECT <include refid="fields"/>
		FROM <include refid="tableName" />
		WHERE `create_time`
		BETWEEN #{beginTime}
		AND #{endTime}
		AND `id` <![CDATA[ <= ]]>  #{anchorId}
		AND `is_delete`=0
		ORDER BY `id` DESC
		LIMIT #{limit}
	</select>

</mapper>