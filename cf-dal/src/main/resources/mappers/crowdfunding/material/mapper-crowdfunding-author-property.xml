<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.material.credit.ICrowdfundingAuthorPropertyDAO" >
    <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.domain.material.credit.CrowdfundingAuthorPropertyDO">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="case_id" property="caseId" jdbcType="INTEGER"/>
        <result column="commercial_insurance" property="commercialInsurance" jdbcType="INTEGER"/>
        <result column="health_insurance" property="healthInsurance" jdbcType="INTEGER"/>
        <result column="living_security" property="livingSecurity" jdbcType="INTEGER"/>
        <result column="gov_relief" property="govRelief" jdbcType="INTEGER"/>
        <result column="home_income" property="homeIncome" jdbcType="INTEGER"/>
        <result column="home_stock" property="homeStock" jdbcType="INTEGER"/>
        <result column="living_security_text" property="livingSecurityText" jdbcType="VARCHAR"/>
        <result column="gov_relief_text" property="govReliefText" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
         id,case_id,commercial_insurance,health_insurance,living_security,gov_relief,home_income,home_stock,living_security_text,gov_relief_text
    </sql>

    <select id="selectByCaseId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from crowdfunding_author_property where
        case_id = #{caseId,jdbcType=INTEGER}
    </select>


    <insert id="insertOrUpdate" parameterType="com.shuidihuzhu.cf.domain.material.credit.CrowdfundingAuthorPropertyDO">
        <selectKey order="AFTER" resultType="long" keyProperty="id">
            select last_insert_id() as id
        </selectKey>
        insert into crowdfunding_author_property(<include refid="Base_Column_List" />)
        values(
        #{id,jdbcType=INTEGER},
        #{caseId,jdbcType=INTEGER},
        #{commercialInsurance,jdbcType=INTEGER},
        #{healthInsurance,jdbcType=INTEGER},
        #{livingSecurity,jdbcType=INTEGER},
        #{govRelief,jdbcType=INTEGER},
        #{homeIncome,jdbcType=INTEGER},
        #{homeStock,jdbcType=INTEGER},
        #{livingSecurityText,jdbcType=VARCHAR},
        #{govReliefText,jdbcType=VARCHAR}
        )

        ON DUPLICATE KEY UPDATE

        commercial_insurance = #{commercialInsurance,jdbcType=INTEGER},
        health_insurance     = #{healthInsurance,jdbcType=INTEGER},
        living_security      = #{livingSecurity,jdbcType=INTEGER},
        gov_relief           = #{govRelief,jdbcType=INTEGER},
        home_income          = #{homeIncome,jdbcType=INTEGER},
        home_stock           = #{homeStock,jdbcType=INTEGER},
        living_security_text = #{livingSecurityText,jdbcType=VARCHAR},
        gov_relief_text      = #{govReliefText,jdbcType=VARCHAR}

    </insert>

</mapper>