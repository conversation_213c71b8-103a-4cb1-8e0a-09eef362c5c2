<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfPatientEvaluationRecordDao">

    <sql id="filedList">
    id, user_id, case_id,volunteer_name, volunteer_type, volunteers_unique_code, evaluation_rank, evaluation_label, evaluation_content
  </sql>
    <sql id="tableName">
    cf_patient_evaluation_record
  </sql>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfPatientEvaluationRecord" useGeneratedKeys="true">
        insert into
        <include refid="tableName"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                user_id,
            </if>
            <if test="caseId != null">
                case_id,
            </if>
            <if test="volunteerName != null">
                volunteer_name,
            </if>
            <if test="volunteerType != null">
                volunteer_type,
            </if>
            <if test="volunteersUniqueCode != null">
                volunteers_unique_code,
            </if>
            <if test="evaluationRank != null">
                evaluation_rank,
            </if>
            <if test="evaluationLabel != null">
                evaluation_label,
            </if>
            <if test="evaluationContent != null">
                evaluation_content,
            </if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                #{userId},
            </if>
            <if test="caseId != null">
                #{caseId},
            </if>
            <if test="volunteerName != null">
                #{volunteerName},
            </if>
            <if test="volunteerType != null">
                #{volunteerType},
            </if>
            <if test="volunteersUniqueCode != null">
                #{volunteersUniqueCode},
            </if>
            <if test="evaluationRank != null">
                #{evaluationRank},
            </if>
            <if test="evaluationLabel != null">
                #{evaluationLabel},
            </if>
            <if test="evaluationContent != null">
                #{evaluationContent},
            </if>

        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfPatientEvaluationRecord">
        update
        <include refid="tableName"/>
        <set>
            <if test="userId != null">
                user_id = #{userId},
            </if>
            <if test="caseId != null">
                case_id = #{caseId},
            </if>
            <if test="volunteersUniqueCode != null">
                volunteers_unique_code = #{volunteersUniqueCode},
            </if>
            <if test="evaluationRank != null">
                evaluation_rank = #{evaluationRank},
            </if>
            <if test="evaluationContent != null">
                evaluation_content = #{evaluationContent},
            </if>

        </set>
        where id = #{id}
    </update>
    <select id="selectRecordByUserIdAndCaseId"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.CfPatientEvaluationRecord">
        select
        <include refid="filedList"/>
        from
        <include refid="tableName"/>
        where is_delete =0
        and user_id = #{userId}
        and case_id = #{caseId}
        limit 1
    </select>
    <select id="selectRecordByCaseId"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.CfPatientEvaluationRecord">
        select
        <include refid="filedList"/>
        from
        <include refid="tableName"/>
        where is_delete =0
        and case_id = #{caseId}
    </select>

</mapper>