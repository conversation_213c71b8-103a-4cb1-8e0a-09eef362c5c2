<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingInfoOperationRecordDao">
	<sql id="tableName">
		crowdfunding_info_op_record
	</sql>

	<insert id="insertOperationRecord">
		INSERT INTO
		<include refid="tableName"/>
		(`info_id`, `op_code`)
		VALUES
		(#{infoId}, #{opCode})
	</insert>

</mapper>