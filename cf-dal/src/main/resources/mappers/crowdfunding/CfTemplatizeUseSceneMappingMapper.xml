<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfTemplateUseSceneMappingDao">


    <sql id="table_name">
      cf_base_templatize_use_scene
    </sql>

    <insert id = "insertUseSceneMapping" parameterType = "com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplatize$TemplateUseSceneMapping">
        INSERT INTO
        <include refid="table_name"/>
        (`template_id`, `use_scene`)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.templateId}, #{item.useScene})
        </foreach>
    </insert>

    <select id="selectByTemplateIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplatize$TemplateUseSceneMapping">
        SELECT * FROM
        <include refid="table_name"/>
        WHERE template_id
        IN
        <foreach collection="templateIds" item = "templateId" open="(" close=")" separator=",">
            #{templateId}
        </foreach>
        AND is_delete = 0
    </select>

    <update id="deleteByTemplateIds">
      UPDATE
        <include refid="table_name"/>
      SET
        is_delete = 1
      WHERE template_id
        IN
        <foreach collection="templateIds" item = "templateId" open="(" close=")" separator=",">
            #{templateId}
        </foreach>
    </update>


    <select id="selectByTemplateIdsAndUseScene" resultType = "java.lang.Integer">
        SELECT template_id FROM
        <include refid="table_name"/>
        WHERE template_id
        IN
        <foreach collection="templateIds" item = "templateId" open="(" close=")" separator=",">
            #{templateId}
        </foreach>
        AND use_scene = #{useScene}
        AND is_delete = 0
    </select>


    <select id="selectTemplateIdsByUseScene" resultType = "java.lang.Integer">
        SELECT template_id FROM
        <include refid="table_name"/>
        WHERE use_scene = #{useScene}
        AND is_delete = 0
    </select>
</mapper>
