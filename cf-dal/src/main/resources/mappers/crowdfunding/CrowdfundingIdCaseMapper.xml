<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingIdCaseDao" >
    <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingIdCase">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="case_id" property="caseId" jdbcType="INTEGER"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="crypto_id_card" property="cryptoIdCard" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,case_id,`name`,crypto_id_card,status,create_time,update_time
    </sql>

    <sql id="Insert_Column_List">
        id,case_id,`name`,crypto_id_card,status
    </sql>

    <select id="getByInfoId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from crowdfunding_id_case where
        case_id = #{caseId, jdbcType=INTEGER}
    </select>

    <select id="getByInfoIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from crowdfunding_id_case
        where case_id in
        <foreach item="item" index="index" collection="caseIds"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="updateStatus">
        update crowdfunding_id_case
        set
        status = #{status, jdbcType=INTEGER}
        where case_id = #{caseId,jdbcType=INTEGER}
    </update>

    <update id="updateIdCase">
        update crowdfunding_id_case
        set
        `name` = #{name, jdbcType=VARCHAR},
        crypto_id_card = #{cryptoIdCard, jdbcType=VARCHAR}
        where case_id = #{caseId,jdbcType=INTEGER}
    </update>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingIdCase">
        <selectKey order="AFTER" resultType="long" keyProperty="id">
            select last_insert_id() as id
        </selectKey>
        insert into crowdfunding_id_case(<include refid="Insert_Column_List" />)
        values(
        #{id,jdbcType=INTEGER},
        #{caseId,jdbcType=INTEGER},
        #{name,jdbcType=VARCHAR},
        #{cryptoIdCard,jdbcType=VARCHAR},
        #{status,jdbcType=INTEGER}
        )
    </insert>

    <select id="getByCryptoIdCard" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from crowdfunding_id_case
        where crypto_id_card = #{cryptoIdCard}
    </select>

</mapper>