<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.risk.ugc.UgcVerifyDAO">

    <sql id="tableName">
		cf_ugc_verify
	</sql>

    <sql id="Base_Column_List">
		id,
		case_id,
		ugc_type,
		ugc_id,
		description
	</sql>

    <sql id="insert_Column_List">
		case_id,
		ugc_type,
		ugc_id,
		description
	</sql>

    <select id="get" resultType="com.shuidihuzhu.cf.domain.risk.RiskUgcVerifyDO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="tableName"/>
        <where>
            `case_id` = #{caseId}
            and `ugc_type` = #{ugcType}
            and `ugc_id` = #{ugcId}
            AND `is_delete` = 0
        </where>
        limit 1
    </select>

    <insert id="save"
            parameterType="com.shuidihuzhu.cf.domain.risk.RiskUgcVerifyDO">
        <selectKey order="AFTER" resultType="long" keyProperty="id">
            select last_insert_id() as id
        </selectKey>
        insert into <include refid="tableName"/>
        (<include refid="insert_Column_List" />)
        values(
        #{caseId},
        #{ugcType},
        #{ugcId},
        #{description}
        )
    </insert>

    <delete id="delete">
        delete from <include refid="tableName"/>
        <where>
            `case_id` = #{caseId}
            and `ugc_type` = #{ugcType}
            and `ugc_id` = #{ugcId}
        </where>
    </delete>

    <update id="deleteById">
        delete from <include refid="tableName"/>
        where `id` = #{id}
    </update>

    <select id="listByAnchor" resultType="com.shuidihuzhu.cf.domain.risk.RiskUgcVerifyDO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="tableName"/>
        <where>
            `id` <![CDATA[ >= ]]> #{start}
            and `is_delete` = 0
        </where>
        limit #{limit}
    </select>

    <select id="listByCaseAndAnchor" resultType="com.shuidihuzhu.cf.domain.risk.RiskUgcVerifyDO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="tableName"/>
        <where>
            `case_id` = #{caseId}
            and `id` <![CDATA[ >= ]]> #{start}
            and `is_delete` = 0
        </where>
        limit #{limit}
    </select>

    <select id="getByCaseIdAndUgcType" resultType="com.shuidihuzhu.cf.domain.risk.RiskUgcVerifyDO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="tableName"/>
        <where>
            `case_id` = #{caseId}
            and `ugc_type` = #{ugcType}
            and `is_delete` = 0
        </where>
    </select>

    <select id="getHitUgcIdList" resultType="java.lang.Long">
        SELECT ugc_id
        FROM
        <include refid="tableName"/>
        <where>
            `case_id` = #{caseId}
            and `ugc_type` = #{ugcType}
            and ugc_id in <foreach collection="ugcIdList" item="item" open="(" separator="," close=")">
                    #{item}
            </foreach>
            and `is_delete` = 0
        </where>
    </select>

    <select id="getAllHitUgcIdList" resultType="java.lang.Long">
        SELECT ugc_id
        FROM
        <include refid="tableName"/>
        <where>
            `case_id` = #{caseId}
            and `ugc_type` = #{ugcType}
            and `is_delete` = 0
        </where>
    </select>
</mapper>
