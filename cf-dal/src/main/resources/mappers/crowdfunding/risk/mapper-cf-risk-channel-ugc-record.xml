<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.risk.RiskChannelUgcRecordDAO">

    <sql id="tableName">
		cf_risk_channel_ugc_record
	</sql>

    <sql id="Base_Column_List">
		id,
		code,
		biz_id,
		channel
	</sql>

    <sql id="insert_Column_List">
		code,
		biz_id,
		channel
	</sql>

    <select id="getByCodeAndBizId" resultType="com.shuidihuzhu.cf.domain.risk.RiskChannelUgcRecordDO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="tableName"/>
        <where>
            `code` = #{code}
            AND `biz_id` = #{bizId}
            AND `is_delete` = 0
        </where>
        limit 1
    </select>

    <insert id="insert"
            parameterType="com.shuidihuzhu.cf.domain.risk.RiskChannelUgcRecordDO" useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="tableName"/>
        (<include refid="insert_Column_List" />)
        values(
        #{code},
        #{bizId} ,
        #{channel}
        )
    </insert>

</mapper>
