<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfCaseEndWhiteListDao">

	<sql id="TABLE">
       	cf_case_end_white_list
    </sql>

	<sql id="FIELDS">
		`case_id` as caseId
	</sql>

	<select id="getList" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfCaseEndWhiteList">
		SELECT
			<include refid="FIELDS"/>
		FROM
			<include refid="TABLE"/>
		WHERE is_delete = 0
	</select>

</mapper>