<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.ModifyVerificationDao">

    <sql id="tableName">
        modify_verification_record
    </sql>

    <sql id="fields">
        `case_id`,
        `verify_id`,
        `verify_user_id`,
        `modify_num`
    </sql>

    <insert id="insertModifyVerificationRecord" parameterType="com.shuidihuzhu.cf.model.crowdfunding.ModifyVerificationDO">
        INSERT INTO
        <include refid="tableName"/>
        (<include refid="fields"/>)
        VALUES
        (#{caseId},#{verifyId},#{verifyUserId},#{modifyNum})
    </insert>

    <select id="selectByVerifyId" resultType="com.shuidihuzhu.cf.model.crowdfunding.ModifyVerificationDO">
        SELECT
        <include refid="fields"/>
        FROM
        <include refid="tableName"/>
        WHERE `verify_id` = #{verifyId}
        and is_delete = 0 limit 1
    </select>

</mapper>

