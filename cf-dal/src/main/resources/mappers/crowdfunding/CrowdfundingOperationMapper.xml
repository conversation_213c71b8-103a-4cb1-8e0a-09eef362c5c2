<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingOperationDao">
    <sql id="tableName">
        crowdfunding_operation
    </sql>

    <sql id="fields">
        `id`,
        `info_id`,
        `operator_id`,
        `operation`,
        `reason`,
        `follow_type` as followType,
        `refuse_count`,
        `user_refuse_count` as userRefuseCont,
        `audit_commit_time` as auditCommitTime,
        `operate_time`as operateTime,
        `report_status` as reportStatus,
        `date_created` as dateCreated,
        `last_modified` as lastModified,
        `case_id` as caseId
    </sql>

    <sql id="insertFields">
		`info_id`,
		`operator_id`,
        `operation`,
        `reason`,
        `follow_type`,
        `case_id`
	</sql>

    <insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOperation" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tableName"/>
        	(<include refid="insertFields"/>)
        VALUES
        	(#{infoId},#{operatorId},#{operation},#{reason},#{followType},#{caseId})
    </insert>

    <update id="update" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOperation">
		UPDATE <include refid="tableName"/>
		SET `operator_id`=#{operatorId},`operation`=#{operation},`reason`=#{reason},`follow_type`=#{followType}
		WHERE info_id = #{infoId}
	</update>

	<update id="updateOperation">
		UPDATE <include refid="tableName"/>
		SET `operator_id`=#{operatorId},`operation`=#{operation},`reason`=#{reason}, `operate_time`=NOW()
		WHERE `info_id` = #{infoUuid}
	</update>

    <select id="getByInfoId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOperation">
    	SELECT <include refid="fields" />
    	FROM <include refid="tableName" />
    	WHERE info_id=#{infoId}
    </select>

    <insert id="insertOrUpdateCommitTime">
        INSERT INTO <include refid="tableName"/>
        (`info_id`, `case_id`, `audit_commit_time`)
        VALUES
        (#{infoId}, #{caseId}, #{commitTime})
        ON DUPLICATE KEY
        UPDATE `audit_commit_time`=#{commitTime}, `case_id` = #{caseId}
    </insert>

    <update id="updateReportStatus">
        UPDATE <include refid="tableName"/>
        SET `report_status`=#{reportStatus}
        WHERE `info_id`=#{infoUuid}
    </update>

    <select id="filterReportedInfoUuid" resultType="java.lang.String">
        SELECT distinct info_id AS infoUuid
        FROM <include refid="tableName"/>
        where `report_status` = 0 AND `info_id` IN
        <foreach collection="infoIds" item="infoId" separator="," open="(" close=")"  >
            #{infoId}
        </foreach>
    </select>

    <select id="selectByInfoUuids" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOperation">
        SELECT *
        FROM <include refid="tableName"/>
        WHERE `info_id` IN
        <foreach collection="infoUuids" item="infoUuid" open="(" separator="," close=")">
            #{infoUuid}
        </foreach>
    </select>

    <select id="getByInfoIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOperation">
        SELECT *
        FROM <include refid="tableName" />
        WHERE info_id in
        <foreach collection="infoIds" open="(" separator="," close=")" item="infoId">#{infoId}</foreach>
    </select>

</mapper>
