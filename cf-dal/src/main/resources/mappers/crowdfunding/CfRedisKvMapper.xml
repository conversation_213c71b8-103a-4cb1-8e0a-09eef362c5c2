<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfRedisKvDao">

    <select id="queryByKey" parameterType="String" resultType="com.shuidihuzhu.common.web.model.RedisKv">
        SELECT * FROM cf_redis_kv WHERE k = #{key}
    </select>

    <select id="queryValueByKey" parameterType="String" resultType="java.lang.String">
        SELECT v FROM cf_redis_kv WHERE k=#{key}
    </select>

    <insert id="saveRedisKv" parameterType="com.shuidihuzhu.common.web.model.RedisKv" useGeneratedKeys="true" keyProperty="id">
        INSERT IGNORE INTO cf_redis_kv
        (
        k, v
        )
        VALUES
        (
        #{k}, #{v}
        )
    </insert>

    <update id="updateRedisKv">
        UPDATE
        cf_redis_kv
        SET
        v = #{value}
        WHERE
        k = #{key}
    </update>

</mapper>