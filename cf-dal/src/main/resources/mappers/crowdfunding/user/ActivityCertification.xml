<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.user.ActivityCertificationDao">
    <sql id="tableName">
		cf_activity_certification
	</sql>

    <sql id="selectFields">
        `id` AS id,
        `user_id` AS userId,
        `document_id` AS documentId
    </sql>

    <sql id="insertFields">
        `user_id`, `document_id`
    </sql>

    <insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.ActivityCertificationDO"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="tableName"/>
        (<include refid="insertFields"/>)
        VALUES
        (#{userId}, #{documentId})
        ON DUPLICATE KEY UPDATE update_time = now()
    </insert>


    <select id="getDocumentId" resultType="java.lang.String">
        SELECT
        `document_id`
        FROM <include refid="tableName"/>
        WHERE
        user_id = #{userId}
    </select>

</mapper>