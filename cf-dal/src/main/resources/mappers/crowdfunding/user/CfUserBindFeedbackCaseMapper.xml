<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.user.CfUserBindFeedbackCaseDao">
    <sql id="tableName">
		cf_user_bind_feedback_case
	</sql>

    <sql id="selectField">
        `id` as id,
        `user_id` as userId,
        `feedback_id` as feedbackId,
        `user_bind_case_time` as userBindCaseTime,
        `case_id` as caseId,
        `letter_type` as letterType
    </sql>

    <sql id="insertFields">
        `user_id`,
        `feedback_id`,
        `user_bind_case_time`,
        `case_id`,
        `letter_type`
    </sql>

    <insert id="addList">
        INSERT INTO <include refid="tableName"/>
        (`user_id`, `feedback_id`, `user_bind_case_time`, `case_id`,`letter_type`)
        VALUES
        <foreach collection="bindFeedbackCaselist" item="feedbackCase" separator=",">
            (#{feedbackCase.userId}, #{feedbackCase.feedbackId}, #{feedbackCase.userBindCaseTime},
             #{feedbackCase.caseId}, #{feedbackCase.letterType})
        </foreach>
    </insert>

    <select id="getMaxFeedbackId" resultType="java.lang.Long">
        select max(feedback_id) from
        <include refid="tableName"/>
        where
        `id` >= 0
    </select>

    <select id="getCaseIdList" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfUserBindFeedbackCaseDO">
        SELECT
        <include refid="selectField"/>
        FROM
        <include refid="tableName"/>
        WHERE
        `user_id` = #{userId}
        ORDER BY `letter_type` ASC, case_id DESC
        LIMIT #{limit}
    </select>

    <select id="listByFeedbackId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfUserBindFeedbackCaseDO">
        SELECT
        <include refid="selectField"/>
        FROM
        <include refid="tableName"/>
        WHERE
        `feedback_id` = #{feedbackId}
    </select>

    <insert id="batchUpdate">
        insert into
        <include refid="tableName"/>
        (`id`, `letter_type`)
        values
        <foreach collection="bindFeedbackCaselist" item="item" separator=",">
            (#{item.id}, #{item.letterType})
        </foreach>
        on duplicate key update
        `letter_type` = values(letter_type)
    </insert>

</mapper>