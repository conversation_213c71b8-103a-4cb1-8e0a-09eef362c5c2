<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.user.NewCfUserCaseDao">
    <sql id="tableName">
		cf_user_case_info_v2
	</sql>

    <sql id="insertFields">
        `user_id`, `case_id`, `amount`, `anonymous_amount`, `anonymous_refund`, `refund`, `share_count`,
         `share_contribute_amount`, `share_anonymous_amount`, `share_anonymous_refund`, `share_refund`,
         `share_contribute_view_time`, `share_contribute_donate_time`, `share_contribute_donator`, `share_contribute_follower`, `share_contribute_view`
    </sql>

    <sql id="selectFields">
        `id` as id,
        `user_id` as userId,
        `case_id` as caseId,
        `amount` as amount,
        `anonymous_amount` as anonymousAmount,
        `anonymous_refund` as anonymousRefund,
        `refund` as refund,
        `share_count` as shareCount,
        `share_contribute_amount` as shareContributeAmount,
        `share_anonymous_amount` as shareAnonymousAmount,
        `share_anonymous_refund` as shareAnonymousRefund,
        `share_refund` as shareRefund,
        `share_contribute_view_time` as shareContributeViewTime,
        `share_contribute_donate_time` as shareContributeDonateTime,
        `share_contribute_donator` as shareContributeDonator,
        `share_contribute_follower` as shareContributeFollower,
        `share_contribute_view` as shareContributeView,
        `create_time` as createTime,
        `update_time` as updateTime
    </sql>


    <insert id="insertOrUpdate" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfUserCaseInfoDO"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="tableName"/>
        (<include refid="insertFields"/>)
        VALUES
        (#{userId}, #{caseId}, #{amount}, #{anonymousAmount}, #{anonymousRefund}, #{refund}, #{shareCount},
         #{shareContributeAmount}, #{shareAnonymousAmount}, #{shareAnonymousRefund}, #{shareRefund}, #{shareContributeViewTime},
        #{shareContributeDonateTime}, #{shareContributeDonator}, #{shareContributeFollower}, #{shareContributeView})
        ON DUPLICATE KEY UPDATE
        <if test="amount >0">
            `amount` = #{amount},
        </if>
        <if test="anonymousAmount >0">
            `anonymous_amount` = #{anonymousAmount},
        </if>
        <if test="anonymousRefund >0">
            `anonymous_refund` = #{anonymousRefund},
        </if>
        <if test="refund >0">
            `refund` = #{refund},
        </if>
        <if test="shareCount >0">
            `share_count` = #{shareCount},
        </if>
        <if test="shareContributeAmount >0">
            `share_contribute_amount` = #{shareContributeAmount},
        </if>
        <if test="shareAnonymousAmount >0">
            `share_anonymous_amount` = #{shareAnonymousAmount},
        </if>
        <if test="shareAnonymousRefund >0">
            `share_anonymous_refund` = #{shareAnonymousRefund},
        </if>
        <if test="shareRefund >0">
            `share_refund` = #{shareRefund},
        </if>
        <if test="shareContributeViewTime != null">
            `share_contribute_view_time` = #{shareContributeViewTime},
        </if>
        <if test="shareContributeDonateTime != null">
            `share_contribute_donate_time` = #{shareContributeDonateTime},
        </if>
        <if test="shareContributeDonator >0">
            `share_contribute_donator` = #{shareContributeDonator},
        </if>
        <if test="shareContributeFollower >0">
            `share_contribute_follower`= #{shareContributeFollower},
        </if>
        <if test="shareContributeView >0">
            `share_contribute_view` = #{shareContributeView},
        </if>
        `id` = LAST_INSERT_ID(`id`)
    </insert>

    <select id="findByCaseIdRangeById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfUserCaseInfoDO">
        SELECT
        <include refid="selectFields"/>
        FROM
        <include refid="tableName"/>
        WHERE
        case_id = #{caseId} AND id >= #{id} order by id limit 500
    </select>

    <select id="findByUniqueIdAndLimit" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfUserCaseInfoDO">
        select
        <include refid="selectFields"/>
        FROM
        <include refid="tableName"/>
        WHERE
        case_id = #{caseId} order by id
        LIMIT #{offset},#{limit}
    </select>

    <select id="findByCaseAndUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfUserCaseInfoDO">
        SELECT
        <include refid="selectFields"/>
        FROM
        <include refid="tableName"/>
        WHERE
        case_id = #{caseId} AND user_id = #{userId}
    </select>

</mapper>