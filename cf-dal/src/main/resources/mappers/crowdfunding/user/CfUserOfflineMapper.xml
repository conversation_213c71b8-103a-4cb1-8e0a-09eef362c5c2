<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.user.CfUserOfflineDao">
    <sql id="tableName">
		cf_user_offline_info
	</sql>


    <sql id="selectFields">
        `id` as id,
        `user_id` as userId,
        `user_local` as userLocal,
        `first_view_time` as firstViewTime,
        `view_count` as viewCount,
        `most_view_city` as mostViewCity,
        `donate_rank` as donateRank,
        `share_donate_time` as shareOrDonateFirstFinishTime,
        `first_finish_id` as firstFinishCaseId,
        `first_finish_duration` as firstFinishDuration,
        `first_finish_amount` as firstFinishAmount,
        `first_finish_donation_count` as firstFinishDonationCount,
        `contribute_view_count` as contributeViewCount,
        `contribute_viewer` as contributeViewer,
        `contribute_amount` as contributeAmount,
        `contribute_donator` as contributeDonator,
        `contribute_share_count` as contributeShareCount,
        `contribute_sharer` as contributeSharer,
        `contribute_share_rank` as contributeShareRank,
        `view_case_id_list` as viewCaseIdList,
        `create_time` as createTime,
        `update_time` as updateTime
    </sql>


    <select id="findByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfUserOfflineInfoDO">
        SELECT <include refid="selectFields"/>
        FROM <include refid="tableName"/>
        WHERE
        user_id = #{userId}
    </select>
</mapper>