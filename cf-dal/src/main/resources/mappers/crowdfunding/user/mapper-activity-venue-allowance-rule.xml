<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.user.ActivityVenueAllowanceRuleDao" >
    <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.domain.activity.ActivityVenueAllowanceRule">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="line_amount" property="lineAmount" jdbcType="INTEGER"/>
        <result column="allowance_amount" property="allowanceAmount" jdbcType="INTEGER"/>
        <result column="unit_name" property="unitName" jdbcType="VARCHAR"/>
        <result column="unit_exchange_unit" property="unitChangeUnit" jdbcType="INTEGER"/>
        <result column="rule_desc" property="ruleDesc" jdbcType="VARCHAR"/>
        <result column="notify_result" property="notifyResult" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,activity_id,line_amount,allowance_amount,unit_name,unit_exchange_unit,notify_result,rule_desc
   </sql>

    <select id="get" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from activity_venue_allowance_rule
        where activity_id = #{activityId} AND is_delete = 0;
    </select>

</mapper>