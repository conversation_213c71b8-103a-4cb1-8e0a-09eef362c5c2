<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.user.ActivityVenueDetailDao" >
    <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.domain.activity.ActivityVenueDetail">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="version" property="version" jdbcType="INTEGER"/>
        <result column="cooperate_activity_id" property="cooperateActivityId" jdbcType="INTEGER"/>
        <result column="venue_type" property="typeEnum" jdbcType="INTEGER" javaType="com.shuidihuzhu.cf.enums.activity.Activity111CaseTypeEnum"
            typeHandler="com.shuidihuzhu.cf.dao.type.handler.Activity111CaseTypeEnumTypeHandler"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,start_time,end_time,status,version,create_time,update_time,cooperate_activity_id,venue_type
   </sql>

    <select id="get" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from activity_venue_detail
        where id = #{id,jdbcType=INTEGER}
    </select>

    <select id="getByCooperateId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from activity_venue_detail
        where cooperate_activity_id = #{cooperateId,jdbcType=INTEGER}
    </select>

    <update id="update" >
        update activity_venue_detail
        <set>
        status =  #{status},
        version = #{newVersion}
        </set>
        where id = #{id} and version = #{oldVersion}
    </update>

</mapper>