<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.user.CfAreaDistanceInfoDao">
    <sql id="tableName">
		area_dinstance_info
	</sql>

    <sql id="insertFields">
        `local_area`,
        `dest_area`,
        `distance_level`
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.AreaDistanceInfoDo">
        INSERT INTO
        <include refid="tableName"/>
        (<include refid="insertFields"/>)
        VALUE (#{localArea}, #{destArea}, #{distanceLevel})
    </insert>

    <select id="findDestArea" resultType="java.lang.String">
        SELECT
        `dest_area`
        FROM
        <include refid="tableName"/>
        WHERE
        `local_area` = #{localArea}
        AND `distance_level` = #{distancLevel}
    </select>

</mapper>