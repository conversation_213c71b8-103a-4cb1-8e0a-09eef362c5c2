<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.user.ActivityVenueUserDonateDao">
    <sql id="tableName">
		activity_venue_user_donate
	</sql>

    <sql id="selectFields">
        `id` AS id,
        `user_id` AS userId,
        `case_id` AS caseId,
        `info_id` AS infoId,
        `activity_id` AS activityId,
        `amount` AS amount,
        `order_id` AS orderId,
        `pay_status` AS payStatus,
        `create_time` AS createTime
    </sql>

    <sql id="insertFields">
        `user_id`, `case_id`, `info_id`, `activity_id`, `amount`, `order_id`
    </sql>

    <insert id="add" parameterType="com.shuidihuzhu.cf.domain.activity.ActivityVenueUserDonateDO"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="tableName"/>
        (<include refid="insertFields"/>)
        VALUES
        (#{userId}, #{caseId}, #{infoId}, #{activityId}, #{amount}, #{orderId})
    </insert>

    <update id="updatePayStatus">
        update
        <include refid="tableName"/>
        set `pay_status` = #{payStatus}, `anonymous` = #{anonymous}
        where `order_id` =#{orderId}
    </update>


    <select id="listPaySucUserDonate" resultType="com.shuidihuzhu.cf.domain.activity.ActivityVenueUserDonateDO">
        SELECT <include refid="selectFields"/>
        FROM <include refid="tableName"/>
        WHERE user_id = #{userId} and pay_status = 1
    </select>

    <select id="getByOrderId" resultType="com.shuidihuzhu.cf.domain.activity.ActivityVenueUserDonateDO">
        SELECT <include refid="selectFields"/>
        FROM <include refid="tableName"/>
        WHERE
        order_id = #{orderId}
        order by id desc
        limit 1
    </select>

    <select id="listLastDonate" resultType="com.shuidihuzhu.cf.domain.activity.ActivityVenueUserDonateDO">
        SELECT <include refid="selectFields"/>
        FROM <include refid="tableName"/>
        where `activity_id` = #{activityId} and `pay_status` = 1 and `anonymous` = 0
        <if test="startTime != null">
            and `create_time` &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            and `create_time` &lt;= #{endTime}
        </if>
        order by id desc limit #{limit}
    </select>

    <select id="countDonateByUserIdAndActivityId" resultType="int">
        SELECT count(*)
        FROM <include refid="tableName"/>
        WHERE user_id = #{userId}
        and activity_id = #{activityId}
        and pay_status = 1
    </select>

    <select id="listPaySucUserDonateByActivityId"
            resultType="com.shuidihuzhu.cf.domain.activity.ActivityVenueUserDonateDO">
        SELECT <include refid="selectFields"/>
        FROM <include refid="tableName"/>
        WHERE user_id = #{userId}
        and activity_id = #{activityId}
        and pay_status = 1
    </select>

</mapper>