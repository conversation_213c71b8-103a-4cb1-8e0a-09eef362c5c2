<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.user.CfUserFirstDonateDao">
    <sql id="tableName">
		cf_user_first_donate_time
	</sql>

    <sql id="insertFields">
        `user_id`, `first_donate_time`
    </sql>

    <sql id="selectFields">
        `id` AS id,
        `user_id` AS userId,
        `first_donate_time` AS firstDonateTime
    </sql>


    <insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfUserFirstDonateTimeInfo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="tableName"/>
        (<include refid="insertFields"/>)
        VALUES
        (#{userId}, #{firstDonateTime})
        ON DUPLICATE KEY UPDATE update_time = now()
    </insert>


    <select id="findByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfUserFirstDonateTimeInfo">
        SELECT <include refid="selectFields"/>
        FROM <include refid="tableName"/>
        WHERE
        user_id = #{userId}
    </select>
</mapper>