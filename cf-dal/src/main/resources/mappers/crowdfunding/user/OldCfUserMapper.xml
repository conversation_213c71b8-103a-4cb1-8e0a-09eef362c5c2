<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.user.OldCfUserDao">
    <sql id="tableName">
		cf_user_info
	</sql>

    <sql id="insertFields">
        `user_id`, `first_share_time`, `share_count`, `share_donate_count`, `remotest_case_id`, `remotest_case_distance`,
        `remotest_case_donate_time`, `donate_child_case_count`, `affected_by_friends`, `affect_friends`,
        `same_action_friends`
    </sql>


    <sql id="selectFields">
        `id` as id,
        `user_id` as userId,
        `first_share_time` as firstShareTime,
        `share_count` as shareCount,
        `share_donate_count` as shareDonateCount,
        `remotest_case_id` as remotestCaseId,
        `remotest_case_distance` as remotestCaseDistance,
        `remotest_case_donate_time` as remotestCaseDonateTime,
        `donate_child_case_count` as donateChildCaseCount,
        `affected_by_friends` as affectedByFriends,
        `affect_friends` as affectFriends,
        `same_action_friends` as sameActionFriends,
        `create_time` as createTime,
        `update_time` as updateTime
    </sql>

    <insert id="insertOrUpdate" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfUserInfoDO"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="tableName"/>
        (<include refid="insertFields"/>)
        VALUES
        (#{userId}, #{firstShareTime}, #{shareCount}, #{shareDonateCount}, #{remotestCaseId}, #{remotestCaseDistance}, #{remotestCaseDonateTime},
        #{donateChildCaseCount}, #{affectedByFriends}, #{affectFriends}, #{sameActionFriends})
        ON DUPLICATE KEY UPDATE
        <if test="firstShareTime != null">
            `first_share_time` = #{firstShareTime},
        </if>
        <if test="shareCount >0">
            `share_count` = #{shareCount},
        </if>
        <if test="shareDonateCount >0">
            `share_donate_count` = #{shareDonateCount},
        </if>
        <if test="remotestCaseId >0">
            `remotest_case_id` = #{remotestCaseId},
        </if>
        <if test="remotestCaseDistance != null">
            `remotest_case_distance` = #{remotestCaseDistance},
        </if>
        <if test="remotestCaseDonateTime != null">
            `remotest_case_donate_time`= #{remotestCaseDonateTime},
        </if>
        <if test="donateChildCaseCount >0">
            `donate_child_case_count` = #{donateChildCaseCount},
        </if>
        <if test="affectedByFriends != null">
            `affected_by_friends` = #{affectedByFriends},
        </if>
        <if test="affectFriends != null">
            `affect_friends` = #{affectFriends},
        </if>
        <if test="sameActionFriends != null">
            `same_action_friends` = #{sameActionFriends},
        </if>
        `id` = LAST_INSERT_ID(`id`)
    </insert>


    <select id="findByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfUserInfoDO">
        SELECT <include refid="selectFields"/>
        FROM <include refid="tableName"/>
        WHERE
        user_id = #{userId}
    </select>
</mapper>