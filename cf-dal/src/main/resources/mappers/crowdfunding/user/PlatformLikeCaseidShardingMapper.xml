<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.user.PlatformLikeCaseIdShardingDao">
    <sql id="tableName">
		platform_like_case_id_sharding
	</sql>

    <sql id="selectFields">
        `id` as id,
        `user_id` as userId,
        `case_id` as caseId
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.PlatformLikeCaseIdSharding">
        insert into
        <include refid="tableName"/>
        (user_id,case_id) values (#{userId},#{caseId})
    </insert>

    <select id="getByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.PlatformLikeCaseIdSharding">
        select
        <include refid="selectFields"/>
        from
        <include refid="tableName"/>
        where case_id = #{caseId}
        and user_id = #{userId}
        and is_delete = 0
        limit 1
    </select>
</mapper>