<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfUserCaseBaseStatDao">

    <sql id="TableName">
        shuidi_crowdfunding_user.crowdfunding_user_case_stat_${tableSuffix}
    </sql>

    <sql id="BaseFields">
		id,user_id,case_id,share_brought_amount,share_first_amount,donate_amount,share_count,create_time,update_time
	</sql>

    <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.model.crowdfunding.CfUserCaseBaseStatDO">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="user_id" property="userId" jdbcType="INTEGER"/>
        <result column="case_id" property="caseId" jdbcType="INTEGER"/>
        <result column="share_brought_amount" property="shareBroughtAmount" jdbcType="INTEGER"/>
        <result column="share_first_amount" property="shareFirstAmount" jdbcType="INTEGER"/>
        <result column="donate_amount" property="donateAmount" jdbcType="INTEGER"/>
        <result column="share_count" property="shareCount" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>


    <insert id="insert"  useGeneratedKeys="true" keyProperty="baseStatDO.id">
        INSERT INTO <include refid="TableName" />
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="baseStatDO.userId != null">
                user_id,
            </if>
            <if test="baseStatDO.caseId != null">
                case_id,
            </if>
            <if test="baseStatDO.shareBroughtAmount != null">
                share_brought_amount,
            </if>
            <if test="baseStatDO.shareFirstAmount != null">
                share_first_amount,
            </if>
            <if test="baseStatDO.donateAmount != null">
                donate_amount,
            </if>
            <if test="baseStatDO.shareCount != null">
                share_count,
            </if>
            <if test="baseStatDO.updateTime != null">
                update_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="baseStatDO.userId != null">
                #{baseStatDO.userId, jdbcType=INTEGER},
            </if>
            <if test="baseStatDO.caseId != null">
                #{baseStatDO.caseId, jdbcType=INTEGER},
            </if>
            <if test="baseStatDO.shareBroughtAmount != null">
                #{baseStatDO.shareBroughtAmount, jdbcType=INTEGER},
            </if>
            <if test="baseStatDO.shareFirstAmount != null">
                #{baseStatDO.shareFirstAmount, jdbcType=INTEGER},
            </if>
            <if test="baseStatDO.donateAmount != null">
                #{baseStatDO.donateAmount, jdbcType=INTEGER},
            </if>
            <if test="baseStatDO.shareCount != null">
                #{baseStatDO.shareCount, jdbcType=INTEGER},
            </if>
            <if test="baseStatDO.updateTime != null">
                #{baseStatDO.updateTime, jdbcType=TIMESTAMP}
            </if>
        </trim>
        on duplicate key update
        <trim prefix="" suffix="" suffixOverrides="," >
            <if test="baseStatDO.userId != null">
                user_id = values(user_id),
            </if>
            <if test="baseStatDO.caseId != null">
                case_id = values(case_id),
            </if>
            <if test="baseStatDO.shareBroughtAmount != null">
                share_brought_amount = values(share_brought_amount),
            </if>
            <if test="baseStatDO.shareFirstAmount != null">
                share_first_amount = values(share_first_amount),
            </if>
            <if test="baseStatDO.donateAmount != null">
                donate_amount = values(donate_amount),
            </if>
            <if test="baseStatDO.shareCount != null">
                share_count = values(share_count),
            </if>
            <if test="baseStatDO.updateTime != null">
                update_time = values(update_time),
            </if>
        </trim>
    </insert>

    <select id="selectByUserAndCase" resultMap="BaseResultMap">
        select <include refid="BaseFields"/>
        from <include refid="TableName"/>
        where user_id = #{userId} and case_id = #{caseId}
    </select>

    <select id="selectByUserAndCases" resultMap="BaseResultMap">
        select
        <include refid="BaseFields"/>
        from
        <include refid="TableName"/>
        where user_id = #{userId} and case_id in
        <foreach collection="caseIds" item="caseId" open="(" close=")" separator=",">
            #{caseId}
        </foreach>
    </select>

    <update id="updateCaseShareInfo">
        update <include refid="TableName"/>
        set share_count = share_count + 1
        where user_id = #{userId} and case_id = #{caseId}
    </update>

    <update id="updateShareBroughtAmount">
        update <include refid="TableName"/>
        set share_brought_amount = share_brought_amount + #{shareBroughtAmountIncr}
        where user_id = #{userId} and case_id = #{caseId}
    </update>

    <update id="currentCaseDonateAmountIncr">
        update <include refid="TableName"/>
        set donate_amount = donate_amount + #{currentCaseDonateAmountIncr}
        where user_id = #{userId} and case_id = #{caseId}
    </update>

    <update id="updateShareFirstDonateAmount">
        update <include refid="TableName"/>
        set share_first_amount = #{shareFirstAmount}
        where user_id = #{userId} and case_id = #{caseId}
    </update>

</mapper>