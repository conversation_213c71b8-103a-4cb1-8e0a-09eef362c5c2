<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingMsgContentDao">
    <sql id="TABLE">
        crowdfunding_msg_content
    </sql>
    <sql id="QUERY_FIELDS">
        `id`,
        `key`,
        `content`,
        `type`,
        `name`,
        `date_created` as dateCreated,
		`last_modified` as lastModified
    </sql>
    
    <select id="getByKey" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingMsgContent">
    	SELECT <include refid="QUERY_FIELDS" />
    	FROM <include refid="TABLE" />
    	WHERE key=#{key}
    </select>
    
    <select id="getAll" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingMsgContent">
    	SELECT <include refid="QUERY_FIELDS" />
    	FROM <include refid="TABLE" />
        WHERE type=0
    </select>
    
</mapper>