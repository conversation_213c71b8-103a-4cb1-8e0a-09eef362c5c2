<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfProveTemplateDao">

    <sql id="tableName">
        cf_send_prove_template
    </sql>

    <sql id="selectFields">
        id,
        case_id,
        prove_id,
        action_id,
        template_id,
        title,
        content,
        commitment_content,
        audit_status
    </sql>

    <select id="findByCaseIdAndProveId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfSendProveTemplate">
        select
        <include refid="selectFields"/>
        from
        <include refid="tableName"/>
        where case_id = #{caseId}
        and prove_id = #{proveId}
        and audit_status in
        <foreach collection="auditStatusList" item="auditStatus" open="(" close=")" separator=",">
            #{auditStatus}
        </foreach>
        and is_delete = 0
    </select>

    <update id="updateContentById">
        update
        <include refid="tableName"/>
        set content = #{content},audit_status = #{auditStatus}
        where id = #{id}
        and is_delete = 0
    </update>
    <select id="getAllProve" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfSendProveTemplate">
        select
        <include refid="selectFields"/>
        from
        <include refid="tableName"/>
        where case_id = #{caseId}
        and prove_id = #{proveId}
        and is_delete = 0
    </select>
    <select id="getAuditProve" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfSendProveTemplate">
        select
        <include refid="selectFields"/>
        from
        <include refid="tableName"/>
        where case_id = #{caseId}
        and prove_id in
        <foreach collection="proveIdList" item="proveIds" open="(" close=")" separator=",">
            #{proveIds}
        </foreach>
        and audit_status = 2
        and is_delete = 0
    </select>
    <select id="getByAuditProve" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfSendProveTemplate">
        select
        <include refid="selectFields"/>
        from
        <include refid="tableName"/>
        where case_id = #{caseId}
        and prove_id = #{proveId}
        and audit_status = 2
        and is_delete = 0
        order by id desc limit 1
    </select>

    <update id="delete">
        update
        <include refid="tableName"/>
        set is_delete = 1
        where case_id = #{caseId}
        and prove_id = #{proveId}
        and audit_status = #{auditStatus}
    </update>

</mapper>
