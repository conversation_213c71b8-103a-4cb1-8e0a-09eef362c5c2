<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfRecommendInfoDao" >
  <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.model.crowdfunding.CfRecommendInfo" >
    <constructor >
      <idArg column="id" jdbcType="INTEGER" javaType="java.lang.Integer" />
      <arg column="info_id" jdbcType="INTEGER" javaType="java.lang.Integer" />
      <arg column="type" jdbcType="SMALLINT" javaType="java.lang.Short" />
      <arg column="create_time" jdbcType="TIMESTAMP" javaType="java.util.Date" />
      <arg column="last_modified" jdbcType="TIMESTAMP" javaType="java.util.Date" />
      <arg column="valid" jdbcType="BIT" javaType="java.lang.Boolean" />
      <arg column="info_uuid" jdbcType="VARCHAR" javaType="java.lang.String" />
    </constructor>
  </resultMap>
  <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfRecommendInfo" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cf_recommend_info (info_id, type, create_time,
      last_modified, valid, info_uuid
      )
    values (#{infoId,jdbcType=INTEGER}, #{type,jdbcType=SMALLINT}, #{createTime,jdbcType=TIMESTAMP},
      #{lastModified,jdbcType=TIMESTAMP}, #{valid,jdbcType=BIT}, #{infoUuid,jdbcType=VARCHAR}
      )
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfRecommendInfo" >
    update cf_recommend_info
    set info_id = #{infoId,jdbcType=INTEGER},
      type = #{type,jdbcType=SMALLINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      last_modified = #{lastModified,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=BIT},
      info_uuid = #{infoUuid,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select id, info_id, type, create_time, last_modified, valid, info_uuid
    from cf_recommend_info
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectRandomInfos" resultMap="BaseResultMap" >
    select t1.*
    from cf_recommend_info t1 WHERE t1.valid = 1
    order by rand() LIMIT #{limit}
  </select>
</mapper>
