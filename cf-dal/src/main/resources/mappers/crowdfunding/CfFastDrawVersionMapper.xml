<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfFastDrawVersionDao">

    <sql id="TABLE">
        cf_fast_draw_version
    </sql>

    <sql id="Base_Field">
        case_id, fast_version
    </sql>
    <update id="save">
        insert ignore into <include refid="TABLE"/> (case_id, fast_version)
        values (#{caseId}, #{fastVersion})
    </update>

    <select id="getByCaseId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfFastDrawVersion">
        select
        <include refid="Base_Field" />
        from
        <include refid="TABLE"/>
        where case_id = #{caseId} and is_delete = 0
    </select>

</mapper>
