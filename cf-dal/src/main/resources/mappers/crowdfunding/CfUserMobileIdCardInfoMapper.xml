<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfUserMobileIdCardInfoDao">

    <sql id="table_name">
        `cf_user_mobile_idcard_info`
    </sql>
    
    <sql id="insert_fields">
        `user_id`,
        `crypto_mobile`,
        `crypto_idcard`,
        `real_name`
    </sql>
    
    <sql id="select_fileds">
        `id`,
        `user_id`,
        `crypto_mobile`,
        `crypto_idcard`,
        `real_name`
    </sql>

    <insert id="insertOne" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfUserMobileIdCardInfo">
        insert into <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        values (#{userId},#{cryptoMobile},#{cryptoIdcard},#{realName})
    </insert>
    
    <select id="selectByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfUserMobileIdCardInfo">
        select <include refid="select_fileds"/>
        from <include refid="table_name"/>
        where `user_id`=#{userId}
    </select>

    <update id="updateByUserId" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfUserMobileIdCardInfo">
        update <include refid="table_name"/>
        set `crypto_mobile`=#{cryptoMobile}, `crypto_idcard`=#{cryptoIdcard}, `real_name`=#{realName}
        where `user_id`=#{userId}
    </update>
</mapper>