<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfUserPortraitRuleDao">

    <sql id="TABLE">
        cf_user_portrait_rule
    </sql>

    <sql id="Base_Field">
        `user_portrait_id` as userPortraitId,
        `crowd_pack` as crowdPack,
        `crowd_pack_data` as crowdPackData,
        `eagle_eye_experiment` as eagleEyeExperiment,
        `eagle_eye_result` as eagleEyeResult
    </sql>

    <select id="getByUserPortraitId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfUserPortraitRule">
        SELECT
        <include refid="Base_Field"/>
        FROM
        <include refid="TABLE"/>
        WHERE
        user_portrait_id = #{userPortraitId}
        AND
        is_delete = 0
    </select>

    <select id="getListByUserPortraitIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfUserPortraitRule">
        SELECT
        <include refid="Base_Field"/>
        FROM
        <include refid="TABLE"/>
        WHERE
        user_portrait_id IN
        <foreach collection="userPortraitIds" item="userPortraitId" open="(" close=")" separator=",">
            #{userPortraitId}
        </foreach>
        AND
        is_delete = 0
    </select>

</mapper>
