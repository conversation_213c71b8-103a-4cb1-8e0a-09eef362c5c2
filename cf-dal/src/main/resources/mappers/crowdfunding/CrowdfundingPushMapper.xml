<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingPushDao">
    <sql id="tableName">
		crowdfunding_push_history
	</sql>

    <sql id="fields">
		`id`, `crowdfunding_id`,`user_id`,`open_id`,`nickname`,`crowdfunding_author_name`,`crowdfunding_title`
		,`crowdfunding_info_id`,`amount`,`target_amount`,`url`,`create_time`,`wx_message_temple_id`
	</sql>

    <sql id="insertFields">
		`crowdfunding_id`,`user_id`,`open_id`,`nickname`,`crowdfunding_author_name`,`crowdfunding_title`
		,`crowdfunding_info_id`,`amount`,`target_amount`,`url`,`wx_message_temple_id`
	</sql>


    <insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPushHistory"
            useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO
        <include refid="tableName"/>
        (<include refid="insertFields"/>)
        VALUES
        (#{crowdfundingId},#{userId},#{openId},#{nickname},#{crowdfundingAuthorName},#{crowdfundingTitle},#{crowdfundingInfoId},#{amount},#{targetAmount},#{url},#{wxMessageTempleId});
    </insert>

    <select id="getAll"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPushHistory">
        SELECT
        <include refid="fields"/>
        FROM
        <include refid="tableName"/>
    </select>
    <select id="getByUserId"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPushHistory">
        SELECT
        <include refid="fields"/>
        FROM
        <include refid="tableName"/>
        WHERE
        `user_id`=#{userId};
    </select>
    <select id="getCountByUserId"
            resultType="int"
    >
        SELECT
        count(id)
        FROM
        <include refid="tableName"/>
        WHERE
        `user_id`=#{userId};
    </select>
    <select id="getCountByUserIdAndCrowdfundingId"
            resultType="int"
    >
        SELECT
        count(id)
        FROM
        <include refid="tableName"/>
        WHERE
        `user_id`=#{userId}
        and `crowdfunding_id` = #{crowdfundingId};
    </select>
</mapper>
