<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfUserCaseRefundStatDao">

    <sql id="TableName">
        shuidi_crowdfunding_user.crowdfunding_user_case_refund_stat_${tableSuffix}
    </sql>

    <sql id="BaseFields">
        id,user_id,case_id,refund_amount,create_time,update_time
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfUserCaseRefundStatDO" useGeneratedKeys="true" keyProperty="baseStatDO.id">
        INSERT INTO <include refid="TableName"/>
        (`user_id`, `case_id`, `refund_amount`)
        VALUES
        (#{baseStatDO.userId}, #{baseStatDO.caseId}, #{baseStatDO.refundAmount})
    </insert>

    <select id="selectByUserAndCaseId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfUserCaseRefundStatDO">
        select * from <include refid="TableName"/>
        where user_id = #{userId} and case_id = #{caseId}
    </select>

    <update id="updateRefundAmount">
        update <include refid="TableName"/>
        set refund_amount = refund_amount + #{refundAmount}
        where user_id = #{userId} and case_id = #{caseId}
    </update>

</mapper>