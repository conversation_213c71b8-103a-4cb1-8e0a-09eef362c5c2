<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfRecommendInfoTagRelationDao">
	<resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.model.crowdfunding.CfRecommendInfoTagRelation">
		<constructor>
			<idArg column="id" jdbcType="INTEGER" javaType="java.lang.Integer"/>
			<arg column="info_id" jdbcType="INTEGER" javaType="java.lang.Integer"/>
			<arg column="tag_id" jdbcType="INTEGER" javaType="java.lang.Integer"/>
			<arg column="valid" jdbcType="BIT" javaType="java.lang.Boolean"/>
			<arg column="create_time" jdbcType="TIMESTAMP" javaType="java.util.Date"/>
			<arg column="last_modified" jdbcType="TIMESTAMP" javaType="java.util.Date"/>
			<arg column="order_num" jdbcType="INTEGER" javaType="java.lang.Integer"/>
			<arg column="tag" jdbcType="VARCHAR" javaType="java.lang.String"/>
			<arg column="type" jdbcType="SMALLINT" javaType="java.lang.Short"/>
		</constructor>
	</resultMap>
	<insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfRecommendInfoTagRelation">
		<selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER">
			SELECT LAST_INSERT_ID()
		</selectKey>
		insert into cf_recommend_info_tag_relation (info_id, tag_id, valid,
		create_time, last_modified, order_num,
		tag, type)
		values (#{infoId,jdbcType=INTEGER}, #{tagId,jdbcType=INTEGER}, #{valid,jdbcType=BIT},
		#{createTime,jdbcType=TIMESTAMP}, #{lastModified,jdbcType=TIMESTAMP}, #{orderNum,jdbcType=INTEGER},
		#{tag,jdbcType=VARCHAR}, #{type,jdbcType=SMALLINT})
	</insert>
	<update id="updateByPrimaryKey" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfRecommendInfoTagRelation">
    update cf_recommend_info_tag_relation
    set info_id = #{infoId,jdbcType=INTEGER},
      tag_id = #{tagId,jdbcType=INTEGER},
      valid = #{valid,jdbcType=BIT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      last_modified = #{lastModified,jdbcType=TIMESTAMP},
      order_num = #{orderNum,jdbcType=INTEGER},
      tag = #{tag,jdbcType=VARCHAR},
      type = #{type,jdbcType=SMALLINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
	<select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
    select id, info_id, tag_id, valid, create_time, last_modified, order_num, tag, type
    from cf_recommend_info_tag_relation
    where id = #{id,jdbcType=INTEGER}
  </select>
	<select id="selectByInfoIds" resultMap="BaseResultMap">
		select id, info_id, tag_id, valid, create_time, last_modified, order_num, tag, type
		from cf_recommend_info_tag_relation
		WHERE
		info_id in
		<foreach item="item" index="index" collection="infoIds"
		         open="(" separator="," close=")">
			#{item}
		</foreach>
		AND valid = 1 order by order_num
	</select>
</mapper>
