<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfReqCommitLogDAO">
    <sql id="TABLE_NAME">
        cf_request_commit_log
    </sql>

    <sql id="Insert_Column_List">
        request_uuid,business_no,status,parameter
    </sql>

    <resultMap id="BaseResultMap" type="com.shuidihuzhu.client.model.CfRequestCommitLogDO">
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="request_uuid" property="requestUuid" jdbcType="VARCHAR"/>
        <result column="business_no" property="businessNo" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="parameter" property="paramter" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <insert id="insert" parameterType="com.shuidihuzhu.client.model.CfRequestCommitLogDO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="TABLE_NAME"/>
        (<include refid="Insert_Column_List"/>)
        VALUES
        (#{requestUuid},#{businessNo},#{status},#{paramter})
    </insert>

    <select id="queryById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select * from <include refid="TABLE_NAME"/>
        where id = #{id}
    </select>

    <select id="queryByRequestId" parameterType="java.lang.String" resultMap="BaseResultMap">
        select * from <include refid="TABLE_NAME"/>
        where request_uuid = #{requestUuid}
    </select>

    <select id="query" resultMap="BaseResultMap">
        select * from <include refid="TABLE_NAME"/>
        where id > #{offset} and create_time <![CDATA[ < ]]> #{before}
        limit #{limit}
    </select>

    <delete id="delete" parameterType="java.lang.Long">
        delete from <include refid="TABLE_NAME"/>
        where id = #{id}
    </delete>

    <update id="updateStatus">
        update <include refid="TABLE_NAME"/>
        set status = #{status}
        where request_uuid = #{requestId}
    </update>
</mapper>