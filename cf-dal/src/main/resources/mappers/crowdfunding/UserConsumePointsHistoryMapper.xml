<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.UserConsumePointsHistoryMapper">
  <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.model.crowdfunding.UserConsumePointsHistory">
    <constructor>
      <idArg column="id" javaType="java.lang.Long" jdbcType="BIGINT" />
      <arg column="user_id" javaType="java.lang.Long" jdbcType="BIGINT" />
      <arg column="biz_type" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="user_third_type" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="points" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="status" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="action" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="out_trade_no" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="create_time" javaType="java.util.Date" jdbcType="TIMESTAMP" />
      <arg column="update_time" javaType="java.util.Date" jdbcType="TIMESTAMP" />
      <arg column="is_delete" javaType="java.lang.Boolean" jdbcType="BIT" />
      <arg column="channel" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="remark" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="trade_no" javaType="java.lang.String" jdbcType="VARCHAR" />
    </constructor>
  </resultMap>
  <sql id="Base_Column_List">
    id, user_id, biz_type, user_third_type, points, status, action, out_trade_no, create_time, 
    update_time, is_delete, channel, remark,trade_no
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from user_consume_points_history
    where id = #{id,jdbcType=BIGINT} and is_delete = 0
  </select>

  <select id="selectByTradeNo" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from user_consume_points_history
    where trade_no = #{tradeNo} and is_delete = 0
  </select>
  <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.UserConsumePointsHistory">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into user_consume_points_history (user_id, biz_type, user_third_type, 
      points, action,
      out_trade_no, channel, remark,trade_no
      )
    values (#{userId,jdbcType=BIGINT}, #{bizType,jdbcType=INTEGER}, #{userThirdType,jdbcType=INTEGER},
      #{points,jdbcType=INTEGER}, #{action,jdbcType=INTEGER},
      #{outTradeNo,jdbcType=VARCHAR}, #{channel,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},#{tradeNo}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.shuidihuzhu.cf.model.crowdfunding.UserConsumePointsHistory">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into user_consume_points_history
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId > 0">
        user_id,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="userThirdType != null">
        user_third_type,
      </if>
      <if test="points != null">
        points,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="action != null">
        action,
      </if>
      <if test="outTradeNo != null">
        out_trade_no,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="remark != null">
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId > 0">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=INTEGER},
      </if>
      <if test="userThirdType != null">
        #{userThirdType,jdbcType=INTEGER},
      </if>
      <if test="points != null">
        #{points,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="action != null">
        #{action,jdbcType=INTEGER},
      </if>
      <if test="outTradeNo != null">
        #{outTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BIT},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shuidihuzhu.cf.model.crowdfunding.UserConsumePointsHistory">
    update user_consume_points_history
    <set>
      <if test="userId > 0">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=INTEGER},
      </if>
      <if test="userThirdType != null">
        user_third_type = #{userThirdType,jdbcType=INTEGER},
      </if>
      <if test="points != null">
        points = #{points,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="action != null">
        action = #{action,jdbcType=INTEGER},
      </if>
      <if test="outTradeNo != null">
        out_trade_no = #{outTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=BIT},
      </if>
      <if test="channel != null">
        channel = #{channel,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shuidihuzhu.cf.model.crowdfunding.UserConsumePointsHistory">
    update user_consume_points_history
    set user_id = #{userId,jdbcType=BIGINT},
      biz_type = #{bizType,jdbcType=INTEGER},
      user_third_type = #{userThirdType,jdbcType=INTEGER},
      points = #{points,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      action = #{action,jdbcType=INTEGER},
      out_trade_no = #{outTradeNo,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_delete = #{isDelete,jdbcType=BIT},
      channel = #{channel,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="backByTradeNo">
    update user_consume_points_history
    set user_id = #{userId,jdbcType=BIGINT},
      status = 1 ,
      remark = #{remark,jdbcType=VARCHAR}
    where trade_no = #{tradeNo} and status = 0
  </update>
</mapper>