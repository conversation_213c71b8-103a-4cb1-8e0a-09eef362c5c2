<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfFirstApproveIdCardWhiteListRecordDao">
    <sql id="TABLE">
		cf_first_approve_idcard_white_list_record
	</sql>

    <insert id="save" parameterType="com.shuidihuzhu.cf.model.crowdfunding.IdcardVerifyWhiteListRecord">
        insert into <include refid="TABLE"/>
        (`verify_id`, `operator`, `operation_remark`, `reason`, `other_reason`)
        values (#{verifyId}, #{operator}, #{operationRemark}, #{reason}, #{otherReason})
    </insert>

    <select id="findByVerifyId" resultType="com.shuidihuzhu.cf.model.crowdfunding.IdcardVerifyWhiteListRecord">
        select * from <include refid="TABLE"/>
        where verify_id = #{verifyId} order by create_time desc
    </select>

</mapper>