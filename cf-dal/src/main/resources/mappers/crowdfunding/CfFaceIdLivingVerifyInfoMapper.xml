<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfFaceIdLivingVerifyInfoDao">
    <sql id="TABLE">
        cf_living_verify_info
    </sql>

    <sql id="FIELDS">
        info_uuid,
        uuid,
        card_name,
        verify_type,
        count_num,
        photo_valid
    </sql>

    <select id="getByInfoUuid" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfFaceIdLivingVerifyInfo">
        SELECT
        <include refid="FIELDS"/>
        FROM
        <include refid="TABLE"/>
        WHERE
        `info_uuid` = #{infoUuid}
    </select>
</mapper>