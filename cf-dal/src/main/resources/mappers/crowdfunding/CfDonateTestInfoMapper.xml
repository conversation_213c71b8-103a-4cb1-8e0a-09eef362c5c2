<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfDonateTestInfoDao">
    <sql id="TABLE">
        cf_donate_test_info
    </sql>
    <sql id="FIELDS">
        id AS id,
        user_id AS userId,
        open_id AS openId,
        msg_status AS msgStatus,
        is_delete AS isDelete
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfDonateTestInfo">
        INSERT IGNORE INTO <include refid="TABLE"/>
        (`user_id`,`open_id`)
        VALUE
        (#{userId},#{openId})
    </insert>

    <select id="selectByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfDonateTestInfo">
        SELECT <include refid="FIELDS"/>
        FROM <include refid="TABLE"/>
        WHERE `user_id`=#{userId}
        AND `is_delete`=0
    </select>

    <select id="selectByOpenId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfDonateTestInfo">
        SELECT <include refid="FIELDS"/>
        FROM <include refid="TABLE"/>
        WHERE `open_id`=#{openId}
        AND `is_delete`=0
    </select>

    <update id="updateMsgStatus">
        UPDATE <include refid="TABLE"/>
        SET `msg_status`=1,`open_id`=#{openId}
        WHERE `user_id`=#{userId}
    </update>
</mapper>
