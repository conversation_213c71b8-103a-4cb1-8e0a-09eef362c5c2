<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CrowdFundingRefuseMsgDao">

	<sql id="table_name">
		cf_refuse_msg_info
	</sql>

    <sql id="select_fields">
        `id`,
        `info_id`,
        `comment_text`,
        `reason`
    </sql>

	<insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingRefuseMsg" >
		INSERT INTO <include refid="table_name"/>
		(info_id,comment_text,reason)
		VALUES
		(#{infoId},#{commentText},#{reason})
	</insert>

	<select id="getByInfoId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingRefuseMsg">
		SELECT <include refid="select_fields"/>
		FROM <include refid="table_name" />
		WHERE info_id=#{infoId}
		ORDER BY id DESC
		LIMIT 1
	</select>

	<select id="getByInfoIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingRefuseMsg">
		SELECT <include refid="select_fields"/>
		FROM <include refid="table_name" />
		WHERE info_id in <foreach collection="infoIds" item="infoId" open="(" separator="," close=")" >#{infoId}</foreach>
	</select>

</mapper>
