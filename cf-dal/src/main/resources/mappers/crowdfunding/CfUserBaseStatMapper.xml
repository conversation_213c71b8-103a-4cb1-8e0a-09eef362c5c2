<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfUserBaseStatDao">

    <sql id="TableName">
        shuidi_crowdfunding_user.crowdfunding_user_stat_${tableSuffix}
    </sql>

    <sql id="BaseFields">
		id,user_id,donate_case_count,donate_count,donate_amount,share_case_count,share_count,create_time,update_time
	</sql>

    <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.model.crowdfunding.CfUserBaseStatDO">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="user_id" property="userId" jdbcType="INTEGER"/>
        <result column="donate_case_count" property="donateCaseCount" jdbcType="INTEGER"/>
        <result column="donate_count" property="donateCount" jdbcType="INTEGER"/>
        <result column="donate_amount" property="donateAmount" jdbcType="INTEGER"/>
        <result column="share_case_count" property="shareCaseCount" jdbcType="INTEGER"/>
        <result column="share_count" property="shareCount" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <update id="insert"  useGeneratedKeys="true" keyProperty="baseStatDO.id">
        INSERT  INTO <include refid="TableName" />
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="baseStatDO.userId != null">
                user_id,
            </if>
            <if test="baseStatDO.donateCaseCount != null">
                donate_case_count,
            </if>
            <if test="baseStatDO.donateCount != null">
                donate_count,
            </if>
            <if test="baseStatDO.donateAmount != null">
                donate_amount,
            </if>
            <if test="baseStatDO.shareCaseCount != null">
                share_case_count,
            </if>
            <if test="baseStatDO.shareCount != null">
                share_count,
            </if>
            <if test="baseStatDO.updateTime != null">
                update_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="baseStatDO.userId != null">
                #{baseStatDO.userId, jdbcType=INTEGER},
            </if>
            <if test="baseStatDO.donateCaseCount != null">
                #{baseStatDO.donateCaseCount, jdbcType=INTEGER},
            </if>
            <if test="baseStatDO.donateCount != null">
                #{baseStatDO.donateCount, jdbcType=INTEGER},
            </if>
            <if test="baseStatDO.donateAmount != null">
                #{baseStatDO.donateAmount, jdbcType=INTEGER},
            </if>
            <if test="baseStatDO.shareCaseCount != null">
                #{baseStatDO.shareCaseCount, jdbcType=INTEGER},
            </if>
            <if test="baseStatDO.shareCount != null">
                #{baseStatDO.shareCount, jdbcType=INTEGER},
            </if>
            <if test="baseStatDO.updateTime != null">
                #{baseStatDO.updateTime, jdbcType=TIMESTAMP}
            </if>
        </trim>
        on duplicate key update
        <trim prefix="" suffix="" suffixOverrides="," >
            <if test="baseStatDO.userId != null">
                user_id = values(user_id),
            </if>
            <if test="baseStatDO.donateCaseCount != null">
                donate_case_count = values(donate_case_count),
            </if>
            <if test="baseStatDO.donateCount != null">
                donate_count = values(donate_count),
            </if>
            <if test="baseStatDO.donateAmount != null">
                donate_amount = values(donate_amount),
            </if>
            <if test="baseStatDO.shareCaseCount != null">
                share_case_count = values(share_case_count),
            </if>
            <if test="baseStatDO.shareCount != null">
                share_count = values(share_count),
            </if>
            <if test="baseStatDO.updateTime != null">
                update_time = values(update_time),
            </if>
        </trim>
    </update>

    <select id="selectByUser" resultMap="BaseResultMap">
        select <include refid="BaseFields"/>
        from <include refid="TableName"/>
        where user_id = #{userId}
    </select>

    <update id="updateUserShareInfo">
        update <include refid="TableName"/>
        set share_case_count = share_case_count + #{shareCaseIncr}, share_count = share_count + 1
        where user_id = #{userId}
    </update>

    <update id="updateDonateStatInfo">
        update <include refid="TableName"/>
        set donate_case_count = donate_case_count + #{donateCaseIncr}, donate_count = donate_count + 1, donate_amount = donate_amount + #{donateAmountIncr}
        where user_id = #{userId}
    </update>
</mapper>