<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.EvaluateLabelPatientRecordDao">

    <sql id="tableName">
        evaluate_label_patient_record
    </sql>
    <insert id="insertOrUpdate">
        insert into <include refid="tableName" />
        (`case_id`,`label_id`,`label_cnt`)
        values
        <foreach collection="labels" item="label" separator=",">
            (#{label.caseId},#{label.labelId},1)
        </foreach>
        on duplicate key update
        `label_cnt` = `label_cnt` + 1
    </insert>

    <update id="addOrUpdate">
        insert into <include refid="tableName"/>
        (`case_id`, `label_id`, `label_cnt`)
        VALUES (#{caseId}, #{labelId}, 0)
        on duplicate key update
        `label_cnt` = `label_cnt` + 1
    </update>

    <select id="getCnt" resultType="java.lang.Integer">
        select label_cnt from <include refid="tableName"/>
        where
        `label_id` = #{labelId}
        and `case_id` = #{caseId}
        and `is_delete` = 0
    </select>
    <select id="getByCaseId" resultType="com.shuidihuzhu.cf.model.crowdfunding.EvaluateLabelPatientRecordDO">
        select * from <include refid="tableName"/>
        where
        `case_id` = #{caseId}
        and `is_delete` = 0
    </select>
    <select id="getIdByCaseId" resultType="com.shuidihuzhu.cf.model.crowdfunding.EvaluateLabelPatientRecordDO">
        select * from <include refid="tableName"/>
        where
        `case_id` = #{caseId}
        and `is_delete` = 0
    </select>

    <select id="getMaxUsedByCaseId" resultType="com.shuidihuzhu.cf.model.crowdfunding.EvaluateLabelPatientRecordDO">
        select * from <include refid="tableName"/>
        <where>
        `case_id` = #{caseId}
        and    `is_delete` = 0
        </where>
        order by label_cnt desc, update_time desc
        limit 1
    </select>
    <select id="getListByCaseIdOrderByCnt" resultType="com.shuidihuzhu.cf.model.crowdfunding.EvaluateLabelPatientRecordDO">
        select * from <include refid="tableName"/>
        <where>
            `case_id` = #{caseId}
        and    `is_delete` = 0
        </where>
        order by label_cnt desc, update_time desc
    </select>
</mapper>