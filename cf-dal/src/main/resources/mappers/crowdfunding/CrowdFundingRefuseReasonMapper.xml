<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CrowdFundingRefuseReasonDao">

	<sql id="tablename">
		cf_refuse_reason
	</sql>
	

	<select id="getListByIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingRefuseReason">
		SELECT `id`, `pid`, `content`
		FROM <include refid="tablename" />
		WHERE `id` IN
		<foreach item="item" index="index" collection="ids"
				 open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>
	
</mapper>