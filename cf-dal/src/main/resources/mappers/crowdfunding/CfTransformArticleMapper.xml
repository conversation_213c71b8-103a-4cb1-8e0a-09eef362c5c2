<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfTransformArticleDao">

    <sql id="table_name">
        cf_transform_article
    </sql>

    <sql id="select_fields">
        `id`,
        `title`,
        `url`,
        `img_url`,
        `create_time`,
        `update_time`
    </sql>

    <sql id="insert_fields">
        `title`,
        `url`,
        `img_url`
    </sql>

    <insert id="insert"
            parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfTransformArticleDo">
        INSERT INTO <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES (
        #{title},
        #{url},
        #{imgUrl}
        )
    </insert>

    <select id="listByAnchor"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.CfTransformArticleDo">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `is_delete` = 0
        ORDER BY `create_time` DESC
        LIMIT #{start}, #{size}
    </select>

    <select id="listByPage"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.CfTransformArticleDo">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `is_delete` = 0
        ORDER BY `create_time` DESC
    </select>

    <select id="listByPageAndTitleKey"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.CfTransformArticleDo">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `is_delete` = 0
        AND `title` LIKE CONCAT(#{titleKey}, '%')
        ORDER BY `create_time` DESC
    </select>

    <update id="delete">
        UPDATE <include refid="table_name"/>
        SET `is_delete`=1
        WHERE `id`=#{id}
    </update>
    
</mapper>