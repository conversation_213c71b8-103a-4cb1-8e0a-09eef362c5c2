<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
CREATE TABLE crowdfunding_bank_card_verify_record (
	`id` INT NOT NULL AUTO_INCREMENT COMMENT '自增长ID',
	`crowdfunding_id` INT NOT NULL COMMENT 'crowdfunding_info的ID',
	`crypto_holder_name` varchar(100) NOT NULL DEFAULT '' COMMENT '加密存储的卡主姓名',
	`identity_type` SMALLINT NOT NULL DEFAULT 0 COMMENT '证件类型,枚举值按序号存储',
	`crypto_id_card` varchar(100) NOT NULL COMMENT '加密存储的证件号码',
	`crypto_bank_card` varchar(100) NOT NULL COMMENT '加密存储的银行卡号',
	`crypto_mobile` varchar(20) NULL COMMENT '加密存储的手机号码',
	`verify_status` varchar(20) NOT NULL COMMENT '银行卡验证状态,枚举值按字符串存储',
	`verify_message` varchar(255) NULL COMMENT '银行卡验证结果消息',
	`date_created` TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
	`last_modified` TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '最后修改时间',
	PRIMARY KEY (`id`),
	KEY `ix_crowdfunding_id` (`crowdfunding_id`, `id`),
	KEY `ix_verify_status` (`verify_status`, `id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT '银行卡号验证记录';

ALTER TABLE crowdfunding_bank_card_verify_record
ADD COLUMN `verify_message2` varchar(255) NULL COMMENT '银行卡验证结果消息2' AFTER `verify_message`;
-->

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.BankCardVerifyRecordDao">
	<sql id="tableName">
		crowdfunding_bank_card_verify_record
	</sql>
	
	<sql id="selectFields">
		`id`, `crowdfunding_id`, `trade_no`, `third_trade_no`, `crypto_holder_name`, `identity_type`, `crypto_id_card`,
		`crypto_bank_card`,
		`crypto_mobile`, `verify_status`, `verify_message`, `verify_message2`, `date_created`, `last_modified`
	</sql>
	
	<select id="get" parameterType="int" resultType="com.shuidihuzhu.cf.model.crowdfunding.BankCardVerifyRecord">
		SELECT
		<include refid="selectFields"/>
		FROM
		<include refid="tableName"/>
		WHERE id = #{id}
	</select>
	
	<select id="getByThreeElements" resultType="com.shuidihuzhu.cf.model.crowdfunding.BankCardVerifyRecord">
		SELECT
		<include refid="selectFields"/>
		FROM
		<include refid="tableName"/>
		WHERE `crypto_holder_name`=#{cryptoHolderName} AND `crypto_id_card`=#{cryptoIdCard}
		AND `crypto_bank_card`=#{cryptoBankCard} AND verify_status=#{verifyStatus}
		ORDER BY `id` DESC
		LIMIT 1
	</select>
	
	<insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.BankCardVerifyRecord" useGeneratedKeys="true"
	        keyProperty="id">
		INSERT INTO
		<include refid="tableName"/>
		(`crowdfunding_id`,`trade_no`,`third_trade_no`,`crypto_holder_name`,`identity_type`,`crypto_id_card`,
		`crypto_bank_card`, `crypto_mobile`,`verify_status`,`verify_message`,`verify_message2`)
		VALUES
		(#{crowdfundingId},#{tradeNo},#{thirdTradeNo},#{cryptoHolderName},#{identityType},#{cryptoIdCard},
		#{cryptoBankCard},#{cryptoMobile},#{verifyStatus},#{verifyMessage},#{verifyMessage2})
	</insert>
	
	<select id="listByVerifyStatus" resultType="com.shuidihuzhu.cf.model.crowdfunding.BankCardVerifyRecord">
		SELECT
		<include refid="selectFields"/>
		FROM
		<include refid="tableName"/>
		WHERE verify_status = #{verifyStatus}
		LIMIT #{offset}, #{limit}
	</select>
	
	<update id="updatePassed">
		update
		<include refid="tableName"/>
		set `verify_status` = 'passed' ,`verify_message` = '认证通过',`verify_message2` = '认证通过'
		where `crowdfunding_id` = #{caseId} and `crypto_holder_name` = #{holderName} and
		`crypto_id_card` =#{cryptoIdCard} and `crypto_bank_card` = #{cryptoBankCard} and `crypto_mobile` = #{cryptoMobile}
	</update>

	<select id="getByCaseIdAndElements" resultType="com.shuidihuzhu.cf.model.crowdfunding.BankCardVerifyRecord">
		SELECT
		<include refid="selectFields"/>
		FROM
		<include refid="tableName"/>
		WHERE `crypto_holder_name`=#{holderName} AND `crypto_id_card`=#{cryptoIdCard}
		AND `crypto_bank_card`=#{cryptoBankCard} AND `crowdfunding_id` = #{caseId}
	</select>
</mapper>