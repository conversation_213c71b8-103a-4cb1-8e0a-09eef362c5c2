<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CaseLabelsJinYunCountyDao">
    <sql id="TABLE">
        jin_yun_county_label
    </sql>

    <select id="getCount" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM
        <include refid="TABLE"/>
        WHERE info_uuid = #{infoUuid} and valid = 0 and is_delete = 0
    </select>

    <update id="update">
        UPDATE
        <include refid="TABLE"/>
        SET
        valid = 1
        WHERE info_uuid = #{infoUuid}
    </update>

</mapper>