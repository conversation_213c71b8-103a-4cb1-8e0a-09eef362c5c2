<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfProveSnapshotDao">
    <sql id="tableName">
        cf_send_prove_snapshot
    </sql>

    <insert id="insertOne">
        insert into
        <include refid="tableName"/>
        (case_id,prove_id,prove_snapshot,audit_status)
        values
        (#{caseId},#{proveId},#{proveSnapshot},#{auditStatus})
    </insert>


</mapper>
