<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfPaySplitFlowRecordDao">
    <sql id="TABLE_NAME">
        `cf_pay_split_flow_record`
    </sql>

    <sql id="FIELDS">
        `id` as id,
        `user_id` as userId,
        `uuid` as uuid,
        `info_uuid` as infoUuid,
        `auth_type` as authType,
        `app_id`  as appId,
        `user_third_type` as userThirdType,
        `rule` as rule,
        `is_delete` as isDelete,
        `create_time` as createTime,
        `update_time` as updateTime,
        `mobile_type` as mobileType,
        `province` as province,
        `city` as city
    </sql>
    
    <insert id="save" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfPaySplitFlowRecord">
        INSERT INTO <include refid="TABLE_NAME"/>
        		(`uuid`,`info_uuid`,`user_id`,`auth_type`,`app_id`,`user_third_type`,`rule`,`mobile_type`,`province`,`city`)
        VALUES
	        (#{uuid},#{infoUuid},#{userId},#{authType},#{appId},#{userThirdType},#{rule},#{mobileType},#{province},#{city})
    </insert>

    <select id="findByUserIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfPaySplitFlowRecord" >
        SELECT *
        FROM <include refid="TABLE_NAME"/>
        WHERE `user_id` IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>


    <select id="findLastByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfPaySplitFlowRecord">
        SELECT <include refid="FIELDS"/>
        FROM <include refid="TABLE_NAME" />
        WHERE `user_id`=#{userId}
        AND `is_delete`=0
        ORDER BY `id` DESC
        LIMIT 1
    </select>

</mapper>
