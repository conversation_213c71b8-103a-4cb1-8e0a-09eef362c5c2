<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.ReportInsteadWorkOrderRelateDAO">

    <sql id="tableName">
        report_instead_work_order_relate
    </sql>

    <sql id="selectFields">
        id,
        report_instead_id,
        work_order_id,
        create_time,
        update_time,
        is_delete
    </sql>
    <insert id="insert">
        insert into
        <include refid="tableName"/>
        (report_instead_id,work_order_id)
        values (#{reportInsteadId},#{workOrderId})
    </insert>
    <select id="getByReportInsteadIds"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.ReportInsteadWorkOrderRelateDO">
        select * from <include refid="tableName"/>
        where report_instead_id  IN
        <foreach collection="reportInsteadIds" open="(" close=")" separator="," item="reportInsteadId">
            #{repostInsteadId}
        </foreach>
        and is_delete = 0
        order by work_order_id desc
    </select>
    <select id="getByWorkOrderId"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.ReportInsteadWorkOrderRelateDO">
        select * from <include refid="tableName"/>
        where work_order_id = #{workOrderId}
        and is_delete = 0
        order by report_instead_id desc
    </select>
    <select id="getOneByWorkOrderId"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.ReportInsteadWorkOrderRelateDO">
        select * from <include refid="tableName"/>
        where work_order_id = #{workOrderId}
        and is_delete = 0
        order by report_instead_id asc limit 1
    </select>


</mapper>

