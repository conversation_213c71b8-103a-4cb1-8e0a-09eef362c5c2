<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfRefundRecordTaskDao">
    <select id="getUnCompleteRefundRecordTask" resultType="java.lang.Integer">
        select count(*)
        from cf_refund_record_task
        where info_uuid=#{infoUuid} and is_delete=0 and status in (0,1)
    </select>
</mapper>