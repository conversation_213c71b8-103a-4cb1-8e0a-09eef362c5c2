<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfInfoShareRecordUserShardingDao">

	<sql id="TABLE">
		cf_info_share_record_user_sharding_${sharding}
	</sql>

	<sql id="fields">
		`id` as id,
		`user_id` as userId,
		`uuid` as uuid,
		`info_id` as infoId,
		`wx_source` as wxSource,
		`source` as source,
		`user_source_id` as userSourceId,
		`to_wx_source` as toWxSource,
		`channel` AS channel,
		`share_id` AS shareId,
		`share_source_id` AS shareSourceId,
		`create_time` as dateCreated
	</sql>

	<insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareRecord">
		INSERT ignore INTO
		<include refid="TABLE"/>
		(`id`,`user_id`,`uuid`,`info_id`,`wx_source`,`source`,
		`to_wx_source`, `channel`,`user_source_id`,`share_id`,`share_source_id`)
		VALUES
		(#{record.id}, #{record.userId},#{record.uuid},#{record.infoId},#{record.wxSource},#{record.source},#{record.toWxSource},
		#{record.channel},#{record.userSourceId},#{record.shareId},#{record.shareSourceId})
	</insert>

	<select id="selByUserIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareRecord">
		SELECT distinct info_id,user_id,uuid,source
		from <include refid="TABLE" />
		WHERE user_id  IN
		<foreach collection="userIds" open="(" close=")" separator="," item="userIds">
			#{userIds}
		</foreach>
		AND `create_time`
		between #{beforeTime} and #{newTime};
	</select>

	<select id="selByUserIdsAndCaseId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareRecord">
		SELECT info_id,user_id,uuid,source,`create_time` as dateCreated
		from <include refid="TABLE" />
		WHERE user_id  IN
		<foreach collection="userIds" open="(" close=")" separator="," item="userIds">
			#{userIds}
		</foreach>
		AND `create_time`
		between #{beforeTime} and #{newTime}
		and info_id = #{caseId};
	</select>

	<select id="findCfInfoShareRecord" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareRecord">
		select
		<include refid="fields"/>
		from <include refid="TABLE"/>
		<trim prefix="WHERE" prefixOverrides="AND|OR" suffixOverrides="AND|OR">
			<if test="userId!=null">
				AND user_id = #{userId}
			</if>
			<if test="startTime!=null and startTime!=''">
				AND <![CDATA[ `create_time` >= #{startTime} ]]>
			</if>
			<if test="endTime!=null and endTime!=''">
				AND <![CDATA[ `create_time` < #{endTime} ]]>
			</if>
		</trim>
	</select>
</mapper>
