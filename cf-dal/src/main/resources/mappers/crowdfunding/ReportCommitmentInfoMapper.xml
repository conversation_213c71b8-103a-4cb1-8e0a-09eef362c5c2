<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.ReportCommitmentInfoDao">
    <sql id="tableName">
        `cf_report_commitment_info`
    </sql>

    <sql id="selectFields">
        `id`,`incr_trust_id`,`illness_name`,`hospital`,`detail`,`treatment`,`user_name`,`add_date`,`patient_name`
    </sql>


    <select id="findByIncrTrustId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfReportCommitmentInfo">
        select
        <include refid="selectFields"/>
        from
        <include refid="tableName"/>
        where incr_trust_id = #{incrTrustId}
        and is_delete = 0
    </select>

    <update id="updateCommitmentInfo">
        update
        <include refid="tableName"/>
        set illness_name = #{illnessName},hospital = #{hospital},detail = #{detail},treatment = #{treatment},
        user_name = #{userName},add_date = #{addDate},patient_name = #{patientName}
        where incr_trust_id = #{incrTrustId}
        and is_delete = 0
    </update>

</mapper>
