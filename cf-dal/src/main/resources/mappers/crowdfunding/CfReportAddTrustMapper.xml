<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfReportAddTrustDao">
    <sql id="tableName">
    `cf_report_add_trust`
    </sql>

    <sql id="insertFields">
        (`info_uuid`,
        `audit_status`,
        `operator_content`,
        `content`,
        `image_urls`)
    </sql>

    <sql id="selectFields">
        `id`,
        `info_uuid`,
        `audit_status`,
        `operator_content`,
        `content`,
        `image_urls`,
        `create_time`,
        `update_time`,
        `issued_commitment`
    </sql>

    <insert id="save" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfReportAddTrust">
        INSERT INTO
        <include refid="tableName"/>
        <include refid="insertFields"/>
        VALUES (#{infoUuid},#{auditStatus},#{operatorContent},#{content},#{imageUrls})
    </insert>

    <update id="update" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfReportAddTrust">
        UPDATE <include refid="tableName"/>
        SET
        `audit_status`=#{auditStatus},
        `operator_content`=#{operatorContent},
        `content`=#{content},
        `image_urls`=#{imageUrls}
        WHERE `id` = #{id}
    </update>

    <select id="getNewestByInfoUuid" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfReportAddTrust">
        SELECT <include refid="selectFields"/>
        from <include refid="tableName"/>
        where `is_delete` = 0
        AND `info_uuid` = #{infoUuid}
        ORDER BY id desc limit 1
    </select>


    <update id="delete">
        UPDATE <include refid="tableName"/>
        SET `is_delete` = 1
        WHERE `info_uuid` = #{infoUuid}
    </update>

    <select id="getTrustById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfReportAddTrust">
        SELECT <include refid="selectFields"/>
        FROM <include refid="tableName"/>
        WHERE id = #{id}
    </select>

</mapper>