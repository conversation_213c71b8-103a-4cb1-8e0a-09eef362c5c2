<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfWxSubscribeReplyDao">

    <sql id="TABLE_NAME">
        cf_wx_subscribe_reply
    </sql>

    <sql id="FIELDS">
        `id` as id,
        `event_key` as eventKey,
        `method` as method,
        `type` as type,
        `keep_default_reply` as keepDefaultReply,
        `content` as content,
        `start_time` as startTime,
        `end_time` as endTime,
        `image_url` as imageUrl,
        `sub_biz_type` as subBizType,
        `third_type` as thirdType,
        `model_num` as modelNum
    </sql>

    <select id="getByEventKey" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfWxSubscribeReply">
        SELECT <include refid="FIELDS"/>
        FROM <include refid="TABLE_NAME"/>
        WHERE `event_key`=#{eventKey}
        AND `third_type`=#{thirdType}
        AND   `valid`=1
        LIMIT 1
    </select>

    <select id="getByMethod" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfWxSubscribeReply">
        SELECT <include refid="FIELDS"/>
        FROM <include refid="TABLE_NAME"/>
        WHERE `method`=#{method}
        AND   `valid`=1
        ORDER BY `id` DESC
    </select>

</mapper>