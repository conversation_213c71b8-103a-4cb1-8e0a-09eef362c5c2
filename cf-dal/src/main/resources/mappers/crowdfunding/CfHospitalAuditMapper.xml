<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfHospitalAuditDao">
    <sql id="tableName">
        `cf_info_hospital_audit`
    </sql>

    <sql id="selectFields">
    `id` ,
	`info_uuid` ,
	`patient_name` ,
	`hospital_name` ,
	`department` ,
	`floor_number` ,
	`bed_number` ,
	`hospitalization_number` ,
	`doctor_name` ,
	`department_tel_number` ,
	`audit_status`,
	`operator_content`,
	`type`,
    `easy_to_verify_time`,
    `easy_to_verify_time_status`,
    `on_work_order`,
    `submit_time`,
	is_delete,
    `hospital_id`,
    `province_id`,
    `city_id`,
    `province_name`,
    `city_name`,
    `audit_time`
    </sql>

    <sql id="insertFields">
        (`info_uuid` ,
        `patient_name` ,
        `hospital_name` ,
        `department` ,
        `floor_number` ,
        `bed_number` ,
        `hospitalization_number` ,
        `doctor_name` ,
        `department_tel_number` ,
        `audit_status`,
        `operator_content`,
        `type`)
    </sql>

    <insert id="save" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfHospitalAuditInfo">
        INSERT INTO
        <include refid="tableName"/>
        <include refid="insertFields"/>
        VALUES
        (#{infoUuid}, #{patientName}, #{hospitalName}, #{department},
        #{floorNumber}, #{bedNumber}, #{hospitalizationNumber}, #{doctorName},
        #{departmentTelNumber}, #{auditStatus}, #{operatorContent}, #{type})
    </insert>


    <select id="getByInfoUuid" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfHospitalAuditInfo">
        SELECT
        <include refid="selectFields"/>
        FROM
        <include refid="tableName"/>
        WHERE `is_delete` = 0
        AND `info_uuid` = #{infoUuid}
        ORDER BY id DESC  limit 1
    </select>



    <select id="getByInfoUuidALL" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfHospitalAuditInfo">
        SELECT
        <include refid="selectFields"/>
        FROM
        <include refid="tableName"/>
        WHERE  `info_uuid` = #{infoUuid}
        ORDER BY id DESC  limit 1
    </select>


    <update id="update" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfHospitalAuditInfo">
        UPDATE <include refid="tableName"/>
        SET
        `info_uuid` = #{infoUuid},
        `patient_name` = #{patientName},
        `hospital_name` = #{hospitalName},
        `department` = #{department},
        `floor_number` = #{floorNumber},
        `bed_number` = #{bedNumber},
        `hospitalization_number` = #{hospitalizationNumber} ,
        `doctor_name` = #{doctorName},
        `department_tel_number` = #{departmentTelNumber},
        `audit_status` = #{auditStatus},
        `operator_content` = #{operatorContent},
        `easy_to_verify_time` = #{easyToVerifyTime},
        `easy_to_verify_time_status` = #{easyToVerifyTimeStatus} ,
        `submit_time` = #{submitTime},
        `hospital_id` = #{hospitalId},
        `province_id` = #{provinceId},
        `city_id` = #{cityId},
        `province_name` = #{provinceName},
        `city_name` = #{cityName}
        WHERE id = #{id}
    </update>

    <select id="getByInfoUuids" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfHospitalAuditInfo">
        SELECT
        <include refid="selectFields"/>
        FROM (SELECT<include refid="selectFields"/>
        FROM  <include refid="tableName"/>
        WHERE `is_delete` = 0
        ORDER  BY id DESC) ciha
        WHERE ciha.`info_uuid` IN
        <foreach collection="infoUuids" open="(" close=")" separator="," item="infoUuid">
            #{infoUuid}
        </foreach>
        GROUP BY info_uuid
    </select>

</mapper>