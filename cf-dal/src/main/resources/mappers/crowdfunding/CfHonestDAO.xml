<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfHonestDAO">
    <resultMap id="ResultMap" type="com.shuidihuzhu.cf.model.crowdfunding.CfHonestInfo">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="case_id" property="caseId" jdbcType="INTEGER"/>
        <result column="hospital" property="hospital" jdbcType="TINYINT"/>
        <result column="raiser_name" property="raiserName" jdbcType="VARCHAR"/>
        <result column="raiser_identity" property="raiserIdentity" jdbcType="VARCHAR"/>
        <result column="raiser_dishonest" property="raiserDishonest" jdbcType="TINYINT"/>
        <result column="payee_name" property="payeeName" jdbcType="VARCHAR"/>
        <result column="payee_identity" property="payeeIdentity" jdbcType="VARCHAR"/>
        <result column="payee_dishonest" property="payeeDishonest" jdbcType="TINYINT"/>
        <result column="author_name" property="authorName" jdbcType="VARCHAR"/>
        <result column="author_identity" property="authorIdentity" jdbcType="VARCHAR"/>
        <result column="author_dishonest" property="authorDishonest" jdbcType="TINYINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="TABLE_NAME">
        cf_honest_info
    </sql>

    <sql id="SELECT_FEILD">
        id,case_id,hospital,raiser_name,raiser_identity,raiser_dishonest,payee_name,payee_identity,payee_dishonest,author_name,author_identity,author_dishonest,create_time,update_time
    </sql>

    <sql id="INSERT_FEILD">
        case_id,hospital,raiser_name,raiser_identity,raiser_dishonest,payee_name,payee_identity,payee_dishonest,author_name,author_identity,author_dishonest
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfHonestInfo" useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="TABLE_NAME"/>
        (<include refid="INSERT_FEILD"/>)
        values
        (#{caseId},#{hospital},#{raiserName},#{raiserIdentity},#{raiserDishonest},#{payeeName},#{payeeIdentity},#{payeeDishonest},#{authorName},#{authorIdentity},#{authorDishonest})
    </insert>

    <update id="updateById" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfHonestInfo">
        update <include refid="TABLE_NAME"/>
        set raiser_name = #{raiserName},raiser_identity = #{raiserIdentity},raiser_dishonest = #{raiserDishonest},payee_name = #{payeeName},payee_identity = #{payeeIdentity},
            payee_dishonest = #{payeeDishonest},author_name = #{authorName},author_identity = #{authorIdentity},author_dishonest = #{authorDishonest}
        where id = #{id}
    </update>

    <select id="query" parameterType="INTEGER" resultMap="ResultMap">
        select <include refid="SELECT_FEILD"/>
        from <include refid="TABLE_NAME"/>
        where case_id = #{caseId}
        order by id desc
        limit 1
    </select>

    <select id="queryById" parameterType="INTEGER" resultMap="ResultMap">
        select <include refid="SELECT_FEILD"/>
        from <include refid="TABLE_NAME"/>
        where id = #{id}
    </select>

    <select id="queryByCaseId" parameterType="INTEGER" resultMap="ResultMap">
        select <include refid="SELECT_FEILD"/>
        from <include refid="TABLE_NAME"/>
        where case_id = #{caseId}
    </select>
</mapper>