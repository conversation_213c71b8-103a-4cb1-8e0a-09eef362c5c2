<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingCityDao">

    <sql id="TABLE">
        crowdfunding_city
    </sql>

    <sql id="QUERY_FIELDS">
        id,
		code,
		name,
		parent_id as parentId,
		first_letter as firstLetter,
		level,
		valid,
		real_parent_id as realParentId,
        real_level as realLevel,
        new_city_json_info_str
    </sql>

    <select id="getAll" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCity">
        SELECT *
        FROM <include refid="TABLE" />
    </select>

    <select id="getByIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCity">
        SELECT *
        FROM <include refid="TABLE" />
        WHERE id in <foreach collection="ids" item="id" open="(" separator="," close=")">#{id}</foreach>
    </select>

    <select id="getByParentId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCity">
        SELECT *
        FROM <include refid="TABLE" />
        WHERE parent_id = #{parentId}
    </select>

    <select id="getByCode" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCity">
        SELECT *
        FROM <include refid="TABLE" />
        WHERE code = #{code}
    </select>

    <select id="getListByCodes" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCity">
        SELECT *
        FROM <include refid="TABLE" />
        WHERE code in
        <foreach item="code" collection="codes" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>

    <select id="getById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCity">
        SELECT *
        FROM <include refid="TABLE" />
        WHERE id = #{id}
    </select>

    <select id="listByCityName" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCity">
        SELECT *
        FROM <include refid="TABLE" />
        WHERE is_delete = 0 and level = 1
        <if test="cityName != null and cityName != ''">
            and name like concat('%',#{cityName},'%')
        </if>
        order by id
        limit #{limit}
    </select>

    <select id="getAllLevel1City" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCity">
        SELECT *
        FROM <include refid="TABLE" />
        WHERE is_delete = 0 and level = 1
    </select>

    <select id="getByRealParentId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCity">
        SELECT <include refid="QUERY_FIELDS"/>
        FROM
        <include refid="TABLE"/>
        WHERE real_parent_id = #{realParentId}
    </select>

    <select id="findByIdAndDelete" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCity">
        select *
        from
        <include refid="TABLE"/>
        where id = #{id} and is_delete = 0
    </select>

    <select id="listByCityNameAndLevel" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCity">
        select *
        from <include refid="TABLE"/>
        where is_delete = 0
        <if test="levels != null and levels.size() > 0">
            and level in
            <foreach item="level" collection="levels" open="(" separator="," close=")">
                #{level}
            </foreach>
        </if>
        <if test="cityName != null and cityName != ''">
            and name like concat('%',#{cityName},'%')
        </if>
        order by id
        <if test="limit != null and limit > 0">
            limit #{limit}
        </if>
    </select>

    <select id="getByProvinceAndCityNameByLike" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCity">
        select *
        from
        <include refid="TABLE"/>
        <where>
            is_delete=0 and level=1 and valid=1 and parent_id=#{parentId}
            <if test="city != null and city != ''">
                and `name` like concat(#{city},'%')
            </if>
        </where>
    </select>

    <select id="getCityByFetchId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCity">
        select *
        from
        <include refid="TABLE"/>
        where is_delete = 0 and id > #{fetchId}
        order by id
        limit #{limit}
    </select>

</mapper>