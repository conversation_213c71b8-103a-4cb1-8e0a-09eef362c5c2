<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfHospitalAuditMirrorDao">
    <sql id="tableName">
        `cf_info_hospital_audit_mirror`
    </sql>

    <sql id="selectFields">
        `id` ,
        `info_uuid` ,
        `operating_record_id` ,
        `operating_type` ,
        `type` ,
        `audit_status` ,
        `hospital_audit_info`
    </sql>

    <sql id="insertFields">
        ( `info_uuid` ,
        `operating_record_id` ,
        `operating_type` ,
        `type` ,
        `audit_status` ,
        `hospital_audit_info`)
    </sql>


    <select id="getLastOneByType" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfHospitalAuditMirror">
        SELECT
        <include refid="selectFields"/>
        FROM
        <include refid="tableName"/>
        WHERE `operating_record_id` =#{operatingRecordId} AND `type`=#{type}
        ORDER BY id DESC
        LIMIT 1
    </select>


    <insert id="save" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfHospitalAuditMirror">
        INSERT INTO
        <include refid="tableName"/>
        <include refid="insertFields"/>
        VALUES (#{infoUuid},#{operatingRecordId},#{operatingType},
        #{type},#{auditStatus},#{hospitalAuditInfo})
    </insert>
</mapper>
