<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.freeservicecharge.CfFreeServiceChargeDao">

    <sql id="TABLE">
        cf_free_service_charge
    </sql>

    <sql id="selectFields">
        `id`, `case_id`, `info_uuid`, `apply`
    </sql>

    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="TABLE"/>
        (`case_id`, `info_uuid`, `apply`)
        VALUES
        (#{caseId}, #{infoUuid}, #{apply})
    </insert>

    <update id="updateApplyById">
        UPDATE
        <include refid="TABLE"/>
        SET `apply`=#{apply}
        WHERE `id`=#{id}
        AND is_delete=0
    </update>

    <select id="getByCaseId" resultType="com.shuidihuzhu.cf.model.crowdfunding.freeservicecharge.CfFreeServiceChargeDo">
        SELECT
        <include refid="selectFields"/>
        FROM
        <include refid="TABLE"/>
        WHERE case_id = #{caseId}
        and is_delete = 0
    </select>
</mapper>
