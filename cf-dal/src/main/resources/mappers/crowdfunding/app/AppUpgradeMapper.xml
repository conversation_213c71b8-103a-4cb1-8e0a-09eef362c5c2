<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.app.AppUpgradeDao">
    <sql id="table1">
		cf_app_upgrade
	</sql>

    <sql id="table2">
		cf_app_upgrade_log
	</sql>

    <sql id="file1">
        `id`,
        `upload_name` AS `uploadName`,
        `platform`,
        `version`,
        `title`,
        `url`,
        `is_force` AS `force`,
        `update_suggest`
    </sql>
    <sql id="file2">
        `id`,
        `upgradelog_id` AS `upgradelogId`,
        `upload_name`,
        `applog` as `upgradeLog`,
        `is_delete` as `isDelete`
    </sql>


    <select id="queryVersionByPlatform" resultType="com.shuidihuzhu.cf.model.crowdfunding.app.AppUpgradeInfo">
        SELECT
        <include refid="file1"/>
        FROM
        <include refid="table1"/>
        WHERE `platform`=#{appPlatform} AND `is_delete`=0
        ORDER BY `version` DESC
        LIMIT 1
    </select>

    <select id="queryLogByid" resultType="java.lang.String">
        SELECT
        applog
        FROM
        <include refid="table2"/>
        WHERE `upgradelog_id`=#{upgradeId} AND `is_delete`=0
    </select>


</mapper>
