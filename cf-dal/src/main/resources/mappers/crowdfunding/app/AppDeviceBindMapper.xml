<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.app.AppDeviceBindDao">
	<sql id="tableName">
		cf_app_uniq_id_client_id_relation
	</sql>

	<sql id="selectFields">
		`id` ,
  		`app_uniq_id`,
  		`client_id` ,
  		`user_id`,
  		`create_time`,
  		`update_time`,
  		`is_delete`
	</sql>

	<insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.app.vo.AppDeviceBindVo"
			useGeneratedKeys="true" keyProperty="id">
		insert into <include refid="tableName"/>
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="appUniqId!=null">
				app_uniq_id,
			</if>
			<if test="userId!=null">
				user_id,
			</if>
			<if test="clientId!=null and clientId!=''">
				client_id,
			</if>
		</trim>
		values
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="appUniqId!=null">
				#{appUniqId},
			</if>
			<if test="userId!=null">
				#{userId},
			</if>
			<if test="clientId!=null and clientId!=''">
				#{clientId},
			</if>
		</trim>
	</insert>

	<update id="update" parameterType="com.shuidihuzhu.cf.model.crowdfunding.app.vo.AppDeviceBindVo">
		update <include refid="tableName"/>
		<set>
			<if test="userId != null">
				user_id=#{userId},
			</if>
			<if test="clientId!=null and clientId!=''">
				client_id=#{clientId},
			</if>
		</set>
		where
		id=#{id}
	</update>

	<select id="getAppDeviceBindVoByAppUnqiueId" resultType="com.shuidihuzhu.cf.model.crowdfunding.app.vo.AppDeviceBindVo">
		SELECT <include refid="selectFields"/>
		FROM <include refid="tableName"/>
		WHERE `app_uniq_id`=#{appUnqiueId}
		AND `is_delete`=0
		ORDER BY id DESC
		LIMIT 1
	</select>


</mapper>