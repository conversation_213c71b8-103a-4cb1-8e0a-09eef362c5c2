<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.app.AppEventDao">
	<sql id="tableName">
		cf_app_event_record
	</sql>

	<sql id="selectFields">
		`id` ,
  		`app_uniq_id`,
  		`event_id` ,
  		`user_id`,
  		`create_time`,
  		`update_time`,
  		`is_delete`
	</sql>

	<insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.app.vo.AppEventVo"
			useGeneratedKeys="true" keyProperty="id">
		insert into <include refid="tableName"/>
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="appUniqId!=null">
				app_uniq_id,
			</if>
			<if test="userId!=null">
				user_id,
			</if>
			<if test="eventId!=null and eventId!=''">
				event_id,
			</if>
		</trim>
		values
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="appUniqId!=null">
				#{appUniqId},
			</if>
			<if test="userId!=null">
				#{userId},
			</if>
			<if test="eventId!=null and eventId!=''">
				#{eventId},
			</if>
		</trim>
	</insert>

	<select id="getAppEventDo" resultType="com.shuidihuzhu.cf.model.crowdfunding.app.vo.AppEventVo">
		SELECT <include refid="selectFields"/>
		FROM <include refid="tableName"/>
		WHERE `app_uniq_id`=#{appUnqiueId}
		AND event_id=#{eventId}
		ORDER BY id DESC
		LIMIT 1
	</select>


</mapper>