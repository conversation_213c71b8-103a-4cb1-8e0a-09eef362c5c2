<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.app.AppNewsDao">
    <sql id="tableName">
            cf_app_news
    </sql>

    <sql id="selectFields">
            `id`          as id,
            `app_name`    as appName,
            `title`       as title,
            `description` as description,
            `pic_url`     as picUrl,
            `url`         as url,
            `create_time` as createTime,
            `sort`        as sort

    </sql>


    <select id="findByAppName" resultType="com.shuidihuzhu.cf.model.crowdfunding.app.AppNews">
        SELECT
        <include refid="selectFields"/>
        FROM
        <include refid="tableName"/>
        WHERE `app_name`=#{name}
        AND `is_delete`=0
        ORDER BY `createTime` DESC
        LIMIT 30
    </select>

</mapper>