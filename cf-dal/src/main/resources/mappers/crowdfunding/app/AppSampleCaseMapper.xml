<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.app.AppSampleCaseDao">
	<sql id="tableName">
		cf_app_sample_case
	</sql>

	<sql id="selectFields">
		`id` as id,
		`app_name` as appName,
		`info_uuid` as infoUuid,
		`title` as title,
		`description` as description,
		`location` as location,
		`pic_url` as picUrl,
		`disease` as disease,
		`username` as username,
		`cost_time` as costTime,
		`target_amount` as targetAmount,
		`amount` as amount,
		`donation_count` as donationCount,
		`user_head_img` as userHeadImg
	</sql>

	<select id="findByAppName" resultType="com.shuidihuzhu.cf.model.crowdfunding.app.AppSampleCase">
		SELECT <include refid="selectFields"/>
		FROM <include refid="tableName"/>
		WHERE `app_name`=#{name}
		AND `is_delete`=0
		ORDER BY `sort` DESC
		LIMIT 30
	</select>

</mapper>