<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.app.AppSampleRaiserDao">
	<sql id="tableName">
		cf_app_sample_raiser
	</sql>

	<sql id="selectFields">
		`id` as id,
		`app_name` as appName,
		`user_id` as userId,
		`info_uuid` as infoUuid,
		`username` as username,
		`head_img` as headImg,
		`amount` as amount,
        `province` as province
	</sql>



	<select id="findByAppName" resultType="com.shuidihuzhu.cf.model.crowdfunding.app.AppSampleRaiser">
		SELECT <include refid="selectFields"/>
		FROM <include refid="tableName"/>
		WHERE `app_name`=#{name}
		AND `is_delete`=0
		ORDER BY `sort` DESC
		LIMIT 30
	</select>

</mapper>