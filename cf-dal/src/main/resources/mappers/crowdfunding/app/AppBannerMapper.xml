<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.app.AppBannerDao">
	<sql id="tableName">
		cf_app_banner
	</sql>

	<sql id="selectFields">
		`id` as id,
		`description` as description,
		`pic_url` as picUrl,
		`url` as url
	</sql>

	<select id="findByAppName" resultType="com.shuidihuzhu.cf.model.crowdfunding.app.AppBanner">
		SELECT <include refid="selectFields"/>
		FROM <include refid="tableName"/>
		WHERE `name`=#{name}
		AND `is_delete`=0
		ORDER BY `sort` DESC
		LIMIT 30
	</select>

</mapper>