<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
CREATE TABLE `crowdfunding_summary` (
	`id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
	`type` int(11) NOT NULL DEFAULT '0' COMMENT '筹款类型',
	`totalAmount` bigint(20) NOT NULL DEFAULT '0' COMMENT '该类型筹款总金额',
	`totalCount` int(11) NOT NULL DEFAULT 0 COMMENT '该类型筹款的捐款总次数',
	`create_time` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
	`update_time` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
	PRIMARY KEY (`id`),
	UNIQUE KEY (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='筹款汇总数据';
-->

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingSummaryDao">
	<sql id="tableName">
		crowdfunding_summary
	</sql>

	<sql id="selectFields">
		`id` as id,
		`type` as type,
		`total_amount` as totalAmount,
		`total_count` as totalCount,
		`create_time` as createTime,
		`update_time` as updateTime,
		`summary_time` as summaryTime
	</sql>

	<insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingSummary" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO
		<include refid="tableName"/>
			(`type`, `total_amount`, `total_count`, `summary_time`)
		VALUES
			(#{type}, #{totalAmount}, #{totalCount}, #{summaryTime})
	</insert>

	<update id="update">
		UPDATE
			<include refid="tableName"/>
		SET
			`total_amount`=#{totalAmount},
			`total_count`=#{totalCount},
			`summary_time`=#{summaryTime}
		WHERE
			id = #{id}
	</update>

	<select id="getByTypeAndSummaryTime" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingSummary">
		SELECT
			<include refid="selectFields"/>
		FROM
			<include refid="tableName"/>
		WHERE
			`type` = #{type}
			AND
			`summary_time` <![CDATA[ = ]]> #{summaryTime}
		ORDER BY create_time
	  	LIMIT 1
	</select>
</mapper>