<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfInfoShareRecordSourceUserShardingDao">

	<sql id="TABLE">
        cf_info_share_record_source_user_sharding_${sharding}
    </sql>

	<sql id="fields">
		`id`,
		`user_id`,
		`info_id`,
		`user_source_id`,
		`create_time`,
		`update_time`,
		`is_delete`
	</sql>

	<insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareSourceUserRecord">
		INSERT ignore INTO
		<include refid="TABLE"/>
		(`id`,`user_id`,`info_id`,`user_source_id`)
		VALUES
		(#{record.id}, #{record.userId},#{record.infoId},#{record.userSourceId})
	</insert>

</mapper>
