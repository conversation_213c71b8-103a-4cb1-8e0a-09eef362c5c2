<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CaseLabelsManagementDao">
    <sql id="TABLE">
        case_labels_management
    </sql>

    <insert id="add">
        insert into
        <include refid="TABLE"/>
        (info_uuid,labels_management)
        values (#{infoUuid}, #{labelsManagement})
    </insert>


    <select id="get" resultType="java.lang.String">
        SELECT
        labels_management
        FROM
        <include refid="TABLE"/>
        WHERE info_uuid = #{infoUuid}
    </select>

    <update id="update">
        UPDATE
        <include refid="TABLE"/>
        SET
        labels_management = #{labelsManagement}
        WHERE info_uuid = #{infoUuid}
    </update>

</mapper>