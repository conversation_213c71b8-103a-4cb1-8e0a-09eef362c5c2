<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfReportDisposeActionDao">

    <sql id="tableName">
        cf_report_prove_dispose_action
    </sql>


    <select id="getDisposeAction"  resultType="java.lang.String">
        SELECT dispose_action
        from
        <include refid="tableName"/>
        where
        `info_uuid` = #{infoUuid} and `is_delete` = 0
        ORDER BY id desc limit 1
    </select>


</mapper>
