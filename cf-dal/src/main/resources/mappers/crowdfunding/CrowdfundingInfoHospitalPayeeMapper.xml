<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingInfoHospitalPayeeDao">

    <sql id="tableName">
        `cf_info_hospital_payee`
    </sql>

    <sql id="insertFields">
        (`info_uuid`,
        `relation_type`,
        `department`,
        `bed_num`,
        `hospitalization_num`,
        `hospital_account_name`,
        `hospital_bank_card`,
        `hospital_bank_branch_name`,
        `case_id`)
    </sql>

    <sql id="selectFields">
        `info_uuid`,
        `relation_type`,
        `department`,
        `bed_num`,
        `hospitalization_num`,
        `hospital_account_name`,
        `hospital_bank_card`,
        `hospital_bank_branch_name`,
        `case_id`,
        `create_time`,
        `hospital_emergency_name`,
        `hospital_emergency_phone`
    </sql>

    <insert id="save" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoHospitalPayee" keyProperty="id" useGeneratedKeys="true">
        INSERT  INTO <include refid="tableName"/>
        <include refid="insertFields"/>
        VALUES (
        #{infoUuid},
        #{relationType},
        #{department},#{bedNum},#{hospitalizationNum},
        #{hospitalAccountName},#{hospitalBankCard},#{hospitalBankBranchName},#{caseId})
    </insert>

    <update id="update" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoHospitalPayee">
        UPDATE <include refid="tableName"/>
        SET
        `relation_type` = #{relationType},
        `department` = #{department},
        `bed_num` = #{bedNum},
        `hospitalization_num` = #{hospitalizationNum},
        `hospital_account_name` = #{hospitalAccountName},
        `hospital_bank_card` = #{hospitalBankCard},
        `hospital_bank_branch_name` = #{hospitalBankBranchName}
        WHERE `is_delete` = 0
        AND `info_uuid`=#{infoUuid}
    </update>

    <select id="getByInfoUuid" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoHospitalPayee">
        SELECT <include refid="selectFields"/>
        FROM <include refid="tableName"/>
        WHERE `is_delete` = 0
        AND `info_uuid` = #{infoUuid}
    </select>

</mapper>