<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingPromotionInfoDao">

	<resultMap type="com.shuidihuzhu.cf.model.crowdfunding.CustomPromotionInfo" id="resultMap">
		<id property="id" column="id"/>
		<result property="shareQrUrl" column="share_qr_url"/>
		<result property="followQrUrl" column="follow_qr_Url" />
		<result property="partnerName" column="partner_name" />
		<result property="logoUrl" column="logo_url"  />
	</resultMap>

	<sql id="tableName">
		`crowdfunding_promotion_info`
	</sql>

    <sql id="fields">
		`id`,
		`share_qr_url`,
		`follow_qr_Url`,
		`partner_name` ,
		`logo_url`
	</sql>

	<select id="getCustomPromotionInfo" resultMap="resultMap">
		select * from <include refid="tableName"/> where channel=#{channel} and status=1 limit 1
	</select>

</mapper>
