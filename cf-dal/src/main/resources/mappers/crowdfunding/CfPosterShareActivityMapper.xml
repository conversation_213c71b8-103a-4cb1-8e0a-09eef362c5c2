<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfPosterShareActivityDao">
	<sql id="TABLE_NAME">
		cf_poster_share_activity
	</sql>

	<sql id="FIELDS">
		`id` as id,
		`description` as description,
		`third_type` as thirdType
	</sql>

	<select id="findLastByThirdType" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfPosterShareActivity">
		SELECT <include refid="FIELDS" />
		FROM <include refid="TABLE_NAME" />
		WHERE
			`third_type` = #{thirdType}
		AND
			`is_delete`=0
		ORDER BY `id` DESC
		LIMIT 1
	</select>

</mapper>