<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingBaseInfoBackupDao">


    <sql id="tableName">
		crowdfunding_base_info_backup
	</sql>

    <sql id="insertFileds">
      user_id, title, content, target_amount, picture_url, channel
    </sql>

    <sql id="insertFiledsV2">
        user_id, title, content, target_amount, picture_url, channel,
        type,material_bundle_id, page_flag,
        pre_audit_image_url,poverty_image_url,
        cf_version, use_template_raise, template_param_detail
    </sql>

    <sql id="selectFields">
      id, user_id, title, content, target_amount, picture_url, channel, create_time, update_time,
      type,
      material_bundle_id, page_flag,
        pre_audit_image_url,poverty_image_url,
        cf_version, use_template_raise, template_param_detail
	</sql>

    <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingBaseInfoBackup">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="content" property="content" jdbcType="LONGVARCHAR"/>
        <result column="target_amount" property="targetAmount" jdbcType="INTEGER"/>
        <result column="picture_url" property="pictureUrl" jdbcType="VARCHAR"/>
        <result column="channel" property="channel" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="pre_audit_image_url" property="preAuditImageUrl" jdbcType="VARCHAR"/>
        <result column="poverty_image_url" property="povertyImageUrl" jdbcType="VARCHAR"/>
        <result column="material_bundle_id" property="materialBundleId" jdbcType="BIGINT"/>
        <result column="page_flag" property="pageFlag" jdbcType="VARCHAR"/>
        <result column="cf_version" property="cfVersion" jdbcType="INTEGER"/>
        <result column="use_template_raise" property="useTemplateRaise" jdbcType="INTEGER"/>
        <result column="template_param_detail" property="templateParamDetail" jdbcType="VARCHAR"/>
    </resultMap>
    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingBaseInfoBackup" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tableName"/>(<include refid="insertFileds"/>)
        VALUES (#{userId}, #{title}, #{content}, #{targetAmount}, #{pictureUrl}, #{channel})
    </insert>

    <insert id="insertV2" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingBaseInfoBackup" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tableName"/>(<include refid="insertFiledsV2"/>)
        VALUES (#{userId}, #{title}, #{content}, #{targetAmount}, #{pictureUrl}, #{channel},
                #{type, jdbcType=INTEGER},
                #{materialBundleId},
                #{pageFlag},
        #{preAuditImageUrl},
        #{povertyImageUrl},
        #{cfVersion},
        #{useTemplateRaise},
        #{templateParamDetail}
        )
    </insert>

    <select id = "selectRecentlyCfByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingBaseInfoBackup">
        SELECT <include refid="selectFields"/>
        FROM <include refid="tableName"/>
        WHERE user_id = #{userId} and is_delete=0
        <if test="type > 0">
            and type = #{type}
        </if>
        ORDER BY create_time DESC LIMIT 1
    </select>

    <select id = "selectByUserId" resultMap="BaseResultMap">
        SELECT <include refid="selectFields"/>
        FROM <include refid="tableName"/>
        WHERE user_id = #{userId}
    </select>

    <update id="delete" >
        UPDATE <include refid="tableName"/>
        SET
        `is_delete` = 1
        WHERE `is_delete` = 0
        AND `user_id`=#{userId}
    </update>



    <update id="update" >
        UPDATE <include refid="tableName"/>
        <set>
            title= #{title},
            content=#{content},
            target_amount =#{targetAmount},
            picture_url=#{pictureUrl},
            channel= #{channel},
            `page_flag` = #{pageFlag},
            <if test="materialBundleId >0">
                `material_bundle_id` = #{materialBundleId},
            </if>
            `pre_audit_image_url` = #{preAuditImageUrl},
            `poverty_image_url` = #{povertyImageUrl},
            `cf_version` = #{cfVersion},
            `use_template_raise` = #{useTemplateRaise},
            `template_param_detail` = #{templateParamDetail},
        </set>
        WHERE `is_delete` = 0
        AND `user_id`=#{userId}
    </update>

    <update id="updateBaseInfo" >
        UPDATE <include refid="tableName"/>
        <set>
            title= #{title},
            content=#{content},
            target_amount =#{targetAmount},
            picture_url=#{pictureUrl},
            channel= #{channel},
            `page_flag` = #{pageFlag},
            <if test="materialBundleId >0">
                `material_bundle_id` = #{materialBundleId},
            </if>
            `cf_version` = #{cfVersion},
            `use_template_raise` = #{useTemplateRaise},
            `template_param_detail` = #{templateParamDetail},
        </set>
        WHERE `is_delete` = 0
        AND `user_id`=#{userId}
    </update>






    <insert id="saveCfIdCardErrorMsg" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfIdCardErrorMsg" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO idcard_error_msg
        (backup_id,error_code,cf_version)
        VALUES (#{backupId}, #{errorCode}, #{cfVersion})
    </insert>


    <select id = "getListByBackupId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfIdCardErrorMsg">
        SELECT *
        FROM idcard_error_msg
        WHERE backup_id in
        <foreach collection="ids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

</mapper>