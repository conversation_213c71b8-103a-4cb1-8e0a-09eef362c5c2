<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.UserGainGpsInfoRecordDao">


    <sql id="tableName">
        user_gain_gps_info_record
    </sql>

    <insert id="insertData" parameterType="com.shuidihuzhu.cf.model.crowdfunding.UserGainGpsInfoRecord" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="tableName"/>
        (`user_id`,`gain_node`,`gps_longitude`,`gps_latitude`,`gps_time`)
        VALUES
        (#{userId},#{gainNode},#{gpsLongitude},#{gpsLatitude},#{gpsTime})
    </insert>

</mapper>