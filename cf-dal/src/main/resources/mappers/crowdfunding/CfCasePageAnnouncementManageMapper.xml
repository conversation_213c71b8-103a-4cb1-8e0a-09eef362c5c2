<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfCasePageAnnouncementManageDao">

    <sql id="TABLE">
        case_page_announcement_manage
    </sql>

    <sql id="Base_Field">
        `id`,
        `type`,
        `top`,
        `img_url` as imgUrl,
        `pop_img_url` as popImgUrl,
        `status`,
        `title`,
        `shortcut_url` as shortcutUrl,
        `shortcut_url_desc` as shortcutUrlDesc,
        `create_time` as createTime
    </sql>

    <select id="getList" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfCasePageAnnouncementManage">
        SELECT
        <include refid="Base_Field"/>
        FROM
        <include refid="TABLE"/>
        <where>
            is_delete = 0
            AND status= 1
        </where>
    </select>

</mapper>
