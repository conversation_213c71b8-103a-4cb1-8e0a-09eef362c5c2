<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfCaseFromInferenceDao">

	<sql id="TABLE">
       	cf_case_from_inference
    </sql>

	<sql id="FIELDS">
		`id` as id,
		`method` as method,
		`key1` as key1,
		`key2` as key2,
		`from`,
		`detail` as detail
	</sql>

	<select id="getList" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfCaseFromInference">
		SELECT
			<include refid="FIELDS"/>
		FROM
			<include refid="TABLE"/>
	</select>

</mapper>