<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfTouFangSignDao">
	<sql id="TABLE">
        cf_toufang_register_day
    </sql>

	<insert id="insertList" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfTouFangSign">
		insert into <include refid="TABLE"/> (
		`channel`,`source`, `match`, `keyword`, `semwp`, `account`,
		`day_key`,`kwid`, `creative`, `e_adposition`, `crypto_mobile`, `referer`, `is_auto`, `relation`, `help`, `disease`, `client_ip`, `primary_channel`)
		values
		<foreach collection="list" item="element" index="index" separator=",">
			(#{element.channel}, #{element.source}, #{element.match}, #{element.keyword}, #{element.semwp},#{element.account},
			#{element.dayKey}, #{element.kwid}, #{element.creative}, #{element.eAdposition}, #{element.cryptoMobile}, #{element.referer}, #{element.isAuto},
			#{element.relation}, #{element.help}, #{element.disease}, #{element.clientIp}, #{element.primaryChannel})
		</foreach>
	</insert>

	<select id="selectByTime" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfTouFangSign">
		SELECT * FROM <include refid="TABLE"/>
		WHERE `create_time` <![CDATA[ > ]]> #{start}
		AND `id` <![CDATA[ <= ]]> #{anchorId}
		ORDER BY `id` DESC
		LIMIT #{limit}
	</select>

	<select id="selectByMobile" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfTouFangSign">
		SELECT * FROM <include refid="TABLE"/>
		WHERE `crypto_mobile`=#{cryptoMobile}
		ORDER BY `id` DESC
		LIMIT 1
	</select>

</mapper>
