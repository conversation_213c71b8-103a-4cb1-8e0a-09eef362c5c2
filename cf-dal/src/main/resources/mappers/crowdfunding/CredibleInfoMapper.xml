<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CredibleInfoDao">

    <sql id="table_name">
        `cf_report_credible_info`
    </sql>

    <select id="getAuditStatus" resultType="int">
        select audit_status from
        <include refid="table_name"/>
        where sub_id = #{subId}
        and type = #{type}
        and is_delete = 0
    </select>


</mapper>