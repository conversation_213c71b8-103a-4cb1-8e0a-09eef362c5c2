<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.UserSatisfactionDao">
	<sql id="tableName">
		user_satisfaction_result
	</sql>

	<sql id="fields">
        `id`,
        `user_id`,
        `case_id`,
        `test_type`,
        `information_name`,
        `result`,
        `is_delete`,
        `create_time`,
        `update_time`
	</sql>
	<insert id="addUserSatisfaction">
		insert into <include refid="tableName"/>
        (`user_id`,`case_id`,`test_type`,`information_name`,`result`)
         values (#{userSatisfactionDTO.userId},#{userSatisfactionDTO.caseId},#{userSatisfactionDTO.testType},#{userSatisfactionDTO.informationName}
		    ,#{userSatisfactionDTO.result})
	</insert>

	<select id="getByUserIdAndCaseId" resultType="com.shuidihuzhu.cf.model.crowdfunding.UserSatisfactionDO">
		select <include refid="fields"/>
        from <include refid="tableName"/>
        where user_id = #{userId}
        and case_id = #{caseId}
        and information_name in
        <foreach collection="informationNames" item="name" open="(" separator="," close=")">
			#{name}
		</foreach>
        and test_type = #{testType}
        and is_delete = 0;
	</select>


</mapper>