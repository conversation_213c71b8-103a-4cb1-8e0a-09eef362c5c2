<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfCaseContentStyleDao">
    
    <sql id="table_name">
        `case_content_style`
    </sql>

    <sql id="insert_fields">
        `case_id`,
        `keywords`
    </sql>

    <sql id="select_fields">
        `case_id`,
        `keywords`
    </sql>

    <insert id="insertCfContentStyle" parameterType="com.shuidihuzhu.cf.model.CfContentStyle">
        INSERT INTO <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES (#{caseId},#{keywords})
    </insert>

    <update id="updateCfContentStyle" parameterType="com.shuidihuzhu.cf.model.CfContentStyle">
        update <include refid="table_name"/>
        set `keywords` = #{keywords}
        where `case_id` = #{caseId}
    </update>

    <select id="getByCaseId" resultType="com.shuidihuzhu.cf.model.CfContentStyle">
        select <include refid="select_fields"/>
        from <include refid="table_name"/>
        where `case_id` = #{caseId}
        limit 1
    </select>

</mapper>