<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">



<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfPatientCaseInfoDao">

    <sql id="tableName">
        `cf_patient_case_info`
    </sql>

    <select id = "selectByIdCardList" resultType="com.shuidihuzhu.cf.model.crowdfunding.material.CfPatientCaseInfo">
        SELECT *
        FROM <include refid="tableName"/>
        WHERE
        patient_crypto_idcard IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND is_delete = 0
    </select>

    <update id = "addOrUpdate">
        insert into <include refid="tableName"/>
        (`patient_crypto_idcard`, `patient_case_info`, `is_delete`)
        VALUES (#{patientCryptoIdcard}, #{patientCaseInfo}, 0)
        on duplicate key update
        `patient_case_info` = VALUES (patient_case_info),
        `is_delete` = 0
    </update>


    <select id = "selectByIdLimit" resultType="com.shuidihuzhu.cf.model.crowdfunding.material.CfPatientCaseInfo">
        SELECT *
        FROM <include refid="tableName"/>
        WHERE id > #{id}
        order by id
        LIMIT #{limit}
    </select>


</mapper>