<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfCharityPayeeDao">

    <sql id="table_name">
        `cf_charity_payee`
    </sql>

    <sql id="fields">
        `info_uuid` as infoUuid,
        `case_id` as caseId,
        `name` as orgName,
        `mobile` as orgMobile,
        `bank_name` as orgBankName,
        `bank_branch_name` as orgBankBranchName,
        `bank_card` as orgBankCard
    </sql>

    <select id="getByCaseUUid" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfCharityPayee">
        SELECT
        <include refid="fields"/>
        FROM
        <include refid="table_name"/>
        WHERE `info_uuid` = #{uuid}
    </select>

    <select id="getCfCharityPayeeByUUids" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfCharityPayee">
        SELECT
        <include refid="fields"/>
        FROM
        <include refid="table_name"/>
        WHERE `info_uuid` in
        <foreach collection="uuids" item="uuid" open="(" close=")" separator=",">
            #{uuid}
        </foreach>
    </select>


    <select id="getCfCharityPayeeBycaseId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfCharityPayee">
        SELECT
        <include refid="fields"/>
        FROM
        <include refid="table_name"/>
        WHERE `case_id` = #{caseId}
    </select>



    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfCharityPayee" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="table_name"/>
        ( info_uuid ,case_id,name,mobile,bank_name,bank_card,bank_branch_name)
        VALUES
        (#{infoUuid},#{caseId},#{orgName},#{orgMobile},#{orgBankName},#{orgBankCard},#{orgBankBranchName})
    </insert>


    <update id="update" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfCharityPayee">
        UPDATE
        <include refid="table_name"/>
        SET
        `name`=#{orgName},
        `mobile`=#{orgMobile},
        `bank_name`=#{orgBankName},
        `bank_card`=#{orgBankCard},
        bank_branch_name= #{orgBankBranchName}
        WHERE
        `info_uuid`=#{infoUuid}
    </update>


</mapper>