<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingReportDao">
    <sql id="tableName">
		crowdfunding_report
	</sql>

    <sql id="fields">
		`id`, `activity_id`,`user_id`,`image_urls`,`content`,`encrypt_contact`,`create_time`,`is_newreport`,`case_follow_status`,`report_source`
	</sql>

    <sql id="insertFields">
		`activity_id`,`user_id`,`image_urls`,`content`,`encrypt_contact`,`is_newreport`,`deal_status`,`case_follow_status`,`report_source`,handle_status,connect_status,report_channel
	</sql>

    <sql id="BaseInsertFields">
		`activity_id`,`user_id`,`image_urls`,`content`,`encrypt_contact`,`is_newreport`,`deal_status`,`case_follow_status`,
		`report_source`,`real_name_report`,`name`,`identity`,`handle_status`,`connect_status`,`hit_black_list`,`report_channel`
	</sql>

    <insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport"
            useGeneratedKeys="true"
            keyProperty="id"
    >
        INSERT INTO
        <include refid="tableName"/>
        (<include refid="insertFields"/>)
        VALUES
        (#{activityId},#{userId},#{imageUrls},#{content},#{encryptContact},#{isNewreport},#{dealStatus},#{caseFollowStatus},#{reportSource},#{handleStatus},#{connectStatus},#{reportChannel});
    </insert>

    <insert id="addRealNameReport" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tableName"/>
        (<include refid="BaseInsertFields"/>)
        VALUES
        (#{activityId},#{userId},#{imageUrls},#{content},#{encryptContact},#{isNewreport},#{dealStatus},#{caseFollowStatus},
        #{reportSource},#{realNameReport},#{name},#{identity},#{handleStatus},#{connectStatus},#{hitBlackList}, #{reportChannel});
    </insert>

    <select id="getListByInfoId" parameterType="int" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport">
        SELECT *
        FROM <include refid="tableName"/>
        WHERE activity_id = #{activityId}
    </select>

    <update id="updateReportStatusList">
        UPDATE  <include refid="tableName"/>
        SET  `case_follow_status` = 1
        where id IN
        <foreach collection="reportIds" item="reportId" separator="," open="(" close=")">
            #{reportId}
        </foreach>
    </update>

    <insert id="addLabel" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReportLabel"
            useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO
        crowdfunding_report_label
        (report_id,report_label)
        VALUES
        <foreach collection="list" item="item" separator="," >
            (#{item.reportId},#{item.reportLabel})
        </foreach>

    </insert>

    <select id="getListByInfoIds" parameterType="int" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport">
        SELECT <include refid="fields"/>
        FROM <include refid="tableName"/>
        WHERE activity_id in <foreach collection="activityIds" open="(" separator="," close=")" item="activityId">#{activityId}</foreach>
        GROUP BY activity_id
    </select>

    <select id="getCfReportCountsMapByInfoIds" resultType="java.util.Map">
        SELECT `activity_id` AS caseId, count(*) AS counts
        FROM
        <include refid="tableName"/>
        WHERE `activity_id` IN
        <foreach collection="activityIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY `activity_id`
    </select>

    <select id="queryReportByUser"  resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport">
        SELECT <include refid="fields"/>
        FROM <include refid="tableName"/>
        WHERE user_id = #{userId}
    </select>

    <select id="queryReportLabelByReportId" parameterType="int" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReportLabel">
        SELECT *
        FROM crowdfunding_report_label
        WHERE report_id = #{reportId}
    </select>
    <select id="getReportByConditions" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport">
        SELECT * FROM <include refid="tableName"/>
        WHERE user_id = #{userId}
        and activity_id = #{activityId}
        order by id desc
    </select>
    <select id="queryReportLabelByReportIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReportLabel">
        SELECT *
        FROM crowdfunding_report_label
        WHERE report_id in <foreach collection="reportIds" open="(" separator="," close=")" item="reportId">#{reportId}</foreach>
        GROUP BY `report_label`
    </select>
    <select id="getById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport">
        SELECT * FROM <include refid="tableName"/>
        WHERE id = #{id}
    </select>
    <select id="getListByIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport">
        SELECT *
        FROM <include refid="tableName"/>
        WHERE id in <foreach collection="reportIds" open="(" separator="," close=")" item="reportId">#{reportId}</foreach>
        and user_id = #{userId}
        order by id desc
    </select>
    <select id="queryLastReportByUserWithReal"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport">
        SELECT *
        FROM <include refid="tableName"/>
        WHERE user_id = #{userId} and name != '' and identity != ''
        order by id desc
        limit 1
    </select>

</mapper>
