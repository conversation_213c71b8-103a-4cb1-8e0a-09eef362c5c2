<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.tidb.TiDBCrowdfundingInfoDao">
	<sql id="tableName">
		crowdfunding_info
	</sql>

    <sql id="fields">
		`id`, `info_id`, `user_id`,
		`relation`, `applicant_name`, `applicant_qq`, `applicant_mail`, `relation_type`, `channel_type`,`channel`,
		`payee_name`, `payee_id_card`, `payee_mobile`, `payee_bank_name`, `payee_bank_branch_name`, `payee_bank_card`,
		`bank_card_verify_status`,
		`bank_card_verify_message`, `bank_card_verify_message2`, `title`, `title_img`, `content`, `encrypt_content`,
		`target_amount`, `amount`, `donation_count`, `status`, `create_time`, `begin_time`, `end_time`, `from`, `use`,`data_status`,
		`type`, `content_type`, `material_plan_id`, `content_image`, `content_image_status`
	</sql>

	<select id="countByPayeeIdCard" resultType="java.lang.Integer">
		SELECT COUNT(*)
		FROM
		<include refid="tableName"/>
		WHERE `payee_id_card`=#{payeeIdCard}
	</select>
</mapper>
