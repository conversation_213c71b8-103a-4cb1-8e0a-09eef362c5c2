<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingOrderShardingCrowdfundingIdNewDao">
	<sql id="TABLE">
        crowdfunding_order_crowdfundingid_sharding
    </sql>
	<sql id="FIELDS">
        `id`,`code`,`user_id`,`crowdfunding_id`,`amount`,`comment`,`pay_status`,
        `ctime`,`pay_time`,`valid`,`ip`,`os_type`,`channel`,`from`,`self_tag`,`user_third_id`,`user_third_type`,`anonymous`
    </sql>
	<sql id="INSERT_FIELDS">
        `id`,`code`,`user_id`,`crowdfunding_id`,`amount`,`comment`,`pay_status`,`ctime`,
        `valid`,`ip`,`os_type`,`channel`,`from`,`self_tag`,`user_third_id`,`user_third_type`,`anonymous`,`activity_id`
    </sql>

	<insert id="addOrder" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		INSERT INTO
		<include refid="TABLE"/>
		(<include refid="INSERT_FIELDS"/>)
		VALUES(#{id},#{code},#{userId},#{crowdfundingId},#{amount},#{comment},
		#{payStatus},#{ctime},#{valid},#{ip},#{osType},#{channel},#{from},#{selfTag},
		#{userThirdId},#{userThirdType},#{anonymous},#{activityId});
	</insert>

	<update id="editByIdAndComment">
		UPDATE <include refid="TABLE"/>
		SET `comment`=#{comment}
		WHERE crowdfunding_id = #{crowdfundingId} and `id`=#{id}
	</update>

	<update id="updatePayStatus">
		UPDATE
		<include refid="TABLE"/>
		SET `pay_status`=#{payStatus},`pay_time`=#{payTime}
		WHERE crowdfunding_id = #{crowdfundingId} and `id`=#{id};
	</update>


	<select id="getUserOrderByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE crowdfunding_id = #{crowdfundingId}
		and user_id = #{userId}
		and pay_status = 1
		and valid = 1
		order by id desc
		limit 1
	</select>

	<select id="getDonateByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE crowdfunding_id = #{crowdfundingId}
		and user_id = #{userId}
		and pay_status = 1
		order by id desc
		limit 1
	</select>

	<update id="updateValid">
		UPDATE <include refid="TABLE"/>
		SET `valid`=#{valid}
		WHERE crowdfunding_id = #{crowdfundingId} and `id`=#{id}
	</update>

	<update id="updateUserId" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		UPDATE
		<include refid="TABLE"/>
		SET `user_id`= #{order.userId}
		WHERE
		crowdfunding_id = #{order.crowdfundingId} and `id` = #{order.id}
	</update>
	<update id="updateAnonymousValid">
		UPDATE
		<include refid="TABLE"/>
		SET anonymous = #{anonymous}
		where crowdfunding_id = #{caseId}
		and code = #{code}
	</update>

	<update id="updateSingleRefund">
		update
		<include refid="TABLE"/>
		set single_refund_flag = 1
		WHERE crowdfunding_id = #{crowdfundingId} and `id`=#{id}
	</update>

	<update id="updateSingleRefundBatch">
		update
		<include refid="TABLE"/>
		set single_refund_flag = 1
		WHERE crowdfunding_id = #{crowdfundingId}
		    and `id` in
		<foreach collection="ids" item="id" open="(" close=")" separator=",">
			#{id}
		</foreach>
	</update>

	<select id="getListByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE crowdfunding_id = #{crowdfundingId}
		and user_id = #{userId}
		and pay_status = 1
		and valid = 1
		order by id desc
		limit #{limit}
	</select>

	<select id="getSuccessCount" resultType="java.lang.Long">
		SELECT
			count(*)
		FROM
		<include refid="TABLE"/>
		WHERE crowdfunding_id = #{crowdfundingId} and pay_status = 1 and valid = 1
		<if test="id != null">
			and id = #{id}
		</if>
		<if test="userId != null">
			and user_id = #{userId}
		</if>
		<if test="amount != null">
			and amount=#{amount}
		</if>
		<if test="ctimeStart != null and ctimeEnd != null">
			and ctime between #{ctimeStart} and #{ctimeEnd}
		</if>
		<if test="comment != null and comment != ''">
			and comment like CONCAT('%',#{comment},'%')
		</if>
		<if test="amountStart >= 0 and amountEnd >= 0">
			and amount between #{amountStart} and #{amountEnd}
		</if>
	</select>

	<select id="getByIdFromMaster" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE `crowdfunding_id`=#{crowdfundingId} and `id`=#{id};
	</select>
</mapper>
