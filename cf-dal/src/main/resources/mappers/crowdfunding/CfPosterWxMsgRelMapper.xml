<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfPosterWxMsgRelDao">
	<sql id="TABLE_NAME">
		cf_poster_wx_msg_rel
	</sql>

	<sql id="FIELDS">
		`id` as id,
		`activity_id` as activityId,
		`third_type` as thirdType,
		`content` as content,
		`media_id` as mediaId,
		`msg_type` as msgType
	</sql>

	<select id="findByActivityIdAndThirdType" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfPosterWxMsgRel">
		SELECT <include refid="FIELDS" />
		FROM <include refid="TABLE_NAME" />
		WHERE
			`activity_id` = #{activityId}
		AND
			`third_type` = #{thirdType}
		AND
			`msg_type` = #{msgType}
		AND
			`is_delete`=0
		LIMIT 1
	</select>

</mapper>