<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfConfirmBlacklistDao">

    <sql id="tableName">
        cf_confirm_blacklist
    </sql>

    <insert id="saveCfConfirmBlacklist">
        INSERT INTO
        <include refid="tableName"/>
        (user_id,valid,blacklist_time)
        VALUES
        (#{userId}, #{valid}, #{blacklistTime})
    </insert>

    <update id="updateValid">
        UPDATE
        <include refid="tableName"/>
        SET
        valid = 0
        WHERE
        user_id = #{userId}
    </update>

    <update id="updateBlacklistTime">
        UPDATE
        <include refid="tableName"/>
        SET
        blacklist_time = #{blacklistTime},
        valid = 1
        WHERE
        user_id = #{userId}
    </update>

    <select id="getMsgByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfConfirmBlacklist">
        SELECT
        *
        FROM
        <include refid="tableName"/>
        WHERE
        user_id = #{userId} and is_delete=0
    </select>

    <select id="getConfirmBlacklist" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfConfirmBlacklist">
        SELECT
        *
        FROM
        <include refid="tableName"/>
        WHERE
        valid = 1 and is_delete=0
    </select>

    <select id="getConfirmBlacklistByUserIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfConfirmBlacklist">
        SELECT
        *
        FROM
        <include refid="tableName"/>
        WHERE
        user_id IN
        <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
            #{userId}
        </foreach>
        and valid = 1 and is_delete=0
    </select>

</mapper>

