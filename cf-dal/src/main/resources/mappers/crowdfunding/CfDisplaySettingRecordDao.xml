<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfDisplaySettingRecordDao">
    <sql id="table">
        case_display_record
    </sql>
    <insert id="insert" parameterType="com.shuidihuzhu.cf.dto.CaseDisplayRecordDto">
        insert into
        <include refid="table"/>
        (`case_id`,`before_setting`,`after_setting`,`unique_code`,`operator_id`, `update_channel`, `reason`)
        values(#{record.caseId}, #{record.before}, #{record.after}, #{record.uniqueCode}, #{record.operatorId}, #{record.updateChannel}, #{record.reason})
    </insert>
</mapper>