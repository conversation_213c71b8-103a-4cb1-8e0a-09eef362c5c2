<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfSuspectedCaseReporterDao">
	<sql id="tableName">
		cf_suspected_case_reporter
	</sql>

	<insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfSuspectedCaseReporter">
		INSERT INTO <include refid="tableName"/>
		(`report_id`, `type`, `name`, `crypto_idcard`, `organization_name`)
		VALUES
		(#{reportId}, #{type}, #{name}, #{cryptoIdcard}, #{organizationName})
	</insert>

</mapper>