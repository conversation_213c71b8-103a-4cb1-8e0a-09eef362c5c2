<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfContributeDao">
	<sql id="tableContribute">crowdfunding_contribute</sql>

	<insert id="addContributeList" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfContribute"
	        useGeneratedKeys="true" keyProperty="id">
		INSERT INTO <include refid="tableContribute"/>
		(`info_id`,`user_id`,`nickname`,`head_img_url`,`donation`,
		`trantsmit_account`,`sum_account`,`counts`,`interact`,`donation_date`)
		VALUES
		<foreach collection="list" separator="," item="item">
			(#{item.infoId},#{item.userId},#{item.nickname},#{item.headImgUrl},#{item.donation},
			#{item.trantsmitAccount},#{item.sumAccount},#{item.counts},#{item.interact},#{item.donationDate})
		</foreach>
	</insert>

	<select id="getTopList" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfContribute">
		SELECT
		`id`,`info_id`,`user_id`,`nickname`,`head_img_url`,`donation`,
		`trantsmit_account`,`sum_account`,`counts`,`interact`,`donation_date`
		FROM <include refid="tableContribute"></include>
		WHERE `info_id` = #{infoId} AND `is_delete` = 0
		ORDER BY `sum_account` DESC , `donation_date` DESC
		LIMIT #{limit}
	</select>

	<update id="deleteByIds" >
	UPDATE <include refid="tableContribute"></include>
	SET `is_delete`=1
	WHERE `id` IN
	<foreach collection="list" item="item" open="(" close=")" separator=",">
		#{item}
	</foreach>
	</update>

	<select id="getBeachByInfoIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfContribute">
		SELECT
		`id`,`info_id`,`user_id`,`nickname`,`head_img_url`,`donation`,
		`trantsmit_account`,`sum_account`,`counts`,`interact`,`donation_date`,`create_time`
		FROM <include refid="tableContribute"></include>
		WHERE `info_id` in <foreach collection="list" open="(" close=")" separator="," item="item">#{item}</foreach>
		AND `is_delete` = 0
		LIMIT #{offset},#{limit}
	</select>

	<update id="updateByIds" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfContribute">
	UPDATE
	<include refid="tableContribute"></include>
	SET `nickname`= CASE `id`
	<foreach collection="list" item="item">
		WHEN #{item.id} THEN #{item.nickname}
	</foreach>
	END,

	`head_img_url`= CASE `id`
	<foreach collection="list" item="item">
		WHEN #{item.id} THEN #{item.headImgUrl}
	</foreach> END,

	`donation`= CASE `id`
	<foreach collection="list" item="item">
		WHEN #{item.id} THEN #{item.donation}
	</foreach> END,

	`trantsmit_account`= CASE `id`
	<foreach collection="list" item="item">
		WHEN #{item.id} THEN #{item.trantsmitAccount}
	</foreach> END,

	`sum_account`= CASE `id`
	<foreach collection="list" item="item">
		WHEN #{item.id} THEN #{item.sumAccount}
	</foreach> END,

	`counts`= CASE `id`
	<foreach collection="list" item="item">
		WHEN #{item.id} THEN #{item.counts}
	</foreach> END,

	`interact`= CASE `id`
	<foreach collection="list" item="item">
		WHEN #{item.id} THEN #{item.interact}
	</foreach> END,

	`donation_date`= CASE `id`
	<foreach collection="list" item="item">
		WHEN #{item.id} THEN #{item.donationDate}
	</foreach> END
	WHERE `id` IN
	<foreach collection="list" item="item" open="(" close=")" separator=",">
		#{item.id}
	</foreach>
	AND `is_delete`=0
	</update>

	<update id="update" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfContribute" >
		UPDATE <include refid="tableContribute"></include>
		SET `nickname` = #{cfContribute.nickname},
		`head_img_url` = #{cfContribute.headImgUrl},
		`donation` = #{cfContribute.donation},
		`trantsmit_account` = #{cfContribute.trantsmitAccount},
		`sum_account` = #{cfContribute.sumAccount},
		`counts` = #{cfContribute.counts},
		`interact` = #{cfContribute.interact},
		`donation_date` = #{cfContribute.donationDate}
		WHERE `id` = #{cfContribute.id}
		AND `is_delete`=0
	</update>

	<select id="getMaxCreatTime" resultType="java.util.Date">
		SELECT
		max(`create_time`)
		FROM  <include refid="tableContribute"/>
	</select>

	<select id="getCountInsert" resultType="java.lang.Integer">
		SELECT
		COUNT(*)
		FROM <include refid="tableContribute" />
		WHERE `is_delete` = 0 AND `create_time` BETWEEN #{startTime} AND #{endTime}
	</select>

	<select id="getCountUpdate" resultType="java.lang.Integer">
		SELECT
		COUNT(*)
		FROM <include refid="tableContribute"/>
		WHERE `is_delete` = 0 AND `update_time` BETWEEN #{startTime} AND #{endTime}
	</select>

	<select id="getCountDelete" resultType="java.lang.Integer">
		SELECT
		COUNT(*)
		FROM <include refid="tableContribute"/>
		WHERE `is_delete` = 1 AND `update_time` BETWEEN #{startTime} AND #{endTime}
	</select>
</mapper>