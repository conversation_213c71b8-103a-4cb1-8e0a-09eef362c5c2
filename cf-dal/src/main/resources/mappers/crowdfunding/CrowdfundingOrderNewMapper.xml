<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingOrderNewDao">


	<sql id="TABLE_NEW">
		crowdfunding_order_id_generate
	</sql>

	<insert id="addOrderId" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder$CrowdfundingOrderIdHolder" keyProperty="id" useGeneratedKeys="true">
		INSERT INTO
		<include refid="TABLE_NEW"/>
		(is_delete)
		VALUES(0);
	</insert>


</mapper>
