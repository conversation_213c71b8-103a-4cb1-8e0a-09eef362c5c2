<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfFirstApproveMaterialDao">
	<sql id="TABLE">
		cf_first_approve_material
	</sql>

	<sql id="QUERY_FIELDS">
		`id`, `user_id`, `date`, `ip`, `info_id`, `info_uuid`, `self_real_name`,
		`self_crypto_idcard`, `patient_real_name`, `patient_crypto_idcard`, `user_relation_type`,
		`image_url`, `status`, `create_time`, `update_time`, `patient_has_idcard`, `target_amount_desc`, `reject_reason_type`,
		`reject_message`,`poverty`,
		`poverty_image_url`,
		`patient_born_card`,`patient_id_type`
	</sql>

	<sql id="INSERT_FIELDS">
		`user_id`, `date`, `ip`, `info_id`, `info_uuid`, `self_real_name`,
		`self_crypto_idcard`, `patient_real_name`, `patient_crypto_idcard`, `user_relation_type`,
		`image_url`, `patient_has_idcard`, `status`,  `target_amount_desc`,`poverty`,
		`poverty_image_url`,
		`patient_born_card`,`patient_id_type`
	</sql>

	<insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial">
		insert into
		<include refid="TABLE"/>
		(<include refid="INSERT_FIELDS"/>)
		values (#{userId}, #{date}, #{ip}, #{infoId}, #{infoUuid}, #{selfRealName},
		#{selfCryptoIdcard}, #{patientRealName}, #{patientCryptoIdcard}, #{userRelationType},
		#{imageUrl}, #{patientHasIdCard}, #{status},
		#{targetAmountDesc},#{poverty},#{povertyImageUrl},#{patientBornCard},#{patientIdType})
	</insert>

	<select id="getByInfoId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial">
		select
		<include refid="QUERY_FIELDS"/>
		from
		<include refid="TABLE"/>
		where `info_id`=#{infoId} and `is_delete`=0
	</select>

	<select id="getByInfoIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial">
		select
		<include refid="QUERY_FIELDS"/>
		from
		<include refid="TABLE"/>
		where `info_id` in
		<foreach collection="infoIds" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
		 and `is_delete`=0
	</select>

	<select id="getByInfoUuid" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial">
		select
		<include refid="QUERY_FIELDS"/>
		from
		<include refid="TABLE"/>
		where `info_uuid`=#{infoUuid} and `is_delete`=0
	</select>

	<update id="updateStatusByCaseId">
		update
		<include refid="TABLE"/>
		set `status`=#{status}
		where `info_id`=#{caseId}
	</update>


	<select id="getByParam" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial">
		select
		<include refid="QUERY_FIELDS"/>
		from
		<include refid="TABLE"/>
		<where>
			`is_delete`=0
			<if test="infoId != 0">
				AND `info_id`=#{infoId}
			</if>
			<if test="selfRealName != null and selfRealName != ''">
				AND self_real_name = #{selfRealName}
			</if>
			<if test="selfCryptoIdcard != null and selfCryptoIdcard != ''">
				AND self_crypto_idcard = #{selfCryptoIdcard}
			</if>
			<if test="patientRealName != null and patientRealName != ''">
				AND patient_real_name = #{patientRealName}
			</if>
			<if test="patientCryptoIdcard != null and patientCryptoIdcard != ''">
				AND patient_crypto_idcard = #{patientCryptoIdcard}
			</if>
			<if test="patientBornCard != null and patientBornCard != ''">
				AND `patient_born_card` = #{patientBornCard}
			</if>
			<if test="patientIdType != 0">
				AND `patient_id_type` = #{patientIdType}
			</if>
		</where>
	</select>


	<update id="updateAfterRejectForSubmit">
		update
		<include refid="TABLE"/>
		<trim prefix="set" suffixOverrides=",">
			<if test="selfRealName != null">
				`self_real_name` = #{selfRealName},
			</if>
			<if test="selfIdCard != null">
				`self_crypto_idcard` = #{selfIdCard},
			</if>

			<if test="relType != null">
				`user_relation_type` = #{userRelationType},
			</if>
			<if test="patientHasIdCard != null">
				`patient_has_idcard` = #{patientHasIdCard},
			</if>


			<if test="patientRealName != null and patientRealName != ''">
				`patient_real_name` = #{patientRealName},
			</if>
			<if test="patientIdCard != null">
				 `patient_crypto_idcard` = #{patientIdCard},
			</if>
			<if test="preAuditImageUrl != null and preAuditImageUrl != ''">
				`image_url`=#{preAuditImageUrl},
			</if>
			<if test="targetAmountDesc != null and targetAmountDesc != ''">
				`target_amount_desc` = #{targetAmountDesc},
			</if>
			<if test="patientBornCard != null">
				`patient_born_card` = #{patientBornCard},
			</if>
			<if test="patientIdType != 0">
				`patient_id_type` = #{patientIdType},
			</if>
			<if test="poverty != null and poverty != 0">
				`poverty` = #{poverty},
			</if>
			<if test="povertyImageUrl != null ">
				`poverty_image_url` = #{povertyImageUrl},
			</if>
			<if test="status != 0">
				`status`= #{status}
			</if>
		</trim>
		where `info_uuid`=#{infoUuid}
	</update>

    <update id="updateRejectTypeByInfoId">
        update
        <include refid="TABLE"/>
        set `reject_reason_type`=#{rejectType},
		`reject_message` = #{rejectMessage}
        where `info_id`=#{infoId}
    </update>

	<select id="getById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial">
		select
		<include refid="QUERY_FIELDS"/>
		from
		<include refid="TABLE"/>
		where `id`=#{id} and `is_delete`=0
	</select>

	<select id="getLastByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial">
		select
		<include refid="QUERY_FIELDS"/>
		from
		<include refid="TABLE"/>
		where `user_id`=#{userId} and `is_delete`=0 order by id desc limit 1
	</select>


	<select id = "countWorkOrderByCaseIdAndApproveStatus" resultType="java.lang.Integer">
		select count(*)
		from admin_task_ugc
		where case_id = #{caseId}
		and content_type = #{contentType}
		and result = #{approveStatus}
	</select>


	<select id="getByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial">
		select
		<include refid="QUERY_FIELDS"/>
		from
		<include refid="TABLE"/>
		where `user_id`=#{userId} and info_id != 0 and `is_delete`=0 order by create_time DESC
	</select>

	<select id="getByPatientCryptoIdCard" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial">
		select
		<include refid="QUERY_FIELDS"/>
		from
		<include refid="TABLE"/>
		where
		patient_crypto_idcard in
		<foreach collection="list" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
		AND patient_id_type != 2
		AND info_id != 0
	</select>

	<select id="getListByPatientBornCard" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial">
		select
		<include refid="QUERY_FIELDS"/>
		from
		<include refid="TABLE"/>
		where
		patient_born_card in
		<foreach collection="list" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

	<select id="findByInfoUuids" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial">
		select
		<include refid="QUERY_FIELDS"/>
		from
		<include refid="TABLE"/>
		where `info_uuid` in
		<foreach collection="infoUuids" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
		and `is_delete`=0
	</select>


	<update id="updateUserIdByIds">
		update
		<include refid="TABLE"/>
		set user_id = #{userId}
		where id in
		<foreach collection="ids" item="id" open="(" separator="," close=")">
			#{id}
		</foreach>
	</update>


	<select id="getSingleByPatientCryptoIdCard" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial">
		select
		<include refid="QUERY_FIELDS"/>
		from
		<include refid="TABLE"/>
		where
		patient_crypto_idcard = #{patientCryptoIdCard}
		AND is_delete = 0
	</select>


	<select id="getSingleByPatientBornCard" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial">
		select
		<include refid="QUERY_FIELDS"/>
		from
		<include refid="TABLE"/>
		where
		patient_born_card = #{patientBornCard}
		AND is_delete = 0
	</select>

	<select id="getListByCaseIdList" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial">
		select *
		from
		<include refid="TABLE"/>
        <where>
		case_id in <foreach collection="caseIdList" item="caseId" separator="," open="(" close=")">
				#{caseId}
			</foreach>
			AND is_delete = 0
		</where>
    </select>
	<select id="getBySelfRealNameAndCreateTime"
			resultType="com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial">
		(select * from <include refid="TABLE"/> where self_real_name = #{selfRealName}  and create_time > #{createTime} limit 50)
		union all
		(select * from <include refid="TABLE"/> where  patient_real_name = #{selfRealName} and create_time > #{createTime} limit 50)
	</select>
</mapper>