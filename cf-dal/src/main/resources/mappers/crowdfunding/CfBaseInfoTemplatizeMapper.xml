<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfBaseInfoTemplatizeDao">
    
    <sql id="table_name">
        `cf_base_info_templatize`
    </sql>

    <sql id="insert_fields">
        `relation_type`,
        `content_type`,
        `content`,
        `min_age`,
        `max_age`
    </sql>

    <sql id="select_fields">
        `id`,
        `relation_type`,
        `channel_type`,
        `content_type`,
        `content`,
        `fp`,
        `min_age`,
        `max_age`
    </sql>

    <insert id="insertOne" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplatize">
        INSERT INTO <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES (#{relationType},#{contentType},#{content})
    </insert>

    <insert id="insertList" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplatize">
        INSERT INTO <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES
        <foreach collection="list" item="item" separator=",">
             (#{relationType},#{contentType},#{content})
        </foreach>
    </insert>

    <select id="selectAllBaseInfoLimit" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplatize">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `is_delete`=0
        AND `channel_type`=1
        LIMIT #{start},#{size}
    </select>

    <select id="selectAllShareLimit" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplatize">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `is_delete`=0
        AND `channel_type`=2
        LIMIT #{start},#{size}
    </select>

    <select id="selectAllShareTemplate" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplatize">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `is_delete`=0
        AND `channel_type`= #{channelType}
        limit #{offset} , #{size}
    </select>

    <select id="selectByRelationAndContentType" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplatize">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        <where>
            <if test="relationType != null">
                AND `relation_type`=#{relationType}
            </if>
            <if test="contentType != null">
                AND `content_type`=#{contentType}
            </if>
            AND `is_delete`=0
        </where>
        LIMIT #{start},#{size}
    </select>


    <select id="selectAllBaseInfoByLimit" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplatize">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE
        id > #{id}
        AND `channel_type`=1
        AND `is_delete`=0
        LIMIT #{limit}
    </select>

</mapper>