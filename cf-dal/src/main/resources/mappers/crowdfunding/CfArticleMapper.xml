<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfArticleDao">
	<sql id="tableName">
		crowdfunding_articles
	</sql>

	<sql id="selectFields">
		`id` as id,
		`title` as title,
		`description` as description,
		`title_img` as titleImg,
		`url` as url,
		`tag` as tag,
		`create_time` as createTime
	</sql>


	<select id="getListByAnchorId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfArticle">
		SELECT
			<include refid="selectFields"/>
		FROM
			<include refid="tableName"/>
		WHERE
			`valid` = 1
		ORDER BY sort
	  	LIMIT #{start}, #{limit}
	</select>
</mapper>