<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfHuKouOptionDao">

    <sql id="TABLE">
        cf_hu_kou_option
    </sql>

    <sql id="Base_Field">
        `case_id`,
        `hu_hao_type`,
        `hu_kou_type`,
        `material_type`
    </sql>

    <insert id="saveHuKouOption" parameterType="com.shuidihuzhu.cf.model.crowdfunding.HuKouOptionDO">
        insert into <include refid="TABLE"/>
        (<include refid="Base_Field"/>)
        values (
        #{caseId} ,
        #{huHaoType} ,
        #{huKouType},
        #{materialType}
        )
    </insert>

    <update id="updateHuKouOption" parameterType="com.shuidihuzhu.cf.model.crowdfunding.HuKouOptionDO">
        update <include refid="TABLE"/>
        set `hu_hao_type` = #{huHaoType}, `hu_kou_type` = #{huKouType}, `material_type` = #{materialType}
        where `case_id` = #{caseId}
    </update>

    <select id="selectByCaseId" resultType="com.shuidihuzhu.cf.model.crowdfunding.HuKouOptionDO">
        select * from <include refid="TABLE"/>
        where `case_id` = #{caseId} limit 1
    </select>

</mapper>
