<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.msg.DailyReportSubscribeDao">
	<sql id="TABLE">
		cf_daily_report_subscribe
	</sql>

	<sql id="QUERY_FIELDS">
		`id`, `user_id`, `info_id`, `subscribe`
	</sql>

	<select id="isSubscribe" resultType="java.lang.Boolean">
		SELECT `subscribe` FROM
		<include refid="TABLE"/>
		WHERE `info_id`=#{infoUuid}
	</select>

	<update id="updateSubscribe">
		UPDATE
		<include refid="TABLE"/>
		SET `subscribe`=#{value}
		WHERE
		`info_uuid`=#{infoUuid}
	</update>

</mapper>