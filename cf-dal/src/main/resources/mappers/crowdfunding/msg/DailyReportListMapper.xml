<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.msg.DailyReportListDao">
	<sql id="TABLE">
		cf_daily_report_list
	</sql>

	<sql id="QUERY_FIELDS">
		`id`, `user_id`, `info_id`,`info_uuid`, `date_str`, `share_count`, `self_money_amount`,
		`share_donate_count`, `share_donate_amount`, `total_donate_amount`
	</sql>

	<sql id="INSERT_FIELDS">
		`user_id`, `info_id`, `info_uuid`, `date_str`, `share_count`, `self_money_amount`,
		`share_donate_count`, `share_donate_amount`, `total_donate_amount`
	</sql>

	<insert id="addBatch" parameterType="com.shuidihuzhu.cf.model.dailyreport.CfDailyReportList">
		INSERT IGNORE  INTO
		<include refid="TABLE"/>
		(<include refid="INSERT_FIELDS"/>)
		VALUES
		<foreach collection="list" item="item" separator=",">
			(#{item.userId}, #{item.infoId}, #{item.infoUuid},
			#{item.dateStr}, #{item.shareCount}, #{item.selfMoneyAmount},
			#{item.shareDonateCount}, #{item.shareDonateAmount}, #{item.totalDonateAmount})
		</foreach>
	</insert>

	<select id="getTop" resultType="com.shuidihuzhu.cf.model.dailyreport.CfDailyReportList">
		SELECT
		<include refid="QUERY_FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE
		`date_str`=#{dateStr} AND
		info_uuid=#{infoUuid} AND
		`is_delete`=0
		ORDER BY `total_donate_amount` DESC
		limit #{limit}
	</select>
</mapper>