<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.msg.CfSendMsgStatisticDao">

    <sql id = "tableName">
        cf_send_msg_statistic
    </sql>

    <insert id = "save" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfSendMsgStatistic" useGeneratedKeys="true" keyProperty="id">
      insert into <include refid="tableName"/>
        (`user_id`, `third_type`, `send_count`)
        values (#{userId}, #{thirdType}, #{sendCount})
        on duplicate key update
        `send_count` = values (send_count)

    </insert>

    <select id="selectSendCount" resultType="java.lang.Integer">
        select send_count from <include refid="tableName"/>
        where `user_id` = #{userId} and `third_type` = #{thirdType}
    </select>

</mapper>