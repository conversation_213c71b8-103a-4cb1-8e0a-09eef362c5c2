<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.msg.DailyReportSummaryDao">
	<sql id="TABLE">
		`cf_daily_report_summary`
	</sql>

	<sql id="QUERY_FIELDS">
		`id`, `user_id`, `info_id`, `info_uuid`, `title`, `nickname`, `date_str`, `total_amount`,
		`current_amount`, `today_amount`, `today_share_count`, `today_share_amount`,
		`money_in_one_share`, `message_status`
	</sql>

	<sql id="INSERT_FIELDS">
		`user_id`, `info_id`, `info_uuid`, `title`, `nickname`, `date_str`, `total_amount`,
		`current_amount`, `today_amount`, `today_share_count`, `today_share_amount`,
		`money_in_one_share`, `message_status`
	</sql>

	<select id="getSummary" resultType="com.shuidihuzhu.cf.model.dailyreport.CfDailyReportSummary">
		SELECT
		<include refid="QUERY_FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE `info_uuid`=#{infoUuid} AND `date_str`=#{dateStr}
		AND `is_delete`=0
	</select>

	<insert id="add" parameterType="com.shuidihuzhu.cf.model.dailyreport.CfDailyReportSummary">
		INSERT  IGNORE INTO
		<include refid="TABLE"/>
		(<include refid="INSERT_FIELDS"/>)
		VALUES (#{userId}, #{infoId}, #{infoUuid}, #{title}, #{nickname}, #{dateStr}, #{totalAmount},
		#{currentAmount}, #{todayAmount}, #{todayShareCount}, #{todayShareAmount},
		#{moneyInOneShare}, #{messageStatus})
	</insert>

	<insert id="addBatch" parameterType="com.shuidihuzhu.cf.model.dailyreport.CfDailyReportSummary">
		INSERT IGNORE INTO
		<include refid="TABLE"/>
		(<include refid="INSERT_FIELDS"/>)
		VALUES
		<foreach collection="list" item="item" separator=",">
			(#{item.userId}, #{item.infoId}, #{item.infoUuid}, #{item.title}, #{item.nickname},
			#{item.dateStr}, #{item.totalAmount},
			#{item.currentAmount}, #{item.todayAmount}, #{item.todayShareCount}, #{item.todayShareAmount},
			#{item.moneyInOneShare}, #{item.messageStatus})
		</foreach>
	</insert>

	<select id="getOfStatus" resultType="com.shuidihuzhu.cf.model.dailyreport.CfDailyReportSummary">
		SELECT
		<include refid="QUERY_FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE
		`date_str`=#{dateStr} AND
		`message_status` IN
		<foreach collection="statusSet" item="item" separator="," open="(" close=")">
			#{item}
		</foreach>
		limit #{offset},#{limit}
	</select>

	<update id="updateStatusBatch" parameterType="com.shuidihuzhu.cf.model.dailyreport.CfDailyReportSummary">
		UPDATE
		<include refid="TABLE"/>
		SET
		`message_status`=#{status}
		WHERE
		`id` IN
		<foreach collection="list" item="item" open="(" close=")" separator=",">
			#{item.id}
		</foreach>
	</update>
</mapper>