<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfVerificationDao">

	<sql id="tableName">
        crowd_funding_verification
    </sql>

	<select id="getByPatientUserIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification">
		SELECT
		*
		FROM
		<include refid="tableName"/>
		WHERE
		patient_user_id IN
		<foreach collection="patientUserids" item="userId" separator="," open="(" close=")">
			#{userId}
		</foreach>
		AND
		valid = 1
	</select>

	<update id="updatePatientUserId">
		update <include refid="tableName"/> set `patient_user_id`=#{patientUserId}, operate_time = now()
		where `id`=#{id}
	</update>

</mapper>
