<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfCapitalTotalDao">

    <sql id="tableName">
        cf_capital_total
    </sql>

    <insert id="saveBatch" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfCapitalTotal" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tableName"/>
        (`info_uuid`,`date`,`pay_num`,`pay_amount`,`single_refund_num`,`single_refund_amount`,`all_refund_num`,`all_refund_amount`,`balance`)
        VALUES
        <foreach collection="cfCapitalTotalList" item="cfCapitalTotal" separator=",">
            (#{cfCapitalTotal.infoUuid},#{cfCapitalTotal.date},#{cfCapitalTotal.payNum},
            #{cfCapitalTotal.payAmount},#{cfCapitalTotal.singleRefundNum},#{cfCapitalTotal.singleRefundAmount},
            #{cfCapitalTotal.allRefundNum},#{cfCapitalTotal.allRefundAmount},#{cfCapitalTotal.balance})
        </foreach>
    </insert>

    <update id="updateForDelete">
        UPDATE <include refid="tableName"/>
        SET `is_delete`=1
        WHERE `date`=#{date}
    </update>

    <select id="getByInfoUuidAndDates" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfCapitalTotal">
        SELECT *
        FROM <include refid="tableName"/>
        WHERE `info_uuid`=#{infoUuid} AND `is_delete`=0
            AND `date` IN <foreach collection="dates" item="date" open="(" separator="," close=")">#{date}</foreach>
    </select>

    <select id="getByInfoUuidAndDate" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfCapitalTotal">
        SELECT *
        FROM <include refid="tableName"/>
        WHERE `info_uuid`=#{infoUuid} AND `is_delete`=0 AND `date`=#{date}
        ORDER BY id DESC
        LIMIT 1
    </select>

    <select id="getLastOneByInfoUuid" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfCapitalTotal">
        SELECT *
        FROM <include refid="tableName"/>
        WHERE `info_uuid`=#{infoUuid} AND <![CDATA[`date`<#{date}]]> AND `is_delete`=0
        ORDER BY `date` DESC
        LIMIT 1
    </select>

</mapper>