<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.ICfCaseUpdateAmountDAO" >

    <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.model.crowdfunding.CfCaseUpdateAmountRecord">
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="case_id" property="caseId" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="target_amount" property="targetAmount" jdbcType="BIGINT"/>
        <result column="original_amount" property="originalAmount" jdbcType="BIGINT"/>
        <result column="reason" property="reason" jdbcType="VARCHAR"/>
        <result column="operator_name" property="operatorName" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="INSERT_Column_List">
        id,case_id,user_id,target_amount,original_amount,reason,operator_name
    </sql>

    <sql id="Base_Column_List">
        id,case_id,user_id,target_amount,original_amount,reason,operator_name,create_time
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfCaseUpdateAmountRecord" useGeneratedKeys="true" keyProperty="id">

        insert into cf_case_update_amount_record(<include refid="INSERT_Column_List" />)
        values
            (
            #{id, jdbcType=BIGINT},
            #{caseId,jdbcType=BIGINT},
            #{userId,jdbcType=BIGINT},
            #{targetAmount,jdbcType=BIGINT},
            #{originalAmount,jdbcType=BIGINT},
            #{reason,jdbcType=VARCHAR},
            #{operatorName,jdbcType=VARCHAR}
            )
    </insert>

    <select id="getLatestByCaseId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from cf_case_update_amount_record
        where case_id = #{caseId} and is_delete = 0 order by id desc limit 1
    </select>

    <select id="getListByCaseId" resultType="com.shuidihuzhu.cf.client.apipure.model.crowdfunding.CfCaseUpdateAmountRecordParam">
        select <include refid="Base_Column_List"/>
        from cf_case_update_amount_record
        where case_id = #{caseId} and is_delete = 0 order by id desc
    </select>

    <select id="getFixTargetAmountRecordByCaseIds" resultType="com.shuidihuzhu.cf.client.apipure.model.crowdfunding.CfCaseUpdateAmountRecordParam">
        select <include refid="Base_Column_List"/>
        from cf_case_update_amount_record
        where case_id in
        <foreach item="item" collection="caseIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        and is_delete = 0
    </select>

</mapper>