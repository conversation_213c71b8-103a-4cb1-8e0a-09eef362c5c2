<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfInfoProgressFollowDao">
	<resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.model.crowdfunding.CfInfoProgressFollow">
		<constructor>
			<idArg column="id" javaType="java.lang.Integer" jdbcType="INTEGER"/>
			<arg column="user_id" javaType="java.lang.Long" jdbcType="BIGINT"/>
			<arg column="is_follow" javaType="java.lang.Boolean" jdbcType="BIT"/>
			<arg column="info_uuid" javaType="java.lang.String" jdbcType="VARCHAR"/>
			<arg column="create_time" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
			<arg column="last_modified" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
			<arg column="valid" javaType="java.lang.Boolean" jdbcType="BIT"/>
		</constructor>
	</resultMap>
	<sql id="Base_Column_List">
    id, user_id, is_follow, info_uuid, create_time, last_modified, valid
  </sql>
	<select id="selectByInfoUuidWithFollow" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List"/>
		from cf_info_progress_follow
		where id > #{id} and info_uuid = #{infoUuid} and is_follow = true and valid = 1
		LIMIT #{limit}
	</select>
	<select id="selectByInfoUuidAndUserId" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List"/>
		from cf_info_progress_follow
		where info_uuid = #{infoUuid} and user_id = #{userId} and valid = 1
		LIMIT 1
	</select>

	<update id="deleteByInfoUuidAndUserId">
     update cf_info_progress_follow
    set valid = 0
    where info_uuid = #{infoUuid} and user_id = #{userId} and valid = 1
  </update>

	<update id="followCfInfo">
		update cf_info_progress_follow
		set is_follow = 1
		where info_uuid = #{infoUuid} and user_id = #{userId} and valid = 1
	</update>

	<update id="unfollowCfInfo">
		update cf_info_progress_follow
		set is_follow = 0
		where info_uuid = #{infoUuid} and user_id = #{userId} and valid = 1
	</update>

	<insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoProgressFollow">
		<selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
			SELECT LAST_INSERT_ID()
		</selectKey>
		INSERT IGNORE INTO cf_info_progress_follow (user_id, is_follow, info_uuid
		)
		values (#{userId,jdbcType=BIGINT}, #{isFollow,jdbcType=BIT}, #{infoUuid,jdbcType=VARCHAR})
	</insert>

</mapper>
