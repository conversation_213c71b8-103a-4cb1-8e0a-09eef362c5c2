<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfSwitcherDao">
    <sql id="TABLE">
        cf_switcher
    </sql>
    <sql id="QUERY_FIELDS">
        `id`,
        `name`,
        `comment`,
        `value`,
        `date_created` as dateCreated,
		`last_modified` as lastModified
    </sql>
    
    <select id="getAll" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfSwitcher">
    	SELECT <include refid="QUERY_FIELDS" />
    	FROM <include refid="TABLE" />
    </select>
    
</mapper>