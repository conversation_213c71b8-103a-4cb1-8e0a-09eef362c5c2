<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfBaseInfoTitleTemplateDao">

    <sql id="table_name">
        `cf_base_info_title_template`
    </sql>

    <sql id="insert_fields">
        `title_type`,
        `template`
    </sql>

    <sql id="select_fields">
        `id`,
        `title_type`,
        `template`
    </sql>

    <insert id="insertOne" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTitleTemplate">
        INSERT INTO <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES (#{titleType},#{template})
    </insert>

    <select id="selectLimit" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTitleTemplate">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `is_delete`=0
        LIMIT #{start},#{size}
    </select>

    <select id="selectTypeLimit" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTitleTemplate">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `is_delete`=0 AND `title_type`=#{titleType}
        #{start},#{size}
    </select>
</mapper>