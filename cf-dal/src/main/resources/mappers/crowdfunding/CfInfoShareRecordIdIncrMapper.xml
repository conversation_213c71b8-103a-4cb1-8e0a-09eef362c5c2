<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfInfoShareRecordIdIncrDao">

	<sql id="TABLE">
        share_id_incr
    </sql>

	<sql id="fields">
		`id`,
		`comment`,
		`create_time`,
		`update_time`,
		`is_delete`
	</sql>

	<insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareRecordIdIncr" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO
		<include refid="TABLE"/>
		(`comment`)
		VALUES
		(#{comment})
	</insert>


</mapper>
