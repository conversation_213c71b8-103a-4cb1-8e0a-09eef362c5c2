<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!--
CREATE TABLE `crowdfunding_treatment` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `crowdfunding_id` int(11) NOT NULL COMMENT 'crowdfunding_info的Id',
  `disease_name` varchar(50) DEFAULT NULL COMMENT '疾病名称',
  `location` varchar(100) DEFAULT NULL COMMENT '医院所在城市',
  `hospital_name` varchar(50) DEFAULT NULL COMMENT '医院名称',
  PRIMARY KEY (`id`),
  UNIQUE KEY `crowdfunding_id` (`crowdfunding_id`)
) ENGINE=InnoDB AUTO_INCREMENT=429 DEFAULT CHARSET=utf8 COMMENT='疾病情况表';


-->
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingTreatmentDao">

    <sql id="selectFields">
        `id`,
        `crowdfunding_id` as crowdfundingId,
        `disease_name` as diseaseName,
        `location`,
        `hospital_name`,
        `diagnose_hospital_name`,
        `hospital_id`,
        `diagnose_hospital_id`,
        `hospital_city_id`,
        `diagnose_hospital_city_id`,
        `sub_attachment_type`,
        `hospital_code`
    </sql>
    <sql id="tableName">
        `crowdfunding_treatment`
    </sql>

	<select id="get" parameterType="int" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingTreatment">
        SELECT <include refid="selectFields" />
        FROM <include refid="tableName" />
        WHERE crowdfunding_id = #{crowdfundingId}
	</select>
	
	<select id="getByInfoIdList" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingTreatment">
        SELECT <include refid="selectFields" />
        FROM <include refid="tableName" />
        WHERE crowdfunding_id in
        <foreach collection="infoIdList" item="crowdfundingId" open="(" separator="," close=")">
			#{crowdfundingId}
		</foreach>
	</select>

	<insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingTreatment" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        	<include refid="tableName" />
	    (`crowdfunding_id`,
		    `disease_name`,
		    `location`,
		    `hospital_name`,
            `diagnose_hospital_name`,
            `hospital_id`,
            `diagnose_hospital_id`,
            `hospital_city_id`,
            `diagnose_hospital_city_id`,
            `sub_attachment_type`
            <if test="hospitalCode != '' and hospitalCode != null">
                ,hospital_code
            </if>
	    ) VALUES (
		    #{crowdfundingId},
		    #{diseaseName},
		    #{location},
		    #{hospitalName},
            #{diagnoseHospitalName},
            #{hospitalId},
            #{diagnoseHospitalId},
            #{hospitalCityId},
            #{diagnoseHospitalCityId},
            #{subAttachmentType}
            <if test="hospitalCode != '' and hospitalCode != null">
            ,#{hospitalCode}
            </if>
		)
    </insert>

    <update id="update" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingTreatment">
        UPDATE <include refid="tableName" />
	    SET
		    `disease_name` = #{diseaseName},
		    `location` = #{location},
		    `hospital_name` = #{hospitalName},
            `diagnose_hospital_name`=#{diagnoseHospitalName},
            `hospital_id`=#{hospitalId},
            `diagnose_hospital_id`=#{diagnoseHospitalId},
            `hospital_city_id`=#{hospitalCityId},
            `diagnose_hospital_city_id`=#{diagnoseHospitalCityId},
            `sub_attachment_type`=#{subAttachmentType}
            <if test="hospitalCode != '' and hospitalCode != null">
                ,hospital_code = #{hospitalCode}
            </if>
        WHERE 
        	`crowdfunding_id`=#{crowdfundingId}
    </update>

    <select id="selectDiseaseName" resultType="String">
        SELECT
            `disease_name`
        FROM
            <include refid="tableName" />
        WHERE
            <bind name="diseasePattern" value="'%' + disease"/>
            `disease_name` LIKE #{diseasePattern}
            AND LOCATE('，', disease_name) = 0
            AND LOCATE('/', disease_name) = 0
            AND LOCATE(' ', disease_name) = 0
            AND LOCATE('(', disease_name) = 0
            AND LOCATE('（', disease_name) = 0
            AND LOCATE('／', disease_name) = 0
            AND LOCATE('―', disease_name) = 0
        GROUP BY `disease_name`
        HAVING COUNT(*) >= #{judge}
        ORDER BY COUNT(*) DESC
        LIMIT #{start},#{size}
    </select>

</mapper>