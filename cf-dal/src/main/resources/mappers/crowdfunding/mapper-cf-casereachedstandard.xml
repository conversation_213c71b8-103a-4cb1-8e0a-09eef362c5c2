<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfCaseReachedStandardDAO">

    <sql id="tableName">
		cf_case_reached_standard
	</sql>

    <sql id="Base_Column_List">
		id,
		create_time,
		update_time,
		info_id,
		user_id,
		unique_code
	</sql>

    <sql id="insert_Column_List">
		info_id,
		user_id,
		unique_code
	</sql>

    <select id="getByInfoIdAndUserIdAndUnqiueCode" resultType="com.shuidihuzhu.cf.domain.CfCaseReachedStandard">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="tableName"/>
        WHERE `info_id` = #{infoId}
        and `user_id` = #{userId}
        and `unique_code` = #{uniqueCode}
        AND `is_delete` = 0
        limit 1;
    </select>

    <insert id="insert"
            parameterType="com.shuidihuzhu.cf.domain.CfCaseReachedStandard">
        <selectKey order="AFTER" resultType="long" keyProperty="id">
            select last_insert_id() as id
        </selectKey>
        insert into <include refid="tableName"/>
        (<include refid="insert_Column_List" />)
        values(
        #{infoId},
        #{userId},
        #{uniqueCode}
        )
    </insert>


</mapper>