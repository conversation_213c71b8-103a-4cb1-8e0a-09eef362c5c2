<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace = "com.shuidihuzhu.cf.dao.crowdfunding.InitialAudit.CrowdfundingInitialAuditInfoDao">

    <sql id="tableName">
       crowdfunding_initial_audit_info
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.initialAudit.CrowdfundingInitialAuditInfo">
        INSERT ignore INTO
        <include refid="tableName"/>
        (`case_id`, `base_info`,  `first_approve_info`,`credit_info`, `reject_detail`, `work_order_id`)
        VALUES (#{caseId}, #{baseInfo},  #{firstApproveInfo}, #{creditInfo}, #{rejectDetail},  #{workOrderId})
    </insert>

    <select id="selectById" resultType="com.shuidihuzhu.cf.model.crowdfunding.initialAudit.CrowdfundingInitialAuditInfo">
        SELECT * FROM
        <include refid="tableName"/>
        WHERE id = #{id}
    </select>

    <update id = "updateInitialAuditInfo" parameterType="com.shuidihuzhu.cf.model.crowdfunding.initialAudit.CrowdfundingInitialAuditInfo">
        UPDATE <include refid="tableName"/>
        <set>
            <if test="baseInfo != 0">
                base_info = #{baseInfo},
            </if>
            <if test="firstApproveInfo != 0">
                first_approve_info = #{firstApproveInfo},
            </if>
            <if test="creditInfo != 0">
                credit_info = #{creditInfo},
            </if>
            <if test="rejectDetail != null">
                reject_detail = #{rejectDetail},
            </if>
            <if test="workOrderId != 0">
                work_order_id = #{workOrderId},
            </if>
        </set>
        WHERE case_id = #{caseId}
    </update>

    <select id="selectByCaseId" resultType="com.shuidihuzhu.cf.model.crowdfunding.initialAudit.CrowdfundingInitialAuditInfo">
        SELECT * FROM
        <include refid="tableName"/>
        WHERE case_id = #{caseId}
    </select>

    <update id="updateState">
        update
        <include refid="tableName"></include>
        set base_info = #{baseInfo} ,
        first_approve_info=#{firstApproveInfo} ,
        credit_info = #{creditInfo},
        reject_detail = #{rejectDetail}
        where case_id = #{caseId}
        limit 1
    </update>
</mapper>