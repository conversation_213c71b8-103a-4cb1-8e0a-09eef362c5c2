<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfAdRegisterDao">
	<sql id="tableName">
		cf_ad_register_record
	</sql>

	<sql id="fields">
		`id` as id,
		`day_key` as dayKey,
		`cropty_mobile` as cryptoMobile,
		`channel` as channel,
		`user_id` as userId,
		`open_id` as openId,
		`user_name` as userName,
		`disease` as disease,
  		`type` as type,
  		`relation` as relation,
  		`help` as help,
  		`note` as note,
  		`info_uuid` as infoUuid,
  		`is_inviter` as isInviter,
  		`cropty_inviter_mobile` as cryptoInviterMobile,
		`create_time` as createTime,
		`last_modified` as lastModified,
		`primary_channel` as primaryChannel,
		`source` as source,
		`semwp` as semwp,
		`keyword` as keyword
	</sql>

	<insert id="insert"
			useGeneratedKeys="true" keyProperty="id"
			parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfAdRegister">
		INSERT INTO <include refid="tableName"/>
		(`day_key`, `cropty_mobile`, `channel`, `user_id`, `open_id`, `user_name`, `disease`, `type`, `relation`, `help`, `info_uuid`, `note`, `is_inviter`, `cropty_inviter_mobile`, `client_ip`, `primary_channel`, `source`,`semwp`,`keyword`)
		VALUES
		(date(now()), #{cryptoMobile}, #{channel}, #{userId}, #{openId}, #{userName}, #{disease}, #{type}, #{relation}, #{help}, #{infoUuid}, #{note}, #{isInviter}, #{cryptoInviterMobile}, #{clientIp}, #{primaryChannel}, #{source},#{semwp},#{keyword})
	</insert>

	<select id="selectByMobile" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfAdRegister">
		SELECT <include refid="fields"/>
		FROM <include refid="tableName"/>
		WHERE
		`cropty_mobile`=#{cryptoMobile}
		ORDER BY `id` DESC
		LIMIT 1
	</select>

	<select id="selectByMobileAndTypeAndDate" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfAdRegister">
		SELECT <include refid="fields"/>
		FROM <include refid="tableName"/>
		WHERE
			`cropty_mobile`=#{cryptoMobile}
		AND
			`type`=#{type}
		AND
			`day_key`=#{dayKey}
		LIMIT 1
	</select>

	<select id="selectByTime" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfAdRegister">
		SELECT <include refid="fields"/>
		FROM <include refid="tableName"/>
		WHERE
			`create_time`
		BETWEEN #{start} AND #{end}
		LIMIT #{offset}, #{limit}
	</select>

	<select id="selectByChannelAndMobile" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfAdRegister">
		SELECT  <include refid="fields"/>
		FROM <include refid="tableName"/>
		WHERE
		`cropty_mobile`=#{cryptoMobile}
		AND `channel` IN <foreach collection="list" item="item" open="(" separator="," close=")" >#{item}</foreach>
		ORDER BY `id` DESC
		LIMIT 1
	</select>

	<select id="getById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfAdRegister">
		SELECT <include refid="fields"/>
		FROM <include refid="tableName"/>
		WHERE
		`id` = #{id}
	</select>

	<update id="updateById" >
		UPDATE  <include refid="tableName"/>
		SET `day_key` = date(now()),
		`channel` = #{cfAdRegister.channel},  	`disease` = #{cfAdRegister.disease}, 	`type`=  #{cfAdRegister.type},
		`relation` = #{cfAdRegister.relation}, 	`help` = #{cfAdRegister.help}, 			`info_uuid` = #{cfAdRegister.infoUuid} ,
		`note` = #{cfAdRegister.note},			`user_name` = #{cfAdRegister.userName}, `primary_channel`=#{cfAdRegister.primaryChannel},
		`source` = #{cfAdRegister.source}
		WHERE `id`=#{cfAdRegister.id}
	</update>
</mapper>
