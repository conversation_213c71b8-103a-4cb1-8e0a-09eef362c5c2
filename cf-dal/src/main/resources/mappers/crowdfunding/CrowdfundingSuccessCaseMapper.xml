<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingSuccessCaseDao">
    <sql id="TABLE_NAME">
        crowdfunding_success_case
    </sql>

    <sql id="FIELDS">
        `info_id` as infoId,
        `short_id` as shortId
    </sql>


    <select id="getIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingSuccessCase">
        SELECT
        info_id as infoId
        FROM
        <include refid="TABLE_NAME"/>
        WHERE
        `valid`=1
        ORDER BY sort, end_time DESC
        <if test="limit!=-1">
            LIMIT #{limit}
        </if>
    </select>

    <select id="getIdsAfterTime" resultType="java.lang.String">
        SELECT
        info_id as infoId
        FROM
        <include refid="TABLE_NAME"/>
        WHERE
        `valid`=1
        AND <![CDATA[ `end_time`>#{time} ]]>
        ORDER BY sort, end_time DESC
    </select>

    <select id="getIdsByType" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingSuccessCase">
        SELECT
        <include refid="FIELDS"/>
        FROM
        <include refid="TABLE_NAME"/>
        WHERE
        `type`=#{type}
        AND
        `valid`=1
        ORDER BY sort, end_time DESC
        <if test="limit!=-1">
            LIMIT #{limit}
        </if>
    </select>

    <insert id="addSuccessCase" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingSuccessCase" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO <include refid="TABLE_NAME" />
        (`info_id`,`type`,`sort`, `end_time`, `short_id`)
        VALUES
        (#{infoId},#{type},#{sort},#{endTime}, #{shortId})
    </insert>

    <select id="getByShortId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingSuccessCase">
        SELECT
        <include refid="FIELDS"/>
        FROM
        <include refid="TABLE_NAME"/>
        WHERE
        `short_id`=#{shortId}
        AND
        `valid`=1
        ORDER BY id DESC
        LIMIT 1
    </select>
</mapper>