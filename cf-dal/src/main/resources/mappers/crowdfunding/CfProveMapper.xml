<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfProveDao">

    <sql id="tableName">
        cf_send_prove
    </sql>

    <sql id="selectFields">
        id,
        case_id,
        picture_url,
        picture_audit_status
    </sql>

    <select id="getlastOne" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfSendProve">
        select
        <include refid="selectFields"/>
        from
        <include refid="tableName"/>
        where case_id = #{caseId}
        and is_delete = 0
        order by id desc limit 1
    </select>
    <select id="getListByCaseId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfSendProve">
        select * from
        <include refid="tableName"/>
        where case_id = #{caseId}
        and is_delete = 0
        order by id desc
    </select>
    <select id="getById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfSendProve">
        select * from
        <include refid="tableName"/>
        where id = #{id}
        and picture_audit_status = 2
        and is_delete = 0
    </select>

    <update id="updateProveById">
        update
        <include refid="tableName"/>
        set picture_url = #{pictureUrl},picture_audit_status = #{pictureAuditStatus}
        where id = #{id}
        and is_delete = 0
    </update>
    <update id="updateCancelReason">
        update
        <include refid="tableName"/>
        set cancel_reason = #{cancelReason}
        where id = #{id}
        and is_delete = 0
    </update>


</mapper>
