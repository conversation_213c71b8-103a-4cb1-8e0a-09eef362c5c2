<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfPosterUserRelDao">
	<sql id="TABLE_NAME">
		cf_poster_user_rel
	</sql>

	<sql id="FIELDS">
		`id` as id,
		`activity_id` as activityId,
		`user_id` as userId,
		`third_type` as thirdType,
		`participate_status` as participateStatus,
		`share_status` as shareStatus
	</sql>

	<select id="findByActivityIdAndUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfPosterUserRel">
		SELECT <include refid="FIELDS" />
		FROM <include refid="TABLE_NAME" />
		WHERE
			`activity_id` = #{activityId}
		AND
			`user_id` = #{userId}
		AND
			`is_delete`=0
		LIMIT 1
	</select>

	<insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfPosterUserRel">
		INSERT IGNORE INTO <include refid="TABLE_NAME"/>
		(`activity_id`, `user_id`, `third_type`, `participate_status`)
		VALUES
		(#{activityId}, #{userId}, #{thirdType}, #{participateStatus})
	</insert>

	<update id="updateShareStatus">
		UPDATE <include refid="TABLE_NAME" />
		SET `share_status`=#{status}
		WHERE `activity_id`=#{activityId}
		AND `user_id`=#{userId}
	</update>

</mapper>