<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingOrderShardingIdDao">
	<sql id="TABLE">
        crowdfunding_order_id_sharding
    </sql>
	<sql id="FIELDS">
        `id`,`crowdfunding_id`
    </sql>
	<sql id="INSERT_FIELDS">
        `id`,`crowdfunding_id`
    </sql>

	<insert id="addCrowdfundingOrderShardingModel" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		INSERT INTO
		<include refid="TABLE"/>
		(<include refid="INSERT_FIELDS"/>)
		VALUES(#{id},#{crowdfundingId});
	</insert>


	<select id="getCrowdfundingIdById" resultType="java.lang.Long">
		SELECT
		crowdfunding_id
		FROM
		<include refid="TABLE"/>
		WHERE `id`=#{id};
	</select>

	<select id="getCrowdfundingIdByIds" resultType="java.lang.Long">
		SELECT
		crowdfunding_id
		FROM
		<include refid="TABLE"/>
		WHERE id in
		<foreach collection="ids" item="id" open="(" separator="," close=")">
			#{id}
		</foreach>
	</select>
	<select id="getCrowdfundingIdsByIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		select  <include refid="FIELDS" />
		from <include refid="TABLE"/>
		WHERE id in
		<foreach collection="ids" item="id" open="(" separator="," close=")">
			#{id}
		</foreach>
	</select>

</mapper>
