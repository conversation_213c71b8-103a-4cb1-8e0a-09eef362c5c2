<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.duiba.DuibaConsumePointsMapper">
    <sql id="tableName">
        user_points_duiba
    </sql>

    <sql id="Valve">
         `s_uid`,`uid`, `credits`, `item_code`, `description`, `order_num`,
         `type`, `face_price`, `actual_price`, `ip`, `params`,`timestamp`,
         `app_key`, `wait_audit`
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CreditConsumeParamsModel">
        insert into
        <include refid="tableName"/>
        (<include refid="Valve"/>)
        VALUES (#{sUid},#{uid}, #{credits}, #{itemCode}, #{description}, #{orderNum}, #{type}, #{facePrice}, #{actualPrice}, #{ip},
         #{params}, #{timestamp}, #{appKey}, #{waitAudit})
    </insert>

    <select id="selectByOrderNum" resultType="com.shuidihuzhu.cf.model.crowdfunding.CreditConsumeParamsModel">
        select <include refid="Valve"/>
        from <include refid="tableName"/>
        where
        `order_num` = #{orderNum}
        and
        `is_delete` = 0
    </select>

    <select id="selectByOrderNumIngoreDel" resultType="com.shuidihuzhu.cf.model.crowdfunding.CreditConsumeParamsModel">
        select <include refid="Valve"/>
        from <include refid="tableName"/>
        where
        `order_num` = #{orderNum}
    </select>

    <select id="selectByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CreditConsumeParamsModel">
        select <include refid="Valve"/>
        from <include refid="tableName"/>
        where
        `uid` = #{userId}
    </select>
    <update id="deleteByOrderNum">
        update
        <include refid="tableName"/>
        set
        is_delete = 1
        where
        `order_num` = #{orderNum}
    </update>
</mapper>