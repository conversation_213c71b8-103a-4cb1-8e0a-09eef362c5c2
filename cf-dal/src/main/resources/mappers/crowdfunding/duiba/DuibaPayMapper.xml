<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.duiba.DuibaPayMapper">
    <sql id="tableName">
        duiba_pay
    </sql>

    <sql id="Valve">
         `s_uid`,`uid`, `order_num`,`trade_no`,`cash`,`amount`
    </sql>
    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.DuibaPayModel">
        insert into
        <include refid="tableName"/>
        (<include refid="Valve"/>)
        VALUES (#{sUid}, #{uid}, #{orderNum}, #{tradeNo}, #{cash}, #{amount})
    </insert>

    <select id="selectByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.DuibaPayModel">
        select * from
        <include refid="tableName"/>
        where
        `uid` = #{userId}
        and `is_delete` = 0
    </select>

    <select id="selectByOrderNum" resultType="com.shuidihuzhu.cf.model.crowdfunding.DuibaPayModel">
        select * from
        <include refid="tableName"/>
        where
        `order_num` = #{orderNum}
        and `is_delete` = 0
    </select>

    <select id="selectByTradeNo" resultType="com.shuidihuzhu.cf.model.crowdfunding.DuibaPayModel">
        select * from
        <include refid="tableName"/>
        where
        `trade_no` = #{tradeNo}
        and `is_delete` = 0
    </select>

    <update id="setPaySuccess">
        update
        <include refid="tableName"/>
        set
        `pay_status` = #{payStatus} , `pay_time` = #{payTime}
        where
        `trade_no` = #{tradeNo}
        and `is_delete` = 0
    </update>

    <update id="setRefundSuccess">
        update
        <include refid="tableName"/>
        set
        `refund_status` = #{refundStatus} , `refund_time` = #{refundTime}
        where
        `trade_no` = #{tradeNo}
        and `is_delete` = 0
    </update>

    <update id="setRefundStartStatus">
        update
        <include refid="tableName"/>
        set
        `refund_status` = #{refundStatus} , `refund_start_time` = #{refundStartTime}
        where
        `trade_no` = #{tradeNo}
        and `is_delete` = 0
    </update>

    <update id="setRefundReason">
        update
        <include refid="tableName"/>
        set
        `refund_reason` = #{refundReason}
        where
        `trade_no` = #{tradeNo}
        and `is_delete` = 0
    </update>

    <update id="setRefundFailedReason">
        update
        <include refid="tableName"/>
        set
        `refund_failed_reason` = #{refundFailedReason}
        where
        `trade_no` = #{tradeNo}
        and `is_delete` = 0
    </update>

    <update id="setRefundStatus">
        update
        <include refid="tableName"/>
        set
        `refund_status` = #{refundStatus}
        where
        `trade_no` = #{tradeNo}
        and `is_delete` = 0
    </update>

    <update id="setThirdType">
        update
        <include refid="tableName"/>
        set
        `third_type` = #{thirdType}
        where
        `trade_no` = #{tradeNo}
        and `is_delete` = 0
    </update>

    <update id="setPayInfoStauts">
        update
        <include refid="tableName"/>
        set
        `pay_status` = #{payStatus}
        where
        `trade_no` = #{tradeNo}
        and `is_delete` = 0
    </update>

    <update id="setOtherConfirm">
        update
        <include refid="tableName"/>
        set
        `other_confirm` = #{status}
        where
        `trade_no` = #{tradeNo}
        and `is_delete` = 0
    </update>

</mapper>