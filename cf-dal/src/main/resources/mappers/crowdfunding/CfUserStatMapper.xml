<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfUserStatDao">
	<sql id="TABLENAME">
		cf_user_stat
	</sql>
	<sql id="QUERY_FIELDS">
		`id`,
		`user_id` as userId,
		`score`,
		`share_count` as shareCount,
		`donation_count` as donationCount,
		`verify_user_count` as verifyUserCount,
		`comment_count` as commentCount,
		`blessing_count` as blessingCount,
		`amount`,
		`create_time` as createTime,
		`update_time` as updateTime,
		`is_delete` as isDelete,
		`had_followed` as hadFollowed,
		`init_status` as initStatus
	</sql>
	<select id="selectByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfUserStat">
		SELECT <include refid="QUERY_FIELDS" />
		FROM <include refid="TABLENAME" />
		WHERE `user_id`=#{userId} 
		AND `is_delete`= 0
	</select>


	<select id="getByUserIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfUserStat">
		SELECT
		<include refid="QUERY_FIELDS" />
		FROM <include refid="TABLENAME" />
		WHERE `user_id` IN
		<foreach collection="userIds" item="userId" separator="," close=")" open="(">
			#{userId}
		</foreach>
		AND `is_delete`= 0
	</select>


	<insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfUserStat">
		insert IGNORE INTO cf_user_stat (user_id, score, share_count,
		donation_count, verify_user_count, comment_count,
		blessing_count, amount, had_followed
		)
		values (#{userId,jdbcType=BIGINT}, #{score,jdbcType=INTEGER}, #{shareCount,jdbcType=INTEGER},
		#{donationCount,jdbcType=INTEGER}, #{verifyUserCount,jdbcType=INTEGER}, #{commentCount,jdbcType=INTEGER},
		#{blessingCount,jdbcType=INTEGER}, #{amount,jdbcType=INTEGER}, #{hadFollowed,jdbcType=BIT}
		)
	</insert>
	<insert id="insertContainStatus">
		insert IGNORE INTO cf_user_stat (user_id, score, share_count,
		donation_count, verify_user_count, comment_count,
		blessing_count, amount, had_followed,init_status
		)
		values (#{userId,jdbcType=BIGINT}, #{score,jdbcType=INTEGER}, #{shareCount,jdbcType=INTEGER},
		#{donationCount,jdbcType=INTEGER}, #{verifyUserCount,jdbcType=INTEGER}, #{commentCount,jdbcType=INTEGER},
		#{blessingCount,jdbcType=INTEGER}, #{amount,jdbcType=INTEGER}, #{hadFollowed,jdbcType=BIT},#{initStatus}
		)
	</insert>

	<update id="addAmountAndScore" >
		UPDATE <include refid="TABLENAME" />
		SET score = score + #{score} ,
		amount = amount + ${amount},
		donation_count = donation_count + 1
		WHERE user_id  = #{userId}
	</update>

	<update id="followAndScore" >
		UPDATE  <include refid="TABLENAME" />
		SET score = score + #{score} ,
		had_followed =1
		WHERE user_id = #{userId}
	</update>

	<update id="addScoreByInc" >
		UPDATE <include refid="TABLENAME" />
		SET score = score + #{score} ,
		${valueFiled} = ${valueFiled} + 1
		WHERE user_id = #{userId}
	</update>

	<update id="minusScore" >
		UPDATE <include refid="TABLENAME" />
		SET score = score - #{score}
		WHERE user_id = #{userId} and score &gt;= #{score}
	</update>

	<update id="addScore" >
		UPDATE <include refid="TABLENAME" />
		SET score = score + #{score}
		WHERE user_id = #{userId}
	</update>

	<update id="updateIniting" >
		UPDATE <include refid="TABLENAME" />
		SET init_status = 1
		WHERE user_id = #{userId} AND init_status = 0
	</update>

	<update id="updateInited" >
		UPDATE <include refid="TABLENAME" />
		SET init_status = 2
		WHERE user_id  = #{userId} AND init_status = 1
	</update>

	<update id="updateForInit" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfUserStat">
		update cf_user_stat
		SET score = #{score,jdbcType=INTEGER},
		share_count = #{shareCount,jdbcType=INTEGER},
		donation_count =   #{donationCount,jdbcType=INTEGER},
		verify_user_count =  #{verifyUserCount,jdbcType=INTEGER},
		comment_count =  #{commentCount,jdbcType=INTEGER},
		blessing_count =  #{blessingCount,jdbcType=INTEGER},
		amount = #{amount,jdbcType=INTEGER},
		had_followed = #{hadFollowed,jdbcType=BIT}
		where user_id =  #{userId,jdbcType=BIGINT}
	</update>

	<update id="updateBatch" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfUserStat">
		UPDATE
		`cf_user_stat`

		SET `score`= CASE `id`
		<foreach collection="items" item="item">
			WHEN #{item.id} THEN #{item.score}
		</foreach>
		END,

		`share_count`= CASE `id`
		<foreach collection="items" item="item">
			WHEN #{item.id} THEN #{item.shareCount}
		</foreach> END,

		`donation_count`= CASE `id`
		<foreach collection="items" item="item">
			WHEN #{item.id} THEN #{item.donationCount}
		</foreach> END,

		`verify_user_count`= CASE `id`
		<foreach collection="items" item="item">
			WHEN #{item.id} THEN #{item.verifyUserCount}
		</foreach> END,

		`comment_count`= CASE `id`
		<foreach collection="items" item="item">
			WHEN #{item.id} THEN #{item.commentCount}
		</foreach> END,

		`blessing_count`= CASE `id`
		<foreach collection="items" item="item">
			WHEN #{item.id} THEN #{item.blessingCount}
		</foreach> END,

		`amount`= CASE `id`
		<foreach collection="items" item="item">
			WHEN #{item.id} THEN #{item.amount}
		</foreach> END,

		`had_followed`= CASE `id`
		<foreach collection="items" item="item">
			WHEN #{item.id} THEN #{item.hadFollowed}
		</foreach> END,

		`init_status`= CASE `id`
		<foreach collection="items" item="item">
			WHEN #{item.id} THEN #{item.initStatus}
		</foreach> END,

		`had_followed`= CASE `id`
		<foreach collection="items" item="item">
			WHEN #{item.id} THEN #{item.hadFollowed}
		</foreach>  END

		WHERE `id` IN
		<foreach collection="items" item="item" open="(" close=")" separator=",">
			#{item.id}
		</foreach>
		AND `is_delete`=0

	</update>

</mapper>