<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfInfoExtDao">

    <sql id="TABLE">
            cf_info_ext
    </sql>

    <sql id="FIELDS">
            id                     as id,
            info_uuid              as infoUuid,
            from_type              as fromType,
            from_detail            as fromDetail,
            self_tag               as selfTag,
            product_name           as productName,
            finish_status          as finishStatus,
            refund_status          as refundStatus,
            transfer_status        as transferStatus,
            refund_end_time        as refundEndTime,
            pay_type               as payType,
            user_third_type        as userThirdType,
            date_created           as dateCreated,
            last_modified          as lastModified,
            crypto_register_mobile as cryptoRegisterMobile,
            first_approve_status   as firstApproveStatus,
            first_approve_time     as firstApproveTime,
            volunteer_unique_code  as volunteerUniqueCode,
            client_ip              as clientIp,
            primary_channel        as primaryChannel,
            need_case_list         as needCaseList,
            cf_version             as cfVersion,
            pre_id                 as preId,
            bd_followed            as bdFollowed,
            finish_str             as finishStr,
            case_id                as caseId,
            no_handling_fee        as noHandlingFee

    </sql>

    <insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO
        <include refid="TABLE"/>
        (`info_uuid`,`self_tag`,`product_name`,`pay_type`,`user_third_type`,`crypto_register_mobile`,`volunteer_unique_code`,`first_approve_status`,
        `client_ip`, `need_case_list`, `primary_channel`,cf_version,pre_id,case_id
        <if test="firstApproveTime!=null">
            ,first_approve_time
        </if>
        )
        VALUES
        (#{infoUuid},#{selfTag},#{productName},#{payType},#{userThirdType},#{cryptoRegisterMobile},#{volunteerUniqueCode},#{firstApproveStatus},
        #{clientIp}, #{needCaseList}, #{primaryChannel},#{cfVersion},#{preId},#{caseId}
        <if test="firstApproveTime!=null">
            ,#{firstApproveTime}
        </if>
        )
    </insert>

    <update id="updateCaseFrom">
        UPDATE
        <include refid="TABLE"/>
        SET
        `from_type`=#{fromType},
        `from_detail`=#{fromDetail}
        WHERE
        `info_uuid`=#{infoUuid}
        LIMIT 1
    </update>

    <select id="getByInfoUuid" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt">
        SELECT
        <include refid="FIELDS"/>
        FROM
        <include refid="TABLE"/>
        WHERE
        `info_uuid` = #{infoUuid}
        LIMIT 1
    </select>

    <select id="getByInfoUuidFromSlave" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt">
        SELECT
        <include refid="FIELDS"/>
        FROM
        <include refid="TABLE"/>
        WHERE
        `info_uuid` = #{infoUuid}
        LIMIT 1
    </select>

    <update id="updateFinishStatus">
        UPDATE
        <include refid="TABLE"/>
        SET
        `finish_status`=#{finishStatus}
        WHERE
        `info_uuid`=#{infoUuid}
        LIMIT 1
    </update>

    <update id="updateFinishStatusByList">
        UPDATE
        <include refid="TABLE"/>
        SET
        `finish_status`=#{finishStatus}
        WHERE
        `info_uuid` IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="updateRefundStatus">
        UPDATE
        <include refid="TABLE"/>
        SET
        `refund_status`=#{refundStatus}
        WHERE
        `info_uuid`=#{infoUuid}
        LIMIT 1
    </update>

    <update id="updateTransferStatus">
        UPDATE
        <include refid="TABLE"/>
        SET
        `transfer_status`=#{transferStatus}
        WHERE
        `info_uuid`=#{infoUuid}
        LIMIT 1
    </update>

    <select id="getListByUuids" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt">
        SELECT
        <include refid="FIELDS"/>
        FROM
        <include refid="TABLE"/>
        WHERE `info_uuid` IN
        <foreach collection="infoUuids" open="(" close=")" separator="," item="infoUuid">
            #{infoUuid}
        </foreach>
    </select>

    <select id="getListByProductNames" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt">
        SELECT
        <include refid="FIELDS"/>
        FROM
        <include refid="TABLE"/>
        WHERE `product_name` IN
        <foreach collection="productNames" open="(" close=")" separator="," item="productName">
            #{productName}
        </foreach>
    </select>

    <update id="updateFromType">
        UPDATE
        <include refid="TABLE"/>
        SET from_type=#{fromType}
        WHERE info_uuid=#{infoUuid}
        LIMIT 1
    </update>

    <update id="updateFirstApproveStatus">
        UPDATE
        <include refid="TABLE"/>
        SET first_approve_status = #{firstApproveStatus}
        WHERE info_uuid=#{infoUuid}
    </update>

    <update id="updateFirstApproveStatusByCaseId">
        UPDATE
        <include refid="TABLE"/>
        SET first_approve_status = #{firstApproveStatus}
        WHERE case_id=#{caseId}
    </update>

    <select id="getByFirstApproveTime" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt">
        SELECT
        <include refid="FIELDS"/>
        FROM
        <include refid="TABLE"/>
        WHERE <![CDATA[ `first_approve_time`>=#{startTime} and `first_approve_time`<#{endTime} ]]>
        LIMIT #{start},#{size}
    </select>

    <update id="updateNeedCaseList">
        UPDATE
        <include refid="TABLE"/>
        SET need_case_list=#{needCaseList}
        WHERE info_uuid=#{infoUuid}
    </update>

    <update id="updateUserRefund">
        UPDATE
        <include refid="TABLE"/>
        SET refund_end_time=#{refundEndTime}
        WHERE info_uuid=#{infoUuid}
    </update>
    <update id="updateBdFollowed">
        update
        <include refid="TABLE"/>
        set bd_followed = #{bdFollowed}
        where info_uuid=#{infoUuid} and volunteer_unique_code=#{uniqueCode}
    </update>

    <insert id="insertList" parameterType="java.util.List">
        INSERT INTO
        <include refid="TABLE"/>
        (`info_uuid`) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item})
        </foreach>
    </insert>

    <select id="selectByInfoUuidList" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt">
        SELECT
        <include refid="FIELDS"/>
        FROM
        <include refid="TABLE"/>
        WHERE `info_uuid` IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectAllVolunteerUniqueCodeWithTime"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt">
        SELECT
        <include refid="FIELDS"/>
        FROM
        <include refid="TABLE"/>
        where (date_created between #{startTime} and #{endTime}) and volunteer_unique_code=#{volunteerUniqueCode}
        order by date_created desc
        limit #{offset},#{pageSize}
    </select>

    <select id="selectFollowedVolunteerUniqueCodeWithTime"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt">
        SELECT
        <include refid="FIELDS"/>
        FROM
        <include refid="TABLE"/>
        where (date_created between #{startTime} and #{endTime}) and volunteer_unique_code=#{volunteerUniqueCode} and
        bd_followed=1
        order by date_created desc
        limit #{offset},#{pageSize}
    </select>
    <select id="getBdServiceCountByTime" resultType="java.lang.Integer">
        SELECT count(*)
        FROM
        <include refid="TABLE"/>
        where (date_created between #{startTime} and #{endTime}) and volunteer_unique_code=#{uniqueCode}
    </select>
    <select id="getBdFollowedServiceCountByTime" resultType="java.lang.Integer">
        SELECT count(*)
        FROM
        <include refid="TABLE"/>
        where (date_created between #{startTime} and #{endTime}) and volunteer_unique_code=#{uniqueCode} and
        bd_followed=1
    </select>


    <update id="updateFinishStr">
        update
        <include refid="TABLE"/>
        set finish_str = #{finishStr}
        where info_uuid=#{caseUuid}
    </update>

    <select id="getByCaseId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt">
        SELECT
        <include refid="FIELDS"/>
        FROM
        <include refid="TABLE"/>
        WHERE
        `case_id` = #{caseId}
        LIMIT 1
    </select>



    <select id="getByCaseIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt">
        SELECT
        <include refid="FIELDS"/>
        FROM
        <include refid="TABLE"/>
        WHERE
        case_id IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

<!--    查询最近3000个案例里面 符合捐款条件的随机N个案例id  -->
    <select id="listInfoUuidForDonationTest" resultType="java.lang.String">
        select cie.info_uuid from cf_info_ext cie
        left join crowdfunding_info ci on ci.id = cie.case_id
        where cie.id > (
            select id from cf_info_ext cie
            order by id desc
            limit 3000,1
            )
        and ci.user_id = #{userId}
        and cie.first_approve_status = 30
        and cie.finish_status = 0
        and ci.end_time > ci.create_time
        order by rand()
        limit #{size};
    </select>

    <update id="updateNoHandlingFeeByInfoUuid">
        UPDATE
        <include refid="TABLE"/>
        SET
        `no_handling_fee`=#{noHandlingFee}
        WHERE
        `info_uuid`=#{infoUuid}
    </update>

</mapper>