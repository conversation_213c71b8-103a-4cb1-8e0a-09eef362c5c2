<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.toufang.CfLandingPageConfigStyleDao">
	<sql id="TABLE">
		cf_landing_page_config_style
	</sql>

	<sql id="QUERY_FIELDS">
		`id`, `template` as style
	</sql>

	<sql id="INSERT_FIELDS">
		`template`
	</sql>

	<insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.toufang.CfLandingPageStyle">
		INSERT INTO
		<include refid="TABLE"/>
		(<include refid="INSERT_FIELDS"/>)
		VALUES (#{style})
	</insert>

	<select id="get" resultType="com.shuidihuzhu.cf.model.crowdfunding.toufang.CfLandingPageStyle">
		SELECT
		<include refid="QUERY_FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE
		`id`=#{id}
	</select>

	<update id="update">
		UPDATE
		<include refid="TABLE"/>
		SET `template`=#{style} WHERE
		`id`=#{id}
	</update>

</mapper>