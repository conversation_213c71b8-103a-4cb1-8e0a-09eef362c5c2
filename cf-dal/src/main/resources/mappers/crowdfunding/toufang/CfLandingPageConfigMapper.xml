<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.toufang.CfLandingPageConfigDao">
	<sql id="TABLE">
		cf_landing_page_config
	</sql>

	<sql id="QUERY_FIELDS">
		`id`, `type`, `account`, `campaign`, `adgroup`, `style_id`
	</sql>

	<sql id="INSERT_FIELDS">
		`type`, `account`, `campaign`, `adgroup`, `style_id`
	</sql>

	<select id="get" resultType="com.shuidihuzhu.cf.model.crowdfunding.toufang.CfLandingPageConfig">
		SELECT
		<include refid="QUERY_FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE `account`=#{account} AND
		`campaign`=#{campaign} AND
		`adgroup`=#{adgroup} AND
		`is_delete`=0
	</select>

	<select id="getByPage" resultType="com.shuidihuzhu.cf.model.crowdfunding.toufang.CfLandingPageConfig">
		SELECT
		<include refid="QUERY_FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE
		`is_delete`=0
		limit #{offset}, #{limit}
	</select>

	<update id="update" parameterType="com.shuidihuzhu.cf.model.crowdfunding.toufang.CfLandingPageConfig">
		UPDATE
		<include refid="TABLE"/>
		SET `campaign`=#{campaign}, `adgroup`=#{adgroup}, `account`=#{account}, `style_id`=#{styleId},
		`type`=#{type}
		WHERE `id`=#{id}
	</update>

	<select id="getCfLandingPageConfigList" resultType="com.shuidihuzhu.cf.model.crowdfunding.toufang.CfLandingPageConfig">
		SELECT
		<include refid="QUERY_FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE `campaign`=#{campaign}
		AND `adgroup`=#{adgroup}
		AND `is_delete`=0
		LIMIT 100
	</select>

</mapper>