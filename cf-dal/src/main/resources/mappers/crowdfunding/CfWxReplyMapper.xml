<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfWxReplyDao">
    <sql id="TABLE">
        cf_wx_reply
    </sql>
    <sql id="QUERY_FIELDS">
        `id`,
        `key_world` as keyWorld,
        `reply_content` as replyContent,
        `need_equal` as needEqual,
        `group_id` as groupId,
        `msg_type` as msgType,
        `valid`,
        `date_created` as dateCreated,
		`last_modified` as lastModified
    </sql>
    <sql id="SELECT_FIELDS">
        id,key_world,reply_content,date_created,need_equal,group_id,msg_type,valid
    </sql>
    
    <select id="getAll" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfWxReply">
    	SELECT <include refid="QUERY_FIELDS" />
    	FROM <include refid="TABLE" />
        WHERE `valid`=1
    </select>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfWxReply">
        INSERT INTO
        <include refid="TABLE"/>
        (key_world,reply_content,need_equal,group_id,msg_type,valid)
        VALUES (#{keyWorld}, #{replyContent}, #{needEqual}, #{groupId}, #{msgType}, #{valid})
    </insert>

    <update id="update" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfWxReply">
        UPDATE <include refid="TABLE"/>
        SET key_world=#{keyWorld},reply_content=#{replyContent},need_equal=#{needEqual},group_id=#{groupId},msg_type=#{msgType},valid=#{valid}
        WHERE id=#{id}
    </update>

    <select id="selectByKeyAndGroupId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfWxReply">
        SELECT <include refid="SELECT_FIELDS"/>
        FROM <include refid="TABLE"/>
        <where>
            <if test="keyWorld != null and keyWorld != ''">
                `key_world`=#{keyWorld}
            </if>
            <if test="groupId != null">
                AND `group_id`=#{groupId}
            </if>
            <if test="replyContent != null and replyContent != ''">
                AND `reply_content`=#{replyContent}
            </if>
            <if test="valid != null">
                AND `valid`=#{valid}
            </if>
            <if test="msgType != null">
                AND `msg_type`=#{msgType}
            </if>
        </where>
    </select>

    <select id="selectById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfWxReply">
        SELECT <include refid="SELECT_FIELDS"/>
        FROM <include refid="TABLE"/>
        WHERE `id`=#{id} AND `valid`=1
    </select>

    <select id="selectByExample" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfWxReplyDomain" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfWxReply">
        SELECT
        <include refid="SELECT_FIELDS"/>
        FROM
        <include refid="TABLE"/>
        <where>
            <if test="keyWorld != null">
                <bind name="keyWorld" value="'%' + keyWorld + '%'"/>
                AND key_world LIKE #{keyWorld}
            </if>
            <if test="replyContent != null">
                <bind name="replyContent" value="'%' + replyContent + '%'"/>
                AND reply_content LIKE #{replyContent}
            </if>
            <if test="needEqual != null">
                AND need_equal = #{needEqual}
            </if>
            <if test="groupId != null">
                AND group_id = #{groupId}
            </if>
            <if test="valid != null">
                AND valid = #{valid}
            </if>
        </where>
        ORDER BY #{orderByClause}
        LIMIT #{pageNo},#{pageSize}
    </select>
</mapper>