<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfInfoBlessingDao">
	<resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.model.crowdfunding.CfInfoBlessing">
		<constructor>
			<idArg column="id" jdbcType="INTEGER" javaType="java.lang.Integer"/>
			<arg column="self_tag" jdbcType="VARCHAR" javaType="java.lang.String"/>
			<arg column="user_id" jdbcType="BIGINT" javaType="java.lang.Long"/>
			<arg column="info_uuid" jdbcType="VARCHAR" javaType="java.lang.String"/>
			<arg column="blessing" jdbcType="BIT" javaType="java.lang.Boolean"/>
			<arg column="valid" jdbcType="BIT" javaType="java.lang.Boolean"/>
			<arg column="create_time" jdbcType="TIMESTAMP" javaType="java.util.Date"/>
			<arg column="last_modified" jdbcType="TIMESTAMP" javaType="java.util.Date"/>
		</constructor>
	</resultMap>

	<insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoBlessing">
		<selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER">
			SELECT LAST_INSERT_ID()
		</selectKey>
		INSERT IGNORE INTO cf_info_blessing (self_tag, user_id, info_uuid,
		blessing)
		values (#{selfTag,jdbcType=VARCHAR}, #{userId,jdbcType=BIGINT}, #{infoUuid,jdbcType=VARCHAR},
		#{blessing,jdbcType=BIT})
	</insert>

	<update id="blessingByUserId" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoBlessing">
		update cf_info_blessing
		set
		blessing = 1
		where user_id = #{userId} AND info_uuid = #{infoUuid} AND blessing = 0
	</update>

	<update id="unBlessingByUserId" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoBlessing">
		update cf_info_blessing
		set
		blessing = 0
		where user_id = #{userId} AND info_uuid = #{infoUuid} AND blessing = 1
	</update>

	<update id="blessingBySelfTag" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoBlessing">
		update cf_info_blessing
		set
		blessing = 1
		where self_tag = #{selfTag} AND info_uuid = #{infoUuid} AND blessing = 0
	</update>

	<update id="unBlessingBySelfTag" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoBlessing">
		update cf_info_blessing
		set
		blessing = 0
		where self_tag = #{selfTag} AND info_uuid = #{infoUuid} AND blessing = 1
	</update>

	<select id="selectByUserId" resultMap="BaseResultMap">
		select id, self_tag, user_id, info_uuid, blessing, valid, create_time, last_modified
		from cf_info_blessing
		WHERE user_id = #{userId} and info_uuid = #{infoUuid} and valid = 1 limit 1
	</select>
	<select id="selectBySelfTag" resultMap="BaseResultMap">
		select id, self_tag, user_id, info_uuid, blessing, valid, create_time, last_modified
		from cf_info_blessing
		WHERE self_tag = #{selfTag} and info_uuid = #{infoUuid} and valid = 1 limit 1
	</select>

	<select id="selectByInfoUuid" resultMap="BaseResultMap">
		select id, self_tag, user_id, info_uuid, blessing, valid, create_time, last_modified
		from cf_info_blessing
		WHERE info_uuid = #{infoUuid} and blessing=1 and valid = 1 and `id` <![CDATA[ <= ]]> #{anchorId}
		ORDER BY `id` DESC
		LIMIT #{limit}
	</select>

	<select id="selectBlessByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoBlessing">
        select *
        from cf_info_blessing
        where user_id = #{userId} AND blessing = 1 AND valid = 1
    </select>

	<update id="updateUserIdByIds">
        update
        cf_info_blessing
        set user_id = #{userId}
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
			#{id}
		</foreach>
    </update>

</mapper>
