<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.share.CfShareWeiboTemplateDao">
	<sql id="TABLE_NAME">
		cf_share_weibo_template
	</sql>

	<sql id="SELECT_FIELDS">
		`code` as code,
		`template` as template
	</sql>

	<select id="getAll" resultType="com.shuidihuzhu.cf.model.crowdfunding.share.CfShareWeiboTemplate">
		SELECT <include refid="SELECT_FIELDS"/>
		FROM <include refid="TABLE_NAME"/>
		WHERE `is_delete`=0
	</select>


</mapper>