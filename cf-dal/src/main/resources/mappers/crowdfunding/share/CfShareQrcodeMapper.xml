<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.share.CfShareQrcodeDao">
	<sql id="TABLE_NAME">
		cf_share_qrcode
	</sql>

	<sql id="SELECT_FIELDS">
		`id` as id,
		`info_uuid` as infoUuid,
		`channel` as channel,
		`url` as url
	</sql>

	<select id="getByInfoUuidAndChannel" resultType="com.shuidihuzhu.cf.model.crowdfunding.share.CfShareQrcode">
		SELECT <include refid="SELECT_FIELDS"/>
		FROM <include refid="TABLE_NAME"/>
		WHERE
		`info_uuid`=#{infoUuid}
		AND
		`channel`=#{channel}
		AND
		`is_delete`=0
		ORDER BY `id` DESC
		LIMIT 1

	</select>

	<insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.share.CfShareQrcode">
		INSERT INTO <include refid="TABLE_NAME"/>
		(`info_uuid`, `channel`, `url`)
		VALUES
		(#{infoUuid}, #{channel}, #{url})
	</insert>


</mapper>