<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.HelpShuidichouVerifyRecordDAO">

    <sql id="tableName">
        help_shuidichou_verify_record
    </sql>

    <sql id="select_list">
        `case_id`,
        `user_id`,
        `is_agree`,
        `verify_content_id`
    </sql>

    <insert id="insert">
        INSERT INTO <include refid="tableName"/>
        (
        case_id, user_id, is_agree, verify_content_id
        )
        VALUES
        (
        #{caseId}, #{userId}, #{isAgree}, #{verifyContentId}
        )
    </insert>

    <select id="selectByCaseId" resultType="com.shuidihuzhu.cf.model.crowdfunding.HelpShuidichouVerifyRecordDO">
        select <include refid="select_list"/>
        from <include refid="tableName"/>
        where `case_id` = #{caseId}
        and `is_delete` = 0
        limit 1
    </select>

    <update id="updateIsAgree">
        UPDATE
        <include refid="tableName"/>
        SET
        is_agree = #{isAgree}
        WHERE
        case_id = #{caseId}
        and user_id = #{userId}
    </update>

</mapper>
