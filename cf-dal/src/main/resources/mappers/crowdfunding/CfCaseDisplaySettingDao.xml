<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfCaseDisplaySettingDao">

    <sql id="table">
        case_display_setting
    </sql>
    <insert id="addOne" parameterType="com.shuidihuzhu.cf.model.CaseDisplaySettingDo">
        insert into
        <include refid="table"/>
        (`case_id`, `language`, `color`)
        values
        (#{setting.caseId}, #{setting.language}, #{setting.color})
    </insert>

    <update id="update" parameterType="com.shuidihuzhu.cf.model.CaseDisplaySettingDo">
        update
        <include refid="table"/>
        set
        `language` = #{setting.language},
        `color` = #{setting.color}
        where
        `case_id` = #{setting.caseId}
    </update>

    <select id="select" resultType="com.shuidihuzhu.cf.model.CaseDisplaySettingDo">
        select *
        from
        <include refid="table"/>
        where `case_id` = #{caseId}
        and `is_delete` = 0
    </select>
</mapper>