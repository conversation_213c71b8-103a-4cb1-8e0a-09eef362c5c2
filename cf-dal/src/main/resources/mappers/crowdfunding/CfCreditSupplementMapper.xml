<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfCreditSupplementDao">
    <sql id="tableName">
        `cf_credit_supplement`
    </sql>

    <sql id="insert_fields">
        `case_id`,`info_uuid`,`count`,`property_type`,`total_value`,`status`,`sell_count`,`convention`,`sale_value`
    </sql>

    <sql id="select_fields">
        `id`,`case_id`,`info_uuid`,`count`,`property_type`,`total_value`,`status`,`sell_count`,`convention`,`sale_value`
    </sql>

    <insert id="addList">
        INSERT INTO <include refid="tableName"/>
          (<include refid="insert_fields"/>)
        VALUES
        <foreach collection="list" separator="," item="item">
          (#{item.caseId},#{item.infoUuid},#{item.count},#{item.propertyType},#{item.totalValue},#{item.status},#{item.sellCount},#{item.convention},#{item.saleValue})
        </foreach>
    </insert>

    <select id="selectByInfoUuid" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfCreditSupplement">
        SELECT <include refid="select_fields"/>
        FROM <include refid="tableName"/>
        WHERE `info_uuid`=#{infoUuid}
        AND `is_delete`=0
    </select>

    <update id="deleteFromInfoUuid">
        UPDATE <include refid="tableName"/>
        SET `is_delete`=1
        WHERE `info_uuid`=#{infoUuid}
    </update>
</mapper>