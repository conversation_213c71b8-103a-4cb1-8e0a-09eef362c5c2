<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfSeekHelpInfoFragmentDao">

	<sql id="TABLE">
		cf_seek_help_info_fragment
	</sql>

	<sql id="SELECT_FIELDS">
		`id`, `info_uuid`, `user_id`, `patient_intro`, `disease`, `diagnose_intro`,
		`want_to_say`, `create_time`, `update_time`
	</sql>

	<sql id="INSERT_FIELDS">
		`info_uuid`, `user_id`, `patient_intro`, `disease`, `diagnose_intro`,
		`want_to_say`
	</sql>

	<select id="get" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingSeekHelpInfoFragment">
		SELECT
		<include refid="SELECT_FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE
		`info_uuid`=#{infoUuid} AND `is_delete`=0
	</select>

	<update id="delete">
		UPDATE
		<include refid="TABLE"/>
		SET `is_delete`=1 WHERE `info_uuid`=#{infoUuid} AND `is_delete`=0
	</update>

	<insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingSeekHelpInfoFragment" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO
		<include refid="TABLE"/>
		(<include refid="INSERT_FIELDS"/>)
		VALUES (#{infoUuid}, #{userId}, #{patientIntro}, #{disease}, #{diagnoseIntro},
		#{wantToSay})
	</insert>

</mapper>