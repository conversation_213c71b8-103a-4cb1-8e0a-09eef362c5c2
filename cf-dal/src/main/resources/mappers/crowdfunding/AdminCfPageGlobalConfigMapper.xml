<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminCfPageGlobalConfigDao">

    <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.model.crowdfunding.CfPageGlobalConfig">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="global_type" property="globalType" jdbcType="INTEGER"/>
        <result column="effect_show" property="effectShow" jdbcType="INTEGER"/>
        <result column="jumper_url" property="jumperUrl" jdbcType="VARCHAR"/>
        <result column="jumper_name" property="jumperName" jdbcType="VARCHAR"/>
        <result column="jumper_desc" property="jumperDesc" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="TABLE">
        cf_page_global_config
    </sql>

    <sql id="Base_Column_List">
		id,global_type,jumper_name,jumper_desc,jumper_url,effect_show,create_time,update_time
	</sql>

    <sql id="insert_Column_List">
		global_type,jumper_url,effect_show,jumper_name,jumper_desc
	</sql>


    <select id="getListByType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="TABLE" />
        WHERE global_type=#{globalType} AND is_delete = 0
    </select>


    <insert id="add" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">

        INSERT  INTO <include refid="TABLE" />
        ( <include refid="insert_Column_List" />)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.globalType},#{item.jumperUrl},#{item.effectShow},#{item.jumperName},#{item.jumperDesc})
        </foreach>

    </insert>


    <update id="delete">
        UPDATE <include refid="TABLE" />
        SET is_delete=1
        WHERE global_type=#{globalType} and is_delete=0
    </update>


</mapper>