<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.EvaluateLabelUserRecordDao">

    <sql id="tableName">
        evaluate_label_user_record
    </sql>
    <insert id="insertBatch">
        insert into <include refid="tableName" />
        (`case_id`,`user_id`,`label_id`)
        values
        <foreach collection="labels" item="label" separator=",">
            (#{label.caseId},#{label.userId},#{label.labelId})
        </foreach>
    </insert>


</mapper>