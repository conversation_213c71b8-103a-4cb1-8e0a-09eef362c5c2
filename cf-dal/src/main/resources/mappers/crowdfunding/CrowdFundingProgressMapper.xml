<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CrowdFundingProgressDao">
    <sql id="tableName">
        crowdfunding_progress
    </sql>

    <sql id="addFields">
        (`user_id`,`crowdfunding_id`,`type`,`title`,`content`,`image_urls`,`is_delete`)
    </sql>

    <resultMap id="selectResult" type="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingProgress">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="activityId" column="crowdfunding_id"/>
        <result property="type" column="type"/>
        <result property="title" column="title"/>
        <result property="content" column="content"/>
        <result property="imageUrls" column="image_urls"/>
        <result property="postDate" column="create_time"/>
        <result property="updateDate" column="update_time"/>
    </resultMap>

    <select id="getActivityProgress" resultMap="selectResult">
        SELECT *
        FROM <include refid="tableName"/>
        WHERE crowdfunding_id=#{activityId}
        AND type in <foreach collection="types" item="type" open="(" separator="," close=")">#{type}</foreach>
        AND is_delete = 0
        ORDER BY update_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <select id="queryProgressByUserId" resultMap="selectResult">
        SELECT *
        FROM <include refid="tableName"/>
        WHERE user_id=#{userId}
    </select>

    <select id="getActivityProgressById" resultMap="selectResult">
        SELECT *
        FROM <include refid="tableName"/>
        WHERE id=#{id}
    </select>

    <select id="getActivityProgressByIdAndCaseId" resultMap="selectResult">
        SELECT *
        FROM <include refid="tableName"/>
        WHERE id = #{id} and crowdfunding_id = #{caseId}
    </select>

    <insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingProgress" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO <include refid="tableName"/>
        	<include refid="addFields"/>
        VALUES
        	(#{userId},#{activityId},#{type},#{title},#{content},#{imageUrls},#{isDelete});
    </insert>

    <select id="queryByType" resultMap="selectResult">
        SELECT *
        FROM <include refid="tableName"/>
        WHERE crowdfunding_id=#{caseId} and `type`=#{pType} and is_delete = 0
    </select>
    <select id="getFirstProgress" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingProgress">
        SELECT *
        FROM <include refid="tableName"/>
        WHERE crowdfunding_id=#{crowdfundingId}  and is_delete = 0
        order by id
        limit 1
    </select>
    <select id="getListByType" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingProgress">
        SELECT *
        FROM <include refid="tableName"/>
        WHERE crowdfunding_id=#{caseId} and `type`=#{type} and is_delete = 0
        ORDER BY id desc
        limit 40
    </select>

    <select id="queryByCaseIdAndTypes" resultMap="selectResult">
        SELECT *
        FROM <include refid="tableName"/>
        WHERE crowdfunding_id=#{caseId}  and is_delete = 0
        and `type` in
        <foreach collection="pTypeList" item="pType" open="(" close=")" separator=",">
            #{pType}
        </foreach>
        ORDER BY id desc
    </select>

    <update id="updateForDelete">
        UPDATE <include refid="tableName"/>
        SET `crowdfunding_id`=#{crowdfundingId}
        WHERE `id`=#{id}
    </update>

    <update id="deleteProgress">
        UPDATE <include refid="tableName"/>
        SET `is_delete`= 1
        WHERE `id`=#{progressId} AND `crowdfunding_id`=#{crowdfundingId}
    </update>

    <update id="updateImageUrls">
        UPDATE <include refid="tableName"/>
        SET `image_urls`=#{imageUrls}
        WHERE `id`=#{id}
    </update>

    <update id="updateType">
        UPDATE <include refid="tableName"/>
        SET `type`=#{toType}
        WHERE `id`=#{id} and `type`=#{fromType}
    </update>


</mapper>