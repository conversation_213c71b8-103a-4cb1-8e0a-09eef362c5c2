<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfCaseRiskDAO">

    <sql id="tableName">
		cf_case_risk
	</sql>

    <sql id="Base_Column_List">
		id,
		create_time,
		update_time,
		info_uuid,
		verified,passed,risk_type,risk,
		case_info_passed,
		delay_period,
		risk_data,
		handle_status
	</sql>

    <sql id="insert_Column_List">
		info_uuid,
		verified,passed,risk_type,risk,
		case_info_passed,
		delay_period,
		risk_data,
		handle_status
	</sql>

    <select id="getByInfoUuid" resultType="com.shuidihuzhu.cf.domain.CfCaseRiskDO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="tableName"/>
        WHERE `info_uuid`=#{infoUuid}
        and `risk_type` = #{riskType}
        AND `is_delete` = 0
    </select>

    <select id="listByCondition" resultType="com.shuidihuzhu.cf.domain.CfCaseRiskDO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="tableName"/>
        WHERE `verified` = #{verified}
        <if test="passedList != null">
            and `passed` in
            <foreach collection="passedList" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
        </if>
        <if test="riskType != 0">
            and `risk_type` = #{riskType}
        </if>
        AND `is_delete` = 0
        LIMIT #{start}, #{size}
    </select>

    <select id="listByConditionAll" resultType="com.shuidihuzhu.cf.domain.CfCaseRiskDO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="tableName"/>
        where `risk_type` = #{riskType}
        and `verified` = #{verified}
        and `passed` in
        <foreach collection="passedList" item="item" separator="," open="(" close=")" >
            #{item}
        </foreach>
        <if test="handleStatusList != null">
            and `handle_status` in
            <foreach collection="handleStatusList" item="item" separator="," open="(" close=")" >
                #{item}
            </foreach>
        </if>
        AND `is_delete` = 0
    </select>

    <insert id="insert"
            parameterType="com.shuidihuzhu.cf.domain.CfCaseRiskDO">
        <selectKey order="AFTER" resultType="long" keyProperty="id">
            select last_insert_id() as id
        </selectKey>
        insert into <include refid="tableName"/>
        (<include refid="insert_Column_List" />)
        values(
        #{infoUuid},
        #{verified},
        #{passed},
        #{riskType},
        #{risk},
        #{caseInfoPassed},
        #{delayPeriod},
        #{riskData},
        #{handleStatus}
        )
    </insert>

    <update id="updateRiskById" >
        update <include refid="tableName"/>
        <set>
            <if test="verified != 0">
                `verified` = #{verified},
            </if>
            <if test="passed != 0">
                `passed` = #{passed},
            </if>
            <if test="risk != 0">
                `risk` = #{risk},
            </if>
            <if test="caseInfoPassed != 0">
                `case_info_passed` = #{caseInfoPassed},
            </if>
            <if test="delayPeriod != 0">
                `delay_period` = #{delayPeriod},
            </if>
            <if test="riskData != null">
                `risk_data` = #{riskData},
            </if>
            <if test="handleStatus != 0">
                `handle_status` = #{handleStatus}
            </if>
        </set>
        where `id` = #{id}
        and `is_delete` = 0
    </update>

    <select id="listByCreateTime" resultType="com.shuidihuzhu.cf.domain.CfCaseRiskDO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="tableName"/>
        <where>
            <if test="startTime != null">
              and create_time <![CDATA[ >= ]]> #{startTime}
            </if>
            <if test="endTime != null">
                and create_time <![CDATA[ <= ]]> #{endTime}
            </if>
        </where>
        limit #{start}, #{size}
    </select>

    <update id="updateStatusByIds" >
        update <include refid="tableName"/>
        <set>
            <if test="handleStatus != 0">
                `handle_status` = #{handleStatus}
            </if>
        </set>
        where `id` in
        <foreach collection="ids" item="item" separator="," open="(" close=")" >
            #{item}
        </foreach>
        and `is_delete` = 0
    </update>


</mapper>
