<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.ICfUserCaseLabelDAO">

    <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.model.crowdfunding.CfUserCaseLabelDO">
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="case_id" property="caseId" jdbcType="INTEGER"/>
        <result column="end" property="end" jdbcType="TINYINT"/>
        <result column="raise_time" property="raiseTime" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="channel" property="channel" jdbcType="VARCHAR"/>
        <result column="raise_channel" property="raiseChannel" jdbcType="TINYINT"/>
        <result column="first_approve" property="firstApprove" jdbcType="TINYINT"/>
        <result column="approve_status" property="approveStatus" jdbcType="TINYINT"/>
        <result column="approve_time" property="approveTime" jdbcType="TIMESTAMP"/>
        <result column="reject_count" property="rejectCount" jdbcType="TINYINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="is_delete" property="delete" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="TABLE_NAME">
        cf_user_case_label
    </sql>

    <sql id="Base_Column_List">
		id,user_id,case_id,`end`,raise_time,end_time,channel,raise_channel,first_approve,approve_status,approve_time,reject_count,create_time,update_time,is_delete
	</sql>

    <update id="insertCaseLabel" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfUserCaseLabelDO" keyProperty="id" useGeneratedKeys="true">
        insert into <include refid="TABLE_NAME"/>
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="userId != null">
                user_id,
            </if>
            <if test="caseId != null">
                case_id,
            </if>
            <if test="end != null">
                `end`,
            </if>
            <if test="raiseTime != null">
                raise_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="channel != null">
                channel,
            </if>
            <if test="raiseChannel != null">
                raise_channel,
            </if>
            <if test="firstApprove != null">
                first_approve,
            </if>
            <if test="approveStatus != null">
                approve_status,
            </if>
            <if test="approveTime != null">
                approve_time,
            </if>
            <if test="rejectCount != null">
                reject_count,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="userId != null">
                #{userId, jdbcType=BIGINT},
            </if>
            <if test="caseId != null">
                #{caseId, jdbcType=INTEGER},
            </if>
            <if test="end != null">
                #{end, jdbcType=TINYINT},
            </if>
            <if test="raiseTime != null">
                #{raiseTime, jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                #{endTime, jdbcType=TIMESTAMP},
            </if>
            <if test="channel != null">
                #{channel, jdbcType=VARCHAR},
            </if>
            <if test="raiseChannel != null">
                #{raiseChannel, jdbcType=INTEGER},
            </if>
            <if test="firstApprove != null">
                #{firstApprove, jdbcType=TINYINT},
            </if>
            <if test="approveStatus != null">
                #{approveStatus, jdbcType=TINYINT},
            </if>
            <if test="approveTime != null">
                #{approveTime, jdbcType=TIMESTAMP},
            </if>
            <if test="rejectCount != null">
                #{rejectCount, jdbcType=TINYINT},
            </if>
            <if test="updateTime != null">
                #{updateTime, jdbcType=TIMESTAMP}
            </if>
        </trim>
        on duplicate key update
        <trim prefix="" suffix="" suffixOverrides="," >
            <if test="userId != null">
                user_id = values(user_id),
            </if>
            <if test="caseId != null">
                case_id = values(case_id),
            </if>
            <if test="end != null">
                `end` = values(`end`),
            </if>
            <if test="raiseTime != null">
                raise_time = values(raise_time),
            </if>
            <if test="endTime != null">
                end_time = values(end_time),
            </if>
            <if test="channel != null">
                channel = values(channel),
            </if>
            <if test="raiseChannel != null">
                raise_channel = values(raise_channel),
            </if>
            <if test="firstApprove != null">
                first_approve = values(first_approve),
            </if>
            <if test="approveStatus != null">
                approve_status = values(approve_status),
            </if>
            <if test="approveTime != null">
                approve_time = values(approve_time),
            </if>
            <if test="rejectCount != null">
                reject_count = values(reject_count),
            </if>
            <if test="updateTime != null">
                update_time = values(update_time),
            </if>
        </trim>
    </update>

    <sql id="Insert_Column_List">
		user_id,case_id,`end`,raise_time,end_time,channel,raise_channel,first_approve,approve_status,approve_time,reject_count
	</sql>

    <insert id="batchInsertCaseLabel" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfUserCaseLabelDO" keyProperty="id" useGeneratedKeys="true">
        insert into <include refid="TABLE_NAME"/> (<include refid="Insert_Column_List"/>)
        values
        <foreach collection="lists" item="list" separator=",">
            (#{list.userId},
              #{list.caseId},
              #{list.end},
              #{list.raiseTime},
              #{list.endTime},
              #{list.channel},
              #{list.raiseChannel},
              #{list.firstApprove},
              #{list.approveStatus},
              #{list.approveTime},
              #{list.rejectCount})
        </foreach>
        on duplicate key update
        user_id = values(user_id),case_id = values(case_id),`end` = values(`end`),raise_time = values(raise_time),end_time = values(end_time),channel = values(channel),raise_channel = values(raise_channel),
        first_approve = values(first_approve),approve_status = values(approve_status),approve_time = values(approve_time),reject_count = values(reject_count)
    </insert>

    <update id="updateEnd">
        update <include refid="TABLE_NAME"/>
        set `end`= 1
        where user_id = #{userId} and case_id = #{caseId}
    </update>

    <update id="updateEndTimeAndStatus">
        update <include refid="TABLE_NAME"/>
        set end_time = #{endTime}, approve_status = #{approveStatus},approve_time = #{approveTime}
        where user_id = #{userId} and case_id = #{caseId}
    </update>

    <update id="updateRejectAndStatus">
        update <include refid="TABLE_NAME"/>
        set reject_count = reject_count + 1,approve_status = #{approveStatus}
        where user_id = #{userId} and case_id = #{caseId}
    </update>

    <select id="queryCaseLabel" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="TABLE_NAME" />
        WHERE user_id = #{userId}
    </select>

    <select id="queryCaseLabelByUserAndCase" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM <include refid="TABLE_NAME" />
        WHERE user_id = #{userId} and case_id = #{caseId}
    </select>
</mapper>