<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfPvTrackConfigDao">
	<sql id="tableName">
		cf_pv_track_config
	</sql>

	<sql id="selectFields">
		`id` as id,
		`page` as page,
		`track` as track,
		`description` as description
	</sql>

	<select id="getByPage" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfPvTrackConfig">
		SELECT <include refid="selectFields"/>
		FROM <include refid="tableName"/>
		WHERE `page`=#{page}
		AND `track`=1
		LIMIT 1
	</select>

	<select id="list" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfPvTrackConfig">
		SELECT <include refid="selectFields"/>
		FROM <include refid="tableName"/>
		WHERE `track`=1
		ORDER BY `sort` DESC
	</select>

</mapper>