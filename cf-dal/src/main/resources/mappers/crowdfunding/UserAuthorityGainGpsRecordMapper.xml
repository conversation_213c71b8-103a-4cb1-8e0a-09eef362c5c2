<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.UserAuthorityGainGpsRecordDao">


    <sql id="tableName" >
        user_authority_gain_gps_record
    </sql>

    <insert id="insertData" parameterType="com.shuidihuzhu.cf.model.crowdfunding.UserAuthorityGainGpsRecord" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO
        <include refid="tableName"/>
        (`user_id`,`authority_gain`,`authority_time`)
        VALUES
        (#{userId},#{authorityGain},#{authorityTime})
    </insert>

</mapper>