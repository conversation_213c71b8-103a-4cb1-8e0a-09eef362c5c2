<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.task.CfInfoTaskRuleDao">

    <sql id="tableName">
        cf_info_task_rule
    </sql>

    <select id="getAllValid" resultType="com.shuidihuzhu.cf.model.task.CfInfoTaskRule">
        SELECT *
        FROM <include refid="tableName"/>
        WHERE `is_delete`=0
    </select>

    <select id="getById" resultType="com.shuidihuzhu.cf.model.task.CfInfoTaskRule">
        SELECT *
        FROM <include refid="tableName"/>
        WHERE `id`=#{id} AND `is_delete`=0
    </select>

</mapper>