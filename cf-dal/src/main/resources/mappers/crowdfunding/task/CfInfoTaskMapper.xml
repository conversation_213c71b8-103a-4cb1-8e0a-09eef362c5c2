<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.task.CfInfoTaskDao">

    <sql id="tableName">
        cf_info_task
    </sql>

    <insert id="save" parameterType="com.shuidihuzhu.cf.model.task.CfInfoTask" useGeneratedKeys="true" keyProperty="id">
        INSERT IGNORE INTO <include refid="tableName"/>
          (`info_uuid`,`date`,`task_rule_id`)
        VALUES
          (#{infoUuid},#{date},#{taskRuleId})
    </insert>

    <insert id="saveBatch" parameterType="com.shuidihuzhu.cf.model.task.CfInfoTask" useGeneratedKeys="true" keyProperty="id">
        INSERT IGNORE INTO <include refid="tableName"/>
          (`info_uuid`,`date`,`task_rule_id`)
        VALUES
          <foreach collection="cfInfoTaskList" item="cfInfoTask" separator=",">
              (#{cfInfoTask.infoUuid},#{cfInfoTask.date},#{cfInfoTask.taskRuleId})
          </foreach>
    </insert>

    <select id="getByInfoUuidAndDate" resultType="com.shuidihuzhu.cf.model.task.CfInfoTask">
        SELECT *
        FROM <include refid="tableName"/>
        WHERE `info_uuid`=#{infoUuid} AND `date`=#{date}
        and `is_delete` = 0
    </select>

    <select id="getByInfoUuidAndDateAndTaskRuleId" resultType="com.shuidihuzhu.cf.model.task.CfInfoTask">
        SELECT *
        FROM <include refid="tableName"/>
        WHERE `info_uuid`=#{infoUuid} AND `date`=#{date} AND `task_rule_id`=#{taskRuleId}
        and `is_delete` = 0
    </select>

    <select id="getByInfoUuidAndTaskRuleId" resultType="com.shuidihuzhu.cf.model.task.CfInfoTask">
        SELECT *
        FROM <include refid="tableName"/>
        WHERE `info_uuid`=#{infoUuid} AND `task_rule_id`=#{taskRuleId}
        and `is_delete` = 0
    </select>

    <update id="updateTimesOnce">
        UPDATE <include refid="tableName"/>
        SET `times`=`times`+1
        WHERE `info_uuid`=#{infoUuid} AND `date`=#{date} AND `task_rule_id`=#{taskRuleId}
    </update>

    <update id="updateTimes">
        UPDATE <include refid="tableName"/>
        SET `times`=#{times}
        WHERE `info_uuid`=#{infoUuid} AND `date`=#{date} AND `task_rule_id`=#{taskRuleId}
    </update>

    <delete id="deleteTaskByInfoUuidAndRuleId">
        delete from <include refid="tableName"/>
        WHERE `info_uuid`=#{infoUuid} AND `task_rule_id`=#{taskRuleId}
    </delete>

    <update id="updateFinish">
        UPDATE <include refid="tableName"/>
        SET `finish`=1
        WHERE `id`=#{id}
    </update>

</mapper>