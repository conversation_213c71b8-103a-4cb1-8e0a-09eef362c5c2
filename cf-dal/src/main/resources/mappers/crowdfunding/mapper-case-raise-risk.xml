<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CaseRaiseRiskDAO">

    <sql id="tableName">
		cf_case_raise_risk
	</sql>

    <sql id="Base_Column_List">
		id,
		case_id,
		info_uuid,
		risk_level,
		passed,
		risk_data
	</sql>

    <sql id="insert_Column_List">
        case_id,
		info_uuid,
		risk_level,
		passed,
		risk_data
	</sql>

    <select id="getByInfoUuid" resultType="com.shuidihuzhu.cf.domain.CaseRaiseRiskDO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="tableName"/>
        WHERE `info_uuid`=#{infoUuid}
        AND `is_delete` = 0
    </select>

    <insert id="insert"
            parameterType="com.shuidihuzhu.cf.domain.CaseRaiseRiskDO">
        <selectKey order="AFTER" resultType="long" keyProperty="id">
            select last_insert_id() as id
        </selectKey>
        replace into <include refid="tableName"/>
        (<include refid="insert_Column_List" />)
        values(
        #{caseId},
        #{infoUuid},
        #{riskLevel},
        #{passed},
        #{riskData}
        )
    </insert>

    <update id="updateByInfoUuid" >
        update <include refid="tableName"/>
        <set>
            <if test="riskLevel != 0">
                `risk_level` = #{riskLevel},
            </if>
            `passed` = #{passed},
            <if test="riskData != null">
                `risk_data` = #{riskData}
            </if>
        </set>
        where `info_uuid` = #{infoUuid}
        and `is_delete` = 0
    </update>


</mapper>
