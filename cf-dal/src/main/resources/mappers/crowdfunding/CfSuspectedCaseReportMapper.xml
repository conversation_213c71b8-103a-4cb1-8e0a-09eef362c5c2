<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfSuspectedCaseReportDao">
	<sql id="tableName">
		cf_suspected_case_report
	</sql>

	<insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfSuspectedCaseReport" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO <include refid="tableName"/>
		(`user_id`, `uuid`, `content`, `case_url`, `evidence`, `ip`, `channel`)
		VALUES
		(#{userId}, #{uuid}, #{content}, #{caseUrl}, #{evidence}, #{ip}, #{channel})
	</insert>

</mapper>