<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CaseLableDao">

    <sql id="TABLE">
        case_label
    </sql>

    <sql id="Base_Field">
        id,case_id,tag_key,tag_value
    </sql>

    <select id="getRaiseEnvByCaseId" resultType="com.shuidihuzhu.cf.model.CaseLableDO">
        SELECT
        <include refid="Base_Field"/>
        FROM
        <include refid="TABLE"/>
        WHERE
        case_id = #{caseId} and tag_key=#{tagKey} ORDER BY case_id LIMIT 1
    </select>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.CaseLableDO">
        INSERT INTO case_label (case_id,tag_key,tag_value) VALUES(#{caseId},#{tagKey},#{tagValue});
    </insert>

</mapper>