<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CaseLabelsJinYunCountyVerificationDao">
    <sql id="TABLE">
        jin_yun_county_verification
    </sql>

    <select id="getCount" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM
        <include refid="TABLE"/>
        WHERE info_uuid = #{infoUuid}
        and is_delete = 0
    </select>

    <insert id="insert">
        insert into <include refid="TABLE"/>
        (info_uuid) values (#{infoUuid})
        ON DUPLICATE KEY UPDATE
        is_delete = 0
    </insert>

    <update id="remove">
        update <include refid="TABLE"/>
        set is_delete = 1
        where info_uuid = #{infoUuid}
    </update>
</mapper>