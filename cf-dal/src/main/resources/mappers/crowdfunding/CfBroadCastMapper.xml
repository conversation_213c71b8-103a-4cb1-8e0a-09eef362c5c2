<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfBroadCastDao">
	<sql id="TABLE">
		cf_broadcast
	</sql>

	<sql id="SELECT_FIELDS">
		`id`, `info_uuid`, `nickname`, `head_img_url`, `amount`,
		`has_share`, `user_id`, `order_id`, `pay_time`
	</sql>

	<sql id="INSERT_FIELDS">
		`info_uuid`, `nickname`, `head_img_url`, `amount`,
		`has_share`, `user_id`, `order_id`, `pay_time`
	</sql>

	<insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.vo.CfLoveBroadcastVO">
		insert into
		<include refid="TABLE"/>
		(<include refid="INSERT_FIELDS"/>)
		values (#{data.infoUuid}, #{data.nickname}, #{data.headImgUrl}, #{data.amount}, #{data.hasShare},
		#{data.userId}, #{data.orderId}, #{data.payTime})
	</insert>


	<insert id="addBatch" parameterType="com.shuidihuzhu.cf.model.crowdfunding.vo.CfLoveBroadcastVO">
		insert into
		<include refid="TABLE"/>
		(<include refid="INSERT_FIELDS"/>)
		values
		<foreach collection="dataSet" item="data" separator=",">
			(#{data.infoUuid}, #{data.nickname}, #{data.headImgUrl}, #{data.amount}, #{data.hasShare},
			#{data.userId}, #{data.orderId}, #{data.payTime})
		</foreach>
	</insert>

	<select id="getByInfoId" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.CfLoveBroadcastVO">
		select
		<include refid="SELECT_FIELDS"/>
		from
		<include refid="TABLE"/>
		where `info_uuid`=#{infoId} and `is_delete`=0
		limit #{size}
	</select>

	<select id="getByOrderId" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.CfLoveBroadcastVO">
		select
		<include refid="SELECT_FIELDS"/>
		from
		<include refid="TABLE"/>
		where `order_id`=#{orderId} and `is_delete`=0
	</select>

	<select id="getByOrderIdBatch" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.CfLoveBroadcastVO">
		select
		<include refid="SELECT_FIELDS"/>
		from
		<include refid="TABLE"/>
		where
		`order_id` in
		<foreach collection="orderIds" item="orderId" separator="," open="(" close=")">
			#{orderId}
		</foreach>
	</select>

	<update id="update" parameterType="com.shuidihuzhu.cf.model.crowdfunding.vo.CfLoveBroadcastVO">
		update
		<include refid="TABLE"/>
		set
		`amount`=#{data.amount},
		`nickname`=#{data.nickname},
		`head_img_url`=#{data.headImgUrl},
		`has_share`=#{data.hasShare}
		where `id`=#{data.id}
	</update>
</mapper>