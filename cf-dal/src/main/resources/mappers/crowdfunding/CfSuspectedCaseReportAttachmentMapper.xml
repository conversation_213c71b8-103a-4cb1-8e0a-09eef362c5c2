<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfSuspectedCaseReportAttachmentDao">
	<sql id="tableName">
		cf_suspected_case_report_attachment
	</sql>

	<insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfSuspectedCaseReportAttachment">
		INSERT INTO <include refid="tableName"/>
		(`report_id`, `type`, `url`)
		VALUES
		<foreach collection="list" item="item" separator=",">
			(#{item.reportId}, #{item.type}, #{item.url})
		</foreach>
	</insert>

</mapper>