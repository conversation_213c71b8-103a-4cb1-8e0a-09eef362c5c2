<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfUniqIdClientIdRelDao">
	<sql id="tableName">
		cf_app_uniq_id_client_id_rel
	</sql>

	<sql id="fields">
		`id` as id,
		`app_uniq_id` as appUniqId,
		`user_id` as userId,
		`client_id` as clientId
	</sql>

	<insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfUniqIdClientIdRel">
		INSERT INTO <include refid="tableName"/>
		(`app_uniq_id`, `user_id`, `client_id`)
		VALUES
		(#{appUniqId}, #{userId}, #{clientId})
	</insert>

	<select id="findByAppUniqId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfUniqIdClientIdRel">
		SELECT <include refid="fields"/>
		FROM <include refid="tableName"/>
		WHERE `app_uniq_id`=#{appUniqId}
		AND `is_delete`=0
		ORDER BY `id` DESC
		LIMIT 1
	</select>


	<update id="deleteById">
		UPDATE <include refid="tableName"/>
		SET `is_delete`=1
		WHERE `id`=#{id}
	</update>

	<select id="findByUniqIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfUniqIdClientIdRel">
		SELECT <include refid="fields"/>
		FROM <include refid="tableName"/>
		WHERE `app_uniq_id` in
		<foreach collection="appUniqIds" item="uniqId" open="(" close=")" separator=",">
			#{uniqId}
		</foreach>
		AND `id` <![CDATA[ <= ]]> #{anchorId}
		AND `is_delete`=0
		LIMIT #{limit}
	</select>

</mapper>