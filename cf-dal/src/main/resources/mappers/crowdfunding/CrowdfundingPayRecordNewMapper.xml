<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
CREATE TABLE `crowdfunding_pay_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pay_uid` varchar(32) NOT NULL COMMENT '支付交易唯一号',
  `crowdfunding_order_id` int(11) NOT NULL COMMENT '众筹订单id',
  `pre_pay_amount` int(11) NOT NULL COMMENT '申请支付金额',
  `real_pay_amount` int(11) DEFAULT NULL COMMENT '微信回调支付成功金额',
  `pay_platform` varchar(16) NOT NULL COMMENT '支付平台',
  `pay_status` tinyint(3) NOT NULL COMMENT '支付结果状态:0待支付，1支付成功，2支付失败',
  `ctime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `callback_time` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '结果回调时间',
  `valid` tinyint(3) NOT NULL DEFAULT '1' COMMENT '是否有效 1：有效 0：无效',
  `last_modified` timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  PRIMARY KEY (`id`),
  KEY `idx_pay_uid` (`pay_uid`)
) ENGINE=InnoDB AUTO_INCREMENT=216308 DEFAULT CHARSET=utf8 COMMENT='众筹支付记录表';

增加退款表结构：

ALTER TABLE crowdfunding_pay_record
ADD COLUMN refund_status tinyint NOT NULL DEFAULT 0 COMMENT '退款状态 0:无退款 1:退款中 2:退款成功 3:退款失败',
ADD COLUMN refund_time timestamp NULL COMMENT '退款时间',
ADD COLUMN refund_amount int(11) DEFAULT NULL COMMENT '退款金额',
ADD COLUMN refund_reason varchar(300) DEFAULT NULL COMMENT '退款原因';

-->

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingPayRecordNewDao">
<!--    <sql id="TABLE">-->
<!--        crowdfunding_pay_record-->
<!--    </sql>-->
<!--    <sql id="FIELDS">-->
<!--        `id`,`pay_uid`,`crowdfunding_order_id`,`pre_pay_amount`,`real_pay_amount`,`pay_platform`,`pay_status`,`ctime`,`callback_time`,`valid`,-->
<!--        `refund_status`,`refund_time`,`refund_amount`,`refund_reason`-->
<!--    </sql>-->
<!--    <sql id="INSERT_FIELDS">-->
<!--        `pay_uid`,`crowdfunding_order_id`,`pre_pay_amount`,`real_pay_amount`,`pay_platform`,`pay_status`,`ctime`,`valid`-->
<!--    </sql>-->

<!--    <insert id="addPayRecord" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord">-->
<!--        INSERT INTO-->
<!--        <include refid="TABLE"/>-->
<!--        (<include refid="INSERT_FIELDS"/>)-->
<!--        VALUES(#{payUid},#{crowdfundingOrderId},#{prePayAmount},#{realPayAmount},#{payPlatform},#{payStatus},#{ctime},#{valid});-->
<!--    </insert>-->

<!--    <update id="updatePayStatus">-->
<!--        UPDATE-->
<!--        <include refid="TABLE"/>-->
<!--        SET `pay_status`=#{payStatus},`callback_time`=#{callbackTime},`real_pay_amount`=#{realPayAmount}-->
<!--        WHERE `pay_uid`=#{payUid} and `pay_status`in (0,2);-->
<!--    </update>-->

<!--	<update id="updateRefundStatus">-->
<!--		UPDATE-->
<!--		<include refid="TABLE"/>-->
<!--		SET `refund_status`=#{refundStatus},-->
<!--		`refund_time`=#{refundTime},-->
<!--		`refund_amount`=#{refundAmount},-->
<!--		`refund_reason`=#{refundReason}-->
<!--		WHERE `pay_uid`=#{payUid};-->
<!--	</update>-->


<!--    <update id="updateRefundStatusByOrderIds">-->
<!--        UPDATE-->
<!--        <include refid="TABLE"/>-->
<!--        SET `refund_status`=#{refundStatus}-->
<!--        WHERE crowdfunding_order_id in <foreach collection="orderIds" item="orderId" open="(" separator="," close=")" >#{orderId}</foreach>-->
<!--    </update>-->

</mapper>
