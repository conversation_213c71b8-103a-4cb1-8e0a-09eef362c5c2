<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfStatDataDao">

    <sql id="TABLE">
		cf_stat_data
    </sql>

    <sql id="SELECT_FIELDS">
        id as id,
        case_id as caseId,
        amount as amount,
        share_count as shareCount,
        donation_count as donationCount,
        refund_count as refundCount,
        refund_amount as refundmount,
        batch_id as batchId,
        finish_time as finishTime,
        case_create_time caseCreateTime
    </sql>


    <insert id="insertOrUpdate" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfStatData" keyProperty="id" useGeneratedKeys="true">

        insert into <include refid="TABLE"/>
        (
        case_id,
        <if test="amount != 0">
            amount,
        </if>
        <if test="shareCount != 0">
            share_count,
        </if>
        <if test="donationCount != 0">
            donation_count,
        </if>
        <if test="refundCount != 0">
            refund_count,
        </if>
        <if test="refundAmount != 0">
            refund_amount,
        </if>

        batch_id,
        case_create_time,
        finish_time
        )
        VALUES
        (
        #{caseId},
        <if test="amount != 0">
            #{amount} ,
        </if>
        <if test="shareCount != 0">
            #{shareCount},
        </if>
        <if test="donationCount != 0">
            #{donationCount},
        </if>
        <if test="refundCount != 0">
            #{refundCount},
        </if>
        <if test="refundAmount != 0">
            #{refundAmount},
        </if>

        #{batchId},
        #{caseCreateTime},
        #{finishTime}
        )
        ON DUPLICATE KEY UPDATE
        <trim suffixOverrides=",">

            <if test="amount != 0">
                amount =amount + #{amount},
            </if>
            <if test="shareCount != 0">
                share_count= share_count + #{shareCount},
            </if>
            <if test="donationCount != 0">
                donation_count=donation_count + #{donationCount},
            </if>
            <if test="refundCount != 0">
                refund_count=refund_count + #{refundCount},
            </if>
            <if test="refundAmount != 0">
                refund_amount=refund_amount + #{refundAmount},
            </if>

        </trim>

    </insert>


    <select id="getCfStatDataByUpdateTime" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfStatData">
        SELECT <include refid="SELECT_FIELDS"/>
        FROM <include refid="TABLE"/>
        WHERE batch_id = #{batchId} order by id
        LIMIT #{limit}
    </select>

    <select id="getCfStatDataById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfStatData">
        SELECT <include refid="SELECT_FIELDS"/>
        FROM <include refid="TABLE"/>
        WHERE batch_id = #{batchId} and id > #{id} order by id
        LIMIT #{limit}
    </select>


    <select id="getCfStatDataBy24H" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfStatData">
        SELECT <include refid="SELECT_FIELDS"/>
        FROM <include refid="TABLE"/>
        WHERE case_id=#{caseId} and (batch_id BETWEEN #{begin} and #{end} )
    </select>



    <select id="getCfStatDataBycaseId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfStatData">
        SELECT <include refid="SELECT_FIELDS"/>
        FROM <include refid="TABLE"/>
        WHERE case_id = #{caseId} and batch_id = #{batchId}
    </select>


    <select id="getCfStatDataByDate" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfStatData">
        SELECT <include refid="SELECT_FIELDS"/>
        FROM <include refid="TABLE"/>
        WHERE case_id in
        <foreach collection="caseIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
         and (batch_id BETWEEN #{begin} and #{end} )
    </select>

</mapper>