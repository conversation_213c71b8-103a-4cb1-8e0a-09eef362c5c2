<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingRecommendCaseDao">
    <sql id="tableName">
        crowdfunding_recommend_case
    </sql>



    <select id="getInfoIdList" resultType="java.lang.String">
        SELECT
        info_id as infoId
        FROM
        <include refid="tableName"/>
        WHERE
        `type`=#{type}
        AND
        `valid`=1
        and
        case_status = 0
        ORDER BY sort,id desc
        LIMIT #{size}, #{limit}
    </select>

    <update id="update">
        UPDATE
        <include refid="tableName"/>
        SET
        patient_name = #{patientName}
        WHERE
        info_id =#{infoId}
    </update>

    <select id="getListByPatientName" resultType="java.lang.String">
        SELECT
        info_id as infoId
        FROM
        <include refid="tableName"/>
        WHERE
        patient_name = #{patientName}
        AND
        `type`=#{type}
        AND
        `valid`=1
        and
        case_status = 0
        ORDER BY sort,id desc
    </select>

    <select id="getListByInfoIds" resultType="java.lang.String">
        SELECT
        info_id as infoId
        FROM
        <include refid="tableName"/>
        WHERE
        info_id in
        <foreach collection="infoIds" item="infoId" open="(" separator="," close=")">
            #{infoId}
        </foreach>
        and
        `type`=1
        AND
        valid=1
        and
        case_status = 0
    </select>

</mapper>