<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfBlessingCardOrderDao">

    <sql id="TABLE">
        cf_blessing_card_order
    </sql>

    <insert id="insert">
        INSERT INTO
        <include refid="TABLE"/>
        (order_id,blessing_card_id,anonymous_valid)
        VALUES
        (#{orderId},#{blessingCardId},#{anonymousValid})
    </insert>

    <select id="getCfBlessingCardOrder" resultType="com.shuidihuzhu.cf.model.CfBlessingCardOrder">
        SELECT
        id ,order_id as orderId,blessing_card_id as blessingCardId,anonymous_valid as anonymousValid
        FROM
        <include refid="TABLE"/>
        WHERE
        order_id in
        <foreach collection="orderIds" item="orderId" separator="," open="(" close=")">
            #{orderId}
        </foreach>
        and is_delete = 0
    </select>
    <select id="getByOrderId" resultType="com.shuidihuzhu.cf.model.CfBlessingCardOrder">
        SELECT id,
        order_id as orderId,blessing_card_id as blessingCardId,anonymous_valid as anonymousValid
        FROM
        <include refid="TABLE"/>
        WHERE
        order_id = #{orderId}
        and is_delete = 0
    </select>

    <update id="updateValidById">
        update  <include refid="TABLE"/>
        set anonymous_valid = 1
        where id = #{id}
        and is_delete = 0
    </update>

</mapper>
