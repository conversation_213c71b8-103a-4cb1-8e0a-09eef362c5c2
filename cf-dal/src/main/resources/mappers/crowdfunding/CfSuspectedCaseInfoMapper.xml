<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfSuspectedCaseInfoDao">
    <sql id="tableName">
		cf_suspected_case_info
	</sql>

    <sql id="fields">
		`id` as id,
		`nickname` as nickname,
		`encrypt_mobile` as encryptMobile,
		`title` as title,
		`info_uuid` as infoUuid,
		`reason` as reason,
		`faithless_date` as faithlessDate,
		`id_number` as idNumber
	</sql>

    <sql id="insert">
		`nickname`,`title`,`info_uuid`,`reason`,`faithless_date`,`id_number`,encrypt_mobile
	</sql>

    <select id="getList" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfSuspectedCaseInfo">
        SELECT
        <include refid="fields"/>
        FROM
        <include refid="tableName"/>
        WHERE `is_delete`=0
        ORDER BY `id` DESC
        LIMIT #{limit}, #{size}
    </select>

    <sql id="SEARCH_WHERE_IGNORE_DEL">
        `is_delete` = 0
        <if test="nickname != '' and nickname != null">and nickname like concat('%',#{nickname},'%')</if>
        <if test="encryptMobile != '' and encryptMobile != null ">and encrypt_mobile like concat('%',#{encryptMobile},'%')</if>
        <if test="title != '' and title != null ">and title  like concat('%',#{title},'%')</if>
        <if test="infoUuid != ''and  infoUuid != null ">and info_uuid like concat('%',#{infoUuid},'%')</if>
        <if test="start != null and end != null">
          and `faithless_date`  between #{start} and #{end}
        </if>
    </sql>

    <select id="getListBySerch" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfSuspectedCaseInfo">
        SELECT
        <include refid="fields"/>
        FROM
        <include refid="tableName"/>
        WHERE
        <include refid="SEARCH_WHERE_IGNORE_DEL"/>
        ORDER BY `faithless_date` DESC
        LIMIT #{limit}, #{size}
    </select>

    <select id="getById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfSuspectedCaseInfo">
        select
        <include refid="fields"/>
        from
        <include refid="tableName"/>
        where
        `id` = #{id}
        and
        `is_delete` = 0
    </select>

    <select id="getAllAndIdNumNotEmpty" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfSuspectedCaseInfo">
        select
        <include refid="fields"/>
        from
        <include refid="tableName"/>
        where
        `id_number` != ''
        and
        `is_delete` = 0
    </select>

    <update id="update" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfSuspectedCaseInfo">
        update
        <include refid="tableName"/>
        <set>
            <if test="nickname != '' and nickname != null">
                `nickname` = #{nickname},
            </if>

            <if test="encryptMobile != '' and encryptMobile != null">
                `encrypt_mobile` = #{encryptMobile},
            </if>
            <if test="title != '' and title != null">
                `title` = #{title},
            </if>
            <if test="idNumber != '' and idNumber != null">
                `id_number` = #{idNumber},
            </if>
            <if test="reason != '' and reason != null">
                `reason` = #{reason},
            </if>
            <if test="faithlessDate != null">
                `faithless_date` = #{faithlessDate},
            </if>
            <if test="infoUuid != '' and infoUuid != null">
                `info_uuid` = #{infoUuid},
            </if>
            `is_delete` = 0
        </set>
        where
        `id` = #{id}
        and
        `is_delete` = 0
    </update>

    <update id="delete">
        update
        <include refid="tableName"/>
        set
        `is_delete` = 1
        where
        `id` = #{id}
    </update>

    <insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfSuspectedCaseInfo">
        insert into
        <include refid="tableName"/>
        (<include refid="insert"/>)
        values
        (#{nickname}, #{title},#{infoUuid}, #{reason},#{faithlessDate}, #{idNumber},#{encryptMobile})
    </insert>

    <select id="total" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM
        <include refid="tableName"/>
        WHERE `is_delete`=0
    </select>

</mapper>