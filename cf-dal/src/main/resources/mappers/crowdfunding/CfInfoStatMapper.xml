<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfInfoStatDao">

	<sql id="TABLE">
        cf_info_stat
    </sql>

	<sql id="FIELDS">
		id as id,
		share_count as shareCount,
		donation_count as donationCount,
		verify_user_count as verifyUserCount,
		comment_count as commentCount,
		amount as amount,
		verify_friend_count as verifyFriendCount,
		verify_hospital_count as verifyHospitalCount,
		blessing_count as blessingCount,
		refund_count as refundCount,
		donator_count as donatorCount
	</sql>

	<insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoStat" useGeneratedKeys="true" keyProperty="id">
		replace INTO
		<include refid="TABLE"/>
		(`id`,`share_count`,`donation_count`,`verify_user_count`,`comment_count`,`amount`,`verify_friend_count`,`verify_hospital_count`)
		VALUES
		(#{id},#{shareCount},#{donationCount},#{verifyUserCount},#{commentCount},#{amount},#{verifyFriendCount},#{verifyHospitalCount})
	</insert>

	<update id="incShareCount">
		UPDATE
		<include refid="TABLE"/>
		SET `share_count`= `share_count` + 1
		WHERE `id`=#{id}
	</update>

	<update id="incDonationCount">
		UPDATE
		<include refid="TABLE"/>
		SET `donation_count`= `donation_count` + 1
		WHERE `id`=#{id}
	</update>

	<update id="incVerifyUserCount">
		UPDATE
		<include refid="TABLE"/>
		SET `verify_user_count`= `verify_user_count` + 1
		WHERE `id`=#{id}
	</update>

	<update id="incCommentCount">
		UPDATE
		<include refid="TABLE"/>
		SET `comment_count`= `comment_count` + 1
		WHERE `id`=#{id}
	</update>

	<update id="incVerifyHospitalCount">
		UPDATE
		<include refid="TABLE"/>
		SET `verify_hospital_count`= `verify_hospital_count` + 1
		WHERE `id`=#{id}
	</update>

	<update id="incVerifyFriendCount">
		UPDATE
		<include refid="TABLE"/>
		SET `verify_friend_count`= `verify_friend_count` + 1
		WHERE `id`=#{id}
	</update>

	<update id="incBlessingCount">
		UPDATE
		<include refid="TABLE"/>
		SET `blessing_count`= `blessing_count` + 1
		WHERE `id`=#{id}
	</update>


	<!-- 减 -->

	<update id="discShareCount">
		UPDATE
		<include refid="TABLE"/>
		SET `share_count`= `share_count` - 1
		WHERE `id`=#{id}
	</update>

	<update id="discDonationCount">
		UPDATE
		<include refid="TABLE"/>
		SET `donation_count`= `donation_count` - 1
		WHERE `id`=#{id}
	</update>

	<update id="discVerifyUserCount">
		UPDATE
		<include refid="TABLE"/>
		SET `verify_user_count`= `verify_user_count` - 1
		WHERE `id`=#{id}
	</update>

	<update id="discCommentCount">
		UPDATE
		<include refid="TABLE"/>
		SET `comment_count`= `comment_count` - 1
		WHERE `id`=#{id}
	</update>

	<update id="discVerifyHospitalCount">
		UPDATE
		<include refid="TABLE"/>
		SET `verify_hospital_count`= `verify_hospital_count` - 1
		WHERE `id`=#{id}
	</update>

	<update id="discVerifyFriendCount">
		UPDATE
		<include refid="TABLE"/>
		SET `verify_friend_count`= `verify_friend_count` - 1
		WHERE `id`=#{id}
	</update>

	<update id="discBlessingCount">
		UPDATE
		<include refid="TABLE"/>
		SET `blessing_count`= `blessing_count` - 1
		WHERE `id`=#{id}
	</update>

	<update id="addAmount">
		UPDATE
		<include refid="TABLE"/>
		SET `amount`= `amount` + #{amount},`donation_count`= `donation_count` + 1
		WHERE `id`=#{id}
	</update>

	<update id="subtractAmount">
		UPDATE
		<include refid="TABLE"/>
		SET `amount`= `amount` - #{amount}
		WHERE `id`=#{id}
	</update>
	<update id="incRefundCount">
		UPDATE
		<include refid="TABLE"/>
		SET `refund_count`= `refund_count` + 1
		WHERE `id`=#{id}
	</update>
	<update id="updateDonatorCount">
		UPDATE
		<include refid="TABLE"/>
		SET `donator_count`= #{donatorCount}
		WHERE `id`=#{id}
	</update>
    <update id="incDonatorCount">
		UPDATE
		<include refid="TABLE"/>
		SET `donator_count`= `donator_count` + 1
		WHERE `id`=#{id}
	</update>
	<update id="decDonatorCount">
		UPDATE
		<include refid="TABLE"/>
		SET `donator_count`= `donator_count` - 1
		WHERE `id`=#{id}
	</update>

	<update id="updateRefundCount">
		UPDATE
		<include refid="TABLE"/>
		SET `refund_count`= #{refundCount}
		WHERE `id`=#{id}
	</update>

	<select id="getById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoStat">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE
		`id` = #{id}
		LIMIT 1
	</select>

	<select id="listByIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoStat">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE
		id in
		<foreach item="item" index="index" collection="ids"
		         open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

</mapper>
