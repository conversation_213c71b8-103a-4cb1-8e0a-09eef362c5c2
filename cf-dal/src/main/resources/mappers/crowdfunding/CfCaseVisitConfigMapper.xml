<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfCaseVisitConfigDao">
	<sql id="TABLE">
		cf_case_visit_config
	</sql>

	<sql id="FIELDS">
		`show_banner` as showBanner,
		`banner_img_url` as bannerImgUrl,
		`banner_url` as bannerUrl,
		`banner_text` as bannerText,
		`start_time` as startTime,
		`end_time` as endTime,
		`sharable` as sharable,
		`can_donate` as canDonate,
		`can_show` as canShow,
		`show_popup` as showPopup,
		`popup_text` as popupText,
		`popup_title` as popupTitle,
		`hidden` as hidden,
		`hidden_title` as hiddenTitle,
		`force_stop` as forceStop,
		`abnormal_hidden` as abnormalHidden,
		`abnormal_hidden_self_title` as abnormalHiddenSelfTitle,
		`abnormal_hidden_other_title` as abnormalHiddenOtherTitle
	</sql>

	<select id="getByCaseId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfCaseVisitConfig">
		SELECT <include refid="FIELDS"/>
		FROM <include refid="TABLE"/>
		WHERE `case_id`=#{caseId}
		AND `is_delete`=0
	</select>

	<select id="getByCaseIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfCaseVisitConfig">
		SELECT <include refid="FIELDS"/>
		FROM <include refid="TABLE"/>
		WHERE `case_id`
		IN
		<foreach collection="list" item="item" open="(" close=")" separator=",">
			#{item}
		</foreach>
		AND `is_delete`=0
	</select>

	<update id="updateShareAndDonate">
		UPDATE <include refid="TABLE"/>
		SET `sharable`=#{canShare},
		`can_donate`=#{canDonate}
		WHERE `case_id`=#{caseId}
	</update>

	<update id="updateHiddenAndHiddenTitle">
		UPDATE <include refid="TABLE"/>
		<set>
			`hidden`=#{hidden},
			<if test="hiddenTitle != null">
				`hidden_title`=#{hiddenTitle}
			</if>
		</set>
		WHERE `case_id`=#{caseId}
	</update>

	<update id="updateForceStop">
		UPDATE <include refid="TABLE"/>
		<set>
			`force_stop`=#{forceStop}
		</set>
		WHERE `case_id`=#{caseId}
	</update>

	<insert id="insertShareAndDonate">
		INSERT INTO <include refid="TABLE"/>
		(`case_id`, `sharable`, `can_donate`)
		VALUES
		(#{caseId}, #{canShare}, #{canDonate})
	</insert>
	<insert id="insertHiddenAndHiddenTitle">
		INSERT INTO <include refid="TABLE"/>
		(`case_id`, `sharable`, `can_donate`,`hidden`,`hidden_title`)
		VALUES
		(#{caseId}, #{canShare}, #{canDonate},#{hidden},#{hiddenTitle})
	</insert>
</mapper>