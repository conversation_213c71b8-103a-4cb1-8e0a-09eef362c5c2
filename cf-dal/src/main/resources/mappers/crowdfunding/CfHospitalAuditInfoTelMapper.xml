<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfHospitalAuditInfoTelDao">
    <sql id="tableName">
        `cf_info_hospital_audit_tel`
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfHospitalAuditInfoTel">
        INSERT INTO <include refid="tableName" />
            (`cf_hospital_audit_info_id`,`area_code`,`tel_num`,`ext_num`)
        VALUES
        <foreach collection="cfHospitalAuditInfoTels" item="item"  separator=",">
            (#{item.cfHospitalAuditInfoId}, #{item.areaCode}, #{item.telNum}, #{item.extNum})
        </foreach>
    </insert>

    <update id="deleteByCfHospitalAuditInfoId">
        update <include refid="tableName" />
        set is_delete = 1
        where cf_hospital_audit_info_id = #{cfHospitalAuditInfoId} and is_delete = 0
    </update>

    <update id="deleteByCfHospitalAuditInfoIds">
        update <include refid="tableName" />
        set is_delete = 1
        where
        cf_hospital_audit_info_id in
        <foreach collection="cfHospitalAuditInfoIds" item="cfHospitalAuditInfoId" separator="," open="(" close=")">
            #{cfHospitalAuditInfoId}
        </foreach>
        and is_delete = 0
    </update>

    <select id="selectByCfHospitalAuditInfoId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfHospitalAuditInfoTel">
        select *
        from <include refid="tableName" />
        where cf_hospital_audit_info_id = #{cfHospitalAuditInfoId} and is_delete = 0
    </select>

</mapper>