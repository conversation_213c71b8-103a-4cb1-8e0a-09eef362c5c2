<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfCommonStoreDao">


	<insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfCommonStoreModel">

        insert into cf_common_store (user_id,store_key,store_value,store_type, update_time)
        values(
          #{userId},
          #{storeKey},
          #{storeValue},
          #{storeType},
          #{updateTime}
        )
         ON DUPLICATE KEY UPDATE
         store_value = #{storeValue},
         is_delete = 0,
         update_time = #{updateTime}


    </insert>

    <select id="getByUserIdAndKey" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfCommonStoreModel">
        select store_key,store_value, update_time
        from  cf_common_store
        where user_id= #{userId} and store_key=#{storeKey} and is_delete = 0
    </select>


    <select id="getByKeys" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfCommonStoreModel">
        select store_key,store_value, update_time
        from  cf_common_store
        where user_id= #{userId} and store_key in
        <foreach collection="storeKeys" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and is_delete = 0
    </select>


    <update id="deleteUserKeyByType">

        update cf_common_store set is_delete=1 where user_id=#{userId} and store_type=#{storeType}

    </update>

</mapper>