<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingCityStreetDao">

    <sql id="TABLE">
        crowdfunding_city_street
    </sql>

    <select id="selectByAreaCode" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCity">
        SELECT *
        FROM <include refid="TABLE" />
        where old_area_code = #{areaCode}
    </select>

</mapper>