<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.casematerial.CfQuestionsAnswersDao">

    <sql id="table_name">
        `cf_questions_answers`
    </sql>

    <sql id="select_fields">
        `id`,
        `questions`,
        `answers`,
        `sort`
    </sql>

    <select id="getByIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.casematerial.CfQuestionsAnswers">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE `is_delete`=0 AND `id` IN
        <foreach collection="idSet" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>
