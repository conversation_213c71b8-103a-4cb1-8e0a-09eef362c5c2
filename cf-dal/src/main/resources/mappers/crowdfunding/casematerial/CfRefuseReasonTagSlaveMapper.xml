<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.casematerial.CfRefuseReasonTagSlaveDao">

    <sql id="table_name">
        `cf_refuse_reason_tag`
    </sql>


    <select id="selectAll" resultType="com.shuidihuzhu.cf.model.crowdfunding.casematerial.CfRefuseReasonTagSlave">
        SELECT *
        FROM
        <include refid="table_name"/>
        WHERE `describe_c` != ''
    </select>

    <select id="selectByType" resultType="com.shuidihuzhu.cf.model.crowdfunding.casematerial.CfRefuseReasonTagSlave">
        SELECT *
        FROM
        <include refid="table_name"/>
        WHERE `describe_c` != ''
        and `data_type` =#{type}
    </select>

</mapper>

