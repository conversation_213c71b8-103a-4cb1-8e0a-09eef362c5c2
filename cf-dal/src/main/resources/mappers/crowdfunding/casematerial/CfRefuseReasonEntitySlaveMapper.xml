<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.casematerial.CfRefuseReasonEntitySlaveDao">

    <sql id="table_name">
        `cf_refuse_reason_entity`
    </sql>

    <sql id="select_fields">
        `id`,
        `content`,
        `tag_id`,
        `item_ids`,
        `frequency`
    </sql>

    <select id="selectByReasonIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.casematerial.CfRefuseReasonEntitySlave">
        SELECT
        <include refid="select_fields"/>
        FROM
        <include refid="table_name"/>
        WHERE `is_delete`=0 AND `id` IN
        <foreach collection="set" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>
