<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.ApplyForFeeFreeDao">
	<sql id="TABLE">
		no_handling_fee_offline_application_record
	</sql>

	<sql id="INSERT_FIELDS">
		`fill_phone`, `case_id`, `case_initiated_time_difference`, `amount_raised`,
		`organizational_relationships`, `case_create_time`
	</sql>

	<insert id="add">
		insert into
		<include refid="TABLE"/>
		(<include refid="INSERT_FIELDS"/>)
		values (#{fillPhone}, #{caseId}, #{caseInitiatedTimeDifference}, #{amountRaised}, #{organizationalRelationships},
		#{caseCreateTime})
	</insert>

</mapper>