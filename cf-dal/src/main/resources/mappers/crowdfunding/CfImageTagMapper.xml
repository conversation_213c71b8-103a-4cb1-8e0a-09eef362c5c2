<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfImageTagDao">

    <sql id="TABLE">
        cf_image_tag_record
    </sql>

    <sql id="Base_Field">
        `case_id`,
        `image_type`,
        `image_id`
    </sql>

    <insert id="saveImageTag" parameterType="com.shuidihuzhu.client.cf.api.model.CfImageTag">
        INSERT INTO <include refid="TABLE"/>
        (<include refid="Base_Field"/>)
        VALUES
        (#{caseId}, #{imageType}, #{imageId})
    </insert>

    <select id="getImageTag" resultType="com.shuidihuzhu.client.cf.api.model.CfImageTag">
        select <include refid="Base_Field"/>
        from <include refid="TABLE"/>
        where `image_id` = #{imageId}
        and `is_delete` = 0
        limit 1
    </select>

    <select id="getImageTags" resultType="com.shuidihuzhu.client.cf.api.model.CfImageTag">
        SELECT <include refid="Base_Field" />
        FROM <include refid="TABLE" />
        WHERE image_id in
        <foreach collection="imageIds" item="imageId" open="(" separator="," close=")">
            #{imageId}
        </foreach>
        and `is_delete` = 0
    </select>

    <select id="getImageTagsByCaseId" resultType="com.shuidihuzhu.client.cf.api.model.CfImageTag">
        SELECT <include refid="Base_Field" />
        FROM <include refid="TABLE" />
        WHERE case_id = #{caseId}
        and `is_delete` = 0
    </select>

</mapper>
