<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfDiseaseNormDao">
    <sql id="selectFields">
        `id` as id,
        `case_id` as caseId,
        `info_uuid` as infoUuid,
        `disease_name` as diseaseName,
        `disease_norm` as diseaseNorm,
        `disease_norm_json` as diseaseNormJson
    </sql>
    <sql id="tableName">
        `cf_disease_norm`
    </sql>

    <insert id="insert">
        INSERT ignore INTO
        <include refid="tableName"/>
        (case_id,info_uuid,disease_name,disease_norm,disease_norm_json)
        VALUES
        (#{caseId},#{infoUuid},#{diseaseName},#{diseaseNorm},#{diseaseNormJson})
    </insert>

    <update id="update">
        UPDATE
        <include refid="tableName"/>
        SET disease_name=#{diseaseName}, disease_norm=#{diseaseNorm}, disease_norm_json=#{diseaseNormJson}
        WHERE case_id = #{caseId}
    </update>

    <select id="selectCount" resultType="java.lang.Integer">
        SELECT count(*)
        FROM
        <include refid="tableName"/>
        WHERE
        case_id = #{caseId}
    </select>

    <select id="selectByCaseId" resultType="com.shuidihuzhu.cf.model.crowdfunding.DiseaseLabel">
        SELECT <include refid="selectFields"/>
        FROM
        <include refid="tableName"/>
        WHERE
        case_id = #{caseId}
    </select>

    <select id="getByCaseId" resultType="com.shuidihuzhu.cf.model.disease.DiseaseNorm">
        SELECT <include refid="selectFields"/>
        FROM
        <include refid="tableName"/>
        WHERE
        case_id = #{caseId}
    </select>

    <select id="selectByCaseIds" resultType="com.shuidihuzhu.cf.model.disease.DiseaseNorm">
        SELECT
        <include refid="selectFields"/>
        FROM
        <include refid="tableName"/>
        WHERE
        case_id in
        <foreach collection="caseIds" item="caseId" open="(" separator="," close=")">
            #{caseId}
        </foreach>
    </select>

</mapper>