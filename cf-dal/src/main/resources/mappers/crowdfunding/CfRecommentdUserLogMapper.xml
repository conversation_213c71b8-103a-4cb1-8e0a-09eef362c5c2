<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfRecommentdUserLogDao">
	<resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.model.crowdfunding.CfRecommentdUserLog">
		<constructor>
			<idArg column="id" javaType="java.lang.Long" jdbcType="BIGINT"/>
			<arg column="from_info_id" javaType="java.lang.String" jdbcType="VARCHAR"/>
			<arg column="info_id" javaType="java.lang.String" jdbcType="VARCHAR"/>
			<arg column="is_pay" javaType="java.lang.Boolean" jdbcType="BIT"/>
			<arg column="user_id" javaType="java.lang.Long" jdbcType="BIGINT"/>
			<arg column="self_tag" javaType="java.lang.String" jdbcType="VARCHAR"/>
			<arg column="create_time" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
			<arg column="update_time" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
			<arg column="valid" javaType="java.lang.Boolean" jdbcType="BIT"/>
		</constructor>
	</resultMap>
	<sql id="Base_Column_List">
    id, from_info_id, info_id, is_pay, user_id, self_tag, create_time, update_time, valid
  </sql>
	<select id="selectByUniqUnionKey" resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List"/>
		from cf_recommentd_user_log
		where self_tag = #{selfTag} and from_info_id = #{fromInfoId} AND info_id = #{infoId} and valid = 1
		limit 1
	</select>

	<insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfRecommentdUserLog">
		<selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
			SELECT LAST_INSERT_ID()
		</selectKey>
		insert into cf_recommentd_user_log (from_info_id, info_id, is_pay,
		user_id, self_tag)
		values (#{fromInfoId,jdbcType=VARCHAR}, #{infoId,jdbcType=VARCHAR}, #{isPay,jdbcType=BIT},
		#{userId,jdbcType=BIGINT}, #{selfTag,jdbcType=VARCHAR})
	</insert>

	<update id="updateByUniqUnionKey" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfRecommentdUserLog">
    update cf_recommentd_user_log
    set
      is_pay = 1,
      user_id = #{userId,jdbcType=BIGINT}
    where self_tag = #{selfTag} and from_info_id = #{fromInfoId} AND info_id = #{infoId} and valid = 1
  </update>
</mapper>
