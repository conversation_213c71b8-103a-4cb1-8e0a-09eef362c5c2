<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.IOssPictureAttrDAO">

    <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.model.crowdfunding.OssPictureAttrDO">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="atta_id" property="attaId" jdbcType="INTEGER"/>
        <result column="case_id" property="caseId" jdbcType="INTEGER"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="format" property="format" jdbcType="VARCHAR"/>
        <result column="maker" property="maker" jdbcType="VARCHAR"/>
        <result column="model" property="model" jdbcType="VARCHAR"/>
        <result column="shoot_time" property="shootTime" jdbcType="VARCHAR"/>
        <result column="modify_time" property="modifyTime" jdbcType="VARCHAR"/>
        <result column="width" property="width" jdbcType="INTEGER"/>
        <result column="height" property="height" jdbcType="INTEGER"/>
        <result column="x_dimension" property="xDimension" jdbcType="INTEGER"/>
        <result column="y_dimension" property="yDimension" jdbcType="INTEGER"/>
        <result column="lat" property="lat" jdbcType="VARCHAR"/>
        <result column="lat_ref" property="latRef" jdbcType="VARCHAR"/>
        <result column="lng" property="lng" jdbcType="VARCHAR"/>
        <result column="lng_ref" property="lngRef" jdbcType="VARCHAR"/>
        <result column="adobe" property="adobe" jdbcType="INTEGER"/>
        <result column="water_mark" property="waterMark" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="TABLE_NAME">
        cf_picture_risk_attr
    </sql>

    <sql id="INSERT_FIELD">
        atta_id,case_id,`type`,format,maker,model,shoot_time,modify_time,width,height,x_dimension,y_dimension,lat,lat_ref,lng,lng_ref,adobe
    </sql>

    <sql id="SELECT_FIELD">
        atta_id,case_id,`type`,format,maker,model,shoot_time,modify_time,width,height,x_dimension,y_dimension,
        lat,lat_ref,lng,lng_ref,adobe,water_mark,ai_ps_prob,ai_ps_software,ai_time,new_ai_ps
    </sql>

    <insert id="batchInsert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.OssPictureAttrDO" useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="TABLE_NAME"/>
        (<include refid="INSERT_FIELD"/>)
        values
        <foreach collection="attrDOS" item="it" separator=",">
            (#{it.attaId},#{it.caseId},#{it.type},#{it.format},#{it.maker},#{it.model},#{it.shootTime},#{it.modifyTime},
            #{it.width},#{it.height},#{it.xDimension},#{it.yDimension},#{it.lat},#{it.latRef},#{it.lng},#{it.lngRef},#{it.adobe})
        </foreach>
        on duplicate key update
        atta_id=values(atta_id),case_id=values(case_id),`type`=values(`type`),format=values(format),maker=values(maker),model=values(model),shoot_time=values(shoot_time),modify_time=values(modify_time),
        width=values(width),height=values(height),x_dimension=values(x_dimension),y_dimension=values(y_dimension),lat=values(lat),lat_ref=values(lat_ref),lng=values(lng),lng_ref=values(lng_ref),adobe=values(adobe)
    </insert>

    <update id="updateWaterMark">
        update <include refid="TABLE_NAME"/> set water_mark = #{waterMark} where atta_id = #{id}
    </update>

    <update id="updateAiResultByModel">
        update <include refid="TABLE_NAME"/> set ai_ps_prob = #{aiPsProb}, ai_ps_software = #{aiPsSoftware}, ai_time = #{aiTime}, new_ai_ps = #{newAiPs} where atta_id = #{attachmentId}
    </update>

    <update id="updateAiResult">
        update <include refid="TABLE_NAME"/> set ai_ps_prob = #{aiPsProb}, ai_ps_software = #{aiPsSoftware}, ai_time = #{aiTime} where atta_id = #{id}
    </update>

    <select id="getByAttachmentIds" resultMap="BaseResultMap">
        select <include refid="SELECT_FIELD"/> from <include refid="TABLE_NAME"/>
        where atta_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>