<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.AdminBlessingCardDao">

    <sql id="TABLE">
        case_details_blessing_card
    </sql>

    <sql id="Base_Field">
        `id` as id,
        `blessing_card_image` as blessingCardImage,
        `blessing_card_text` as blessingCardText,
        `blessing_card_amount` as blessingCardAmount
    </sql>

    <select id="getList" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.AdminBlessingCardVo">
        SELECT
        <include refid="Base_Field"/>
        FROM
        <include refid="TABLE"/>
        WHERE
        is_delete = 0
    </select>

</mapper>
