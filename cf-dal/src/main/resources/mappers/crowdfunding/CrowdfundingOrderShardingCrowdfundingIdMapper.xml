<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingOrderShardingCrowdfundingIdDao">
	<sql id="TABLE">
        crowdfunding_order_crowdfundingid_sharding
    </sql>
	<sql id="SHARDING_TABLE">
        crowdfunding_order_crowdfundingid_sharding_${sharding}
    </sql>
	<sql id="FIELDS">
        `id`,`code`,`user_id`,`crowdfunding_id`,`amount`,`comment`,`pay_status`,
        `ctime`,`pay_time`,`valid`,`ip`,`os_type`,`channel`,`from`,`self_tag`,`user_third_id`,
		`user_third_type`,`anonymous`,`activity_id`,`single_refund_flag`
    </sql>
	<sql id="INSERT_FIELDS">
        `id`,`code`,`user_id`,`crowdfunding_id`,`amount`,`comment`,`pay_status`,`ctime`,
        `valid`,`ip`,`os_type`,`channel`,`from`,`self_tag`,`user_third_id`,`user_third_type`,`anonymous`
    </sql>

	<select id="getOrderByPage" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
			<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE `crowdfunding_id`=#{crowdfundingId}
		AND `pay_status`=1
		AND `valid`=1
		AND `user_id` IS NOT NULL
		LIMIT #{offset}, #{limit};
	</select>
	<select id="getByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		FORCE index(idx_cfid_userid)
		WHERE `crowdfunding_id`=#{crowdfundingId}
		AND `user_id` IN
		<foreach collection="userIds" item="userId" open="(" close=")" separator=",">
			#{userId}
		</foreach>
		AND `pay_status`=1
		AND `valid`=1
		ORDER BY `id` DESC
	</select>
	<select id="getOrderNoAnonymous" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		FORCE index(idx_cfid_userid)
		WHERE `crowdfunding_id`=#{crowdfundingId}
		AND `user_id` IN
		<foreach collection="userIds" item="userId" open="(" close=")" separator=",">
			#{userId}
		</foreach>
		AND `pay_status`=1
		AND `valid`=1
		AND anonymous = 0
		ORDER BY `id` DESC
	</select>
	<select id="getNoAnonymousByAnchorId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		FORCE index(idx_cfid_id)
		WHERE `crowdfunding_id`=#{crowdfundingId}
		<if test="anchorId != null">
			AND `id` &lt; #{anchorId}
		</if>
		and anonymous = 0
		AND `pay_status`=1
		AND `valid`=1
		ORDER BY `id` DESC
		LIMIT #{limit};
	</select>
	<select id="getByAnchorId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		FORCE index(idx_cfid_id)
		WHERE `crowdfunding_id`=#{crowdfundingId}
		<if test="anchorId != null">
			AND `id` &lt; #{anchorId}
		</if>
		AND `pay_status`=1
		AND `valid`=1
		ORDER BY `id` DESC
		LIMIT #{limit};
	</select>
	<select id="getAllByAnchorIdAndUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		FORCE index(idx_cfid_userid)
		WHERE `crowdfunding_id`=#{crowdfundingId}
		AND `user_id` IN
		<foreach collection="userIds" item="userId" open="(" close=")" separator=",">
			#{userId}
		</foreach>
		AND `id` &lt; #{anchorId}
		AND `amount`>=#{amount}
		AND `pay_status`=1
		AND `valid`=1
		ORDER BY `id` DESC
		LIMIT #{limit};
	</select>

	<select id="getByAnchorIdAndTime" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE `crowdfunding_id`=#{crowdfundingId}
		AND `id` &lt; #{anchorId}
		AND `pay_status`=1
		AND `valid`=1
		<if test="startTime!=null and startTime!=''">
			<![CDATA[ AND `pay_time` >= #{startTime} ]]>
		</if>
		<if test="endTime!=null and endTime!=''">
			<![CDATA[ AND `pay_time` < #{endTime} ]]>
		</if>
		ORDER BY `id` DESC
		LIMIT #{limit};
	</select>
	<select id="getUserOrderCount" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrderCount">
        SELECT
        sum(case when id &lt;= #{orderId} then 1 else 0 end) as sequence,
        count(1) as countNum
        FROM <include refid="TABLE"/>
        WHERE crowdfunding_id = #{crowdfundingId}
        	and pay_status = 1
        	and valid =1
	</select>
	<select id="getOrderCountByTime" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrderCount">
		SELECT sum(amount) as amount, count(*) as countNum
		FROM <include refid="TABLE"/>
		WHERE crowdfunding_id = #{crowdfundingId}
			AND <![CDATA[ `pay_time` >= #{startTime} AND `pay_time` < #{endTime} ]]>
	</select>

	<select id="getListByUserIdAndAnchorId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT <include refid="FIELDS"/>
		FROM <include refid="TABLE"/>
		WHERE crowdfunding_id = #{crowdfundingId}
		AND <![CDATA[ id <= #{anchorId}]]>
		and user_id = #{userId}
		and pay_status = 1
		and valid = 1
		order by id desc
		limit #{limit}
	</select>
	<select id="getOrderListByCrowdfundingIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE crowdfunding_id in
		<foreach collection="infoIds" item="infoId" open="(" separator="," close=")">
			#{infoId}
		</foreach>
		and user_id = #{userId}
		AND <![CDATA[ id <= #{anchorId}]]>
		AND valid = 1
		AND pay_status = 1
		order by id desc
		limit #{limit}
	</select>
	<select id="getOrderListByCrowdfundingIdsV2" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE crowdfunding_id in
		<foreach collection="infoIds" item="infoId" open="(" separator="," close=")">
			#{infoId}
		</foreach>
		and user_id = #{userId}
		AND <![CDATA[ id <= #{anchorId}]]>
		AND pay_status = 1
		order by id desc
		limit #{limit}
	</select>
	<select id="getOrderListByCrowdfundingIdsV2WithSuffixTableName" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="SHARDING_TABLE"/>
		WHERE crowdfunding_id in
		<foreach collection="infoIds" item="infoId" open="(" separator="," close=")">
			#{infoId}
		</foreach>
		and user_id = #{userId}
		AND <![CDATA[ id <= #{anchorId}]]>
		AND pay_status = 1
		order by id desc
	</select>
	<select id="getOrderCountByCrowdfundingIds" resultType="java.lang.Integer">
		SELECT count(1)
		FROM
		<include refid="TABLE"/>
		WHERE crowdfunding_id in
		<foreach collection="infoIds" item="infoId" open="(" separator="," close=")">
			#{infoId}
		</foreach>
		and user_id = #{userId}
		AND valid=1
		AND pay_status=1
	</select>
	<select id="getOrderCountByCrowdfundingIdsWithSuffixTableName" resultType="java.lang.Integer">
		SELECT count(1)
		FROM
		<include refid="SHARDING_TABLE"/>
		WHERE crowdfunding_id in
		<foreach collection="infoIds" item="infoId" open="(" separator="," close=")">
			#{infoId}
		</foreach>
		and user_id = #{userId}
		AND valid=1
		AND pay_status=1
	</select>
	<select id="getLatestOrderOfCrowdfundingIdsAndUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE crowdfunding_id in
		<foreach collection="infoIds" item="infoId" open="(" separator="," close=")">
			#{infoId}
		</foreach>
		and user_id = #{userId}
		AND valid = 1
		AND pay_status = 1
		order by id desc
		limit 1
	</select>
	<select id="getListByids" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE crowdfunding_id in
		<foreach collection="infoIds" item="infoId" open="(" separator="," close=")">
			#{infoId}
		</foreach>
		and `id` in <foreach collection="ids" item="id" open="(" separator="," close=")" >#{id}</foreach>
		<if test="valid!=null">
			and valid= #{valid}
		</if>
		<if test="payStatus!=null">
			and pay_status = #{payStatus}
		</if>
		<if test="offset != null and limit != null">
			limit #{offset} ,#{limit}
		</if>
	</select>
	<select id="getListCount" resultType="java.lang.Long">
		SELECT
		count(*)
		FROM
		<include refid="TABLE"/>
		WHERE crowdfunding_id in
		<foreach collection="infoIds" item="infoId" open="(" separator="," close=")">
			#{infoId}
		</foreach>
		and `id` in <foreach collection="ids" item="id" open="(" separator="," close=")" >#{id}</foreach>
		<if test="valid!=null">
			and valid= #{valid}
		</if>
		<if test="payStatus!=null">
			and pay_status = #{payStatus}
		</if>
	</select>
	<select id="getListByInfoId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE crowdfunding_id = #{crowdfundingId} and pay_status = 1 and valid = 1
		LIMIT #{offset}, #{limit}
	</select>
	<select id="getListByInfoIdWithSuffixTableName" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="SHARDING_TABLE"/>
		WHERE crowdfunding_id = #{crowdfundingId} and pay_status = 1 and valid = 1
		LIMIT #{offset}, #{limit}
	</select>
	<select id="getSuccessByPage" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE crowdfunding_id = #{crowdfundingId} and pay_status = 1 and valid = 1
		<if test="id != null">
			and id = #{id}
		</if>
		<if test="userId != null">
			and user_id = #{userId}
		</if>
		<if test="amount != null">
			and amount=#{amount}
		</if>
		<if test="ctimeStart != null and ctimeEnd != null">
			and ctime between #{ctimeStart} and #{ctimeEnd}
		</if>
		<if test="comment != null and comment != ''">
			and comment like CONCAT('%',#{comment},'%')
		</if>
		<if test="amountStart >= 0 and amountEnd >= 0">
			and amount between #{amountStart} and #{amountEnd}
		</if>
		order by pay_time desc
		<if test="offset != null and limit != null">
			limit #{offset} ,#{limit}
		</if>
	</select>

	<select id="getListByInfoIdsAndTime" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE
		`crowdfunding_id` IN
		<foreach collection="infoIds" item="infoId" open="(" close=")" separator=",">
			#{infoId}
		</foreach>
		AND `pay_time`>=#{start} and  <![CDATA[ `pay_time`<#{end} ]]> AND  `pay_status`=1 AND `valid`=1
		limit #{offset},#{limit}
	</select>
	<select id="getListCountByInfoIdsAndTime" resultType="java.lang.Long">
		SELECT
		count(*)
		FROM
		<include refid="TABLE"/>
		WHERE
		`crowdfunding_id` IN
		<foreach collection="infoIds" item="infoId" open="(" close=")" separator=",">
			#{infoId}
		</foreach>
		AND `pay_time`>=#{start} and  <![CDATA[ `pay_time`<#{end} ]]> AND  `pay_status`=1 AND `valid`=1
	</select>
	<select id="getListByInfoIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE crowdfunding_id IN
		<foreach collection="infoIds" open="(" close=")" item="item" separator=",">
			#{item}
		</foreach>
		AND `id` <![CDATA[ <= ]]> #{anchorId}
		AND `pay_status` = 1
		ORDER BY `id` DESC
		LIMIT #{size}
	</select>

	<select id="getByCrowdfundingIdsAndUserIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE crowdfunding_id IN
		<foreach collection="infoIds" open="(" close=")" item="item" separator=",">
			#{item}
		</foreach>
		and `user_id` IN
		<foreach collection="userIds" item="userId" open="(" close=")" separator=",">
			#{userId}
		</foreach>
		AND `valid`=1
		AND `pay_status`=1
	</select>
	<select id="getByCrowdfundingIdsAndUserIdsWithSuffixTableName" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="SHARDING_TABLE"/>
		WHERE id IN
		<foreach collection="ids" open="(" close=")" item="item" separator=",">
			#{item}
		</foreach>
		AND `valid`=1
		AND `pay_status`=1
	</select>

	<select id="getAllPayByCrowdfundingIdsAndUserIdsWithSuffixTableName" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="SHARDING_TABLE"/>
		WHERE id IN
		<foreach collection="ids" open="(" close=")" item="item" separator=",">
			#{item}
		</foreach>
		AND `pay_status`=1
	</select>


	<select id="getOrderByInfoIds" resultType="java.util.Map">
		SELECT `crowdfunding_id` AS infoId,`user_id` AS userId,sum(amount) AS donation,max(pay_time) AS donationDate
		FROM <include refid="TABLE" />
		WHERE `crowdfunding_id` in <foreach collection="list" item="item" open="(" separator="," close=")" >#{item}</foreach>
		AND `pay_status`='1' AND anonymous= '0'
		GROUP BY `crowdfunding_id`,`user_id`
		LIMIT #{offset},#{limit}
	</select>
	<select id="getInfoCountPeople" resultType="java.lang.Integer">
		SELECT
		count(DISTINCT `user_id`)
		FROM <include refid="TABLE"/>
		WHERE crowdfunding_id = #{crowdfundingId}
		and pay_status = 1
		and valid =1
	</select>
	<select id="getMoreThanAmount" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE
		`crowdfunding_id` = #{cfId} AND
		`amount`>=#{amountInFen}
		AND `pay_status`=1
		AND `valid`=1
		AND `anonymous`=0
		ORDER BY `id` DESC
		limit #{limit}
	</select>
	<select id="getCountSuccessByInfoIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CaseDonationStatisticsModel">
		SELECT crowdfunding_id as crowdfundingId,count(*) as count,sum(amount) as totalAmount
		FROM <include refid="TABLE"/>
		WHERE crowdfunding_id in
		<foreach collection="crowdfundingIds" open="(" separator="," close=")" item="crowdfundingId">
			#{crowdfundingId}
		</foreach>
		AND pay_status=1
		GROUP BY crowdfundingId
	</select>
	<select id="getCountSuccessByInfoIdsWithSuffixTableName" resultType="com.shuidihuzhu.cf.model.crowdfunding.CaseDonationStatisticsModel">
		SELECT crowdfunding_id as crowdfundingId,count(*) as count,sum(amount) as totalAmount
		FROM <include refid="SHARDING_TABLE"/>
		WHERE crowdfunding_id in
		<foreach collection="crowdfundingIds" open="(" separator="," close=")" item="crowdfundingId">
			#{crowdfundingId}
		</foreach>
		AND pay_status=1
		GROUP BY crowdfundingId
	</select>
	<select id="selectByIdListSuccess" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT <include refid="FIELDS"/>
		FROM <include refid="TABLE"/>
		WHERE crowdfunding_id in
		<foreach collection="crowdfundingIds" open="(" separator="," close=")" item="crowdfundingId">
			#{crowdfundingId}
		</foreach>
		and `pay_status`= 1
		AND `id` IN
		<foreach collection="list" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>
	<select id="getListByInfoIdAndPayStatus" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE crowdfunding_id = #{crowdfundingId} and pay_status = 1
		LIMIT #{offset}, #{limit}
	</select>
	<select id="selectByUserIdAndThirdType" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT <include refid="FIELDS"/>
		FROM <include refid="TABLE"/>
		<where>
			crowdfunding_id in
			<foreach collection="crowdfundingIds" open="(" separator="," close=")" item="crowdfundingId">
				#{crowdfundingId}
			</foreach>
			<if test="userId != null and userId > 0">
				AND `user_id`=#{userId}
			</if>
			<if test="thirdType != null">
				AND `user_third_type`=#{thirdType}
			</if>
		</where>
		AND `pay_status` = 1 AND `valid` = 1
		<if test="offset != null and limit != null">
			limit #{offset} ,#{limit}
		</if>
	</select>
	<select id="selectCountByUserIdAndThirdType" resultType="java.lang.Long">
		SELECT count(*)
		FROM <include refid="TABLE"/>
		<where>
			crowdfunding_id in
			<foreach collection="crowdfundingIds" open="(" separator="," close=")" item="crowdfundingId">
				#{crowdfundingId}
			</foreach>
			<if test="userId != null and userId > 0">
				AND `user_id`=#{userId}
			</if>
			<if test="thirdType != null">
				AND `user_third_type`=#{thirdType}
			</if>
		</where>
		AND `pay_status` = 1 AND `valid` = 1
	</select>
	<select id="selectExtreme" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT <include refid="FIELDS"/>
		FROM <include refid="TABLE"/>
		WHERE `crowdfunding_id`=#{caseId}
		ORDER BY `id`
		<if test="orderHandle == 1">
			ASC
		</if>
		<if test="orderHandle == 2">
			DESC
		</if>
		LIMIT 1
	</select>
	<select id="selectOrderOneByInfoId" parameterType="java.util.Set" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT <include refid="FIELDS"/>
		FROM <include refid="TABLE"/>
		WHERE crowdfunding_id IN
		<foreach collection="caseIds" item="caseId" open="(" separator="," close=")">
			#{caseId}
		</foreach>
		group by crowdfunding_id
	</select>
	<select id="countByUserIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfUserOrder">
		SELECT
		`user_id` AS userId,
		count(*) AS orderNum
		FROM <include refid="TABLE"/>
		WHERE crowdfunding_id IN
		<foreach collection="caseIds" item="caseId" open="(" separator="," close=")">
			#{caseId}
		</foreach>
		and user_id IN
		<foreach collection="userIds" item="userId" separator="," open="(" close=")">
			#{userId}
		</foreach>
		<if test="payStatus!=null">
			AND `pay_status`=#{payStatus}
		</if>
		GROUP BY `user_id`
	</select>
	<select id="findCrowdfundingOrder" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		select
		id,
		code,
		user_id,
		crowdfunding_id,
		amount,
		comment,
		pay_status,
		ctime,
		pay_time,
		valid,
		ip,
		os_type,
		channel,
		`from`,
		self_tag,
		user_third_id,
		user_third_type,
		last_modified,
		anonymous
		from <include refid="TABLE"/>
		<trim prefix="WHERE" prefixOverrides="AND|OR" suffixOverrides="AND|OR">
			crowdfunding_id IN
			<foreach collection="caseIds" item="caseId" open="(" separator="," close=")">
				#{caseId}
			</foreach>
			<if test="userId!=null">
				AND user_id = #{userId}
			</if>
			<if test="startTime!=null and startTime!=''">
				AND <![CDATA[ `pay_time` >= #{startTime} ]]>
			</if>
			<if test="endTime!=null and endTime!=''">
				AND <![CDATA[ `pay_time` < #{endTime} ]]>
			</if>
		</trim>
	</select>

	<select id="getByAnchorIdAndPayStatus" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE `crowdfunding_id`=#{crowdfundingId}
		AND `pay_status`=1
		<choose>
			<when test="pagingType == 'previous'">
				AND <![CDATA[ `id` > #{anchorId} ]]>
				ORDER BY `id`
			</when>
			<otherwise>
				AND <![CDATA[ `id` < #{anchorId} ]]>
				ORDER BY `id` DESC
			</otherwise>
		</choose>
		LIMIT #{limit};
	</select>
	<select id="getById" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE `crowdfunding_id`=#{crowdfundingId} and `id`=#{id};
	</select>

	<select id="getByIdFromMaster" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE `crowdfunding_id`=#{crowdfundingId} and `id`=#{id};
	</select>

	<select id="getNoPayListByInfoId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE crowdfunding_id = #{crowdfundingId} and pay_status = 0 and valid = 1
		LIMIT #{offset}, #{limit}
	</select>
	<select id="getOrderIdByInfoId" resultType="java.lang.Long">
		select id from <include refid="SHARDING_TABLE"/>
		where crowdfunding_id = #{crowdfundingId} and pay_status = 1
		LIMIT #{offset}, #{limit}
	</select>

	<select id="donatorCountByCaseId" resultType="java.lang.Integer">
		SELECT COUNT(distinct(user_id))
		FROM <include refid="TABLE"/>
		WHERE crowdfunding_id = #{caseId}
		AND `pay_status`=1
		AND `valid`=1
	</select>

	<select id="donatorCountByCaseIdAndPayTime" resultType="java.lang.Integer">
		SELECT COUNT(distinct(user_id))
		FROM <include refid="TABLE"/>
		WHERE crowdfunding_id = #{caseId}
		AND `pay_time` >= #{payTime}
		AND `pay_status`=1
		AND `valid`=1
	</select>

	<select id="listByCaseIdAndUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="SHARDING_TABLE"/>
		WHERE `crowdfunding_id` = #{caseId}
		AND `user_id` = #{userId}
		AND `pay_status`=1
		AND `valid`=1
	</select>

	<select id="getOrderByCaseIdAndCode" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="SHARDING_TABLE"/>
		WHERE `crowdfunding_id` = #{caseId}
		AND `code` = #{code}
	</select>

	<select id="getCaseOrderCountByPayTime" resultType="java.lang.Integer">
		SELECT COUNT(*)
		FROM
		<include refid="SHARDING_TABLE"/>
		WHERE `crowdfunding_id` = #{caseId}
		AND `pay_status`=1
		AND `valid`=1
		AND <![CDATA[ `pay_time` >= #{startTime} ]]>
		AND <![CDATA[ `pay_time` < #{endTime} ]]>
	</select>

	<select id="getLatelyCaseOrder" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="SHARDING_TABLE"/>
		WHERE `crowdfunding_id` = #{caseId}
		AND `pay_status`=1
		AND `valid`=1
		ORDER BY ID DESC
		LIMIT #{limit}
	</select>

	<select id="getOrderByOrderIdRangeTime" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		select
		<include refid="FIELDS"/>
		from
		<include refid="TABLE"/>
		where
		crowdfunding_id IN
		<foreach collection="caseIdList" item="caseId" open="(" separator="," close=")">
			#{caseId}
		</foreach>
		and user_id = #{userId}
		<if test="startTime!=null and startTime!=''">
			AND <![CDATA[ `pay_time` >= #{startTime} ]]>
		</if>
		<if test="endTime!=null and endTime!=''">
			AND <![CDATA[ `pay_time` < #{endTime} ]]>
		</if>
	</select>
	<select id="getPaySuccessByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		FORCE index(idx_cfid_userid)
		WHERE `crowdfunding_id`=#{crowdfundingId}
		AND `user_id` IN
		<foreach collection="userIds" item="userId" open="(" close=")" separator=",">
			#{userId}
		</foreach>
		AND `pay_status`=1
		ORDER BY `id` DESC
	</select>

	<select id="getNewPaySuccessByUserId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		FORCE index(idx_cfid_userid)
		WHERE `crowdfunding_id`=#{crowdfundingId}
		AND `user_id` = #{userId}
		AND `pay_status` = 1
		ORDER BY `id` DESC
		limit 1
	</select>


	<select id="getAllBySearchDto" parameterType="com.shuidihuzhu.cf.dto.OrderSearchDto" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		FORCE index(idx_cfid_userid)
		WHERE `crowdfunding_id`=#{orderSearchDto.cfId}
		AND `user_id` IN
		<foreach collection="orderSearchDto.friendsUserIds" item="userId" open="(" close=")" separator=",">
			#{userId}
		</foreach>
		<if test="orderSearchDto.valid != -1">
			AND `valid`=#{orderSearchDto.valid}
		</if>
		<if test="orderSearchDto.anonymous != -1">
			AND `anonymous`=#{orderSearchDto.anonymous}
		</if>
		AND `pay_status`=1
		ORDER BY `id` DESC
	</select>
	<select id="getOrderByCaseIdAndCodeForUser" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		select <include refid="FIELDS"/>
		FROM
		<include refid="SHARDING_TABLE"/>
		WHERE `crowdfunding_id` = #{caseId}
		and code = #{code}
		and user_id = #{userId}
		and `pay_status`=1
		and valid = 1
	</select>
	<select id="getByAmountAndCaseId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		select <include refid="FIELDS"/>
		FROM
		<include refid="SHARDING_TABLE"/>
		WHERE `crowdfunding_id` = #{caseId}
		AND `pay_status` = 1
		AND id > #{anchorId}
		AND amount >= #{amount}
		order by id desc
		<if test="limit > 0">
			limit #{limit}
		</if>
	</select>

	<select id="getUserCountByCaseId" resultType="java.lang.Integer">
		SELECT count(*) as userCount FROM
		<include refid="SHARDING_TABLE"/>
		WHERE `crowdfunding_id` = #{caseId}
		AND `pay_status` = 1
		AND `user_id` IS NOT NULL
		GROUP BY user_id HAVING userCount >= #{countLimit}
	</select>

	<select id="selectCountByMin" resultType="com.shuidihuzhu.cf.model.crowdfunding.vo.CfCountSumVo">
		SELECT COUNT(*) AS counts, IFNULL(SUM(amount),0) AS sum
		FROM <include refid="SHARDING_TABLE"/>
		WHERE <![CDATA[ `pay_time`>=#{begin} ]]> AND <![CDATA[ `pay_time`<#{end} ]]>
		<if test="payStatus!=null">
			AND `pay_status` = #{payStatus}
		</if>
	</select>
	<select id="getSingleRefundSuccessAndPaySuccessUserIdListByCrowdfundingId" resultType="java.lang.Long">
		SELECT
		`user_id` as userId
		FROM
		<include refid="TABLE"/>
		WHERE crowdfunding_id = #{crowdfundingId}
		and pay_status = 1
		and single_refund_flag = 0
		LIMIT #{offset}, #{limit};
	</select>

	<select id="getListByUserIdAndAnchorIdAndSingleRefundFlag"
			resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT <include refid="FIELDS"/>
		FROM <include refid="TABLE"/>
		WHERE crowdfunding_id = #{crowdfundingId}
		AND <![CDATA[ id <= #{anchorId}]]>
		and user_id = #{userId}
		and pay_status = 1
		and single_refund_flag = 0
		order by id desc
		limit #{limit}
	</select>

	<select id="getAllByAnchorIdAndUserIdAndSingleRefundFlag"
			resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		FORCE index(idx_cfid_userid)
		WHERE `crowdfunding_id`=#{crowdfundingId}
		AND `user_id` IN
		<foreach collection="userIds" item="userId" open="(" close=")" separator=",">
			#{userId}
		</foreach>
		AND `id` &lt; #{anchorId}
		AND `amount`>=#{amount}
		AND `pay_status`=1
		AND `single_refund_flag`=0
		ORDER BY `id` DESC
		LIMIT #{limit};
	</select>
	<select id="getByAnchorIdAndTimeAndSingleRefundFlag"
			resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE `crowdfunding_id`=#{crowdfundingId}
		AND `id` &lt; #{anchorId}
		AND `pay_status`=1
		AND `single_refund_flag`=0
		<if test="startTime!=null and startTime!=''">
			<![CDATA[ AND `pay_time` >= #{startTime} ]]>
		</if>
		<if test="endTime!=null and endTime!=''">
			<![CDATA[ AND `pay_time` < #{endTime} ]]>
		</if>
		ORDER BY `id` DESC
		LIMIT #{limit};
	</select>
	<select id="getByAnchorIdAndSingleRefundFlag"
			resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		FORCE index(idx_cfid_id)
		WHERE `crowdfunding_id`=#{crowdfundingId}
		<if test="anchorId != null">
			AND `id` &lt; #{anchorId}
		</if>
		AND `pay_status`=1
		AND `single_refund_flag`=0
		ORDER BY `id` DESC
		LIMIT #{limit};
	</select>
    <select id="getValidUserIdByCaseIdAndNotAnonymous" resultType="java.lang.Long">
		SELECT
		`user_id` as userId
		FROM
		<include refid="TABLE"/>
		WHERE crowdfunding_id = #{caseId}
		and pay_status = 1
		and single_refund_flag = 0
		and anonymous = 0
		order by id
		LIMIT #{offset}, #{size}
	</select>
	<select id="getByUserIdsAndCaseIdWithNotSingleRefundFromSharding"
			resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		FORCE index(idx_cfid_userid)
		WHERE `crowdfunding_id`=#{caseId}
		AND `user_id` IN
		<foreach collection="userIdSet" item="userId" open="(" close=")" separator=",">
			#{userId}
		</foreach>
		AND `pay_status`=1
		AND single_refund_flag = 0
		ORDER BY `id` DESC
	</select>
	<select id="getByUserIdsAndCaseId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE"/>
		WHERE `crowdfunding_id`=#{caseId}
		AND `user_id` IN
		<foreach collection="userIds" item="userId" open="(" close=")" separator=",">
			#{userId}
		</foreach>
		ORDER BY `id` DESC
	</select>

</mapper>
