<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfInfoShareRecordCaseShardingDao">

	<sql id="TABLE">
        cf_info_share_record_${sharding}
    </sql>

	<sql id="fields">
		`id` as id,
		`user_id` as userId,
		`uuid` as uuid,
		`info_id` as infoId,
		`wx_source` as wxSource,
		`source` as source,
		`user_source_id` as userSourceId,
		`to_wx_source` as toWxSource,
		`channel` AS channel,
		`share_id` AS shareId,
		`share_source_id` AS shareSourceId,
		`create_time` as dateCreated
	</sql>

	<insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareRecord">
		INSERT ignore INTO
		<include refid="TABLE"/>
		(`id`,`user_id`,`uuid`,`info_id`,`wx_source`,`source`,
		`to_wx_source`, `channel`,`user_source_id`,`share_id`,`share_source_id`)
		VALUES
		(#{record.id},#{record.userId},#{record.uuid},#{record.infoId},#{record.wxSource},#{record.source},#{record.toWxSource},
		#{record.channel},#{record.userSourceId},#{record.shareId},#{record.shareSourceId})
	</insert>

	<select id="countShareByUserIdAndInfoId" resultType="java.lang.Integer">
		SELECT
		COUNT(*)
		FROM <include refid="TABLE"/>
		WHERE info_id = #{infoId} AND user_id = #{userId}
	</select>

	<select id="countShareByUserIdAndInfoIdWithDate" resultType="java.lang.Integer">
		SELECT
		COUNT(*)
		FROM <include refid="TABLE"/>
		WHERE info_id = #{infoId} AND user_id = #{userId} and <![CDATA[ `create_time`>=#{beginTime} ]]> AND <![CDATA[ `create_time`<#{endTime} ]]>
	</select>

	<select id="countShareByUserIdWithSourceType" resultType="java.lang.Integer">
		SELECT
		COUNT(*)
		FROM <include refid="TABLE"/>
		WHERE info_id = #{infoId} AND user_id = #{userId} and <![CDATA[ `create_time`>=#{beginTime} ]]> AND <![CDATA[ `create_time`<#{endTime} ]]>
		AND to_wx_source = #{toWxSource}
	</select>

	<select id="getShareCountMin" resultType="java.lang.Integer">
		SELECT COUNT(*)
		FROM <include refid="TABLE"/>
		WHERE <![CDATA[ `create_time`>=#{begin} ]]> AND <![CDATA[ `create_time`<#{end} ]]>
	</select>

	<select id="countShareByInfoIdAndUserIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareRecordCountGroupByUserIdModel">
		SELECT `user_id` as userId, count(1) as counts
		FROM <include refid="TABLE"/>
		where
		`info_id`=#{infoId} and
		`user_id` in
		<foreach collection="userIds" item="userId" open="(" close=")" separator=",">
			#{userId}
		</foreach>
		group by `user_id`
	</select>

	<select id="findByUuidAndInfoId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareRecord">
		SELECT <include refid="fields"/>
		FROM <include refid="TABLE"/>
		WHERE `uuid`=#{uuid}
		AND `info_id`=#{infoId}
		ORDER BY `id` DESC
		LIMIT 1
	</select>

	<select id="getCfInfoShareRecordCountModelByInfoIdsAndTime" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareRecordCountModel">
		SELECT `info_id` AS infoId, count(*) AS count
		FROM <include refid="TABLE" />
		WHERE
		`info_id` in <foreach collection="list" item="item" open="(" separator="," close=")" >#{item}</foreach>
		AND `create_time` > #{start}
		AND <![CDATA[ create_time < #{end} ]]>
		GROUP BY `info_id`
	</select>

	<select id="getShareRecordList" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareRecord">
		SELECT <include refid="fields"/>
		FROM <include refid="TABLE"/>
		WHERE
		`info_id` in <foreach collection="list" item="item" open="(" separator="," close=")" >#{item}</foreach>
		AND create_time>#{start}
		AND <![CDATA[ create_time<#{end} ]]>
		LIMIT #{offset},#{limit}
	</select>

	<select id="getByInfoId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareRecord">
		SELECT <include refid="fields"/>
		FROM <include refid="TABLE"/>
		WHERE
		`info_id` = #{infoId}
		AND create_time > #{start}
		AND <![CDATA[ create_time<#{end} ]]>
		order by `id`
		LIMIT #{offset} , #{limit}
	</select>

	<select id="getListByInfoId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareRecord">
		SELECT <include refid="fields"/>
		FROM <include refid="TABLE"/>
		WHERE `info_id`=#{infoId}
		AND `id` <![CDATA[ <= ]]> #{anchorId}
		ORDER BY `id` DESC
		LIMIT #{limit}
	</select>

	<select id="findLastByUserIdAndInfoId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareRecord">
		SELECT <include refid="fields"/>
		FROM <include refid="TABLE"/>
		WHERE `info_id`=#{infoId}
		AND `user_id` =#{userId}
		ORDER BY `id` DESC
		LIMIT 1
	</select>

	<select id="selectByUserAndCase" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareRecord">
		SELECT <include refid="fields"/>
		FROM <include refid="TABLE"/>
		WHERE `info_id`=#{infoId} AND `user_id` =#{userId}
	</select>

	<select id="findLastByUserIdListAndInfoId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareRecord">
		select MAX(create_time) date_created, user_id
		FROM <include refid="TABLE"/>
		WHERE `info_id`=#{infoId}
		AND `user_id` in
		<foreach collection="userIdList" open="(" separator="," item="item" close=")">
			#{item}
		</foreach>
		GROUP BY user_id
	</select>

	<select id="getCountByUseridAndInfoId" resultType="java.lang.Integer">
		SELECT count(1)
		FROM <include refid="TABLE"/>
		WHERE `info_id`=#{infoId}
		AND `user_id` =#{userId}
	</select>

	<select id="getShareCount" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareRecordCountModel">
		SELECT info_id as infoId,count(id) as count
		FROM
		<include refid="TABLE"/>
		WHERE info_id IN
		<foreach collection="infoIds" open="(" close=")" separator="," item="id">
			#{id}
		</foreach>
		GROUP BY info_id
	</select>

	<select id="getCountByInfoIdWithDateCretead" resultType="java.lang.Integer">
		SELECT
		COUNT(id)
		FROM
		<include refid="TABLE"/>
		WHERE info_id = #{infoId}
		and  <![CDATA[ `create_time`<= #{endTime} ]]>
	</select>

	<select id="getFirstByInfoId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareRecord">
		SELECT <include refid="fields"/>
		FROM <include refid="TABLE"/>
		WHERE `info_id`= #{infoId}
		ORDER BY `id` ASC
		LIMIT 1
	</select>

	<select id="getLatelyShareByInfoId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareRecord">
		SELECT
		<include refid="fields"/>
		FROM
		<include refid="TABLE"/>
		WHERE `info_id`= #{infoId}
		LIMIT #{limit}
	</select>

	<select id="getListByQuasiMaxId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareRecord">
		SELECT
		<include refid="fields"/>
		FROM
		<include refid="TABLE"/>
		WHERE id > #{id}
		limit #{limit}
	</select>

	<select id="getListByInfoIdAndUserIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareRecord">
		SELECT
		<include refid="fields"/>
		FROM
		<include refid="TABLE"/>
		where
		info_id=#{infoId} and
		user_id in
		<foreach collection="userIds" item="userId" open="(" close=")" separator=",">
			#{userId}
		</foreach>
	</select>

	<select id="getListDescByInfoIdAndUserIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareRecord">
		SELECT
		<include refid="fields"/>
		FROM
		<include refid="TABLE"/>
		where
		info_id=#{infoId} and
		user_id in
		<foreach collection="userIds" item="userId" open="(" close=")" separator=",">
			#{userId}
		</foreach>
		order by id desc
	</select>
	<select id="getSharedUserByInfoId" resultType="com.shuidihuzhu.cf.model.crowdfunding.dto.CfSharedUserDto">
		SELECT
		    user_id, count(*) as shareCount
		FROM
		    <include refid="TABLE"/>
		WHERE
		    info_id = #{infoId}
		GROUP BY user_id
		LIMIT #{offset}, #{limit}
	</select>


</mapper>
