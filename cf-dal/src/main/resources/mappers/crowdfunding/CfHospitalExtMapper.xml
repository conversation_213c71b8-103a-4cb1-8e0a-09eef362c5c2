<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfHospitalExtDao">
    <sql id="table_name">
        `cf_hospitial_ext`
    </sql>

    <sql id="insert_fields">
        `province_id`,
        `city_id`,
        `hospital_id`,
        `hospital_name`,
        `user_input`
    </sql>

    <sql id="select_fields">
        `id`,
        `province_id`,
        `city_id`,
        `hospital_id`,
        `hospital_name`,
        `user_input`
    </sql>

    <insert id="insertOne" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfHospitalExt">
        INSERT INTO <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES
        (
          #{provinceId},
          #{cityId},
          #{hospitalId},
          #{hospitalName},
          #{userInput}
        )
    </insert>
</mapper>