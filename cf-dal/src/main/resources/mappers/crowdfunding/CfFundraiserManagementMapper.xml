<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfFundraiserManagementDao">
    <sql id="tableName">
		cf_fundraiser_management
	</sql>

    <sql id="selectFields">
		`id`,
		`date`,
		`info_uuid`,
		`fundraising_amount`,
		`share_count`,
		`share_people`,
		`pay_num`
	</sql>

    <select id="getCfFundraiserManagementList"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.CfFundraiserManagement">
        select
        <include refid="selectFields"/>
        from
        <include refid="tableName"/>
        where `info_uuid` = #{infoUuid} and `date` = #{date} and `is_delete`=0
    </select>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfFundraiserManagement">
        insert into
        <include refid="tableName"/>
        (`date`,`info_uuid`,`fundraising_amount`,`share_count`,`share_people`,`pay_num`)
        values
        (#{date}, #{infoUuid},
        #{fundraisingAmount},
        #{shareCount},
        #{sharePeople},
        #{payNum})
        ON DUPLICATE KEY UPDATE
        `fundraising_amount` = `fundraising_amount` + #{fundraisingAmount},
        `share_count` =`share_count` + #{shareCount},
        `pay_num` = `pay_num` + #{payNum}
    </insert>

    <update id="update" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfFundraiserManagement">
        UPDATE
        <include refid="tableName"/>
        <set>
            `fundraising_amount` = `fundraising_amount` + #{fundraisingAmount},
            `share_count` = `share_count` + #{shareCount},
            <if test="sharePeople > 0">
                `share_people` = #{sharePeople},
            </if>
            `pay_num` = `pay_num` + #{payNum}
        </set>
        where `id` = #{id}
    </update>

    <select id="getLastSevenDaysCfFundraiserManagementList"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.CfFundraiserManagement">
        select
        <include refid="selectFields"/>
        from
        <include refid="tableName"/>
        WHERE `info_uuid`=#{infoUuid} AND `is_delete`=0
        AND `date` IN
        <foreach collection="dates" item="date" open="(" separator="," close=")">#{date}</foreach>
    </select>

    <select id="getCfFundraiserManagementListByInfoUuid"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.CfFundraiserManagement">
        select
        <include refid="selectFields"/>
        from
        <include refid="tableName"/>
        WHERE `info_uuid`=#{infoUuid} AND `is_delete`=0
    </select>


</mapper>