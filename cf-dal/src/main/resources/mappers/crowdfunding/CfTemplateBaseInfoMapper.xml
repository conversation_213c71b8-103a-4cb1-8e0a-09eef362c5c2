<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfBaseInfoTemplateRecordDao">

    <sql id="table_name">
        `cf_base_info_template_record`
    </sql>
    
    <sql id="insert_fields">
        `info_uuid`,
        `title_id`,
        `content_id`,
        `relationship`,
        `amount`,
        `name`,
        `author_name`,
        `disease_name`,
        `hospital_name`,
        `age`,
        `hometown`,
        `disaster_day`,
        `cost`
    </sql>
    
    <sql id="select_fields">
        `id`,
        `info_uuid`,
        `title_id`,
        `content_id`,
        `relationship`,
        `amount`,
        `name`,
        `author_name`,
        `disease_name`,
        `hospital_name`,
        `age`,
        `hometown`,
        `disaster_day`,
        `cost`
    </sql>

    <select id="selectByInfoUuid" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplateRecord">
        SELECT <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE `info_uuid`=#{infoUuid}
        ORDER BY `id` DESC
        LIMIT 1
    </select>

    <insert id="insertOne" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplateRecord">
        INSERT INTO <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES (
        <if test="infoUuid!=null and infoUuid!=''">
            #{infoUuid},
        </if>
        <if test="infoUuid==null or infoUuid==''">
            '',
        </if>
        #{titleId},#{contentId},
        <if test="relationship!=null and relationship!=0">
            #{relationship},
        </if>
        <if test="relationship==null or relationship==0">
            0,
        </if>
        <if test="amount!=null and amount!=0">
            #{amount},
        </if>
        <if test="amount==null or amount==0">
            0,
        </if>
        <if test="name!='' and name!=null">
            #{name},
        </if>
        <if test="name=='' or name==null">
            '',
        </if>
        <if test="authorName!=null and authorName!=''">
            #{authorName},
        </if>
        <if test="authorName==null or authorName==''">
            '',
        </if>
        <if test="diseaseName!=null and diseaseName!=''">
            #{diseaseName},
        </if>
        <if test="diseaseName==null or diseaseName==''">
            '',
        </if>
        <if test="hospitalName!=null and hospitalName!=''">
            #{hospitalName},
        </if>
        <if test="hospitalName==null or hospitalName==''">
            '',
        </if>
        <if test="age!=null">
            #{age},
        </if>
        <if test="age==null">
            0,
        </if>
        <if test="hometown!=null and hometown!=''">
            #{hometown},
        </if>
        <if test="hometown==null or hometown==''">
            '',
        </if>
        <if test="disasterDay!=null and disasterDay!=''">
            #{disasterDay},
        </if>
        <if test="disasterDay==null or disasterDay==''">
            '',
        </if>
        <if test="cost!=null">
            #{cost}
        </if>
        <if test="cost==null">
            0
        </if>
        )
    </insert>
</mapper>