<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.CfFirstApproveIdCardWhiteListDao">
	<sql id="TABLE">
		cf_first_approve_idcard_white_list
	</sql>

	<sql id="QUERY_FIELDS">
		`id`,
		`real_name`,
		`crypto_idcard`,
		`remark`,
		`images`
	</sql>

	<sql id="INSERT_FIELDS">
		`real_name`,
		`crypto_idcard`,
		`images`
	</sql>

	<select id="getByNameAndIdcard" resultType="com.shuidihuzhu.cf.model.crowdfunding.IdcardVerifyWhiteList">
		select
		<include refid="QUERY_FIELDS"/>
		from
		<include refid="TABLE"/>
		where `real_name`=#{name} and
		`crypto_idcard`=#{cryptoIdCard} and
		`is_delete`=0
		limit 1
	</select>

	<insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.IdcardVerifyWhiteList" useGeneratedKeys="true" keyProperty="id">
		insert into
		<include refid="TABLE"/>
		(<include refid="INSERT_FIELDS"/>)
		values(#{realName}, #{cryptoIdCard}, #{images})
	</insert>


	<insert id="deleteFirstApproveWhiteIdById">
		update
		<include refid="TABLE"/>
		set is_delete = 1
		where id = #{id}
	</insert>

	<select id="selectAllWhiteIdCardList" resultType="com.shuidihuzhu.cf.model.crowdfunding.IdcardVerifyWhiteList">
		select *
		from
		<include refid="TABLE"/>
		where is_delete = 0
		order by create_time desc
	</select>

	<select id="selectAllWhiteIdCardListAll"
			resultType="com.shuidihuzhu.cf.model.crowdfunding.IdcardVerifyWhiteList">
		select *
		from
		<include refid="TABLE"/>
		<where>
			is_delete = 0
			<if test="name != null and name != ''">
				and `real_name`=#{name}
			</if>
			<if test="idCard != null and idCard != ''">
				and `crypto_idcard`=#{idCard}
			</if>
		</where>
		order by create_time desc
	</select>

</mapper>