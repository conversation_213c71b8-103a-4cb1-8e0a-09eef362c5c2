<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.TdWorkOrderDao">
    <sql id="table">
        work_order
    </sql>

    <select id="getLastWorkOrderByType" resultType="com.shuidihuzhu.client.cf.workorder.read.BasicWorkOrder">
        select * from
        <include refid="table"/>
        where
        `case_id` = #{caseId}
        and `order_type` = #{type}
        and `is_delete` = 0
        order by `id` desc
        limit 1;
    </select>
    <select id="getWorkOrderById" resultType="com.shuidihuzhu.client.cf.workorder.read.BasicWorkOrder">
        select * from
        <include refid="table"/>
        where
        `id` = #{id}
        and `is_delete` = 0
    </select>
</mapper>