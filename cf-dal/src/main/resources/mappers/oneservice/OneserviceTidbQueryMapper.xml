<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.oneservice.OneserviceTidbQueryDao">

    <select id="getShareAvgData" resultType="java.util.Map">
        SELECT
        avg_case_share_cnt AS avg_case_share_cnt,
        avg_case_visit_cnt AS avg_case_visit_cnt
        FROM shuidi_rpt.rpt_cf_case_avg_share_analysis_d
        WHERE 1 = 1
        AND stat_dt = #{dt}
        AND dt = #{dt}
        LIMIT 1
    </select>
    <select id="getCaseStatData" resultType="java.util.Map">
        SELECT
        case_id AS case_id,
        crk_id AS crk_id,
        first_share_time AS first_share_time,
        case_share_num AS case_share_num,
        case_share_cnt AS case_share_cnt,
        case_visit_num AS case_visit_num,
        case_visit_cnt AS case_visit_cnt,
        donate_user_num AS donate_user_num,
        donate_amt AS donate_amt,
        donate_order_num AS donate_order_num,
        share_share_cnt AS share_share_cnt,
        share_share_num AS share_share_num,
        share_visit_num AS share_visit_num,
        share_visit_cnt AS share_visit_cnt,
        share_donate_uv AS share_donate_uv,
        share_donate_cnt AS share_donate_cnt,
        share_donate_amt AS share_donate_amt,
        stat_dt AS stat_dt
        FROM shuidi_rpt.rpt_cf_case_share_analysis_h
        WHERE 1 = 1
        AND case_id = #{infoId}
        AND crk_id = #{userId}
        AND stat_dt = #{dt}
        LIMIT 1
    </select>
    <select id="getSettleCaseIds" resultType="java.lang.String">
        SELECT
        case_id AS case_id
        FROM shuidi_rpt.rpt_cf_case_capital_statistical_m
        WHERE 1 = 1
        AND stat_dt = #{dt}
        <if test="infoIds != null and infoIds.size() > 0">
            AND case_id in
            <foreach collection="infoIds" item="infoId" open="(" close=")" separator=",">
                #{infoId}
            </foreach>
        </if>
        LIMIT #{offset}, #{size}
    </select>
</mapper>