<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.disease.DiseaseKnowledgeDao">

    <sql id="selectFields">
        `disease_norm` as diseaseNorm,
        `disease_intro` as diseaseIntro,
        `cure_and_cost` as cureAndCost,
        `prognosis`,
        `is_delete` as isDelete,
        `create_time` as createTime,
        `update_time` as updateTime
    </sql>
    <sql id="tableName">
        `disease_knowledge`
    </sql>

	<select id="getNorms" resultType="com.shuidihuzhu.cf.model.disease.DiseaseKnowledge">
        SELECT
         `id`,`disease_norm` as diseaseNorm
        FROM <include refid="tableName" />
        WHERE is_delete = 0
	</select>

    <select id="getDiseaseKnowledge" resultType="com.shuidihuzhu.cf.model.disease.DiseaseKnowledge">
        SELECT
        <include refid="selectFields"></include>
        FROM <include refid="tableName" />
        WHERE `id` in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
               #{id}
            </foreach>
        and is_delete = 0
    </select>

</mapper>