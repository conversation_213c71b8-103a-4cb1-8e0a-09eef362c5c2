<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.disease.DiseaseNormShipDao">

    <sql id="selectFields">
        `id`,
        `info_id` as infoUuid,
        `case_id` as caseId,
        `disease_name` as diseaseName,
        `disease_norm_ship` as diseaseNormShip,
        `is_delete` as isDelete,
        `create_time` as createTime,
        `update_time` as updateTime
    </sql>
    <sql id="tableName">
        `cf_disease_norm_ship`
    </sql>

	<select id="getBycaseId"  resultType="com.shuidihuzhu.cf.model.disease.DiseaseNormShip">
        SELECT <include refid="selectFields" />
        FROM <include refid="tableName" />
        WHERE case_id = #{caseId}
        AND is_delete = 0
        Order by id desc
        limit 1
	</select>

    <insert id="insertDiseaseNormShip">
        INSERT ignore INTO
        <include refid="tableName"/>
        (`info_id`,`case_id`,`disease_name`
            <if test='diseaseNormShip != ""'>
                ,`disease_norm_ship`
            </if>
        )
        values
        (#{infoUuid},#{caseId},#{diseaseName}
        <if test='diseaseNormShip != ""'>
            ,#{diseaseNormShip}
        </if>
        )
    </insert>
	

</mapper>