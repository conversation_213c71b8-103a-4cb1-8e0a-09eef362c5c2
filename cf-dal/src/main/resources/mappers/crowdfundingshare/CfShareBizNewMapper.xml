<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfundingshare.CfShareBizMapNewDao">

    <sql id="TABLE">
		cf_share_biz_map
	</sql>

    <sql id="SELECT_FIELDS">
		`id`, `crowdfunding_id`, `user_id`, `share_id`, `biz_id`, `type`, `create_time`, `update_time`
	</sql>

    <sql id="INSERT_FIELDS">
		`crowdfunding_id`, `user_id`, `share_id`, `biz_id`, `type`
	</sql>

    <sql id="BATCH_INSERT_FIELDS">
        `crowdfunding_id`, `user_id`, `share_id`, `biz_id`, `type`,`create_time`
    </sql>


    <insert id="addBatch" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfShareBizMap">
        INSERT IGNORE INTO
        <include refid="TABLE"/>
        (<include refid="INSERT_FIELDS"/>)
        VALUES
        <foreach collection="items" item="item" separator=",">
            (#{item.crowdfundingId}, #{item.userId}, #{item.shareId}, #{item.bizId}, #{item.type})
        </foreach>
    </insert>

    <select id="get" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfShareBizMap">
        SELECT
        <include refid="SELECT_FIELDS"/>
        FROM
        <include refid="TABLE"/>
        WHERE
        `crowdfunding_id`=#{crowdfundingId} AND
        `type`=#{type} AND
        `is_delete`=0
        ORDER BY `biz_id` DESC
        limit #{limit}
    </select>

    <select id="getByBizIds" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfShareBizMap">
        SELECT
        <include refid="SELECT_FIELDS"/>
        FROM
        <include refid="TABLE"/>
        WHERE
        `biz_id` IN
        <foreach collection="bizIdSet" item="bizId" separator="," open="(" close=")">
            #{bizId}
        </foreach>
        AND `type`=#{type}
        AND `is_delete`=0
    </select>


</mapper>