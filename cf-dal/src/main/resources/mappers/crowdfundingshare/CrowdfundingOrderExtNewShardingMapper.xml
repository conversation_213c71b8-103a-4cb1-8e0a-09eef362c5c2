<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfundingshare.CrowdfundingOrderExtNewShardingDao">

    <sql id="table_prefix">
        crowdfunding_order_ext_ctime_sharding_${sharding}
    </sql>
    
    <insert id="add" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrderExt" keyProperty="crowdfundingOrderExt.id" useGeneratedKeys="true">
		INSERT INTO <include refid="table_prefix" />
	    	(`order_id`,`audit_status`,`data_status`,`split_flow_uuid`,`share_source_id`, `ctime`)
	    VALUES
	    	(#{crowdfundingOrderExt.orderId},#{crowdfundingOrderExt.auditStatus},#{crowdfundingOrderExt.dataStatus},#{crowdfundingOrderExt.splitFlowUuid},#{crowdfundingOrderExt.shareSourceId}, #{crowdfundingOrderExt.ctime})
	</insert>


    <select id="getByOrderId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrderExt">
        select *
        from <include refid="table_prefix" />
        where order_id = #{orderId}
    </select>

</mapper>