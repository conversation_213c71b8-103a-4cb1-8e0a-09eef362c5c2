<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.crowdfundingshare.CfSharePromoteOrderShardingDao">
    <sql id="tableName">
		cf_share_promote_order_${sharding}
	</sql>

    <sql id="selectFields">
		`id` as id,
		`order_id` as orderId,
		`amount` as amount,
		`info_id` as infoId,
		`user_id` as userId,
		`source_user_id` as sourceUserId,
		`source` as source,
		`create_time` as createTime
	</sql>


    <insert id="insert" parameterType="com.shuidihuzhu.cf.model.crowdfunding.CfSharePromoteOrder">
        INSERT IGNORE INTO
        <include refid="tableName"/>
        (`order_id`, `amount`, `info_id`, `user_id`, `source_user_id`, `source`, `anonymous`)
        VALUES
        (#{order.orderId}, #{order.amount}, #{order.infoId}, #{order.userId}, #{order.sourceUserId}, #{order.source},
        #{order.anonymous})
    </insert>

    <select id="findLast" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfSharePromoteOrder">
        SELECT <include refid="selectFields"/>
        FROM <include refid="tableName"/>
        WHERE `source_user_id` = #{sourceUserId}
        AND `info_id` = #{infoId}
        AND `is_delete` = 0
        AND `user_id` <![CDATA[ <> ]]> #{sourceUserId}
        AND `anonymous` = 0
        ORDER BY `id` DESC
        LIMIT 1
    </select>

    <select id="findByUserAndCase" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfSharePromoteOrder">
        SELECT <include refid="selectFields"/>
        FROM <include refid="tableName"/>
        WHERE `source_user_id` = #{sourceUserId}
        AND `info_id` = #{infoId}
        AND `is_delete` = 0
        AND `user_id` <![CDATA[ <> ]]> #{sourceUserId}
        AND `anonymous` = 0
        ORDER BY `id` ASC
    </select>

    <select id="findByInfoId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfSharePromoteOrder">
        SELECT <include refid="selectFields"/>
        FROM <include refid="tableName"/>
        WHERE `info_id` = #{infoId}
        AND `id` <![CDATA[ <= ]]> #{anchorId}
        AND `is_delete`	= 0
        ORDER BY `id` DESC
        LIMIT #{limit}
    </select>

    <select id="getSumAmountByInfoIdAndUserId" resultType="java.lang.Integer">
        SELECT sum(amount)
        FROM <include refid="tableName" />
        WHERE `info_id` = #{infoId}
        AND `user_id` = #{userId}
    </select>

    <update id="refund">
        update <include refid="tableName"/>
        set `refund_status` = 1
        where
        `info_id` = #{infoId}
        AND `order_id` = #{orderId}
    </update>

    <select id="getByOrderId" resultType="com.shuidihuzhu.cf.model.crowdfunding.CfSharePromoteOrder">
        select <include refid="selectFields"/>
        from <include refid="tableName"/>
        where `info_id` = #{infoId}
        and `order_id` = #{orderId}
    </select>
    <select id="getByOrderIdBySouceIdAndInfoIdAndUserId"
            resultType="com.shuidihuzhu.cf.model.crowdfunding.CfSharePromoteOrder">
        select <include refid="selectFields"/>
        from <include refid="tableName"/>
        where `info_id` = #{infoId}
        and refund_status = 0
        <if test="orderIds!= null and orderIds.size()>0">
            and `order_id` in
          <foreach collection="orderIds" item="item" separator="," close=")" open="(">
              #{item}
          </foreach>
        </if>
        <if test="sourceUserId != null">
            and `source_user_id` = #{sourceUserId}
        </if>
    </select>

</mapper>