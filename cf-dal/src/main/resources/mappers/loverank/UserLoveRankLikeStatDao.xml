<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.loverank.UserLoveRankLikeStatDao">

  <sql id="Base_Column_List">
    id, user_id, case_id, anonymous, like_count, view_user_ids, is_delete, create_time,
    update_time
  </sql>

  <sql id="table_name">
    user_love_rank_like_stat
  </sql>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.shuidihuzhu.cf.model.loverank.UserLoveRankLikeStat" useGeneratedKeys="true">
    insert into <include refid="table_name"/> (user_id, case_id, anonymous,
      like_count, view_user_ids)
    values (#{userId}, #{caseId}, #{anonymous},
      #{likeCount}, #{viewUserIds})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.shuidihuzhu.cf.model.loverank.UserLoveRankLikeStat" useGeneratedKeys="true">
    insert into <include refid="table_name"/>
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="caseId != null">
        case_id,
      </if>
      <if test="anonymous != null">
        anonymous,
      </if>
      <if test="likeCount != null">
        like_count,
      </if>
      <if test="viewUserIds != null">
        view_user_ids,
      </if>

    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId},
      </if>
      <if test="caseId != null">
        #{caseId},
      </if>
      <if test="anonymous != null">
        #{anonymous},
      </if>
      <if test="likeCount != null">
        #{likeCount},
      </if>
      <if test="viewUserIds != null">
        #{viewUserIds},
      </if>

    </trim>
  </insert>
  <select id="selectLikeStatByUserIds" resultType="com.shuidihuzhu.cf.model.loverank.UserLoveRankLikeStat">
    select
    <include refid="Base_Column_List"/>
    from
    <include refid="table_name"/>
    where is_delete = 0
    and case_id = #{caseId}
    <if test="userIds!= null and userIds.size()>0">
      and user_id in (
      <foreach collection="userIds" index="index" item="item" separator=",">
        #{item}
      </foreach>
      )
    </if>
  </select>
    <select id="selectLikeByUserIdAndCaseIdAndAnonymous"
            resultType="com.shuidihuzhu.cf.model.loverank.UserLoveRankLikeStat">
      select <include refid="Base_Column_List"/>
      from <include refid="table_name"/>
      where  is_delete = 0
      and user_id = #{userId}
      and case_id = #{caseId}
      and anonymous = #{anonymous}
      limit 1
    </select>

    <update id="minusLikeById">
    update <include refid="table_name"/>
    set like_count = like_count -1
    where id = #{id}
      and user_id = #{userId}
      and case_id = #{caseId}
  </update>

  <update id="addLikeById">
    update <include refid="table_name"/>
    set like_count = like_count +1
    where id = #{id}
    and user_id = #{userId}
    and case_id = #{caseId}
  </update>
  <update id="updateViewUserIdsById">
    update <include refid="table_name"/>
    set view_user_ids = json_array_append(view_user_ids,'$',#{loginUserId})
    where is_delete = 0
    and user_id = #{userId}
    and id = #{id}
    and case_id = #{caseId}
  </update>


</mapper>