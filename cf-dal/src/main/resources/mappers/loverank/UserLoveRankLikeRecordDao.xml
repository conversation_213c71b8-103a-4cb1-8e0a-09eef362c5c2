<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.loverank.UserLoveRankLikeRecordDao">

  <sql id="Base_Column_List">
    id, user_id, case_id,like_user_id, anonymous, `like_flag`, is_delete, create_time, update_time
  </sql>
  <sql id="table_name">
    user_love_rank_like_record
  </sql>


    <select id="selectLikeRecordByUserId"
            resultType="com.shuidihuzhu.cf.model.loverank.UserLoveRankLikeRecord">
      select <include refid="Base_Column_List"/>
      from <include refid="table_name"/>
      where is_delete = 0
      and user_id = #{userId}
      and case_id = #{caseId}
      <if test="userIdSet != null and userIdSet.size()>0">
          and like_user_id in
          <foreach collection="userIdSet"  separator="," item="item" open="(" close=")">
              #{item}
          </foreach>
      </if>

    </select>
  <select id="selectNewLikeByUserId" resultType="com.shuidihuzhu.cf.model.loverank.UserLoveRankLikeRecord">
    select <include refid="Base_Column_List"/>
    from <include refid="table_name"/>
    where is_delete = 0
    and user_id = #{userId}
    and case_id = #{caseId}
    and like_user_id = #{likeUserId}
    and anonymous = #{anonymous}
    order by id desc
    limit 1
  </select>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.shuidihuzhu.cf.model.loverank.UserLoveRankLikeRecord" useGeneratedKeys="true">
    insert into <include refid="table_name"/> (user_id, case_id, anonymous,
      `like_flag`)
    values (#{userId}, #{caseId}, #{anonymous},#{likeFlag})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.shuidihuzhu.cf.model.loverank.UserLoveRankLikeRecord" useGeneratedKeys="true">
    insert into user_love_rank_like_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="caseId != null">
        case_id,
      </if>
      <if test="likeUserId != null">
        like_user_id ,
      </if>
      <if test="anonymous != null">
        anonymous,
      </if>
      <if test="likeFlag != null">
        `like_flag`,
      </if>

    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId},
      </if>
      <if test="caseId != null">
        #{caseId},
      </if>
      <if test="likeUserId != null">
        #{likeUserId},
      </if>
      <if test="anonymous != null">
        #{anonymous},
      </if>
      <if test="likeFlag != null">
        #{likeFlag},
      </if>

    </trim>
  </insert>

</mapper>