<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.insurance.CfReceiveGiftInsuranceRecordDao">
    <sql id="tableName">
		cf_receive_gift_insurance_record
	</sql>

	<insert id="insert" parameterType="java.lang.Long">
		insert into <include refid="tableName"/> (`user_id`) values (#{userId});
	</insert>

	<select id="selectRecordCount" parameterType="java.lang.Long" resultType="java.lang.Integer">
		select count(1) from <include refid="tableName"/>
		where user_id = #{userId} and is_delete = 0
	</select>

</mapper>