<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.crowdfunding.visitconfig.VisitConfigLogDAO">

    <sql id="tableName">
		cf_case_visit_config_log
	</sql>

    <sql id="Base_Column_List">
		id,
		info_uuid,
		operator_id,
		action_type,
		source,
		action_info,
		create_time
	</sql>

    <sql id="insert_Column_List">
		info_uuid,
		operator_id,
		action_type,
		source,
		action_info
	</sql>

    <insert id="insert"
            parameterType="com.shuidihuzhu.cf.domain.visitconfig.VisitConfigLogDO">
        <selectKey order="AFTER" resultType="long" keyProperty="id">
            select last_insert_id() as id
        </selectKey>
        insert into <include refid="tableName"/>
        (<include refid="insert_Column_List" />)
        values(
        #{infoUuid},
        #{operatorId},
        #{actionType},
        #{source},
        #{actionInfo}
        )
    </insert>

    <select id="listByInfoUuidAndActionType"
            resultType="com.shuidihuzhu.cf.domain.visitconfig.VisitConfigLogDO">
        select <include refid="Base_Column_List"/>
        from <include refid="tableName"/>
        <where>
            `info_uuid` = #{infoUuid}

            <if test="actionType != null">
                and `action_type` = #{actionType}
            </if>
            and is_delete = 0
        </where>
        order by id desc
    </select>

    <select id="listByCondition"
            resultType="com.shuidihuzhu.cf.domain.visitconfig.VisitConfigLogDO">
        select <include refid="Base_Column_List"/>
        from <include refid="tableName"/>
        <where>
            `info_uuid` = #{infoUuid}

            <if test="actionTypes != null and actionTypes.size() > 0">
                and `action_type` in
                <foreach collection="actionTypes" item="actionType" open="(" separator="," close=")">
                    #{actionType}
                </foreach>
            </if>
            <if test="sources != null and sources.size() > 0">
                and `source` in
                <foreach collection="sources" item="source" open="(" separator="," close=")">
                    #{source}
                </foreach>
            </if>

            and is_delete = 0
        </where>
        order by id desc
    </select>


</mapper>
