<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.toG.GovCooperationMarkDao">

    <sql id="table_name">
        cf_gov_cooperation_mark
    </sql>

    <sql id="insert_fields">
        case_id, gov_code, mark_type, attributes
    </sql>

    <sql id="select_fields">
        id, case_id, gov_code, mark_type, attributes, create_time, update_time
    </sql>

    <insert id="add" parameterType="com.shuidihuzhu.client.cf.api.model.GovCooperationMark" useGeneratedKeys="true" keyProperty="id">
        insert into
        <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        VALUES (
            #{caseId}, #{govCode}, #{markType}, #{attributes}
        )
    </insert>

    <update id="update" parameterType="com.shuidihuzhu.client.cf.api.model.GovCooperationMark">
        update <include refid="table_name"/>
        set mark_type = #{markType}, attributes = #{attributes}
        where case_id = #{caseId} and gov_code = #{govCode}
    </update>

    <select id="selectByCaseIdAndGovCode" resultType="com.shuidihuzhu.client.cf.api.model.GovCooperationMark">
        SELECT 
        <include refid="select_fields"/>
        FROM <include refid="table_name"/>
        WHERE case_id = #{caseId} 
        AND gov_code = #{govCode}
        AND is_delete = 0
    </select>

    <select id="listByCaseIdsAndGovCode" resultType="com.shuidihuzhu.client.cf.api.model.GovCooperationMark">
        SELECT 
        <include refid="select_fields" />
        FROM <include refid="table_name"/>
        WHERE gov_code = #{govCode}
        AND case_id IN 
        <foreach collection="caseIdList" item="caseId" open="(" separator="," close=")">
            #{caseId}
        </foreach>
        AND is_delete = 0
    </select>

    <select id="listMarkRecordsByAllGov" resultType="com.shuidihuzhu.client.cf.api.model.GovCooperationMark">
        SELECT
        <include refid="select_fields" />
        FROM <include refid="table_name"/>
        WHERE case_id = #{caseId}
        AND is_delete = 0
    </select>

</mapper> 