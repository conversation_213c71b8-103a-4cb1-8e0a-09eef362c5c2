<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.toG.GuangzhouMarkRecordDAO">
    <sql id="table_name">
        cf_guang_zhou_mark_record
    </sql>
    <update id="updateGuangzhouLabel">
        update
        <include refid="table_name"/>
        <set>
            <if test="showStatusToC != null">
                show_status_to_c = #{showStatusToC},
            </if>
            <if test="reason != null and reason != ''">
                reason = #{reason},
            </if>
        </set>
        where `case_id` = #{caseId}
        and `is_delete` = 0
    </update>

    <update id="deleteByCaseId">
        update
        <include refid="table_name"/>
        set `is_delete` = 1
        where `case_id` = #{caseId}
    </update>

    <select id="selectByCaseId" resultType="com.shuidihuzhu.client.cf.api.model.GuangzhouMarkRecord">
        select *
        from
        <include refid="table_name"/>
        where `case_id` = #{caseId}
        and `is_delete` = 0
        limit 1
    </select>

    <select id="selectByInfoId" resultType="com.shuidihuzhu.client.cf.api.model.GuangzhouMarkRecord">
        select *
        from
        <include refid="table_name"/>
        where `info_id` = #{infoId}
        and `is_delete` = 0
        limit 1
    </select>

    <insert id="saveGuangzhouLabel" parameterType="com.shuidihuzhu.client.cf.api.model.GuangzhouMarkRecord">
        insert into
        <include refid="table_name"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="caseId != null">
                case_id,
            </if>
            <if test="infoId != null and infoId != ''">
                info_id,
            </if>
            <if test="showStatusToC != null">
                show_status_to_c,
            </if>
            <if test="reason != null and reason != ''">
                reason,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="caseId != null">
                #{caseId},
            </if>
            <if test="infoId != null and infoId != ''">
                #{infoId},
            </if>
            <if test="showStatusToC != null">
                #{showStatusToC},
            </if>
            <if test="reason != null and reason != ''">
                #{reason},
            </if>
        </trim>
    </insert>

</mapper>