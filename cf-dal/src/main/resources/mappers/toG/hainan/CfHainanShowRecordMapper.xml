<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.toG.hainan.CfHainanShowRecordDao">
    <sql id="table_name">
        cf_hainan_show_record
    </sql>

    <sql id="insert_fields">
        `case_id`, `info_id`, `cf_create_time`, `show_type`, `patient_name`, `encrypt_mobile`
    </sql>

    <sql id="select_fields">
        `id`, `case_id` , `info_id`, `cf_create_time`, `create_time`, `update_time`, `show_type`,  `first_approve_status`, `finish_status`, `patient_name`, `encrypt_mobile`
    </sql>

    <sql id="select_fields2">
        `info_id`, `cf_create_time`, `create_time`, `update_time`, `show_type`,  `first_approve_status`, `finish_status`, `patient_name`, `encrypt_mobile`
    </sql>

    <select id="selectByCaseId" resultType="com.shuidihuzhu.cf.domain.toG.hainan.CfHainanShowRecord">
        select
        <include refid="select_fields"/>
        from
        <include refid="table_name"/>
        where `case_id` = #{caseId}
        and `is_delete` = 0
        limit 1
    </select>


    <update id="updateFirstApproveStatus">
        update
        <include refid="table_name"/>
        set `first_approve_status` = #{firstApproveStatus}
        where `case_id` = #{caseId}
    </update>

    <update id="updateFinishStatus">
        update
        <include refid="table_name"/>
        set `finish_status` = #{finishStatus}
        where `case_id` = #{caseId}
    </update>

    <insert id="add" parameterType="com.shuidihuzhu.cf.domain.toG.hainan.CfHainanShowRecord">
        insert into
        <include refid="table_name"/>
        (<include refid="insert_fields"/>)
        values (#{caseId}, #{infoId}, #{cfCreateTime}, #{showType}, #{patientName}, #{encryptMobile})
    </insert>

    <select id="selectPage" resultType="com.shuidihuzhu.cf.domain.toG.hainan.CfHainanShowRecord">
        select
        <include refid="select_fields2"/>
        from
        <include refid="table_name"/>
        <where>
            `is_delete` = 0
            <if test="firstApproveStatus != null">
                and first_approve_status = #{firstApproveStatus}
            </if>
            <if test="finishStatus != null">
                and finish_status = #{finishStatus}
            </if>
        </where>
        order by cf_create_time desc
        limit #{page}, #{pageSize}
    </select>

    <select id="countByPage" resultType="java.lang.Integer">
        select
        count(*)
        from
        <include refid="table_name"/>
        <where>
            `is_delete` = 0
            <if test="firstApproveStatus != null">
                and first_approve_status = #{firstApproveStatus}
            </if>
            <if test="finishStatus != null">
                and finish_status = #{finishStatus}
            </if>
        </where>
    </select>

    <select id="listByParam" resultType="com.shuidihuzhu.cf.domain.toG.hainan.CfHainanShowRecord">
        select * from <include refid="table_name"/>
        <where>
            is_delete = 0
            <if test="anchor != 0">
                <if test="pre == true">
                    AND id  <![CDATA[ > ]]> #{anchor}
                </if>
                <if test="pre == false">
                    AND id  <![CDATA[ < ]]> #{anchor}
                </if>
            </if>
            <if test="infoUuid != null and infoUuid != ''">
                and info_id = #{infoUuid}
            </if>
            <if test="paientName != null and paientName != ''">
                and patient_name = #{paientName}
            </if>
            <if test="lauchPhone != null and lauchPhone != ''">
                and encrypt_mobile = #{encryptMobile}
            </if>
            <if test="lauchStartTime != null">
                and cf_create_time <![CDATA[ >= ]]> #{lauchStartTime}
            </if>
            <if test="lauchEndTime != null">
                and cf_create_time <![CDATA[ < ]]> #{lauchEndTime}
            </if>
        </where>
        <if test="pre == false">
            order by id desc
        </if>
        limit #{size}
    </select>

    <update id="removeByCaseId">
        update <include refid="table_name"/>
        set is_delete = 1
        where case_id = #{caseId}
    </update>

    <select id="getListByCaseIdList" resultType="com.shuidihuzhu.cf.domain.toG.hainan.CfHainanShowRecord">
        select * from <include refid="table_name"/>
        <where>
            case_id in <foreach collection="caseIdList" item="caseId" open="(" close=")" separator=",">
            #{caseId}
        </foreach>
        </where>
    </select>
</mapper>