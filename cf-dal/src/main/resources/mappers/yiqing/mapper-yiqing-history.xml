<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.yiqing.IYiqingHistoryDAO" >
    <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.domain.yiqing.YiqingHistory">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="day_value" property="dayValue" jdbcType="INTEGER"/>
        <result column="confirmed_count" property="confirmedCount" jdbcType="INTEGER"/>
        <result column="suspected_count" property="suspectedCount" jdbcType="INTEGER"/>
        <result column="death_count" property="deathCount" jdbcType="INTEGER"/>
        <result column="cured_count" property="curedCount" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="INSERT_Column_List">
         day_value,confirmed_count,suspected_count,death_count,cured_count
    </sql>

    <sql id="Base_Column_List">
         id,day_value,confirmed_count,suspected_count,death_count,cured_count
    </sql>

    <select id="getYesterDayAndToday" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from yiqing_history where
        `day_value` in (#{today}, #{yesterday})

    </select>
    <select id="getHistory" resultType="com.shuidihuzhu.cf.domain.yiqing.YiqingHistory">
        select <include refid="Base_Column_List"/>
        from yiqing_history where
        `day_value`  <![CDATA[ < ]]> #{today}
    </select>


    <insert id="replace" parameterType="com.shuidihuzhu.cf.domain.yiqing.YiqingHistory">

        replace into yiqing_history(<include refid="INSERT_Column_List" />)
        values(
        #{dayValue,jdbcType=INTEGER},
        #{confirmedCount,jdbcType=INTEGER},
        #{suspectedCount,jdbcType=INTEGER},
        #{deathCount,jdbcType=INTEGER},
        #{curedCount,jdbcType=INTEGER}
        )
    </insert>

</mapper>