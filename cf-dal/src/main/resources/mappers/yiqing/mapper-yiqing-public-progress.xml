<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.yiqing.IYiqingPublicProgressDAO" >
    <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.domain.yiqing.YiqingPublicProgress">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="progress_date" property="progressDate" jdbcType="VARCHAR"/>
        <result column="progress_time" property="progressTime" jdbcType="VARCHAR"/>
        <result column="progress_source" property="progressSource" jdbcType="VARCHAR"/>
        <result column="content" property="content" jdbcType="VARCHAR"/>
        <result column="has_vedio" property="hasVedio" jdbcType="INTEGER"/>
        <result column="vedio_url" property="vedioUrl" jdbcType="VARCHAR"/>
        <result column="operator_name" property="operatorName" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="INSERT_Column_List">
         id,progress_date,progress_time,progress_source,content,has_vedio,vedio_url,operator_name
    </sql>

    <sql id="UPDATE_Column_List">
         progress_date,progress_time,progress_source,content,has_vedio,vedio_url,operator_name
    </sql>

    <sql id="Base_Column_List">
         id,progress_date,progress_time,progress_source,content,has_vedio,vedio_url,operator_name,create_time,update_time
    </sql>

    <select id="getLatestOne" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from yiqing_pubic_progress where is_delete = 0 order by id desc limit 1
    </select>

    <select id="getAll" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from yiqing_pubic_progress where is_delete = 0 order by id desc
    </select>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.domain.yiqing.YiqingPublicProgress">
        <selectKey order="AFTER" resultType="long" keyProperty="id">
            select last_insert_id() as id
        </selectKey>
        insert into yiqing_pubic_progress(<include refid="INSERT_Column_List" />)
        values(
        #{id,jdbcType=INTEGER},
        #{progressDate,jdbcType=INTEGER},
        #{progressTime,jdbcType=INTEGER},
        #{progressSource,jdbcType=INTEGER},
        #{content,jdbcType=INTEGER},
        #{hasVedio,jdbcType=INTEGER},
        #{vedioUrl,jdbcType=INTEGER},
        #{operatorName,jdbcType=INTEGER}
        )
    </insert>

    <update id="update" parameterType="com.shuidihuzhu.cf.domain.yiqing.YiqingPublicProgress">
        update yiqing_pubic_progress
        set
        progress_date = #{progressDate,jdbcType=INTEGER},
        progress_time = #{progressTime,jdbcType=INTEGER},
        progress_source = #{progressSource,jdbcType=INTEGER},
        content = #{content,jdbcType=INTEGER},
        has_vedio = #{hasVedio,jdbcType=INTEGER},
        vedio_url = #{vedioUrl,jdbcType=INTEGER},
        operator_name = #{operatorName,jdbcType=INTEGER}

        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="delete">
        update yiqing_pubic_progress
        set is_delete = 1
        where id = #{id,jdbcType=INTEGER}
    </update>

</mapper>