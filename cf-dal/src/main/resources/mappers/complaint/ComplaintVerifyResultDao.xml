<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.complaint.ComplaintVerifyResultDao">
  <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.dao.crowdfunding.complaint.ComplaintVerifyResultDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_id" jdbcType="BIGINT" property="bizId" />
    <result column="biz_type" jdbcType="TINYINT" property="bizType" />
    <result column="is_check" jdbcType="TINYINT" property="isCheck" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="is_available" jdbcType="TINYINT" property="isAvailable" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, biz_id, biz_type, is_check, is_delete, is_available, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from complaint_verify_result
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.shuidihuzhu.cf.dao.crowdfunding.complaint.ComplaintVerifyResultDO" useGeneratedKeys="true">
    insert into complaint_verify_result (biz_id, biz_type, is_check, 
      is_delete, is_available, create_time, 
      update_time)
    values (#{bizId,jdbcType=BIGINT}, #{bizType,jdbcType=TINYINT}, #{isCheck,jdbcType=TINYINT}, 
      #{isDelete,jdbcType=TINYINT}, #{isAvailable,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.shuidihuzhu.cf.dao.crowdfunding.complaint.ComplaintVerifyResultDO" useGeneratedKeys="true">
    insert into complaint_verify_result
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bizId != null">
        biz_id,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="isCheck != null">
        is_check,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="isAvailable != null">
        is_available,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bizId != null">
        #{bizId,jdbcType=BIGINT},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=TINYINT},
      </if>
      <if test="isCheck != null">
        #{isCheck,jdbcType=TINYINT},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="isAvailable != null">
        #{isAvailable,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shuidihuzhu.cf.dao.crowdfunding.complaint.ComplaintVerifyResultDO">
    update complaint_verify_result
    <set>
      <if test="bizId != null">
        biz_id = #{bizId,jdbcType=BIGINT},
      </if>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=TINYINT},
      </if>
      <if test="isCheck != null">
        is_check = #{isCheck,jdbcType=TINYINT},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="isAvailable != null">
        is_available = #{isAvailable,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shuidihuzhu.cf.dao.crowdfunding.complaint.ComplaintVerifyResultDO">
    update complaint_verify_result
    set biz_id = #{bizId,jdbcType=BIGINT},
      biz_type = #{bizType,jdbcType=TINYINT},
      is_check = #{isCheck,jdbcType=TINYINT},
      is_delete = #{isDelete,jdbcType=TINYINT},
      is_available = #{isAvailable,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getByBizIdAndBizType"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from complaint_verify_result
    where is_delete =0
    and is_available = 1
    and biz_id = #{bizId}
    and biz_type = #{bizType}
  </select>

</mapper>