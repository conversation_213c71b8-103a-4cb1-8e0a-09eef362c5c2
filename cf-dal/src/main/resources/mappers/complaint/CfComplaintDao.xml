<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.complaint.CfComplaintDao">
  <resultMap id="BaseResultMap" type="com.shuidihuzhu.cf.dao.crowdfunding.complaint.CfComplaintDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="info_id" jdbcType="VARCHAR" property="infoId" />
    <result column="crowd_funding_info_id" jdbcType="VARCHAR" property="crowdFundingInfoId" />
    <result column="biz_id" jdbcType="BIGINT" property="bizId" />
    <result column="biz_type" jdbcType="TINYINT" property="bizType" />
    <result column="complaint_user_id" jdbcType="BIGINT" property="complaintUserId" />
    <result column="complaint_type" jdbcType="TINYINT" property="complaintType" />
    <result column="complaint_result" jdbcType="TINYINT" property="complaintResult" />
    <result column="work_order_id" jdbcType="BIGINT" property="workOrderId" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="is_available" jdbcType="TINYINT" property="isAvailable" />
    <result column="complaint_time" jdbcType="TIMESTAMP" property="complaintTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, info_id, crowd_funding_info_id, biz_id, biz_type, complaint_user_id,
    complaint_type, complaint_result, work_order_id, is_delete, is_available, complaint_time, 
    create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from cf_complaint
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.shuidihuzhu.cf.dao.crowdfunding.complaint.CfComplaintDO" useGeneratedKeys="true">
    insert into cf_complaint (info_id, crowd_funding_info_id, biz_id, 
      biz_type, complaint_user_id,
      complaint_type, complaint_result, work_order_id, 
      is_delete, is_available, complaint_time, 
      create_time, update_time)
    values (#{infoId,jdbcType=VARCHAR}, #{crowdFundingInfoId,jdbcType=VARCHAR}, #{bizId,jdbcType=BIGINT}, 
      #{bizType,jdbcType=TINYINT}, #{complaintUserId,jdbcType=BIGINT},
      #{complaintType,jdbcType=TINYINT}, #{complaintResult,jdbcType=TINYINT}, #{workOrderId,jdbcType=BIGINT}, 
      #{isDelete,jdbcType=TINYINT}, #{isAvailable,jdbcType=TINYINT}, #{complaintTime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.shuidihuzhu.cf.dao.crowdfunding.complaint.CfComplaintDO" useGeneratedKeys="true">
    insert into cf_complaint
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="infoId != null">
        info_id,
      </if>
      <if test="crowdFundingInfoId != null">
        crowd_funding_info_id,
      </if>
      <if test="bizId != null">
        biz_id,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="complaintUserId != null">
        complaint_user_id,
      </if>
      <if test="complaintType != null">
        complaint_type,
      </if>
      <if test="complaintResult != null">
        complaint_result,
      </if>
      <if test="workOrderId != null">
        work_order_id,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="isAvailable != null">
        is_available,
      </if>
      <if test="complaintTime != null">
        complaint_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="infoId != null">
        #{infoId,jdbcType=VARCHAR},
      </if>
      <if test="crowdFundingInfoId != null">
        #{crowdFundingInfoId,jdbcType=VARCHAR},
      </if>
      <if test="bizId != null">
        #{bizId,jdbcType=BIGINT},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=TINYINT},
      </if>
      <if test="complaintUserId != null">
        #{complaintUserId,jdbcType=BIGINT},
      </if>
      <if test="complaintType != null">
        #{complaintType,jdbcType=TINYINT},
      </if>
      <if test="complaintResult != null">
        #{complaintResult,jdbcType=TINYINT},
      </if>
      <if test="workOrderId != null">
        #{workOrderId,jdbcType=BIGINT},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="isAvailable != null">
        #{isAvailable,jdbcType=TINYINT},
      </if>
      <if test="complaintTime != null">
        #{complaintTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.shuidihuzhu.cf.dao.crowdfunding.complaint.CfComplaintDO">
    update cf_complaint
    <set>
      <if test="infoId != null">
        info_id = #{infoId,jdbcType=VARCHAR},
      </if>
      <if test="crowdFundingInfoId != null">
        crowd_funding_info_id = #{crowdFundingInfoId,jdbcType=VARCHAR},
      </if>
      <if test="bizId != null">
        biz_id = #{bizId,jdbcType=BIGINT},
      </if>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=TINYINT},
      </if>
      <if test="complaintUserId != null">
        complaint_user_id = #{complaintUserId,jdbcType=BIGINT},
      </if>
      <if test="complaintType != null">
        complaint_type = #{complaintType,jdbcType=TINYINT},
      </if>
      <if test="complaintResult != null">
        complaint_result = #{complaintResult,jdbcType=TINYINT},
      </if>
      <if test="workOrderId != null">
        work_order_id = #{workOrderId,jdbcType=BIGINT},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="isAvailable != null">
        is_available = #{isAvailable,jdbcType=TINYINT},
      </if>
      <if test="complaintTime != null">
        complaint_time = #{complaintTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.shuidihuzhu.cf.dao.crowdfunding.complaint.CfComplaintDO">
    update cf_complaint
    set info_id = #{infoId,jdbcType=VARCHAR},
      crowd_funding_info_id = #{crowdFundingInfoId,jdbcType=VARCHAR},
      biz_id = #{bizId,jdbcType=BIGINT},
      biz_type = #{bizType,jdbcType=TINYINT},
      complaint_user_id = #{complaintUserId,jdbcType=BIGINT},
      complaint_type = #{complaintType,jdbcType=TINYINT},
      complaint_result = #{complaintResult,jdbcType=TINYINT},
      work_order_id = #{workOrderId,jdbcType=BIGINT},
      is_delete = #{isDelete,jdbcType=TINYINT},
      is_available = #{isAvailable,jdbcType=TINYINT},
      complaint_time = #{complaintTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="listByCondition" resultType="com.shuidihuzhu.cf.dao.crowdfunding.complaint.CfComplaintDO">
    select
    <include refid="Base_Column_List" />
    from cf_complaint
    where is_delete = 0
    and is_available = 1
    and biz_id = #{bizId}
    and biz_type = #{bizType}
    and complaint_user_id = #{complaintUserId}
  </select>

  <select id="listByBizIdAndBizTypeAndComplaintResult" resultType="com.shuidihuzhu.cf.dao.crowdfunding.complaint.CfComplaintDO">
    select
    <include refid="Base_Column_List" />
    from cf_complaint
    where is_delete = 0
    and is_available = 1
    and biz_id = #{bizId}
    and biz_type = #{bizType}
    and complaint_result = #{complaintResult}
  </select>

  <select id="listByBizIdAndBizType" resultType="com.shuidihuzhu.cf.dao.crowdfunding.complaint.CfComplaintDO">
    select
    <include refid="Base_Column_List" />
    from cf_complaint
    where is_delete = 0
    and is_available = 1
    and biz_id = #{bizId}
    and biz_type = #{bizType}
  </select>

</mapper>