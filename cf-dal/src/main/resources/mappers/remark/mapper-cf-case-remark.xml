<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.remark.RemarkDAO">

    <sql id="tableName">
		cf_case_remark
	</sql>

    <sql id="Base_Column_List">
		id,
        create_time,
        update_time,
		case_id,
		remark_type,
		description,
		operator_id,
		stack_trace,
		ip,
		trace_id,
		content
	</sql>

    <sql id="insert_Column_List">
		case_id,
		remark_type,
		description,
		operator_id,
		stack_trace,
		ip,
		trace_id,
		content
	</sql>

    <select id="getById" resultType="com.shuidihuzhu.cf.domain.RemarkDO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="tableName"/>
        WHERE `id`=#{id}
        AND `is_delete` = 0
    </select>

    <select id="listByCaseId" resultType="com.shuidihuzhu.cf.domain.RemarkDO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="tableName"/>
        WHERE `case_id` = #{caseId}
        AND `is_delete` = 0
    </select>

    <select id="listByCaseIdAndRemarkTypes" resultType="com.shuidihuzhu.cf.domain.RemarkDO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="tableName"/>
        <where>
            `case_id` = #{caseId}
            <if test="remarkTypes != null and remarkTypes.size() > 0">
                and `remark_type` in
                <foreach collection="remarkTypes" item="type" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
            AND `is_delete` = 0
        </where>
    </select>

    <select id="getLastByCaseIdAndRemarkTypes" resultType="com.shuidihuzhu.cf.domain.RemarkDO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="tableName"/>
        <where>
            `case_id` = #{caseId}
            <if test="remarkTypes != null and remarkTypes.size() > 0">
                and `remark_type` in
                <foreach collection="remarkTypes" item="type" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
            AND `is_delete` = 0
        </where>
        order by id desc
        limit 1
    </select>

    <insert id="insert"
            parameterType="com.shuidihuzhu.cf.domain.RemarkDO">
        <selectKey order="AFTER" resultType="long" keyProperty="id">
            select last_insert_id() as id
        </selectKey>
        insert into <include refid="tableName"/>
        (<include refid="insert_Column_List" />)
        values(
        #{caseId},
        #{remarkType},
        #{description},
        #{operatorId},
        #{stackTrace},
        #{ip},
        #{traceId},
        #{content}
        )
    </insert>

</mapper>
