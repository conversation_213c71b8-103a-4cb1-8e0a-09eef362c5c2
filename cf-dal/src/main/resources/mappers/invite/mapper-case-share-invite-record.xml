<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.invite.CaseShareInviteRecordDAO">

    <sql id="tableName">
		case_share_invite_record
	</sql>

    <sql id="Base_Column_List">
		id,
        create_time,
        update_time,
		user_id,
		case_id,
		friend_user_id
	</sql>

    <sql id="insert_Column_List">
		user_id,
		case_id,
		friend_user_id
	</sql>

    <select id="getById" resultType="com.shuidihuzhu.cf.domain.ShareInviteRecordDO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="tableName"/>
        WHERE `id`=#{id}
        AND `is_delete` = 0
    </select>

    <select id="listByCondition" resultType="com.shuidihuzhu.cf.domain.ShareInviteRecordDO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="tableName"/>
        <where>
            `user_id` = #{userId}
            and `case_id` = #{caseId}
            and `create_time` between #{startTime} and #{endTime}
            AND `is_delete` = 0
        </where>
    </select>

    <insert id="insert"
            parameterType="com.shuidihuzhu.cf.domain.ShareInviteRecordDO">
        <selectKey order="AFTER" resultType="long" keyProperty="id">
            select last_insert_id() as id
        </selectKey>
        insert into <include refid="tableName"/>
        (<include refid="insert_Column_List" />)
        values(
        #{userId},
        #{caseId},
        #{friendUserId}
        )
    </insert>

    <insert id="insertFriendUserIds" >
        <selectKey order="AFTER" resultType="long" keyProperty="id">
            select last_insert_id() as id
        </selectKey>
        insert into <include refid="tableName"/>
        (<include refid="insert_Column_List" />)
        values
        <foreach collection="friendUserIds" item="friendUserId" separator="," >
            (
            #{userId},
            #{caseId},
            #{friendUserId}
            )
        </foreach>
    </insert>

</mapper>
