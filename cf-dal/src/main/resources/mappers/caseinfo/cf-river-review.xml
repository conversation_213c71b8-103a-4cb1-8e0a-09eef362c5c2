<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.caseinfo.RiverReviewDAO">

    <sql id="tableName">
		cf_river_review
	</sql>

    <sql id="insert_Column_List">
        `case_id`,
        `usage_type`,
        `info_status`,
        `reject_detail`
	</sql>

    <sql id="Base_Column_List">
        `id`,`update_time`,
        <include refid="insert_Column_List"/>
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        insert into
        <include refid="tableName"/>
        (<include refid="insert_Column_List"/>)
        values (
        #{caseId},
        #{usageType},
        #{infoStatus},
        #{rejectDetail}
        )
    </insert>

    <select id="getLastByCaseIdAndUsageType" resultType="com.shuidihuzhu.cf.model.river.RiverReviewDO">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="tableName"/>
        <where>
            `case_id` = #{caseId}
            and `usage_type` = #{usageType}
        </where>
        order by id desc
        limit 1
    </select>

    <update id="updateById">
        update
        <include refid="tableName"/>
        <set>
            <if test="status > 0">
                `info_status` = #{status},
            </if>
            <if test="rejectDetail != null">
                `reject_detail` = #{rejectDetail},
            </if>
        </set>
        where `id` = #{id}
    </update>

    <select id="getById" resultType="com.shuidihuzhu.cf.model.river.RiverReviewDO">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="tableName"/>
        <where>
            `id` = #{id}
        </where>
    </select>

    <select id="getByCaseIdsAndUsageType" resultType="com.shuidihuzhu.cf.model.river.RiverReviewDO">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="tableName"/>
        <where>
            `case_id` in
            <foreach collection="caseIds" item="caseId" open="(" separator="," close=")">
                #{caseId}
            </foreach>
            and `usage_type` = #{usageType}
        </where>
    </select>

    <select id="getByCaseIdAndUsageType" resultType="com.shuidihuzhu.cf.model.river.RiverReviewDO">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="tableName"/>
        where `case_id` = #{caseId}
        and `usage_type` = #{usageType}
        limit 1
    </select>
</mapper>
