<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.caseinfo.CaseEndRecordDAO">

    <sql id="tableName">
		cf_case_end_record
	</sql>

    <sql id="insert_Column_List">
        `case_id`,
        `finish_status`,
        `description`,
        `operator_id`,
        `operator_name`,
        `reason_type`
	</sql>

    <sql id="Base_Column_List">
        `id`,
        `create_time`,
        <include refid="insert_Column_List"/>
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.domain.caseinfo.CaseEndRecordDO" useGeneratedKeys="true" keyProperty="id">
        insert into
        <include refid="tableName"/>
        (<include refid="insert_Column_List"/>)
        values (
        #{caseId} ,
        #{finishStatus}  ,
        #{description}  ,
        #{operatorId}  ,
        #{operatorName},
        #{reasonType}
        )
    </insert>

    <select id="getLastByCaseId" resultType="com.shuidihuzhu.cf.domain.caseinfo.CaseEndRecordDO">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="tableName"/>
        where `case_id` = #{caseId}
        and `is_delete` = 0
        order by id desc
        limit 1
    </select>

</mapper>
