<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.caseinfo.CaseInfoApproveStageDAO">

    <sql id="tableName">
		cf_case_info_approve_stage
	</sql>

    <sql id="insert_Column_List">
        `case_id`,
        `title`,
        `content`,
        `images`
	</sql>

    <sql id="Base_Column_List">
        `id`,
        <include refid="insert_Column_List"/>
    </sql>

    <insert id="insert">
        insert into <include refid="tableName"/>
        (<include refid="insert_Column_List"/>)
        values (
        #{caseId},
        #{title},
        #{content},
        #{images}
        )
    </insert>

    <update id="update">
        update <include refid="tableName"/>
        <set>
            <if test="title != null">
                ,`title` = #{title}
            </if>
            <if test="content != null">
                ,`content` = #{content}
            </if>
            <if test="images != null">
                ,`images` = #{images}
            </if>
        </set>
        <where>
            `case_id` = #{caseId}
        </where>
    </update>

    <select id="getByCaseId" resultType="com.shuidihuzhu.cf.domain.caseinfo.CaseInfoApproveStageDO">
        select <include refid="Base_Column_List"/>
        from <include refid="tableName"/>
        <where>
            `case_id` = #{caseId}
        </where>
    </select>

    <select id="getByCaseIdBatch" resultType="com.shuidihuzhu.cf.domain.caseinfo.CaseInfoApproveStageDO">
        select <include refid="Base_Column_List"/>
        from <include refid="tableName"/>
        <where>
            `case_id` in
            <foreach collection="caseIdList" item="caseId" open="(" close=")" separator=",">
                #{caseId}
            </foreach>
        </where>
    </select>


</mapper>
