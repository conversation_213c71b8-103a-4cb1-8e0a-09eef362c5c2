<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.shuidihuzhu.cf.dao.caseinfo.InfoCapitalCompensationDAO">

    <sql id="tableName">
		cf_info_capital_compensation
	</sql>

    <sql id="insert_Column_List">
        `case_id`,
        `status`,
        `other_org_help_have`,
        `other_org_help`,

        `other_platform_fundraising_have`,
        `other_platform_fundraising`,

        `other_social_gift_have`,
        `other_social_gift`,

        `treatment_arrears_have`,
        `treatment_arrears`,

        `own_treatment_fee_have`,
        `own_treatment_fee`,
        `images`
	</sql>

    <sql id="Base_Column_List">
        `id`,
        <include refid="insert_Column_List"/>
    </sql>

    <insert id="insert" parameterType="com.shuidihuzhu.cf.domain.caseinfo.CfInfoCapitalCompensationDO" useGeneratedKeys="true" keyProperty="id">
        insert into
        <include refid="tableName"/>
        (<include refid="insert_Column_List"/>)
        values (
        #{caseId} ,
        #{status} ,
        #{otherOrgHelpHave} ,
        #{otherOrgHelp} ,
        #{otherPlatformFundraisingHave} ,
        #{otherPlatformFundraising} ,
        #{otherSocialGiftHave} ,
        #{otherSocialGift} ,
        #{treatmentArrearsHave} ,
        #{treatmentArrears} ,
        #{ownTreatmentFeeHave} ,
        #{ownTreatmentFee} ,
        #{images}
        )
    </insert>

    <update id="updateByCaseId">
      update <include refid="tableName"/>
      <set>
          `status` = #{status} ,
          `other_org_help_have` = #{otherOrgHelpHave} ,
          `other_org_help` = #{otherOrgHelp} ,

          `other_platform_fundraising_have` = #{otherPlatformFundraisingHave} ,
          `other_platform_fundraising` = #{otherPlatformFundraising} ,

          `other_social_gift_have` = #{otherSocialGiftHave} ,
          `other_social_gift` = #{otherSocialGift} ,

          `treatment_arrears_have` = #{treatmentArrearsHave} ,
          `treatment_arrears` = #{treatmentArrears} ,

          `own_treatment_fee_have` = #{ownTreatmentFeeHave} ,
          `own_treatment_fee` = #{ownTreatmentFee} ,
          `images` = #{images},
      </set>
      <where>
          `case_id` = #{caseId}
          and is_delete = 0
      </where>
    </update>

    <update id="updateStatusByCaseId">
      update <include refid="tableName"/>
      <set>
          `status` = #{status}
      </set>
      <where>
          `case_id` = #{caseId}
          and `is_delete` = 0
      </where>
    </update>

    <select id="getByCaseId" resultType="com.shuidihuzhu.cf.domain.caseinfo.CfInfoCapitalCompensationDO">
        select
        <include refid="Base_Column_List"/>
        from
        <include refid="tableName"/>
        where `case_id` = #{caseId}
        and `is_delete` = 0
    </select>

</mapper>
