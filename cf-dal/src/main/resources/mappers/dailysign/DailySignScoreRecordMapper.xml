<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.dailysign.DailySignScoreRecordDao">
	<sql id="TABLE_NAME">
		cf_daily_sign_score_record
	</sql>

	<sql id="FIELDS">
		`id` as id,
		`sign_id` as signId,
		`user_id` as userId,
		`points` as points,
		`confirmed` as confirmed,
		`channel` as channel,
		`user_third_type` as userThirdType,
		`out_trade_no` as outTradeNo,
		`trade_no` as tradeNo,
		`action`
	</sql>

	<insert id="insert" parameterType="com.shuidihuzhu.cf.model.dailysign.DailySignScoreRecord" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO
		<include refid="TABLE_NAME"/>
		(`sign_id`, `points`, `user_id`, `channel`, `user_third_type`, `out_trade_no`, `action`, `remark`)
		VALUES
		(#{signId}, #{points}, #{userId}, #{channel}, #{userThirdType}, #{outTradeNo}, #{action}, #{remark})
	</insert>

	<update id="updateConfirm" >
		UPDATE
		<include refid="TABLE_NAME"/>
		SET
		`confirmed`=1, `trade_no`=#{tradeNo}
		WHERE
		`id`=#{id}
		AND
		`confirmed`=0
		AND
		`is_delete`=0
	</update>

	<select id="getBySignId" resultType="com.shuidihuzhu.cf.model.dailysign.DailySignScoreRecord">
		SELECT
		<include refid="FIELDS"/>
		FROM
		<include refid="TABLE_NAME"/>
		WHERE
		`sign_id`=#{signId}
		AND
		`is_delete`=0
		LIMIT 1
	</select>

</mapper>