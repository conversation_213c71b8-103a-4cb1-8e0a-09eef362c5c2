<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.dailysign.DailySignUserStatsDao">
	<sql id="TABLE_NAME">
		cf_daily_sign_user_stats
	</sql>

	<sql id="FIELDS">
		`id` as id,
		`user_id` as userId,
		`points` as points,
		`sign_count` as signCount
	</sql>

	<insert id="insertOrUpdate" parameterType="com.shuidihuzhu.cf.model.dailysign.DailySignUserStat" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO
		<include refid="TABLE_NAME"/>
		(`user_id`, `points`, `sign_count`)
		VALUES
		(#{userId}, #{points}, #{signCount})
		ON DUPLICATE KEY UPDATE
		`points`=`points` + #{points},
		`sign_count`=`sign_count`+1
	</insert>

	<select id="getByUserId" resultType="com.shuidihuzhu.cf.model.dailysign.DailySignUserStat">
		SELECT <include refid="FIELDS"/>
		FROM <include refid="TABLE_NAME" />
		WHERE `user_id`=#{userId}
		AND `is_delete`=0
		LIMIT 1
	</select>

</mapper>