<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.dailysign.DailySignDao">
	<sql id="TABLE_NAME">
		cf_daily_sign
	</sql>

	<sql id="FIELDS">
		`id` as id,
		`user_id` as userId,
		`day_key` as dayKey,
		`points` as points,
		`create_time` as createTime
	</sql>

	<insert id="insert" parameterType="com.shuidihuzhu.cf.model.dailysign.DailySign" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO
		<include refid="TABLE_NAME"/>
		(`user_id`, `day_key`, `points`)
		VALUES
		(#{userId}, #{dayKey}, #{points})
	</insert>

	<update id="insertUpdate" parameterType="com.shuidihuzhu.cf.model.dailysign.DailySign" useGeneratedKeys="true" keyProperty="id">
		insert into <include refid="TABLE_NAME"/>
		(`user_id`, `day_key`, `points`)
		VALUES
		(#{userId}, #{dayKey}, #{points})
		on duplicate key update
		user_id = values(user_id),
		day_key = values(day_key),
		points = values(points)
	</update>

	<select id="getByUserIdAndDayKey" resultType="com.shuidihuzhu.cf.model.dailysign.DailySign">
		SELECT <include refid="FIELDS"/>
		FROM <include refid="TABLE_NAME"/>
		WHERE `user_id`=#{userId}
		AND `day_key`=#{dayKey}
		AND `is_delete`=0
		LIMIT 1
	</select>

	<select id="getListByUserId" resultType="com.shuidihuzhu.cf.model.dailysign.DailySign">
		SELECT <include refid="FIELDS"/>
		FROM <include refid="TABLE_NAME"/>
		WHERE `user_id`=#{userId}
		AND `id` <![CDATA[ <= ]]> #{anchorId}
		AND `is_delete`=0
		ORDER BY `id` DESC
		LIMIT #{limit}
	</select>

</mapper>