<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.baike.BaikeWordDao">
	<sql id="tableName">
		cf_bk_word
	</sql>

	<sql id="fields">
		`id` as id,
		`word` as word,
		`content` as content,
		`create_time` as createTime
	</sql>

	<select id="findByWord" resultType="com.shuidihuzhu.cf.model.baike.BaikeWord">
		SELECT <include refid="fields"/>
		FROM <include refid="tableName"/>
		WHERE `word`=#{word}
		AND `is_delete`=0
		LIMIT 1
	</select>

</mapper>