<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.questionnaire.WxQuestionnaireDao">

    <sql id="TABLE_NAME">
      `wx_questionnaire`
    </sql>

    <sql id="FIELDS">
      `id`,
      `name`,
      `title`
    </sql>

    <select id="getById" resultType="com.shuidihuzhu.cf.vo.questionnaire.WxQuestionnaireVo">
        SELECT <include refid="FIELDS" /> FROM <include refid="TABLE_NAME" />
        WHERE `is_delete` =0
        AND `id` = #{id}
    </select>


</mapper>