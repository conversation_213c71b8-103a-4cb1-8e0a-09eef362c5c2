<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.questionnaire.WxQuestionDao">

    <sql id="TABLE_NAME">
      `wx_question`
    </sql>

    <sql id="FIELDS">
      `id` AS qId,
      `qnr_id`,
      `tag_code`,
      `tag_name`,
      `content`,
      `reply_type`,
      `option_value` as `option`,
      `min_value`,
      `max_value`,
      `remark`,
      `sequence`
    </sql>

    <select id="getByQnrId" resultType="com.shuidihuzhu.cf.vo.questionnaire.WxQuestionVo">
        SELECT <include refid="FIELDS" />
        FROM <include refid="TABLE_NAME" />
        WHERE `qnr_id` =#{qnrId} AND `is_delete` =0
    </select>
    <select id="getById" resultType="com.shuidihuzhu.cf.vo.questionnaire.WxQuestionVo">
        SELECT <include refid="FIELDS" />
        FROM <include refid="TABLE_NAME" />
        WHERE `id` =#{qId} AND `is_delete` =0
    </select>

</mapper>