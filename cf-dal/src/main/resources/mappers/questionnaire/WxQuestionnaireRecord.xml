<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shuidihuzhu.cf.dao.questionnaire.WxQuestionnaireRecordDao">

    <sql id="TABLE_NAME">
      `wx_questionnaire_record`
    </sql>

    <sql id="FIELDS">
      `qnr_id`,
      `q_id`,
      `user_id`,
      `reply_type`,
      `label_value` as `label`,
      `tag_code`,
      `tag_value`,
      `create_time`,
      `update_time`
    </sql>

    <insert id="batchAdd" parameterType="java.util.List">
        INSERT INTO
        <include refid="TABLE_NAME"/>
        (`qnr_id`, `q_id`, `user_id`, `reply_type`, `label_value`, `tag_code`, `tag_value`)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.qnrId}, #{item.qId}, #{item.userId}, #{item.replyType}, #{item.label}, #{item.tagCode}, #{item.tagValue})
        </foreach>
    </insert>
</mapper>