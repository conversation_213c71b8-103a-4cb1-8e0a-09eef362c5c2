<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.shuidihuzhu.cf.dao.deposit.CfPayeeParamRecordDao">
	
	<sql id="table_name">
		`cf_payee_param_record`
	</sql>
	
	<insert id="insertPayeeParamRecord" parameterType="com.shuidihuzhu.cf.model.deposit.CfPayeeParamRecord">
		insert into
		<include refid="table_name"/>
		(`case_id`,`info_uuid`,`name`,`encrypt_id_card`,`encrypt_bank_card_no`,`encrypt_reserved_mobile`,`bank_name`,
		`bank_branch_name`,`cnaps_branch_id`,`source_channel`,`ip`)
		values
		(#{caseId},#{infoUuid},#{name},#{encryptIdCard},#{encryptBankCardNo},#{encryptReservedMobile},#{bankName},
		#{bankBranchName},#{cnapsBranchId},#{sourceChannel},#{ip});
	</insert>
	<select id="getByFourElementsLastOne" resultType="com.shuidihuzhu.cf.model.deposit.CfPayeeParamRecord">
		select * from
		<include refid="table_name"/>
		where `case_id` = #{caseId} and `name` = #{name}
		and`encrypt_id_card` = #{encryptIdCard} and`encrypt_bank_card_no` = #{encryptBankCardNo}
		order by id desc
		limit 1
	</select>

	<select id="getCfPayeeParamRecord" resultType="com.shuidihuzhu.cf.model.deposit.CfPayeeParamRecord">
		select * from
		<include refid="table_name"/>
		where `case_id` = #{caseId}
		order by id desc
	</select>
</mapper>