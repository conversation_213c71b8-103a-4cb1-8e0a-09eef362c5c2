package com.shuidihuzhu.cf.dao.crowdfunding.casematerial;

import com.shuidihuzhu.cf.model.crowdfunding.casematerial.CfQuestionsAnswers;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2018/11/13 13:16
 */
@DataSource(DS.CF_SLAVE)
public interface CfQuestionsAnswersDao {
    List<CfQuestionsAnswers> getByIds(@Param("idSet") Set<Integer> idSet);
}
