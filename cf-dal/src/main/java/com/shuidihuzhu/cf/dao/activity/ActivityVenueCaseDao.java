package com.shuidihuzhu.cf.dao.activity;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.model.activity.ActivityVenueCaseModel;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by dongcf on 2020/3/26
 */
@DataSource(CfDataSource.CF_USER_INFO_SHARDING_CROWDFUNDING)
public interface ActivityVenueCaseDao {

    List<ActivityVenueCaseModel> findByPoolType(@Param("activityId") int activityId, @Param("poolType") int poolType);

    List<ActivityVenueCaseModel> findByPoolTypes(@Param("activityId") int activityId, @Param("poolTypes") List<Integer> poolTypes);

    List<ActivityVenueCaseModel> listByCaseId(@Param("caseId") int caseId);

    int updateVenueDonateByCaseId(@Param("venueDonate") int venueDonate, @Param("activityId") int activityId, @Param("caseId") int caseId);

    int updateVenueDonateOutByCaseId(@Param("venueDonateOut") int venueDonate, @Param("activityId") int activityId, @Param("caseId") int caseId);

    int updateCooperateDonateByCaseId(@Param("cooperateDonate") int venueDonate, @Param("activityId") int activityId, @Param("caseId") int caseId);

    ActivityVenueCaseModel getByCaseId(@Param("activityId") int activityId, @Param("caseId") int caseId);

    int updateById(ActivityVenueCaseModel data);

    int deleteById(@Param("id") long id);

    int insert(ActivityVenueCaseModel data);


    @DataSource(CfDataSource.CF_USER_INFO_SHARDING_CROWDFUNDING_SLAVE)
    ActivityVenueCaseModel getMinVenueDonate(@Param("activityId") int activityId);

    List<ActivityVenueCaseModel> listByCaseTypes(@Param("activityId") int activityId,
                                                 @Param("caseTypes") List<Integer> caseTypes,
                                                 @Param("current") long current,
                                                 @Param("pageSize") int pageSize
    );

    List<ActivityVenueCaseModel> findByCaseId(@Param("caseId") int caseId);

    ActivityVenueCaseModel getByInfoId(@Param("infoId") String infoId);
}
