package com.shuidihuzhu.cf.dao.crowdfunding.user;

import com.shuidihuzhu.cf.domain.activity.ActivityVenueUserDonateDO;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * @author: fengxuan
 * @create 2020-02-20 16:33
 **/
@DataSource("cfUserDataSource")
public interface ActivityVenueUserDonateDao {

    int add(ActivityVenueUserDonateDO donateDO);

    int updatePayStatus(@Param("orderId") long orderId, @Param("payStatus") int payStatus, @Param("anonymous") int anonymous);

    List<ActivityVenueUserDonateDO> listPaySucUserDonate(@Param("userId") long userId);

    @DataSource("cfUserDataSourceSlave")
    ActivityVenueUserDonateDO getByOrderId(@Param("orderId") long orderId);

    @DataSource("cfUserDataSourceSlave")
    List<ActivityVenueUserDonateDO> listLastDonate(@Param("limit") int limit,
                                                   @Param("startTime") Date startTime,
                                                   @Param("endTime") Date endTime,
                                                   @Param("activityId") long activityId);

    int countDonateByUserIdAndActivityId(@Param("userId") long userId, @Param("activityId") int activityId);

    List<ActivityVenueUserDonateDO> listPaySucUserDonateByActivityId(@Param("userId") long userId, @Param("activityId") int activityId);
}
