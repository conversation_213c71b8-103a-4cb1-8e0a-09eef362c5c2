package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.EvaluateLabelUserRecordDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.CF)
public interface EvaluateLabelUserRecordDao {

    void insertBatch(@Param("labels")List<EvaluateLabelUserRecordDO> labels);

}
