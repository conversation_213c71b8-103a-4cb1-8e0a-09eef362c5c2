package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfConfirmBlacklist;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;


/**
 * <AUTHOR>
 */
@DataSource(DS.CF)
public interface CfConfirmBlacklistDao {

    int saveCfConfirmBlacklist(@Param("userId") long userId, @Param("valid") int valid, @Param("blacklistTime") Timestamp blacklistTime);

    int updateValid(@Param("userId") long userId);

    int updateBlacklistTime(@Param("userId") long userId, @Param("blacklistTime") Timestamp blacklistTime);

    @DataSource(DS.CF_SLAVE)
    CfConfirmBlacklist getMsgByUserId(@Param("userId") long userId);

    @DataSource(DS.CF_SLAVE)
	List<CfConfirmBlacklist> getConfirmBlacklist();

    @DataSource(DS.CF_SLAVE)
    List<CfConfirmBlacklist> getConfirmBlacklistByUserIds(@Param("userIds") List<Long> userIds);


}
