package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoTag;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by wangsf on 17/1/16.
 */
@DataSource(DS.CF_SLAVE)
public interface CrowdfundingInfoTagSlaveDao {

    /**
     * @author: wanghui
     * @time: 2018/10/31 11:26 AM
     * @description: getTagsByInfoId 根据案例的uuid 获得 CrowdfundingInfoTag 标识数据
     * @param: [infoId]
     * @return: com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoTag
     */
    CrowdfundingInfoTag getTagsByInfoId(@Param("infoId") String infoId);

    List<CrowdfundingInfoTag> getAllCaseTags();
}
