package com.shuidihuzhu.cf.dao.publictrust;


import com.shuidihuzhu.cf.model.publictrust.CfPublicTrustPromotionStatisticsDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

@DataSource(DS.CF)
public interface CfPublicTrustPromotionStatisticsDao {

    int insert(CfPublicTrustPromotionStatisticsDO record);

    int insertSelective(CfPublicTrustPromotionStatisticsDO record);

    CfPublicTrustPromotionStatisticsDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CfPublicTrustPromotionStatisticsDO record);

    int updateByPrimaryKey(CfPublicTrustPromotionStatisticsDO record);

    CfPublicTrustPromotionStatisticsDO getByUserIdAndIsClickAndType(@Param("userId") long userId,
                                                                    @Param("isClick") int isClick,
                                                                    @Param("type") int type);
}