package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.vo.CfLoveBroadcastVO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@DataSource(DS.CF)
public interface CfBroadCastDao {
	int add(@Param("data") CfLoveBroadcastVO cfLoveBroadcastVO);

	void addBatch(@Param("dataSet") List<CfLoveBroadcastVO> cfLoveBroadcastVOS);

	@DataSource(DS.CF_SLAVE)
	List<CfLoveBroadcastVO> getByInfoId(@Param("infoId") String infoId, @Param("size") int size);

	@DataSource(DS.CF_SLAVE)
	CfLoveBroadcastVO getByOrderId(@Param("orderId") long orderId);

	@DataSource(DS.CF_SLAVE)
	CfLoveBroadcastVO getByOrderIdBatch(@Param("orderIds") Set<Long> orderIds);

	void update(@Param("data") CfLoveBroadcastVO cfLoveBroadcastVO);
}
