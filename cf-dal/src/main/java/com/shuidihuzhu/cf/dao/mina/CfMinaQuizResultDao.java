package com.shuidihuzhu.cf.dao.mina;

import com.shuidihuzhu.cf.model.mina.CfMinaQuizResult;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by dongcf on 2017/12/12
 */
@DataSource(DS.FRAME)
public interface CfMinaQuizResultDao {

    @DataSource(DS.FRAME_SLAVE)
    CfMinaQuizResult getByQuizIdAndScore(@Param("minaQuizId") Integer minaQuizId, @Param("score") Integer score);

    Integer insert(CfMinaQuizResult cfMinaQuizResult);

    @DataSource(DS.FRAME_SLAVE)
    List<CfMinaQuizResult> findByMinaQuizId(@Param("minaQuizId") Integer minaQuizId);

    Integer updateDeleteByMinaQuizId(@Param("isDelete") Integer isDelete, @Param("minaQuizId") Integer minaQuizId);
}
