package com.shuidihuzhu.cf.dao.crowdfunding;


import com.shuidihuzhu.cf.model.crowdfunding.AdminCfReportRiskTagLabel;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <AUTHOR>
 */
@DataSource(DS.CF)
public interface AdminCfReportRiskTagLabelDao {

    AdminCfReportRiskTagLabel getLastByCaseId(@Param("caseId") int caseId);
}
