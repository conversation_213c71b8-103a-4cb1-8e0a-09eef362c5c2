package com.shuidihuzhu.cf.dao.topList;


import com.shuidihuzhu.cf.model.topList.CfTopListLifeCycleRecord;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource("cfUserDataSource")
public interface CfVerificationTopListLifeCycleRecordDao {


    int insertLifeCycleRecordList(@Param("list") List<CfTopListLifeCycleRecord> records);


    int updatePrizeTime(@Param("ids") List<Long> ids,
                        @Param("userPrizeTime") String time, @Param("prizeChannel")String prizeChannel);

    int updateIfPrize(@Param("ids") List<Long> ids);

    @DataSource("cfUserDataSourceSlave")
    CfTopListLifeCycleRecord selectLastByCaseIdAndUserId(@Param("caseId") int caseId, @Param("userId") long userId);

}
