package com.shuidihuzhu.cf.dao.ai;

import com.shuidihuzhu.cf.model.ai.CfAiIdCardResult;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2022/12/21 15:44
 * @Description:
 */
@DataSource(DS.CF)
public interface CfAiIdCardResultDAO {

    int insert(CfAiIdCardResult cfAiIdCardResult);

    List<CfAiIdCardResult> getLastByCaseId(@Param("caseId") int caseId);
}
