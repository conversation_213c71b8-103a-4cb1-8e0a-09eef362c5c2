package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@DataSource(DS.CF)
public interface CfVerificationDao {

    @DataSource(DS.STAT_CF)
    List<CrowdFundingVerification> getByPatientUserIds(@Param("patientUserids") Set<Long> patientUserIds);

    @DataSource(DS.CF)
    void updatePatientUserId(@Param("id") long id,
                             @Param("patientUserId") long patientUserId);

}
