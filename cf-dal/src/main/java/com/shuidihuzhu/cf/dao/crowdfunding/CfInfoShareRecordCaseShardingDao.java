package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareRecordCountGroupByUserIdModel;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareRecordCountModel;
import com.shuidihuzhu.cf.model.crowdfunding.dto.CfSharedUserDto;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Set;


/**
 * @author: wanghui
 * @time: 2019/7/8 3:47 PM
 * @description:
 * @param:  * @param null :
 * @return:  * @return : null
 */
@DataSource(CfDataSource.SHUIDI_CROWDFUNDING_SHARE_SLAVE)
public interface CfInfoShareRecordCaseShardingDao {

	@DataSource(CfDataSource.SHUIDI_CROWDFUNDING_SHARE_MASTER)
	int add(@Param("record") CfInfoShareRecord cfInfoShareRecord, @Param("sharding") String sharding);

	int countShareByUserIdAndInfoId(@Param("infoId") Integer infoId, @Param("userId") long userId, @Param("sharding") String sharding);

	int countShareByUserIdAndInfoIdWithDate(@Param("infoId") Integer infoId,
											@Param("userId") long userId,
											@Param("beginTime") Date beginTime,
											@Param("endTime") Date endTime,
											@Param("sharding") String sharding);

	int countShareByUserIdWithSourceType(@Param("infoId") Integer infoId,
										 @Param("userId") long userId,
										 @Param("beginTime") Date beginTime,
										 @Param("endTime") Date endTime,
										 @Param("sharding") String sharding,
										 @Param("toWxSource") int toWxSource);

	List<CfInfoShareRecordCountGroupByUserIdModel> countShareByInfoIdAndUserIds(@Param("infoId") int infoId,
																				@Param("userIds") Set<Long> userIds, @Param("sharding") String sharding);

	int getShareCountMin(@Param("begin") Timestamp begin, @Param("end") Timestamp end, @Param("sharding") String sharding);

	CfInfoShareRecord findByUuidAndInfoId(@Param("uuid") String uuid, @Param("infoId") int infoId, @Param("sharding") String sharding);


	List<CfInfoShareRecordCountModel> getCfInfoShareRecordCountModelByInfoIdsAndTime(@Param("list") List<Integer> cfInfoIdList,
                                                                                     @Param("start") Date startTime,
                                                                                     @Param("end") Date endTime, @Param("sharding") String sharding);

	List<CfInfoShareRecord> getShareRecordList(@Param("list") List<Integer> cfInfoIdList,
                                               @Param("start") Date startTime,
                                               @Param("end") Date endTime,
                                               @Param("offset") int offset,
                                               @Param("limit") int limit, @Param("sharding") String sharding);

	List<CfInfoShareRecord> getByInfoId(@Param("infoId") int infoId,
				@Param("start") Date startTime,
				@Param("end") Date endTime,
				@Param("offset") int offset,
				@Param("limit") int limit, @Param("sharding") String sharding);

	List<CfInfoShareRecord> getListByInfoId(@Param("infoId") int infoId,
                                            @Param("anchorId") long anchorId,
                                            @Param("limit") int limit, @Param("sharding") String sharding);

	CfInfoShareRecord findLastByUserIdAndInfoId(@Param("userId") long userId, @Param("infoId") int infoId,
												@Param("sharding") String sharding);

	List<CfInfoShareRecord> selectByUserAndCase(@Param("userId") long userId,
												@Param("infoId") int infoId, @Param("sharding") String sharding);

	List<CfInfoShareRecord> findLastByUserIdListAndInfoId(@Param("userIdList") List<Long> userIdList,
                                                          @Param("infoId") int infoId, @Param("sharding") String sharding);

	int getCountByUseridAndInfoId(@Param("userId") long userId, @Param("infoId") int infoId, @Param("sharding") String sharding);

	List<CfInfoShareRecordCountModel> getShareCount(@Param("infoIds") List<Integer> infoIds, @Param("sharding") String sharding);

	int getCountByInfoIdWithDateCretead(@Param("infoId") int infoId,
                                        @Param("endTime") Date endTime, @Param("sharding") String sharding);

	CfInfoShareRecord getFirstByInfoId(@Param("infoId")int infoId, @Param("sharding") String sharding);

	List<CfInfoShareRecord> getLatelyShareByInfoId(@Param("infoId") int infoId, @Param("limit") int limit, @Param("sharding") String sharding);

	List<CfInfoShareRecord> getListByQuasiMaxId(@Param("id") long id, @Param("limit") int limit, @Param("sharding") String sharding);


	List<CfInfoShareRecord> getListByInfoIdAndUserIds(@Param("infoId") int infoId, @Param("userIds") List<Long> userIds, @Param("sharding") String sharding);

	List<CfInfoShareRecord> getListDescByInfoIdAndUserIds(@Param("infoId") int infoId, @Param("userIds") List<Long> userIds, @Param("sharding") String sharding);

	List<CfSharedUserDto> getSharedUserByInfoId(@Param("infoId") int infoId,@Param("offset") int offset, @Param("limit") int limit, @Param("sharding") String sharding);
}
