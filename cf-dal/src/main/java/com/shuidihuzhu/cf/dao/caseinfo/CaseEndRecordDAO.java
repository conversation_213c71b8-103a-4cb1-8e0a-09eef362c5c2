package com.shuidihuzhu.cf.dao.caseinfo;

import com.shuidihuzhu.cf.domain.caseinfo.CaseEndRecordDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@DataSource(DS.CF)
public interface CaseEndRecordDAO {

    int insert(CaseEndRecordDO caseEndRecordDO);

    @DataSource(DS.CF_SLAVE)
    CaseEndRecordDO getLastByCaseId(@Param("caseId") int caseId);
}
