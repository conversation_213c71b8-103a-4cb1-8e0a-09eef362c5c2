package com.shuidihuzhu.cf.dao.mina;

import com.shuidihuzhu.cf.model.mina.CfMinaQuizPushConfig;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by dongcf on 2018/2/2
 */
@DataSource(DS.FRAME)
public interface CfMinaQuizPushConfigDao {

    @DataSource(DS.FRAME_SLAVE)
    CfMinaQuizPushConfig getByThirdType(@Param("thirdType") Integer thirdType, @Param("sbType") Integer sbType);

    @DataSource(DS.FRAME_SLAVE)
    Integer getVisitTimes(@Param("sbType") Integer sbType);

    Integer updateVisitTimesById(@Param("id") Integer id);

    Integer insert(CfMinaQuizPushConfig cfMinaQuizPushConfig);

    @DataSource(DS.FRAME_SLAVE)
    CfMinaQuizPushConfig getByMinaQuizIdAndSbType(@Param("minaQuizId") Integer minaQuizId, @Param("thirdType") Integer thirdType, @Param("sbType") Integer sbType);

    Integer updateStatusById(@Param("status") Integer status, @Param("id") Integer id);

    List<CfMinaQuizPushConfig> findByOffset(@Param("offset")Integer offset,@Param("limit")Integer limit);

    CfMinaQuizPushConfig getById(@Param("id")Integer id);

    Integer updateById(CfMinaQuizPushConfig pushConfig);

    Integer countAll();
}
