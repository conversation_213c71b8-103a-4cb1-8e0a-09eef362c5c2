package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.OssPictureAttrDO;
import com.shuidihuzhu.client.cf.api.model.AttachmentAttrModel;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2019/5/22 下午8:59
 * @desc
 */
@DataSource(DS.CF)
public interface IOssPictureAttrDAO {

    int batchInsert(@Param("attrDOS") List<OssPictureAttrDO> attrDOS);

    @DataSource(DS.CF_SLAVE)
    List<OssPictureAttrDO> getByAttachmentIds(@Param("list") List<Integer> ids);

    int updateWaterMark(@Param("id") int id,
                        @Param("waterMark") int waterMark);

    int updateAiResultByModel(AttachmentAttrModel attachmentAttrModel);

    int updateAiResult(@Param("id") int id, @Param("aiPsProb") String aiPsProb,  @Param("aiPsSoftware") String aiPsSoftware,  @Param("aiTime") long aiTime);
}
