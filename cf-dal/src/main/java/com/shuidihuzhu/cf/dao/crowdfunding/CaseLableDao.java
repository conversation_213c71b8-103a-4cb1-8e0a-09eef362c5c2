package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.CaseLableDO;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;
import com.shuidihuzhu.common.datasource.DS;

@DataSource(DS.CF)
public interface CaseLableDao {
    int insert(CaseLableDO caseLableDO);

    @DataSource(DS.CF_SLAVE)
    CaseLableDO getRaiseEnvByCaseId(@Param("caseId") int caseId, @Param("tagKey") String tagKey);
}
