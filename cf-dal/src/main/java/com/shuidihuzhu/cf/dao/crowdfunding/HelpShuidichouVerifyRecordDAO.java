package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.HelpShuidichouVerifyRecordDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;


@DataSource(DS.CF)
public interface HelpShuidichouVerifyRecordDAO {
    int insert(HelpShuidichouVerifyRecordDO helpShuidichouVerifyRecordDO);

    @DataSource(DS.CF_SLAVE)
    HelpShuidichouVerifyRecordDO selectByCaseId(@Param("caseId") Integer caseId);

    int updateIsAgree(@Param("isAgree") int isAgree, @Param("caseId") int caseId, @Param("userId") long userId);
}
