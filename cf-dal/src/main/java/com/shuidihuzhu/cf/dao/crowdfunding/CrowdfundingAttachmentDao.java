package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.finance.enums.FinanceDSConstants;
import com.shuidihuzhu.cf.fulllink.measurement.annotation.Operate;
import com.shuidihuzhu.cf.fulllink.measurement.utils.OperateType;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * Created by wuxinlong on 6/21/16.
 */
@DataSource(DS.CF)
public interface CrowdfundingAttachmentDao {

	@DataSource(DS.CF_SLAVE)
	List<CrowdfundingAttachment> getFundingAttachment(int parentId);

	@DataSource(DS.CF_SLAVE)
	List<CrowdfundingAttachment> queryByParentId(int parentId);

	@DataSource(DS.CF_SLAVE)
	@Operate(OperateType.READ)
	List<CrowdfundingAttachment> getAttachmentsByType(@Param("parentId") int parentId,
													  @Param("type") AttachmentTypeEnum type);

	@DataSource(DS.CF_SLAVE)
	@Operate(OperateType.READ)
	List<CrowdfundingAttachment> getAttachmentsByTypeToSea(@Param("parentId") int parentId,
													  @Param("type") AttachmentTypeEnum type);

	@DataSource(DS.CF_SLAVE)
	@Operate(OperateType.READ)
	List<CrowdfundingAttachment> getAttachmentsByTypeToSeaAndTime(@Param("parentId") int parentId,
																  @Param("type") int type,
																  @Param("firstApproveTime") Date firstApproveTime);

	@DataSource(DS.CF_SLAVE)
	@Operate(OperateType.READ)
	CrowdfundingAttachment getAttachmentById(@Param("parentId") int parentId, @Param("id") Integer id);

	int add(List<CrowdfundingAttachment> crowdfundingAttachmentList);

	int addOne(CrowdfundingAttachment crowdfundingAttachment);

	int deleteByParentId(int parentId);

	int deleteByIds(@Param("parentId") int parentId, @Param("ids") List<Integer> ids);

	int saveOrUpdate(@Param("list") List<CrowdfundingAttachment> crowdfundingAttachments);

	int deleteByParentIdAndType(@Param("parentId") int parentId, @Param("typeList") List<AttachmentTypeEnum> typeList);

	int addForAdmin(List<CrowdfundingAttachment> crowdfundingAttachmentList);

	@DataSource(DS.CF_SLAVE)
	List<CrowdfundingAttachment> getListByInfoIdListAndType(@Param("parentIdList") List<Integer> parentIdList,
															@Param("type") AttachmentTypeEnum type);

	@DataSource(DS.CF_SLAVE)
	List<CrowdfundingAttachment> getAttachmentsByTypes(@Param("parentId") int parentId,
													   @Param("types") List<Integer> types);

	@DataSource(DS.CF_SLAVE_2)
	List<CrowdfundingAttachment> getAttachmentsByParentIdAndType(@Param("parentId") int parentId, @Param("type") int type);

	@DataSource(DS.CF_SLAVE)
	List<CrowdfundingAttachment> getAttachmentsByCaseIdsWithDelete(@Param("parentIdList") List<Integer> parentIdList,
																   @Param("types") List<AttachmentTypeEnum> types);

	@DataSource(DS.CF_SLAVE)
	List<CrowdfundingAttachment> getAttachmentsByIdListToSea(@Param("parentId") int parentId, @Param("idList") List<Integer> idList);

}
