package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfDailyCharityCfInfo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@DataSource(DS.CF)
public interface CfDailyCharityCfInfoDao {

	int insert(CfDailyCharityCfInfo record);

	@DataSource(DS.CF_SLAVE)
	CfDailyCharityCfInfo selectByPrimaryKey(Integer id);

	@DataSource(DS.CF_SLAVE)
	List<CfDailyCharityCfInfo> listByDt(@Param("dt") Date dt, @Param("limit") Integer limit);

	int updateByPrimaryKey(CfDailyCharityCfInfo record);
}
