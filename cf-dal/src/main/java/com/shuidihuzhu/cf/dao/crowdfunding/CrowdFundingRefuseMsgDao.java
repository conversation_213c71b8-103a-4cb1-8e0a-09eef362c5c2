package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingRefuseMsg;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.CF)
public interface CrowdFundingRefuseMsgDao {

	int add(CrowdfundingRefuseMsg refuseMsg);

    @DataSource(DS.CF_SLAVE)
	CrowdfundingRefuseMsg getByInfoId(@Param("infoId") int infoId);

    @DataSource(DS.CF_SLAVE)
	List<CrowdfundingRefuseMsg> getByInfoIds(@Param("infoIds") List<Integer> infoIds);
}
