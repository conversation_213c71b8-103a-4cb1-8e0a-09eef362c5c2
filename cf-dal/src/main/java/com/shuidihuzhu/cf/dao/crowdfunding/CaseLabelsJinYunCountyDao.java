package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;


/**
 * <AUTHOR>
 */
@DataSource(DS.CF)
public interface CaseLabelsJinYunCountyDao {

    @DataSource(DS.CF_SLAVE)
    int getCount(@Param("infoUuid") String infoUuid);

    int update(@Param("infoUuid") String infoUuid);
}
