package com.shuidihuzhu.cf.dao.rule;

import com.shuidihuzhu.cf.model.rule.RuleCollectionDetail;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * Created by sven on 18/8/7.
 *
 * <AUTHOR>
 */
@DataSource(DS.CF)
public interface IRuleCollectionDetailDAO {

    @DataSource(DS.CF_SLAVE)
    RuleCollectionDetail getById(@Param("id") long id);

    @DataSource(DS.CF_SLAVE)
    List<RuleCollectionDetail> getByOwner(@Param("adminUserId") int adminUserId);

    int enableRuleCollection(@Param("id") long id);

    int update(RuleCollectionDetail detail);

    int updateTime(@Param("id") long id,
                   @Param("startTime") Date startTime,
                   @Param("endTime") Date endTime);

    int updateFailModel(@Param("id") long id,
                        @Param("failModel") int failMode);

    int insert(RuleCollectionDetail ruleCollectionDetail);
}
