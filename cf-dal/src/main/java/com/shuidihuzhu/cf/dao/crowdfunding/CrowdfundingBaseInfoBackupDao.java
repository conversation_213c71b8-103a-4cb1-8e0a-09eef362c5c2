package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfIdCardErrorMsg;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingBaseInfoBackup;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.RecordLog;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.CF)
public interface CrowdfundingBaseInfoBackupDao {

    /**
     * @see {@link com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingBaseInfoBackupDao#insertV2(CrowdfundingBaseInfoBackup)}
     * @deprecated 优先使用v2，这个以后找机会删除
     * @param info
     * @return
     */
    @Deprecated
    int insert(CrowdfundingBaseInfoBackup info);

    int insertV2(CrowdfundingBaseInfoBackup info);

    CrowdfundingBaseInfoBackup selectRecentlyCfByUserId(@Param("userId") long userId,@Param("type") int type);

    List<CrowdfundingBaseInfoBackup> selectByUserId(@Param("userId") long userId);

    int delete(long userId);

    int update(CrowdfundingBaseInfoBackup info);

    int updateBaseInfo(CrowdfundingBaseInfoBackup info);


    void saveCfIdCardErrorMsg(CfIdCardErrorMsg errorMsg);

    List<CfIdCardErrorMsg> getListByBackupId(@Param("ids") List<Long> ids);
}
