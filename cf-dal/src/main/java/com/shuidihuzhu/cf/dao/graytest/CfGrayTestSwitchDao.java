package com.shuidihuzhu.cf.dao.graytest;

import com.shuidihuzhu.cf.fulllink.measurement.annotation.Operate;
import com.shuidihuzhu.cf.fulllink.measurement.utils.OperateType;
import com.shuidihuzhu.cf.model.graytest.CfGrayTestSwitch;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by wangsf on 17/5/12.
 */
@DataSource(DS.CF)
public interface CfGrayTestSwitchDao {

	@Operate(OperateType.READ)
	@DataSource(DS.CF_SLAVE)
	CfGrayTestSwitch getByCode(String code);

	boolean delete(String code);
	
	/**
	 * @param bizType        业务类型 1-互助；2-水滴筹
	 * @param code           测试case代码，不超过20个字符的串
	 * @param casePercentage 每个测试case的百分比，多个数字之间通过逗号分隔。 例如 10,10,80 总和必须是100
	 * @param description    测试场景描述
	 */
	boolean addItem(@Param("bizType") int bizType,
                 @Param("code") String code,
                 @Param("casePercentage") String casePercentage,
                 @Param("description") String description,
                 @Param("isRecordResult") boolean isRecordResult);

	@DataSource(DS.CF_SLAVE)
	List<CfGrayTestSwitch> getByPage(@Param("start") int start,
                                     @Param("size") int size);

	boolean updateIfRecordResult(@Param("code") String code,
                              @Param("isRecordResult") boolean isRecordResult);

	@DataSource(DS.CF_SLAVE)
	int total();

	@DataSource(DS.CF_SLAVE)
	List<CfGrayTestSwitch> getLikeCode(@Param("code") String code);
}
