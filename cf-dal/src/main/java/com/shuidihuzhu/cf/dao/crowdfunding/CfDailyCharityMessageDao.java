package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfDailyCharityMessage;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

@DataSource(DS.CF)
public interface CfDailyCharityMessageDao {
	int deleteByPrimaryKey(Integer id);

	int insert(CfDailyCharityMessage record);

	@DataSource(DS.CF_SLAVE)
	CfDailyCharityMessage selectByPrimaryKey(Integer id);

	int updateByPrimaryKey(CfDailyCharityMessage record);
}
