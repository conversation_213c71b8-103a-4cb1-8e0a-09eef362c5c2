package com.shuidihuzhu.cf.dao.crowdfunding;


import com.shuidihuzhu.cf.model.crowdfunding.ReportInsteadWorkOrderRelateDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <AUTHOR>
 */
@DataSource(DS.CF)
public interface ReportInsteadWorkOrderRelateDAO {

    int insert(ReportInsteadWorkOrderRelateDO reportInsteadWorkOrderRelateDO);

    List<ReportInsteadWorkOrderRelateDO> getByReportInsteadIds(@Param("reportInsteadIds") List<Long> repostInsteadIds);

    List<ReportInsteadWorkOrderRelateDO> getByWorkOrderId(@Param("workOrderId") long workOrderId);

    ReportInsteadWorkOrderRelateDO getOneByWorkOrderId(@Param("workOrderId") long workOrderId);
}

