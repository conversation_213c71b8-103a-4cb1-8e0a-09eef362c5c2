package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfReportCommitmentInfo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

@DataSource(DS.CF)
public interface ReportCommitmentInfoDao {

    @DataSource(DS.CF_SLAVE)
    CfReportCommitmentInfo findByIncrTrustId(@Param("incrTrustId") long incrTrustId);

    int updateCommitmentInfo(CfReportCommitmentInfo cfReportCommitmentInfo);

}
