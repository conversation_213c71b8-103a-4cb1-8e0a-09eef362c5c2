package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfUserPortraitInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.AdminBlessingCardVo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/25  10:59 上午
 */
@DataSource(DS.CF)
public interface CfUserPortraitInfoDao {

    @DataSource(DS.CF_SLAVE)
    List<CfUserPortraitInfo> getAll();

    @DataSource(DS.CF_SLAVE)
    CfUserPortraitInfo getFirst();
}
