package com.shuidihuzhu.cf.dao.graytest;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * Created by ch<PERSON> on 2017/6/29.
 */
@DataSource(DS.CF)
public interface CfGrayTestRecordDao {
	void add(@Param("grayTestId") int grayTestId,
             @Param("userId") long userId,
             @Param("openId") String openId,
             @Param("selfTag") String selfTag,
             @Param("grayTestResult") int result);
}
