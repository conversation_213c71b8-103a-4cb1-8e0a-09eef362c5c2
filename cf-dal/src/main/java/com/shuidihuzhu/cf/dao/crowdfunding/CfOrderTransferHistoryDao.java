package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfPropertyTransferHistory;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by ch<PERSON> on 2017/10/13.
 */
@DataSource(DS.CF)
public interface CfOrderTransferHistoryDao {
    void addBatch(@Param("historyList") List<CfPropertyTransferHistory> historyList);

    @DataSource(DS.CF_SLAVE)
    List<CfPropertyTransferHistory> get(@Param("fromUserId") long fromUserId,
                                        @Param("toUserId") long toUserId,
                                        @Param("bizId") long bizId,
                                        @Param("bizType") int bizType);
    @DataSource(DS.CF_SLAVE)
    List<CfPropertyTransferHistory> getByToUserIdAndBizId(@Param("toUserId") long toUserId,
                                                            @Param("bizId") Long bizId);

    @DataSource(DS.CF_SLAVE)
    List<CfPropertyTransferHistory> getListByBizTypeAndBizId(@Param("bizType") int bizType, @Param("bizId") long bizId);
}