package com.shuidihuzhu.cf.dao.mina;

import com.shuidihuzhu.cf.model.mina.CfMinaMajorDiseaseMenu;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by ahrievil on 2017/7/5.
 */
@DataSource(DS.CF)
public interface CfMinaMajorDiseaseMenuDao {
    @DataSource(DS.CF_SLAVE)
    List<CfMinaMajorDiseaseMenu> selectByDiseaseId(@Param("diseaseId") int diseaseId);
    @DataSource(DS.CF_SLAVE)
    CfMinaMajorDiseaseMenu selectIdByDiseaseIdAndOrder(@Param("diseaseId") int diseaseId, @Param("menuOrder") int menuOrder);
    int insertList(@Param("list") List<CfMinaMajorDiseaseMenu> list);
    int insertOne(CfMinaMajorDiseaseMenu cfMinaMajorDiseaseMenu);
}
