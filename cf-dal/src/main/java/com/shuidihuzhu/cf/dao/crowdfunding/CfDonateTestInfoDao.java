package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfDonateTestInfo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

@DataSource(DS.CF)
public interface CfDonateTestInfoDao {
    int insert(CfDonateTestInfo cfDonateTestInfo);

    @DataSource(DS.CF_SLAVE)
    CfDonateTestInfo selectByUserId(long userId);

    @DataSource(DS.CF_SLAVE)
    CfDonateTestInfo selectByOpenId(String openId);

    int updateMsgStatus(@Param("userId")long userId, @Param("openId") String openId);
}