package com.shuidihuzhu.cf.dao.goods;

import com.shuidihuzhu.cf.fulllink.measurement.annotation.Operate;
import com.shuidihuzhu.cf.fulllink.measurement.utils.OperateType;
import com.shuidihuzhu.cf.model.goods.GoodsOrderExt;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.CF)
public interface GoodsOrderExtDao {

	int save(GoodsOrderExt goodsOrderExt);

	@Operate(OperateType.READ)
	@DataSource(DS.CF_SLAVE)
	GoodsOrderExt get(@Param("orderId") long orderId);

	@DataSource(DS.CF_SLAVE)
	List<GoodsOrderExt> getListByOrderIds(@Param("orderIds") List<Long> orderIds);

	@DataSource(DS.CF_SLAVE)
	List<GoodsOrderExt> getByAnchorId(@Param("infoUuid") String infoUuid, @Param("gearId") Long gearId,
			@Param("anchorId") int anchorId, @Param("limit") int limit);

	int updateSuccessPayStatus(@Param("orderId") long orderId);

}
