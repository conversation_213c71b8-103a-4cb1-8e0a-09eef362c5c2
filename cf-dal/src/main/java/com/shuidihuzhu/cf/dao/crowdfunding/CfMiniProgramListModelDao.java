package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfMiniProgramListModel;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;

/**
 * Created by ahrievil on 2017/5/26.
 */
@DataSource(DS.CF)
public interface CfMiniProgramListModelDao {
    int insert(@Param("list") List<CfMiniProgramListModel> list);

    @DataSource(DS.CF_SLAVE)
    List<CfMiniProgramListModel> selectByPage(@Param("date") Timestamp date, @Param("anchorId") int anchorId, @Param("size") int size);
}
