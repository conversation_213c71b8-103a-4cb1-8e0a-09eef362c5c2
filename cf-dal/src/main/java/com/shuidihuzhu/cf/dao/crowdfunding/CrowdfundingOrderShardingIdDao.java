package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.fulllink.measurement.annotation.Operate;
import com.shuidihuzhu.cf.fulllink.measurement.utils.OperateType;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoStat;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrderShardingModel;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * @author: wanghui
 * @time: 2019/3/22 5:45 PM
 * @description:  以id 分表的dao
 */

@DataSource(CfDataSource.SHARDING_DONATION_BIG_TABLE_MASTER)
public interface CrowdfundingOrderShardingIdDao {

	int addCrowdfundingOrderShardingModel(CrowdfundingOrder order);

	@Operate(OperateType.READ)
	Long getCrowdfundingIdById(@Param("id") Long id);

	List<Long> getCrowdfundingIdByIds(@Param("ids") List<Long> ids);


	@DataSource(CfDataSource.SHARDING_DONATION_BIG_TABLE_SLAVE)
	List<CrowdfundingOrder> getCrowdfundingIdsByIds(@Param("ids") List<Long> ids);

}
