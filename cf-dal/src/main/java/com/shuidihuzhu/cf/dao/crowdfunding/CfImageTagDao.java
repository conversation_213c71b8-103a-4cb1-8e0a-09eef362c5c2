package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.client.cf.api.model.CfImageTag;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2022/9/15 7:29 下午
 */
@DataSource(CfDataSource.ADMIN_DB_SLAVE)
public interface CfImageTagDao {

    @DataSource(CfDataSource.ADMIN_DB_MASTER)
    void saveImageTag(CfImageTag cfImageTag);

    CfImageTag getImageTag(@Param("imageId") Integer imageId);

    List<CfImageTag> getImageTags(@Param("imageIds") List<Integer> imageIds);

    List<CfImageTag> getImageTagsByCaseId(@Param("caseId") Integer caseId);

}
