package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfUserMobileIdCardInfo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @date : 2018/8/1 16:02
 */
@DataSource(DS.CF)
public interface CfUserMobileIdCardInfoDao {

    int insertOne(CfUserMobileIdCardInfo cfUserMobileIdCardInfo);

    @DataSource(DS.CF_SLAVE)
    CfUserMobileIdCardInfo selectByUserId(@Param("userId") long userId);

    int updateByUserId(CfUserMobileIdCardInfo cfUserMobileIdCardInfo);
}
