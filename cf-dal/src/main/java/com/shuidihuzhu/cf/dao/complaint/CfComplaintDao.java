package com.shuidihuzhu.cf.dao.complaint;


import com.shuidihuzhu.cf.dao.crowdfunding.complaint.CfComplaintDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;
@DataSource(DS.CF)
public interface CfComplaintDao {

    int insert(CfComplaintDO record);

    int insertSelective(CfComplaintDO record);

    CfComplaintDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CfComplaintDO record);

    int updateByPrimaryKey(CfComplaintDO record);

    /**
     * 根据业务id,业务类型,投诉用户id,投诉结果查询list
     */
    List<CfComplaintDO> listByCondition(@Param("bizId") Long bizId,
                                        @Param("bizType") Integer bizType,
                                        @Param("complaintUserId") Long complaintUserId);

    /**
     * 根据业务id,业务类型,投诉用户id,投诉结果查询list
     */
    List<CfComplaintDO> listByBizIdAndBizTypeAndComplaintResult(@Param("bizId") Long bizId,
                                        @Param("bizType") Integer bizType,
                                        @Param("complaintResult") Integer complaintResult);

    /**
     * 根据业务id,业务类型,投诉用户id,投诉结果查询list
     */
    List<CfComplaintDO> listByBizIdAndBizType(@Param("bizId") Long bizId,
                                                                @Param("bizType") Integer bizType);
}