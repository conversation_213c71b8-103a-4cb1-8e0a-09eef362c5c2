package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.client.apipure.model.crowdfunding.CfCaseUpdateAmountRecordParam;
import com.shuidihuzhu.cf.model.crowdfunding.CfCaseUpdateAmountRecord;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.CF)
public interface ICfCaseUpdateAmountDAO {

    int insert(CfCaseUpdateAmountRecord record);

    @DataSource(DS.CF_SLAVE)
    CfCaseUpdateAmountRecord getLatestByCaseId(@Param("caseId")int caseId);

    @DataSource(DS.CF_SLAVE)
    List<CfCaseUpdateAmountRecordParam> getListByCaseId(@Param("caseId")int caseId);

    @DataSource(DS.CF_SLAVE)
    List<CfCaseUpdateAmountRecordParam> getFixTargetAmountRecordByCaseIds(@Param("caseIds") List<Integer> caseIds);
}
