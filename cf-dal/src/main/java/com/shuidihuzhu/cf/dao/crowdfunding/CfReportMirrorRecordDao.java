package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfReportMirrorRecord;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/10/18.
 */
@DataSource(DS.CF)
public interface CfReportMirrorRecordDao {

    int save(CfReportMirrorRecord cfReportMirrorRecord);

    @DataSource(DS.CF_SLAVE)
    CfReportMirrorRecord getLastoneByInfoUuid(@Param("infoUuid") String infoUuid, @Param("type")int type);

    @DataSource(DS.CF_SLAVE)
    List<CfReportMirrorRecord> getCfReportMirrorRecordListByInfoUuid(String infoUuid);
}
