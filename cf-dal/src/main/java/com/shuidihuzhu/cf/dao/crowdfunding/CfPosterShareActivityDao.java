package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfPosterShareActivity;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * Created by wangsf on 17/11/13.
 */
@DataSource(DS.CF)
public interface CfPosterShareActivityDao {

	@DataSource(DS.CF_SLAVE)
	CfPosterShareActivity findLastByThirdType(@Param("thirdType") int thirdType);

}
