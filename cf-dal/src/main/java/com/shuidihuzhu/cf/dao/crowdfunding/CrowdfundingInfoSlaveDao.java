package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.fulllink.measurement.annotation.Operate;
import com.shuidihuzhu.cf.fulllink.measurement.utils.OperateType;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingSummary;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * Created by wangsf on 17/1/16.
 */
@DataSource(DS.CF_SLAVE)
public interface CrowdfundingInfoSlaveDao {

    CrowdfundingSummary getCrownfundingSummary(@Param("type") int type, @Param("summaryTime") Timestamp summaryTime);

    List<CrowdfundingInfo> getListBetween(@Param("start") Timestamp start, @Param("end") Timestamp end);

    List<CrowdfundingInfo> getNotEnd(@Param("type") int type, @Param("offset") int offset, @Param("limit") int limit);

    List<CrowdfundingInfo> getNotEndByUserId(@Param("userId") long userId);

    List<CrowdfundingInfo> getNotEndListByUserId(@Param("userId") long userId);

    CrowdfundingInfo getLastNotEndByUserId(@Param("userId") long userId);

    List<Long> listUserIdBetween(@Param("start") Timestamp start, @Param("end") Timestamp end);

    CrowdfundingInfo getLastByUserId(long userId);

    CrowdfundingInfo getFirstByUserId(@Param("userId") long userId);

    CrowdfundingInfo selectLastByUserIdAnChannel(@Param("userId") long userId, @Param("channel") String channel);

    List<CrowdfundingInfo> selectWithChannelNotFinishLimit(@Param("channel") String channel, @Param("createTime") Timestamp createTime,
                                                           @Param("endTime") Timestamp endTime, @Param("start") int start, @Param("size") int size);
    List<Long> selectByTimeFindUserId(@Param("begin") Timestamp begin, @Param("end") Timestamp end, @Param("start") int start, @Param("size") int size);

    List<CrowdfundingInfo> getByInfoUuidPrefix(@Param("infoUuidPrefix") String infoUuidPrefix);

    String selectLatestByUserId(@Param("userId") long userId);

    List<CrowdfundingInfo> selectInfoUuidAndUserIdByTime(@Param("begin") Timestamp begin, @Param("end") Timestamp end, @Param("start") int start, @Param("size") int size);

    List<CrowdfundingInfo> selectByUserId(@Param("userId") long userId);

    List<CrowdfundingInfo> selectByUserIds(@Param("userIds") List<Long> userIds);

    Set<Long> selectUserIdWhenFinishCaseByTime(@Param("begin") Timestamp begin, @Param("end") Timestamp end, @Param("start") int start, @Param("size") int size);

    Set<Long> selectUserIdByThirdTypeWhenNotFinishCase(@Param("start") int start, @Param("size") int size, @Param("thirdType") int thirdType);

    @Operate(OperateType.READ)
    CrowdfundingInfo getFundingInfo(String infoId);

    CrowdfundingInfo getFundingInfoById(int id);

    List<CrowdfundingInfo> selectIdAndUuidAndTypeByInfoUuid(@Param("set") Set<String> set);

    List<CrowdfundingInfo> getCrowdfundingInfoByUserIdAndChannel(@Param("userId") long userId, @Param("channels") List<String> channels);

    CrowdfundingInfo getLastCrowdfundedByUserId(@Param("userId") long userId);

    Integer getIdByUserIdAndCreateTime(@Param("userId") long userId, @Param("begin") Timestamp begin, @Param("end") Timestamp end);

    List<Integer> getIdsByEndTime(@Param("id") int id,  @Param("endTime") Date endTime, @Param("limit") int limit);

    List<Integer> listNotEndCaseIdOfUser(@Param("userId") long userId);
}
