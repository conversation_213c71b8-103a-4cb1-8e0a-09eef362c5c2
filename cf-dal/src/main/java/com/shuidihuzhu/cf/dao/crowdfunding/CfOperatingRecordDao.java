package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.fulllink.measurement.annotation.Operate;
import com.shuidihuzhu.cf.fulllink.measurement.utils.OperateType;
import com.shuidihuzhu.cf.model.crowdfunding.CfOperatingRecord;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.CF)
public interface CfOperatingRecordDao {
	
	int save(CfOperatingRecord cfOperatingRecord);

	int updateResion(@Param("id") long id,@Param("financialComment") String financialComment);

	@DataSource(DS.CF_SLAVE)
	@Operate(OperateType.READ)
	CfOperatingRecord getLastOneByType(@Param("infoUuid") String infoUuid, @Param("type") int type);

    @DataSource(DS.CF_SLAVE)
    List<CfOperatingRecord> getAllOperateByInfoUuidAndTypes(@Param("infoUuid")String infoUuid, @Param("types") List<Integer> types);

	@DataSource(DS.CF_SLAVE)
	List<CfOperatingRecord> getListByInfoUuid(@Param("infoUuid") String infoUuid);

	int deleteByInfoUuidAndId(@Param("infoUuid") String infoUuid, @Param("id") long id);
}
