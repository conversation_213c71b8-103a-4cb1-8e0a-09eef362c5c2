package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoView;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPushHistory;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * Created by roy
 */

@DataSource(DS.CF)
public interface CrowdfundingPushDao {

    int add(CrowdfundingPushHistory crowdfundingPushHistory);

    @DataSource(DS.CF_SLAVE)
    List<CrowdfundingPushHistory> getByUserId(@Param("userId")long userId);

    @DataSource(DS.CF_SLAVE)
    List<CrowdfundingPushHistory> getAll();

    @DataSource(DS.CF_SLAVE)
    int getCountByUserId(@Param("userId")long userId);

    @DataSource(DS.CF_SLAVE)
    int getCountByUserIdAndCrowdfundingId(@Param("userId")long userId
            , @Param("crowdfundingId") Integer crowdfundingId);

}
