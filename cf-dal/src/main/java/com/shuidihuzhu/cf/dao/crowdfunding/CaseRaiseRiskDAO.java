package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.domain.CaseRaiseRiskDO;
import com.shuidihuzhu.cf.domain.CfCaseRiskDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-08-04  10:32
 */
@DataSource(DS.CF)
public interface CaseRaiseRiskDAO {

    int insert(CaseRaiseRiskDO caseRaiseRiskDO);

    /**
     * 更新风险信息
     *
     * @return
     */
    int updateByInfoUuid(@Param("infoUuid") String infoUuid,
                         @Param("riskLevel") int riskLevel,
                         @Param("passed") int passed,
                         @Param("riskData") String riskData);

    @DataSource(DS.CF_SLAVE)
    CaseRaiseRiskDO getByInfoUuid(@Param("infoUuid") String infoUuid);

}
