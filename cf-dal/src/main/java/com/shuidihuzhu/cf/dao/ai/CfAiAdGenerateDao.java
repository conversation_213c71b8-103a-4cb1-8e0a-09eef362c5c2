package com.shuidihuzhu.cf.dao.ai;

import com.shuidihuzhu.client.cf.api.model.AdGenerationResult;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 广告语生成
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/6/20 14:49
 */
@DataSource(DS.CF)
public interface CfAiAdGenerateDao {

    int insert(AdGenerationResult adGenerationResult);

    int deleteAdByCaseId(@Param("caseId") Integer caseId);

    int deleteAdByAdId(@Param("adId") String adId);

    @DataSource(DS.CF_SLAVE)
    AdGenerationResult queryAiAd(@Param("caseId") Integer caseId, @Param("profileKey") String profileKey);

}
