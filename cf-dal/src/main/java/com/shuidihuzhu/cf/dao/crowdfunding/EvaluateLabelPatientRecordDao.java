package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.EvaluateLabelPatientRecordDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.CF_SLAVE)
public interface EvaluateLabelPatientRecordDao {

    @DataSource(DS.CF)
    void insertOrUpdate(@Param("labels") List<EvaluateLabelPatientRecordDO> labels);

    int getCnt(@Param("labelId") int labelId,@Param("caseId") int caseId);

    @DataSource(DS.CF)
    void addOrUpdate(@Param("labelId") int labelId,@Param("caseId") int caseId);

    List<EvaluateLabelPatientRecordDO> getByCaseId(@Param("caseId") int caseId);

    EvaluateLabelPatientRecordDO getMaxUsedByCaseId(@Param("caseId") int caseId);

    EvaluateLabelPatientRecordDO getIdByCaseId(@Param("caseId") int caseId);

    List<EvaluateLabelPatientRecordDO> getListByCaseIdOrderByCnt(@Param("caseId") int caseId);

}
