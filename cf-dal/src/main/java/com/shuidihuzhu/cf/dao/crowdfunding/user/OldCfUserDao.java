package com.shuidihuzhu.cf.dao.crowdfunding.user;

import com.shuidihuzhu.cf.model.crowdfunding.CfUserInfoDO;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

/**
 * @author: fengxuan
 * @create 2019-09-30 14:28
 **/
@DataSource("cfUserInfoShardingCrowdfunding")
public interface OldCfUserDao {

    int insertOrUpdate(CfUserInfoDO cfUserInfoDO);


    CfUserInfoDO findByUserId(long userId);
}
