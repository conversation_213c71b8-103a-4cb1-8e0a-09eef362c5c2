package com.shuidihuzhu.cf.dao.activity;

import com.shuidihuzhu.cf.model.wx.Cf111WxEventRecord;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@DataSource(DS.CF)
public interface Cf111WxEventRecordDao {

    int saveCf111WxEventRecord(Cf111WxEventRecord cf111WxEventRecord);

    @DataSource(DS.CF_SLAVE)
    List<Cf111WxEventRecord> get111StateEvents(@Param("fromUserNames") Set<String> fromUserNames);

    int unsubscribeShuiDiChou(@Param("fromUserName") String fromUserName);

    int recoverNon111State(@Param("fromUserName") String fromUserName);

    @DataSource(DS.CF_SLAVE)
    List<Cf111WxEventRecord> getSubscribedBy111Souvenir(@Param("fromUserName") String fromUserName);
}
