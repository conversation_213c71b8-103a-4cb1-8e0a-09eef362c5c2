package com.shuidihuzhu.cf.dao.crowdfunding.comment;


import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingComment;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 以crowdfunding_id 分表
 */
@DataSource(CfDataSource.SHUIDI_CF_COMMENT_SHARE)
public interface CrowdfundingCommentShareDao {
    int add(CrowdfundingComment crowdfundingComment);

    void removeByCrowdfundingIdAndCommentId(@Param("crowdfundingId") Long crowdfundingId, @Param("commentId") Long commentId);


    @DataSource(CfDataSource.SHUIDI_CF_COMMENT_SLAVE_SHARE)
    List<CrowdfundingComment> getByPageNoCareCommentIdList(@Param("crowdfundingIds") List<Long> crowdfundingIds,
                                                           @Param("parentIdList") List<Long> parentIdList,
                                                           @Param("offset") Integer offset,
                                                           @Param("limit") Integer limit,
                                                           @Param("type") Integer type);

    @DataSource(CfDataSource.SHUIDI_CF_COMMENT_SLAVE_SHARE)
    List<CrowdfundingComment> getByParentIdList(@Param("crowdfundingIds") List<Long> crowdfundingIds,
                                                @Param("parentIdList") List<Long> parentIdList,
                                                @Param("userId") Long userId,
                                                @Param("offset") Integer offset, @Param("limit") Integer limit,
                                                @Param("type") Integer type);


    @DataSource(CfDataSource.SHUIDI_CF_COMMENT_SLAVE_SHARE)
    CrowdfundingComment getByIdNoCareDeleted(@Param("crowdfundingId") Long crowdfundingId, @Param("id") Long id);


    @DataSource(CfDataSource.SHUIDI_CF_COMMENT_SLAVE_SHARE)
    List<CrowdfundingComment> getListByIdNoCareDeleted(@Param("crowdfundingIds") List<Long> crowdfundingIds, @Param("ids") List<Long> ids);


    @DataSource(CfDataSource.SHUIDI_CF_COMMENT_SLAVE_SHARE)
    CrowdfundingComment getByUserId(@Param("crowdfundingId") Long crowdfundingId, @Param("parentId") Long parentId,
                                    @Param("userId") Long userId, @Param("type") Integer type);

    @DataSource(CfDataSource.SHUIDI_CF_COMMENT_SLAVE_SHARE)
    List<CrowdfundingComment> getListByIdNoCareDeletedV1(@Param("crowdfundingIds") List<Integer> crowdfundingIds, @Param("ids") List<Long> ids);
}

