package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfHospitalAuditInfo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/11/16.
 */
@DataSource(DS.CF)
public interface CfHospitalAuditDao {

    int save(CfHospitalAuditInfo cfHospitalAuditInfo);

    int update(CfHospitalAuditInfo cfHospitalAuditInfo);

    @DataSource(DS.CF_SLAVE)
    CfHospitalAuditInfo getByInfoUuid(@Param("infoUuid") String infoUuid);

    @DataSource(DS.CF_SLAVE)
    CfHospitalAuditInfo getByInfoUuidALL(@Param("infoUuid") String infoUuid);

    @DataSource(DS.CF_SLAVE)
    List<CfHospitalAuditInfo> getByInfoUuids(@Param("infoUuids") List<String> infoUuids);
}
