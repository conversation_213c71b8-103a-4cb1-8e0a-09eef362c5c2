package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCity;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2023/5/18 19:15
 * @Description:
 */
@DataSource(DS.CF_SLAVE)
public interface CrowdfundingCityStreetDao {

    List<CrowdfundingCity> selectByAreaCode(@Param("areaCode") String areaCode);

}
