package com.shuidihuzhu.cf.dao.crowdfunding.app;

import com.shuidihuzhu.cf.model.crowdfunding.app.AppUpgradeInfo;
import com.shuidihuzhu.cf.model.crowdfunding.app.AppUpgradeLog;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;


import java.util.List;

/**
 * Created by lixurui on 18/12/02
 */

@DataSource(DS.CF_SLAVE)
public interface AppUpgradeDao {

    AppUpgradeInfo queryVersionByPlatform(@Param("appPlatform") String appPlatform);

    List<String> queryLogByid(int upgradeId);
}
