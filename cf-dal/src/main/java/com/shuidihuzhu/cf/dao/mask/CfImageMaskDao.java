package com.shuidihuzhu.cf.dao.mask;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.domain.CfImageAiMaskDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2022/9/8 4:22 下午
 */
@DataSource(CfDataSource.ADMIN_DB_SLAVE)
public interface CfImageMaskDao {

    @DataSource(CfDataSource.ADMIN_DB_MASTER)
    int insertImageRecord(@Param("cfImageAiMaskDOS") List<CfImageAiMaskDO> cfImageAiMaskDOS);

    @DataSource(CfDataSource.ADMIN_DB_MASTER)
    int deleteMaskImageByBizType(@Param("caseId") Integer caseId, @Param("bizType") Integer bizType);

    CfImageAiMaskDO selectByBizId(@Param("bizId") Long bizId);

    @DataSource(CfDataSource.ADMIN_DB_MASTER)
    int updateMaskImage(CfImageAiMaskDO cfImageAiMaskDOS);

    List<CfImageAiMaskDO> selectByCaseIdAndType(@Param("caseId") Integer caseId, @Param("bizType") List<Integer> bizType);

    List<CfImageAiMaskDO> selectDeleteByBizIdsAndType(@Param("bizIds") List<Long> bizIds, @Param("bizType") List<Integer> bizType);

    List<CfImageAiMaskDO> selectByBizIdAndType(@Param("bizId") Long bizId, @Param("bizType") Integer bizType);

    @DataSource(CfDataSource.ADMIN_DB_MASTER)
    int deleteMaskImageByBiz(@Param("bizIds") List<Long> bizIds, @Param("bizType") List<Integer> bizType);

    @DataSource(CfDataSource.ADMIN_DB_MASTER)
    int deleteMaskProgressImage(@Param("bizIds") List<Long> bizIds, @Param("bizType") List<Integer> bizType, @Param("imageUrl") String imageUrl);

}
