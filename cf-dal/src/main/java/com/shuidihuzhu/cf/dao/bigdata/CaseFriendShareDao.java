package com.shuidihuzhu.cf.dao.bigdata;

import com.shuidihuzhu.cf.model.bigdata.CfCaseFriendShareDo;
import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/11  15:22
 */
@DataSource(CfDataSource.CF_STAR_ROCKS_DATASOURCE)
public interface CaseFriendShareDao {

    List<CfCaseFriendShareDo> listByInfoId(@Param("infoId") Long infoId, @Param("dt") String dt);

}
