package com.shuidihuzhu.cf.dao.deposit;

import com.shuidihuzhu.cf.finance.enums.FinanceDSConstants;
import com.shuidihuzhu.cf.finance.model.deposit.CfCaseDepositAccount;
import com.shuidihuzhu.cf.fulllink.measurement.annotation.Operate;
import com.shuidihuzhu.cf.fulllink.measurement.utils.OperateType;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2019年12月13日15:37:47
 */
@DataSource(FinanceDSConstants.CF_FINANCE_MASTER)
public interface CfCaseDepositAccountDao {
    /**
     * 获取案例子账户
     *
     * @param caseId
     * @return
     */
    @Operate(OperateType.READ)
    CfCaseDepositAccount getByCaseId(@Param("caseId") int caseId);
}
