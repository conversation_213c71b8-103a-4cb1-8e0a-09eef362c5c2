package com.shuidihuzhu.cf.dao.crowdfundingshare;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CfShareBizMap;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2019-07-05
 */
@DataSource(CfDataSource.SHUIDI_CROWDFUNDING_SHARE_SLAVE)
public interface CfShareBizMapNewDao {

    @DataSource(CfDataSource.SHUIDI_CROWDFUNDING_SHARE_MASTER)
    void addBatch(@Param("items") List<CfShareBizMap> items);

    List<CfShareBizMap> get(@Param("crowdfundingId") int crowdfundingId,
                            @Param("type") int type,
                            @Param("limit") int limit);

    List<CfShareBizMap> getByBizIds(@Param("bizIdSet") Set<Long> bizIds, @Param("type") int type);

}
