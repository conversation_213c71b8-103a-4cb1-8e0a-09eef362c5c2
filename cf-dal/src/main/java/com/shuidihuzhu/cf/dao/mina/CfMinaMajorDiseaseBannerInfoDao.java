package com.shuidihuzhu.cf.dao.mina;

import com.shuidihuzhu.cf.model.mina.CfMinaMajorDiseaseBannerInfo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by ahrievil on 2017/7/5.
 */
@DataSource(DS.CF)
public interface CfMinaMajorDiseaseBannerInfoDao {
    @DataSource(DS.CF_SLAVE)
    List<CfMinaMajorDiseaseBannerInfo> selectByDiseaseId(@Param("diseaseId") int diseaseId);
    int insertOne(CfMinaMajorDiseaseBannerInfo cfMinaMajorDiseaseBannerInfo);
    @DataSource(DS.CF_SLAVE)
    int selectMaxOrder(@Param("diseaseId") int diseaseId);
    int updateOrder(@Param("bannerOrder") int bannerOrder, @Param("id") int id);
    int insertList(@Param("list") List<CfMinaMajorDiseaseBannerInfo> list);
}
