package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.dto.OrderSearchDto;
import com.shuidihuzhu.cf.fulllink.measurement.annotation.Operate;
import com.shuidihuzhu.cf.fulllink.measurement.utils.OperateType;
import com.shuidihuzhu.cf.model.crowdfunding.CaseDonationStatisticsModel;
import com.shuidihuzhu.cf.model.crowdfunding.CfUserOrder;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrderCount;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfCountSumVo;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: wanghui
 * @time: 2019/3/22 5:50 PM
 * @description: 以crowdfundingid分表的 dao
 */

@DataSource(CfDataSource.SHARDING_DONATION_BIG_TABLE_NEW_SLAVE)
public interface CrowdfundingOrderShardingCrowdfundingIdDao {

	List<CrowdfundingOrder> getOrderByPage(@Param("crowdfundingId") Long crowdfundingId, @Param("offset") Integer offset,
										   @Param("limit") Integer limit);

	List<CrowdfundingOrder> getByUserId(@Param("userIds") Set<Long> userIds,
										@Param("crowdfundingId") Long crowdfundingId);

	List<CrowdfundingOrder> getOrderNoAnonymous(@Param("userIds") Set<Long> userIds,
												@Param("crowdfundingId") Long crowdfundingId);

	List<CrowdfundingOrder> getByAnchorId(@Param("crowdfundingId") Long crowdfundingId,
										  @Param("anchorId") Long anchorId, @Param("limit") Integer limit);

	List<CrowdfundingOrder> getNoAnonymousByAnchorId(@Param("crowdfundingId") Long crowdfundingId,
										  @Param("anchorId") Long anchorId, @Param("limit") Integer limit);

	List<CrowdfundingOrder> getAllByAnchorIdAndUserId(@Param("crowdfundingId") long cfId,
													  @Param("anchorId") long anchorId,
													  @Param("limit") int limit,
													  @Param("userIds") Set<Long> userIds,
													  @Param("amount") int amount);

	List<CrowdfundingOrder> getByAnchorIdAndTime(@Param("crowdfundingId") Long crowdfundingId,
												 @Param("anchorId") Long anchorId, @Param("limit") Integer limit,
												 @Param("startTime") String startTime, @Param("endTime") String endTime);

	CrowdfundingOrderCount getUserOrderCount(@Param("crowdfundingId") Long crowdfundingId,
											 @Param("orderId") Long orderId);

	CrowdfundingOrderCount getOrderCountByTime(@Param("crowdfundingId") long crowdfundingId,
											   @Param("startTime") String startTime, @Param("endTime") String endTime);

	List<CrowdfundingOrder> getListByUserIdAndAnchorId(@Param("crowdfundingId") long crowdfundingId,
													   @Param("userId") long userId, @Param("anchorId") long anchorId, @Param("limit") int limit);

	List<CrowdfundingOrder> getOrderListByCrowdfundingIds(@Param("infoIds") List<Long> infoIds, @Param("userId") Long userId, @Param("anchorId") long anchorId,
														  @Param("limit") int limit);

	/**
	 * @param infoIds
	 * @param anchorId
	 * @param limit
	 * @return
	 */
	List<CrowdfundingOrder> getOrderListByCrowdfundingIdsV2(@Param("infoIds") List<Long> infoIds,
															@Param("userId") Long userId,
															@Param("anchorId") Long anchorId,
															@Param("limit") int limit);

	/**
	 * @param infoIds
	 * @param anchorId
	 * @return
	 */
    @DataSource(CfDataSource.CF_ORDER_NEW_SLAVE_DATASOURCE)
	List<CrowdfundingOrder> getOrderListByCrowdfundingIdsV2WithSuffixTableName(@Param("sharding") String sharding,
																			   @Param("infoIds") List<Long> infoIds,
																			   @Param("userId") Long userId,
																			   @Param("anchorId") Long anchorId);


	Integer getOrderCountByCrowdfundingIds(@Param("infoIds") List<Long> infoIds, @Param("userId") Long userId);

	@DataSource(CfDataSource.CF_ORDER_NEW_SLAVE_DATASOURCE)
	Integer getOrderCountByCrowdfundingIdsWithSuffixTableName(@Param("sharding") String sharding, @Param("infoIds") List<Long> infoIds, @Param("userId") Long userId);

	CrowdfundingOrder getLatestOrderOfCrowdfundingIdsAndUserId(@Param("infoIds") List<Long> infoIds, @Param("userId") Long userId);

	List<CrowdfundingOrder> getListByids(@Param("infoIds") List<Long> infoIds,
										 @Param("ids") List<Long> ids,
										 @Param("valid") Integer valid,
										 @Param("payStatus") Integer payStatus,
										 @Param("offset") Integer offset,
										 @Param("limit") Integer limit);

	Long getListCount(@Param("infoIds") List<Long> infoIds,
					  @Param("ids") List<Long> ids,
					  @Param("valid") Integer valid,
					  @Param("payStatus") Integer payStatus);

	List<CrowdfundingOrder> getListByInfoId(@Param("crowdfundingId") long crowdfundingId,
											@Param("offset") Integer offset, @Param("limit") Integer limit);

    @DataSource(CfDataSource.CF_ORDER_NEW_SLAVE_DATASOURCE)
	List<CrowdfundingOrder> getListByInfoIdWithSuffixTableName(@Param("sharding") String sharding, @Param("crowdfundingId") long crowdfundingId,
															   @Param("offset") Integer offset, @Param("limit") Integer limit);

	List<CrowdfundingOrder> getSuccessByPage(@Param("id") Long id,
	                                         @Param("crowdfundingId") int crowdfundingId,
	                                         @Param("userId") Long userId,
	                                         @Param("amount") Integer amount,
	                                         @Param("comment") String comment,
	                                         @Param("ctimeStart") Date ctimeStart,
	                                         @Param("ctimeEnd") Date ctimeEnd,
	                                         @Param("offset") Integer offset,
	                                         @Param("limit") Integer limit,
	                                         @Param("amountStart") int amountStart,
	                                         @Param("amountEnd") int amountEnd);

	List<CrowdfundingOrder> getListByInfoIdsAndTime(@Param("infoIds") List<Long> infoIds,
													@Param("start") Date startTime,
													@Param("end") Date endTime,
													@Param("offset") int offset,
													@Param("limit") int limit);

	List<CrowdfundingOrder> getListByInfoIds(@Param("infoIds") List<Long> infoIds, @Param("anchorId") Long anchorId,
											 @Param("size") int size);

	List<CrowdfundingOrder> getByCrowdfundingIdsAndUserIds(@Param("infoIds") List<Long> infoIds, @Param("userIds") List<Long> userIds);

	@DataSource(CfDataSource.CF_ORDER_NEW_SLAVE_DATASOURCE)
	List<CrowdfundingOrder> getByCrowdfundingIdsAndUserIdsWithSuffixTableName(@Param("sharding") String sharding, @Param("ids") List<Long> ids);

	@DataSource(CfDataSource.CF_ORDER_NEW_SLAVE_DATASOURCE)
	List<CrowdfundingOrder> getAllPayByCrowdfundingIdsAndUserIdsWithSuffixTableName(@Param("sharding") String sharding, @Param("ids") List<Long> ids);

	List<Map<String, Object>> getOrderByInfoIds(@Param("list") List<Long> list,
												@Param("offset") int offset, @Param("limit") int limit);

	Integer getInfoCountPeople(@Param("crowdfundingId") long crowdfundingId);

	List<CrowdfundingOrder> getMoreThanAmount(@Param("cfId") long cfId,
											  @Param("amountInFen") int amountInFen,
											  @Param("limit") int limit);

	List<CaseDonationStatisticsModel> getCountSuccessByInfoIds(@Param("crowdfundingIds") List<Long> crowdfundingIds);

	@DataSource(CfDataSource.CF_ORDER_NEW_SLAVE_DATASOURCE)
	List<CaseDonationStatisticsModel> getCountSuccessByInfoIdsWithSuffixTableName(@Param("sharding") String sharding, @Param("crowdfundingIds") List<Long> crowdfundingIds);

	List<CrowdfundingOrder> selectByIdListSuccess(@Param("crowdfundingIds") List<Long> crowdfundingIds, @Param("list") List<Long> list);

	/**
	 * 限定 payStatus 为1
	 *
	 * @param crowdfundingId
	 * @param offset
	 * @param limit
	 * @return
	 */
	List<CrowdfundingOrder> getListByInfoIdAndPayStatus(@Param("crowdfundingId") int crowdfundingId,
														@Param("offset") Integer offset, @Param("limit") Integer limit);

	List<CrowdfundingOrder> selectByUserIdAndThirdType(@Param("userId") Long userId, @Param("crowdfundingIds") List<Long> crowdfundingIds,
													   @Param("thirdType") Integer thirdType,
													   @Param("offset") int offset,
													   @Param("limit") int limit);

	Long selectCountByUserIdAndThirdType(@Param("userId") Long userId, @Param("crowdfundingIds") List<Long> crowdfundingIds,
										 @Param("thirdType") Integer thirdType);

	CrowdfundingOrder selectExtreme(@Param("caseId") long caseId, @Param("orderHandle") int orderHandle);

	List<CrowdfundingOrder> selectOrderOneByInfoId(@Param("caseIds") Set<Long> caseIds);

	List<CfUserOrder> countByUserIds(@Param("caseIds") Set<Long> caseIds, @Param("userIds") List<Long> userIds, @Param("payStatus") Integer payStatus);

	/**
	 * 没有调用了
	 *
	 * @param caseIds
	 * @param userId
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	@Deprecated
	List<CrowdfundingOrder> findCrowdfundingOrder(@Param("caseIds") Set<Long> caseIds,
												  @Param("userId") Long userId,
												  @Param("startTime") String startTime,
												  @Param("endTime") String endTime);

	List<CrowdfundingOrder> getByAnchorIdAndPayStatus(@Param("crowdfundingId") Long crowdfundingId,
													  @Param("anchorId") Long anchorId, @Param("limit") Integer limit,
													  @Param("pagingType") String pagingType);

	Long getListCountByInfoIdsAndTime(@Param("infoIds") List<Long> collect,
									  @Param("start") Date startTime,
									  @Param("end") Date endTime);

	@Operate(OperateType.READ)
	CrowdfundingOrder getById(@Param("crowdfundingId") long crowdfundingId, @Param("id") Long id);

	/**
	 * 限制有效且支付为 0
	 *
	 * @param crowdfundingId
	 * @param offset
	 * @param limit
	 * @return
	 */
	List<CrowdfundingOrder> getNoPayListByInfoId(@Param("crowdfundingId") int crowdfundingId,
												 @Param("offset") Integer offset, @Param("limit") Integer limit);

	@DataSource(CfDataSource.CF_ORDER_NEW_SLAVE_DATASOURCE)
	List<Long> getOrderIdByInfoId(@Param("sharding") String sharding,
									 @Param("crowdfundingId") long crowdfundingId,
									 @Param("offset") int offset,
									 @Param("limit") int limit);


	int donatorCountByCaseId(@Param("caseId") int caseId);

	int donatorCountByCaseIdAndPayTime(@Param("caseId") int caseId, @Param("payTime") Date payTime);

	List<CrowdfundingOrder> listByCaseIdAndUserId(@Param("sharding") String sharding,
												  @Param("caseId") int caseId,
												  @Param("userId") Long userId);

	CrowdfundingOrder getOrderByCaseIdAndCode(@Param("sharding") String sharding,@Param("caseId") int caseId, @Param("code") String code);

	int getCaseOrderCountByPayTime(@Param("sharding") String sharding,
								   @Param("caseId") long caseId,
								   @Param("startTime") Date startTime,
								   @Param("endTime") Date endTime);

	List<CrowdfundingOrder> getLatelyCaseOrder(@Param("sharding") String sharding,
											   @Param("caseId") long caseId,
											   @Param("limit") int limit);

	List<CrowdfundingOrder> getOrderByOrderIdRangeTime(@Param("caseIdList") List<Long> caseIdList,
													   @Param("startTime") String startTime,
													   @Param("endTime") String endTime,
													   @Param("userId") long userId);


	List<CrowdfundingOrder> getPaySuccessByUserId(@Param("userIds") Set<Long> userIds,
										@Param("crowdfundingId") Long crowdfundingId);

	CrowdfundingOrder getNewPaySuccessByUserId(@Param("userId") Long userId,
												  @Param("crowdfundingId") Long crowdfundingId);


	List<CrowdfundingOrder> getAllBySearchDto(@Param("orderSearchDto") OrderSearchDto orderSearchDto);


	CrowdfundingOrder getOrderByCaseIdAndCodeForUser(@Param("sharding") String sharding,
													 @Param("caseId") long caseId,
													 @Param("code") String orderCode,
													 @Param("userId") long userId);

    List<CrowdfundingOrder> getByAmountAndCaseId(@Param("sharding") String sharding,
												 @Param("caseId") long caseId,
												 @Param("anchorId") Long anchorId,
												 @Param("amount") Integer amount,
												 @Param("limit") int limit);

	@DataSource(CfDataSource.CF_ORDER_NEW_SLAVE_DATASOURCE)
	List<Integer> getUserCountByCaseId(@Param("sharding") String sharding,
									   @Param("caseId") long caseId,
									   @Param("countLimit") int countLimit);

	@DataSource(CfDataSource.CF_ORDER_NEW_SLAVE_DATASOURCE)
	CfCountSumVo selectCountByMin(@Param("sharding") String sharding,
								  @Param("begin") Timestamp begin,
								  @Param("end") Timestamp end,
								  @Param("payStatus") Integer payStatus);
	/**
	 * 过滤掉单笔退款的订单
	 * @param crowdfundingId
	 * @return
	 */
	List<Long> getSingleRefundSuccessAndPaySuccessUserIdListByCrowdfundingId(@Param("crowdfundingId") int crowdfundingId,
																			 @Param("offset") int offset,
																			 @Param("limit") int limit);

    List<CrowdfundingOrder> getListByUserIdAndAnchorIdAndSingleRefundFlag(@Param("crowdfundingId") int crowdfundingId, @Param("userId") long userId, @Param("anchorId") long anchorId, @Param("limit") int limit);

	List<CrowdfundingOrder> getAllByAnchorIdAndUserIdAndSingleRefundFlag(@Param("crowdfundingId") int crowdfundingId, @Param("anchorId") long anchorId, @Param("limit") int limit, @Param("userIds") Set<Long> userIds, @Param("amount") int amount);

    List<CrowdfundingOrder> getByAnchorIdAndTimeAndSingleRefundFlag(@Param("crowdfundingId") int crowdfundingId, @Param("anchorId") long anchorId, @Param("limit") int limit, @Param("startTime") String startTime, @Param("endTime") String endTime);

	List<CrowdfundingOrder> getByAnchorIdAndSingleRefundFlag(@Param("crowdfundingId") int crowdfundingId, @Param("anchorId") long anchorId, @Param("limit") int limit);

    List<Long> getValidUserIdByCaseIdAndNotAnonymous(@Param("caseId") int caseId, @Param("size") int size, @Param("offset") int offset);

    List<CrowdfundingOrder> getByUserIdsAndCaseIdWithNotSingleRefundFromSharding(@Param("userIdSet") Set<Long> userIdSet, @Param("caseId") int caseId);

	List<CrowdfundingOrder> getByUserIdsAndCaseId(@Param("userIds") Set<Long> userIds, @Param("caseId") int caseId);
}
