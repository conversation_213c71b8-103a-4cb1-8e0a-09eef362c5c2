package com.shuidihuzhu.cf.dao.mina;

import com.shuidihuzhu.cf.model.mina.CfMinaQuizResultConfig;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by dongcf on 2018/3/12
 */
@DataSource(DS.FRAME)
public interface CfMinaQuizResultConfigDao {

    List<CfMinaQuizResultConfig> findByQuizResultId(@Param("minaQuizResultId")Integer minaQuizResultId);

    Integer updateInvalidById(@Param("id")Integer id);

    Integer insert(CfMinaQuizResultConfig cfMinaQuizResultConfig);

    List<CfMinaQuizResultConfig> findByQuizResultIds(@Param("quizResultIds")List<Integer> quizResultIds);
}
