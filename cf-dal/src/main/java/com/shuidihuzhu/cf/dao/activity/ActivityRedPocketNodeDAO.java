package com.shuidihuzhu.cf.dao.activity;

import com.shuidihuzhu.cf.domain.activity.ActivityRedPocketDO;
import com.shuidihuzhu.cf.enums.activity.ActivityRedPocketStatusEnum;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * Created by sven on 2020/4/26.
 *
 * <AUTHOR>
 */
@DataSource(DS.CF)
public interface ActivityRedPocketNodeDAO {

    int insertList(List<ActivityRedPocketDO> list);

    @DataSource(DS.CF_SLAVE)
    List<ActivityRedPocketDO> getByCaseId(@Param("caseId") int caseId);

    @DataSource(DS.CF_SLAVE)
    ActivityRedPocketDO getByNodeId(@Param("caseId") int caseId, @Param("id") long id);

    int updateSendResult(@Param("caseId") int caseId,@Param("id") long id);

    int updateNeedAlert(@Param("caseId") int caseId,@Param("id") long id);

    int addMoney(@Param("caseId") int caseId,@Param("id") long id,@Param("money") int money);

    int updateStatus(@Param("caseId") int caseId, @Param("id") long id,
                     @Param("status")ActivityRedPocketStatusEnum status);

    int updateStatusStartTime(@Param("caseId") int caseId, @Param("id") long id,
                     @Param("status")ActivityRedPocketStatusEnum status,
                     @Param("startTime") Date startTime);

    int updateStatusStartTimeEndTime(@Param("caseId") int caseId, @Param("id") long id,
                              @Param("status")ActivityRedPocketStatusEnum status,
                              @Param("startTime") Date startTime,
                              @Param("endTime") Date endTime);

    int updateStatusEndTime(@Param("caseId") int caseId, @Param("id") long id,
                     @Param("status")ActivityRedPocketStatusEnum status,
                     @Param("endTime") Date endTime);
}
