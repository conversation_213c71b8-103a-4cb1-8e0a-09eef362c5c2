package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;


/**
 * <AUTHOR>
 */
@DataSource(DS.CF)
public interface CaseLabelsJinYunCountyVerificationDao {

    @DataSource(DS.CF_SLAVE)
    int getCount(@Param("infoUuid") String infoUuid);

    int insert(@Param("infoUuid") String infoUuid);

    int remove(@Param("infoUuid") String infoUuid);

}
