package com.shuidihuzhu.cf.dao.type.handler;

import com.google.common.base.Preconditions;
import com.shuidihuzhu.cf.enums.activity.Activity111CaseTypeEnum;
import com.shuidihuzhu.cf.enums.activity.ActivityRedPocketStatusEnum;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.TypeHandler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Created by sven on 2020/4/26.
 *
 * <AUTHOR>
 */
public class ActivityRedPocketStatusEnumTypeHandler implements TypeHandler<ActivityRedPocketStatusEnum> {
    @Override
    public void setParameter(PreparedStatement ps, int i, ActivityRedPocketStatusEnum parameter, JdbcType jdbcType) throws SQLException {
        Preconditions.checkNotNull(parameter);
        ps.setInt(i, parameter.getCode());
    }

    @Override
    public ActivityRedPocketStatusEnum getResult(ResultSet rs, String columnName) throws SQLException {
        int type = rs.getInt(columnName);
        return ActivityRedPocketStatusEnum.parse(type);
    }

    @Override
    public ActivityRedPocketStatusEnum getResult(ResultSet rs, int columnIndex) throws SQLException {
        int type = rs.getInt(columnIndex);
        return ActivityRedPocketStatusEnum.parse(type);
    }

    @Override
    public ActivityRedPocketStatusEnum getResult(CallableStatement cs, int columnIndex) throws SQLException {
        int type = cs.getInt(columnIndex);
        return ActivityRedPocketStatusEnum.parse(type);
    }
}
