package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfCapitalTotal;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: lixuan
 * @date: 2018/3/21 12:03
 */
@DataSource(DS.CF)
public interface CfCapitalTotalDao {

    int saveBatch(@Param("cfCapitalTotalList") List<CfCapitalTotal> cfCapitalTotalList);

    int updateForDelete(@Param("date") String date);

    @DataSource(DS.CF_SLAVE)
    List<CfCapitalTotal> getByInfoUuidAndDates(@Param("infoUuid") String infoUuid, @Param("dates") List<String> dates);

    @DataSource(DS.CF_SLAVE)
    CfCapitalTotal getByInfoUuidAndDate(@Param("infoUuid") String infoUuid, @Param("date") String date);

    @DataSource(DS.CF_SLAVE_2)
    CfCapitalTotal getLastOneByInfoUuid(@Param("infoUuid") String infoUuid, @Param("date") String date);
}
