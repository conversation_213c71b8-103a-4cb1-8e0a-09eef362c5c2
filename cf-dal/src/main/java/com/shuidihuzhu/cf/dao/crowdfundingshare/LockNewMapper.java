package com.shuidihuzhu.cf.dao.crowdfundingshare;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CfLock;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-07-07
 */
@DataSource(CfDataSource.SHUIDI_CROWDFUNDING_SHARE_MASTER)
public interface LockNewMapper {

    int tryLock(@Param("userKey") String userKey, @Param("sysKey") String sysKey, @Param("keyType") int keyType, @Param("expiryTime") Timestamp expiryTime);

    int checkLock(@Param("userKey") String userKey);

}
