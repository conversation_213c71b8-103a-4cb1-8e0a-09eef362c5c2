package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfInfoProgressFollow;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.CF)
public interface CfInfoProgressFollowDao {
	int deleteByInfoUuidAndUserId(@Param("infoUuid") String infoUuid, @Param("userId")long userId);

	int insert(CfInfoProgressFollow record);
	@DataSource(DS.CF_SLAVE)
	CfInfoProgressFollow selectByInfoUuidAndUserId(@Param("infoUuid") String infoUuid, @Param("userId")long userId);
	@DataSource(DS.CF_SLAVE)
	List<CfInfoProgressFollow> selectByInfoUuidWithFollow(@Param("infoUuid") String infoUuid, @Param("id") Integer id,
	                                                      @Param("limit") Integer limit);

	int followCfInfo(@Param("infoUuid") String infoUuid, @Param("userId")long userId);

	int unfollowCfInfo(@Param("infoUuid") String infoUuid, @Param("userId")long userId);

}
