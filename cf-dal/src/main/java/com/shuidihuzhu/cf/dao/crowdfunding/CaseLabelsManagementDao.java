package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;


/**
 * <AUTHOR>
 */
@DataSource(DS.CF)
public interface CaseLabelsManagementDao {
    int add(@Param("infoUuid") String infoUuid, @Param("labelsManagement") String labelsManagement);

    @DataSource(DS.CF_SLAVE)
    String get(@Param("infoUuid") String infoUuid);

    int update(@Param("infoUuid") String infoUuid, @Param("labelsManagement") String labelsManagemen);
}
