package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.fulllink.measurement.annotation.Operate;
import com.shuidihuzhu.cf.fulllink.measurement.utils.OperateType;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingTreatment;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;

/**
 * Created by wuxinlong on 7/5/16.
 */

@DataSource(DS.CF)
public interface CrowdfundingTreatmentDao {
	
    int add(CrowdfundingTreatment crowdfundingTreatment);

    int update(CrowdfundingTreatment crowdfundingTreatment);

    @DataSource(DS.CF_SLAVE)
    @Operate(OperateType.READ)
	CrowdfundingTreatment get(@Param("crowdfundingId") int crowdfundingId);

	@DataSource(DS.CF_SLAVE)
	List<CrowdfundingTreatment> getByInfoIdList(@Param("infoIdList") List<Integer> infoIdList);

	@DataSource(DS.STAT_CF)
    List<String> selectDiseaseName(@Param("disease") String disease, @Param("judge") int judge,
                                   @Param("start") int start, @Param("size") int size);
	
}
