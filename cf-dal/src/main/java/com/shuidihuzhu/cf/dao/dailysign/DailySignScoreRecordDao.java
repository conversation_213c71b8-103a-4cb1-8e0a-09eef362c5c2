package com.shuidihuzhu.cf.dao.dailysign;

import com.shuidihuzhu.cf.model.dailysign.DailySign;
import com.shuidihuzhu.cf.model.dailysign.DailySignScoreRecord;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * Created by wangsf on 18/4/27.
 */
@DataSource(DS.CF)
public interface DailySignScoreRecordDao {

	int insert(DailySignScoreRecord record);

	int updateConfirm(@Param("id") long id, @Param("tradeNo") String tradeNo);

	@DataSource(DS.CF_SLAVE)
	DailySignScoreRecord getBySignId(@Param("signId") long signId);
}
