package com.shuidihuzhu.cf.dao.topList;

import com.shuidihuzhu.cf.model.topList.CfTopListLifeCycleRecord;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource("cfUserDataSource")
public interface CfTopListLifeCycleRecordDAO {

    int insertLifeCycleRecordList(@Param("tableIndex") String tableIndex, @Param("list") List<CfTopListLifeCycleRecord> records);

    int updateThanksTime(@Param("tableIndex") String tableIndex, @Param("ids") List<Long> ids, @Param("thanksTime") String time);

    int updateInviteTime(@Param("tableIndex") String tableIndex, @Param("ids")  List<Long> ids, @Param("inviteTime") String time);

    int updatePrizeTime(@Param("tableIndex") String tableIndex, @Param("ids") List<Long> ids,
                        @Param("userPrizeTime") String time, @Param("prizeChannel")String prizeChannel);

    int updateIfPrize(@Param("tableIndex") String tableIndex, @Param("ids") List<Long> ids);

    @DataSource("cfUserDataSourceSlave")
    CfTopListLifeCycleRecord selectLastByCaseIdAndUserId(@Param("tableIndex") String tableIndex,
                                             @Param("caseId") int caseId, @Param("userId") long userId,
                                             @Param("topListId") int topListTypeId);

    @DataSource("cfUserDataSourceSlave")
    List<CfTopListLifeCycleRecord> selectRankHistoryByCaseId(@Param("tableIndex") String tableIndex,
                                                             @Param("caseId") int caseId,  @Param("topListId") int topListTypeId);


    List<CfTopListLifeCycleRecord> selectRankHistoryByCaseIdAndUserId(@Param("tableIndex") String tableIndex,
                                                                      @Param("caseId") int caseId,
                                                                      @Param("userId") long userId,
                                                                      @Param("topListId") int topListTypeId
    );
}
