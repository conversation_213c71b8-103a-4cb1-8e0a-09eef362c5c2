package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplatize;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@DataSource(DS.CF)
public interface CfTemplateUseSceneMappingDao {

    @DataSource(DS.CF_SLAVE)
    List<CfBaseInfoTemplatize.TemplateUseSceneMapping> selectByTemplateIds(@Param("templateIds")List<Integer> templateIds);

    int deleteByTemplateIds(@Param("templateIds") List<Integer> templateIds);

    int insertUseSceneMapping(@Param("list")List<CfBaseInfoTemplatize.TemplateUseSceneMapping> mappings);

    @DataSource(DS.CF_SLAVE)
    List<Integer> selectByTemplateIdsAndUseScene(@Param("templateIds")List<Integer> templateIds,
                                     @Param("useScene") int useScene);
    @DataSource(DS.CF_SLAVE)
    List<Integer> selectTemplateIdsByUseScene(@Param("useScene") int useScene);
}
