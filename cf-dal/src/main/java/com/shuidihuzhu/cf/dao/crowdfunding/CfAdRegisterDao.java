package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfAdRegister;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;


/**
 * Created by wangsf on 17/4/27.
 */

@DataSource(DS.CF)
public interface CfAdRegisterDao {

	int insert(CfAdRegister cfAdRegister);

	@DataSource(DS.CF_SLAVE)
	CfAdRegister selectByMobileAndTypeAndDate(@Param("cryptoMobile") String cryptoMobile, @Param("type") String type, @Param("dayKey") Date dayKey);

	@DataSource(DS.CF_SLAVE_2)
	List<CfAdRegister> selectByTime(@Param("start") Timestamp start, @Param("end") Timestamp end, @Param("offset") int offset, @Param("limit") int limit);
	@DataSource(DS.CF_SLAVE)
	CfAdRegister selectByChannelAndMobile (@Param("cryptoMobile") String cryptoMobile, @Param("list") List<String> list);
	
	int updateById(@Param("cfAdRegister") CfAdRegister cfAdRegister );

	@DataSource(DS.CF_SLAVE)
	CfAdRegister selectByMobile(@Param("cryptoMobile") String cryptoMobile);

	@DataSource(DS.CF_SLAVE)
	CfAdRegister getById(@Param("id") long id);
}
