package com.shuidihuzhu.cf.dao.miniprogram;

import com.shuidihuzhu.cf.model.miniprogram.CfCommentPraisedNumber;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@DataSource(DS.FRAME)
public interface CfCommentPraisedNumberDao {
    int insertOne(@Param("userId") long userId, @Param("praisedNumber") int praisedNumber, @Param("date") Date date);

    @DataSource(DS.FRAME_SLAVE)
    List<CfCommentPraisedNumber> listByUserIdsAndDate(@Param("userIds") List<Long> userIds, @Param("date") Date date);

    int insertList(@Param("cfCommentPraisedNumbers") List<CfCommentPraisedNumber> cfCommentPraisedNumbers);
}
