package com.shuidihuzhu.cf.dao.inventory;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.domain.CfImageAiMaskDO;
import com.shuidihuzhu.cf.domain.UserDeviceInfo;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/1/11 5:06 下午
 */
@DataSource(CfDataSource.ADMIN_DB_MASTER)
public interface CfInventoryDao {

    int insertDeviceInfoList(@Param("userDeviceInfos") List<UserDeviceInfo> userDeviceInfos);

    UserDeviceInfo getOneDeviceInfoByUserId(@Param("userId") Long userId, @Param("identification") String identification);

    int getDeviceInfoCount(@Param("userId") Long userId, @Param("identification") String identification, @Param("time") Timestamp time);

}
