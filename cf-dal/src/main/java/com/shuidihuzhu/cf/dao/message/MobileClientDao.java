//package com.shuidihuzhu.cf.dao.message;
//
//import com.shuidihuzhu.common.datasource.DS;
//import com.shuidihuzhu.common.datasource.annotation.DataSource;
//import com.shuidihuzhu.common.web.model.mobile.MobileClient;
//import org.apache.ibatis.annotations.Param;
//
//import java.util.List;
//import java.util.Map;
//
//@DataSource(DS.SD)
//public interface MobileClientDao {
//
//	List<MobileClient> get(Map<String, Object> params);
//
//	List<MobileClient> getByRegStatus(@Param("eventStatus") int eventStatus, @Param("userIdList") List<Long> userIdList);
//
//	int save(MobileClient mobileClient);
//
//	int updateRegStatusById(@Param("eventStatus") int eventStatus, @Param("id") long id);
//
//	int updateRegStatusByIdList(@Param("eventStatus") int eventStatus, @Param("idList") List<Long> idList);
//
//	int updateRegStatusByUserId(@Param("eventStatus") int eventStatus, @Param("userId") long userId, @Param("bizType") int bizType);
//
//	int updateRegStatusByUserIdAndClientId(@Param("eventStatus") int eventStatus, @Param("userId") long userId,
//                                           @Param("clientId") String clientId);
//
//	List<MobileClient> getByUserIdList(@Param("userIdList") List<Long> userIdList, @Param("eventStatus") Integer eventStatus
//            , @Param("bizType") Integer bizType);
//
//}
