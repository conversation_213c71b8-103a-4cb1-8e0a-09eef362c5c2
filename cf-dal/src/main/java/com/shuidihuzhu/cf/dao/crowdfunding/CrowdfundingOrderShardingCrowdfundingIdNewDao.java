package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * @author: wanghui
 * @time: 2019/3/22 5:50 PM
 * @description: 以crowdfundingid分表的 dao
 */

@DataSource(CfDataSource.SHARDING_DONATION_BIG_TABLE_NEW_MASTER)
public interface CrowdfundingOrderShardingCrowdfundingIdNewDao {

    int addOrder(CrowdfundingOrder order);

    int updatePayStatus(@Param("crowdfundingId") long crowdfundingId, @Param("id") long id, @Param("payStatus") int payStatus, @Param("payTime") Date payTime);

    int updateValid(@Param("crowdfundingId") long crowdfundingId, @Param("valid") int valid, @Param("id") long id);

    void updateUserId(@Param("order") CrowdfundingOrder order);

    int editByIdAndComment(@Param("comment") String comment, @Param("crowdfundingId") Long crowdfundingId, @Param("id") long id);

    CrowdfundingOrder getUserOrderByUserId(@Param("crowdfundingId") Long crowdfundingId, @Param("userId") long userId);

    List<CrowdfundingOrder> getListByUserId(@Param("crowdfundingId") long crowdfundingId, @Param("userId") long userId,
                                            @Param("limit") int limit);

    Long getSuccessCount(@Param("id") Long id,
                         @Param("crowdfundingId") int crowdfundingId,
                         @Param("userId") Long userId,
                         @Param("amount") Integer amount,
                         @Param("comment") String comment,
                         @Param("ctimeStart") Date ctimeStart,
                         @Param("ctimeEnd") Date ctimeEnd,
                         @Param("amountStart") int amountStart,
                         @Param("amountEnd") int amountEnd);

    /**
     * 读主库
     *
     * @param crowdfundingId
     * @param id
     * @return
     */
    CrowdfundingOrder getByIdFromMaster(@Param("crowdfundingId") long crowdfundingId, @Param("id") Long id);

    /**
     * 判断用户是否捐过款
     * 不关心是否退款
     *
     * @param crowdfundingId
     * @param userId
     * @return
     */
    CrowdfundingOrder getDonateByUserId(@Param("crowdfundingId") Long crowdfundingId, @Param("userId") long userId);

    int updateAnonymousValid(@Param("anonymous") boolean anonymous, @Param("code") String orderCode, @Param("caseId") int caseId);

    /**
     * 更新单笔退款
     *
     * @param crowdfundingId -
     * @param id             -
     * @return -
     */
    int updateSingleRefund(@Param("crowdfundingId") int crowdfundingId, @Param("id") long id);

    int updateSingleRefundBatch(@Param("crowdfundingId") int crowdfundingId, @Param("ids") List<Long> ids);

}
