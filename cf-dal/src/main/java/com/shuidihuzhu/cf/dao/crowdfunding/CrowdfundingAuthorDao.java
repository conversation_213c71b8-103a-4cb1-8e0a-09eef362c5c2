package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.fulllink.measurement.annotation.Operate;
import com.shuidihuzhu.cf.fulllink.measurement.utils.OperateType;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;


/**
 * Created by wuxinlong on 6/21/16.
 */

@DataSource(DS.CF)
public interface CrowdfundingAuthorDao {
	
	@DataSource(DS.CF_SLAVE)
	@Operate(OperateType.READ)
	CrowdfundingAuthor get(int crowdfundingId);

	@DataSource(DS.CF_SLAVE)
	List<CrowdfundingAuthor> getByName(@Param("name") String name);

    int add(CrowdfundingAuthor crowdfundingAuthor);

    int update(CrowdfundingAuthor crowdfundingAuthor);

	//只更新身份信息
	int updateIdentity(CrowdfundingAuthor crowdfundingAuthor);

    @DataSource(DS.CF_SLAVE)
	List<CrowdfundingAuthor> getByInfoIdList(@Param("infoIdList") List<Integer> infoIdList);

	@DataSource(DS.CF_SLAVE)
	int getApplyCount(@Param("name") String name, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

	int updateInsurance(@Param("caseId") int caseId,
						@Param("healthInsurance") int healthInsurance,
						@Param("commercialInsurance") int commercialInsurance);

	@DataSource(DS.CF_SLAVE)
	List<CrowdfundingAuthor> getByCryptoIdCards(@Param("list") Collection<String> list);

	@DataSource(DS.CF_SLAVE)
	List<Integer> getCaseIdByCryptoIdCards(@Param("list") Collection<String> list);

	@DataSource(DS.CF_SLAVE)
    List<CrowdfundingAuthor> getByNameAndCreateTime(@Param("name") String name, @Param("createTime") Date createTime);
}
