package com.shuidihuzhu.cf.dao.bigapp;

import com.shuidihuzhu.cf.bigapp.AppUpdate;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * Author:梁洪超
 * Date:2017/11/19
 */
@DataSource(DS.CF_SLAVE)
public interface AppUpdateDao {

 AppUpdate getAppUpdateByVersion (@Param("selfVersion") int selfVersion , @Param("bundleId") String bundleId ,
                                  @Param("systemType") String systemType);
	
}