package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.domain.CfCaseReachedStandard;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

@DataSource(DS.CF)
public interface CfCaseReachedStandardDAO {

    @DataSource(DS.CF_SLAVE)
    CfCaseReachedStandard getByInfoIdAndUserIdAndUnqiueCode(@Param("infoId")String infoId,
                                                            @Param("userId")long userId,
                                                            @Param("uniqueCode")String uniqueCode);

    int insert(CfCaseReachedStandard cfCaseReachedStandard);

}
