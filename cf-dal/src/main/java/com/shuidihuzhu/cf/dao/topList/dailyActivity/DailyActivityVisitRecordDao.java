package com.shuidihuzhu.cf.dao.topList.dailyActivity;

import com.shuidihuzhu.cf.model.topList.DailyActivityTopList;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;
import org.springframework.context.annotation.Bean;

@DataSource("cfUserInfoShardingCrowdfunding")
public interface DailyActivityVisitRecordDao {

    @Bean("cfUserInfoShardingCrowdfundingSlave")
    DailyActivityTopList.BringVisitRecord selectVisitRecord(@Param("userId") long userId,
                                                            @Param("visitUserId") long visitUserId,
                                                            @Param("activityPeriod") int activityPeriod);

    int insertVisitRecord(@Param("userId") long userId,
                          @Param("visitUserId") long visitUserId,
                          @Param("activityPeriod") int activityPeriod);
}
