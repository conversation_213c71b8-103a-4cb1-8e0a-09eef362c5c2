package com.shuidihuzhu.cf.dao.toG.hainan;

import com.shuidihuzhu.cf.domain.toG.JinYunListPageQuery;
import com.shuidihuzhu.cf.domain.toG.hainan.CfHainanShowRecord;
import com.shuidihuzhu.cf.enums.toG.FundraisingListParam;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@DataSource(DS.CF)
public interface CfHainanShowRecordDao {
    @DataSource(DS.CF_SLAVE)
    CfHainanShowRecord selectByCaseId(@Param("caseId") int caseId);

    int updateFinishStatus(@Param("finishStatus") int finishStatus, @Param("caseId") int caseId);

    int updateFirstApproveStatus(@Param("firstApproveStatus") int firstApproveStatus, @Param("caseId") int caseId);

    int add(CfHainanShowRecord cfHainanShowRecord);

    // 复用缙云查询model
    List<CfHainanShowRecordDao> selectPage(JinYunListPageQuery jinYunListPageQuery);

    // 复用缙云查询model
    int countByPage(JinYunListPageQuery jinYunListPageQuery);

    // 复用缙云查询model
    @DataSource(DS.CF_SLAVE)
    List<CfHainanShowRecord> listByParam(FundraisingListParam fundraisingListParam);

    int removeByCaseId(@Param("caseId") int caseId);

    @DataSource(DS.CF_SLAVE)
    List<CfHainanShowRecord> getListByCaseIdList(@Param("caseIdList") List<Integer> caseIdList);


}
