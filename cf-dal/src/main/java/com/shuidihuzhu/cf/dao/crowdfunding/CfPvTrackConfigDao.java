package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfPvTrackConfig;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by wangsf on 17/5/22.
 */

@DataSource(DS.CF_SLAVE)
public interface CfPvTrackConfigDao {

	CfPvTrackConfig getByPage(@Param("page") String page);

	List<CfPvTrackConfig> list();

}
