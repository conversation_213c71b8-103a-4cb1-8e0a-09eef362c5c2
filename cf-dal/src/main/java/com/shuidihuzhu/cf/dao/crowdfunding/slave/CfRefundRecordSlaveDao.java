package com.shuidihuzhu.cf.dao.crowdfunding.slave;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.model.crowdfunding.NewCfRefundRecord;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(CfDataSource.DONATION_BIGTABLE_SLAVE_1)
public interface CfRefundRecordSlaveDao {

    List<NewCfRefundRecord> getByInfoUuidAndAnchorId(@Param("infoUuid") String infoUuid,
                                                     @Param("anchorId") Long anchorId,
                                                     @Param("limit") int limit);
}
