package com.shuidihuzhu.cf.dao.tdsql;


import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingComment;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@DataSource(CfDataSource.TD_CF_DS)
public interface TdCrowdfundingCommentShareDao {

    List<CrowdfundingComment> getReplyByParentList(@Param("type") Integer type,
                                                   @Param("orderIdList") List<Long> orderIdList,
                                                   @Param("limit") int limit);

}

