package com.shuidihuzhu.cf.dao.caseinfo;

import com.shuidihuzhu.cf.domain.caseinfo.CfInfoCapitalCompensationDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2019-04-18  16:17
 */
@DataSource(DS.CF)
public interface InfoCapitalCompensationDAO {

    int insert(CfInfoCapitalCompensationDO cfInfoCapitalCompensationDO);

    int updateByCaseId(CfInfoCapitalCompensationDO cfInfoCapitalCompensationDO);

    int updateStatusByCaseId(@Param("caseId")int caseId, @Param("status")int status);

    @DataSource(DS.CF_SLAVE)
    CfInfoCapitalCompensationDO getByCaseId(@Param("caseId")int caseId);
}
