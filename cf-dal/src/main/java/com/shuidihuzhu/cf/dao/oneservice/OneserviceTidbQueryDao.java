package com.shuidihuzhu.cf.dao.oneservice;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/2/20
 */
@DataSource(CfDataSource.TIDB_COMMON_SHUIDI_RPT)
public interface OneserviceTidbQueryDao {

    Map<String, Object> getShareAvgData(@Param("dt") String dt);

    Map<String, Object> getCaseStatData(@Param("infoId") String infoId, @Param("userId") long userId, @Param("dt") String dt);

    List<String> getSettleCaseIds(@Param("dt") String dt, @Param("infoIds") List<String> infoIds, @Param("offset") int offset, @Param("size") int size);
}
