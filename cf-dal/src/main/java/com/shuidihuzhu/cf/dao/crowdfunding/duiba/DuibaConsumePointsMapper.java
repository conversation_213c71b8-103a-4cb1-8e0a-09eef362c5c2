package com.shuidihuzhu.cf.dao.crowdfunding.duiba;


import com.shuidihuzhu.cf.model.crowdfunding.CreditConsumeParamsModel;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @package: com.shuidihuzhu.baseservice.userpoints.dao
 * @Author: liujiawei
 * @Date: 2018/8/6  16:48
 */
@DataSource("userPointsDataSource")
public interface DuibaConsumePointsMapper {
    int insert(CreditConsumeParamsModel creditConsumeParams);

    CreditConsumeParamsModel selectByOrderNum(@Param("orderNum") String orderNum);

    CreditConsumeParamsModel selectByOrderNumIngoreDel(@Param("orderNum") String orderNum);

    List<CreditConsumeParamsModel> selectByUserId(@Param("userId") Long userId);

    int deleteByOrderNum(@Param("orderNum") String orderNum);
}
