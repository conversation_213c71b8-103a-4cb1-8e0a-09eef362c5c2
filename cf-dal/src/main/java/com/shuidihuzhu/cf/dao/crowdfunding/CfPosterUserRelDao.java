package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfPosterUserRel;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * Created by wangsf on 17/11/13.
 */
@DataSource(DS.CF)
public interface CfPosterUserRelDao {

	int insert(CfPosterUserRel cfPosterUserRel);

	@DataSource(DS.CF_SLAVE)
	CfPosterUserRel findByActivityIdAndUserId(@Param("activityId") int activityId, @Param("userId")long userId);

	int updateShareStatus(@Param("activityId") int activityId, @Param("userId")long userId, @Param("status") int status);

}
