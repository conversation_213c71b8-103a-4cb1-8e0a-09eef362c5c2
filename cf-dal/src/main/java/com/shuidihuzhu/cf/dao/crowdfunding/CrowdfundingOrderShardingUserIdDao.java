package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoStat;
import com.shuidihuzhu.cf.model.crowdfunding.CfUserOrder;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrderShardingModel;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * @author: wanghui
 * @time: 2019/3/13 3:53 PM
 * @description: 以userid 分表的dao
 */

@DataSource(CfDataSource.SHARDING_DONATION_BIG_TABLE_SLAVE)
public interface CrowdfundingOrderShardingUserIdDao {

	@DataSource(CfDataSource.DONATION_BIGTABLE_MASTER)
	int addCrowdfundingOrderShardingModelBatch(@Param("sharding") String sharding,@Param("orders") List<CrowdfundingOrder> orders);
	@DataSource(CfDataSource.DONATION_BIGTABLE_MASTER)
	List<Long> getExistIdByIds(@Param("sharding") String sharding,@Param("orderIds") List<Long> orderIds);
	@DataSource(CfDataSource.SHARDING_DONATION_BIG_TABLE_MASTER)
    int deleteByUserIdWithOrderIds(@Param("userId") long userId,@Param("orderIds") List<Long> orderIds);

	List<CrowdfundingOrderShardingModel> getCrowdfundingOrderShardingModelListByUserId(@Param("userId") long userId);

	List<CrowdfundingOrderShardingModel> getCrowdfundingOrderShardingModelListByUserIdWithLimit(@Param("userId") long userId,
																								@Param("anchorId") Long anchorId,
																								@Param("offset") Integer offset,
																								@Param("limit") Integer limit);

	List<CrowdfundingOrderShardingModel> getCrowdfundingOrderShardingModelListByUserIds(@Param("userIds") List<Long> userIds);

	@DataSource(CfDataSource.DONATION_BIGTABLE_SLAVE_1)
	List<CrowdfundingOrderShardingModel> getCrowdfundingOrderShardingModelListByUserIdsWithTableSuffixName(@Param("sharding") String sharding,@Param("userIds") List<Long> userIds);

	List<CrowdfundingOrderShardingModel> getOrderShardingByUserIdRangeTime(@Param("userId") long userId,
																					   @Param("startTime")String startTime,
																					   @Param("endTime")String endTime);

}
