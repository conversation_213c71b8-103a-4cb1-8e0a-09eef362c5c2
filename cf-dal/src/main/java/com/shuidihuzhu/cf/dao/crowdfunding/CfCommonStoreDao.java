package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfCommonStoreModel;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2020/7/29
 */
@DataSource(DS.CF)
public interface CfCommonStoreDao {


    int insert(CfCommonStoreModel model);

    CfCommonStoreModel getByUserIdAndKey(@Param("userId") long userId,
                                         @Param("storeKey") String storeKey);

    List<CfCommonStoreModel> getByKeys(@Param("userId") long userId,
                                       @Param("storeKeys") List<String> storeKeys);


    int deleteUserKeyByType(@Param("userId") long userId,
                            @Param("storeType") int storeType);

}
