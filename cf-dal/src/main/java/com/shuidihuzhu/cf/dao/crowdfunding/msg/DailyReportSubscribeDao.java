package com.shuidihuzhu.cf.dao.crowdfunding.msg;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * Created by ch<PERSON> on 2017/11/10.
 */
@DataSource(DS.CF)
public interface DailyReportSubscribeDao {
	@DataSource(DS.CF_SLAVE)
	Boolean isSubscribe(@Param("infoUuid") String infoUuid);

	void updateSubscribe(@Param("infoUuid") String infoUuid,
	                     @Param("value") Boolean value);

}
