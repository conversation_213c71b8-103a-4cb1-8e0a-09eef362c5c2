package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingPushFlag;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * Created by ch<PERSON> on 16/7/15.
 */

@DataSource(DS.CF)
public interface CrowdFundingPushFlagDao {
    void add(CrowdFundingPushFlag pushFlag);

    void update(CrowdFundingPushFlag pushFlag);

    @DataSource(DS.CF_SLAVE)
    CrowdFundingPushFlag queryPushFlag(@Param("userId")long userId,
                          @Param("crowdFundingId") Integer crowdFundingId);
}
