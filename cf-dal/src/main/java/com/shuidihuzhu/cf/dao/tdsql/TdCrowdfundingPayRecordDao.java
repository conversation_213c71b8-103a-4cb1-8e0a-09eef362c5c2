package com.shuidihuzhu.cf.dao.tdsql;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@DataSource(CfDataSource.TD_CF_ORDER_DS)
public interface TdCrowdfundingPayRecordDao {

    List<CrowdfundingPayRecord> getAllPaySuccessByIdAndTime(@Param("id") long id, @Param("beginTime") Date beginTime,
                                                            @Param("endTime")Date endTime,@Param("size") int size);

    CrowdfundingPayRecord selectByOrderId(@Param("orderId") long orderId);
}
