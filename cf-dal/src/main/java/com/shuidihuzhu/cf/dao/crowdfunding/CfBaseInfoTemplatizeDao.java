package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplatize;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by Ahrievil on 2017/12/8
 */
@DataSource(DS.CF)
public interface CfBaseInfoTemplatizeDao {

    int insertOne(CfBaseInfoTemplatize cfBaseInfoTemplatize);

    int insertList(@Param("list")List<CfBaseInfoTemplatize> list);

    @DataSource(DS.CF_SLAVE)
    List<CfBaseInfoTemplatize> selectAllBaseInfoLimit(@Param("start") int start, @Param("size") int size);

    @DataSource(DS.CF_SLAVE)
    List<CfBaseInfoTemplatize> selectAllShareLimit(@Param("start") int start, @Param("size") int size);

    @DataSource(DS.CF_SLAVE)
    List<CfBaseInfoTemplatize> selectAllShareTemplate(@Param("channelType")Integer channelType, @Param("offset") Integer current, @Param("size") Integer size);

    @DataSource(DS.CF_SLAVE)
    List<CfBaseInfoTemplatize> selectByRelationAndContentType(@Param("relationType") int relationType,
                                                              @Param("contentType") int contentType,
                                                              @Param("start") int start, @Param("size") int size);


    @DataSource(DS.CF_SLAVE)
    List<CfBaseInfoTemplatize> selectAllBaseInfoByLimit(@Param("id") int id, @Param("limit") int limit);

}
