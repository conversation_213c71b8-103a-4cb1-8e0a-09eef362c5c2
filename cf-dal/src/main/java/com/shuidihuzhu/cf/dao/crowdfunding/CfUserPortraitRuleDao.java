package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfUserPortraitInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CfUserPortraitRule;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020/11/25  10:59 上午
 */
@DataSource(DS.CF)
public interface CfUserPortraitRuleDao {

    @DataSource(DS.CF_SLAVE)
    List<CfUserPortraitRule> getByUserPortraitId(@Param("userPortraitId") long userPortraitId);

    @DataSource(DS.CF_SLAVE)
    List<CfUserPortraitRule> getListByUserPortraitIds(@Param("userPortraitIds") List<Long> userPortraitIds);
}
