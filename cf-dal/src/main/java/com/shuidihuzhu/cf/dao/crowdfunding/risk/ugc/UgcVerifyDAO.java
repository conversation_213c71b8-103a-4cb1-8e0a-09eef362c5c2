package com.shuidihuzhu.cf.dao.crowdfunding.risk.ugc;

import com.shuidihuzhu.cf.domain.risk.RiskUgcVerifyDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2018-11-19  14:59
 */
@DataSource(DS.CF)
public interface UgcVerifyDAO {

    @DataSource(DS.CF_SLAVE)
    RiskUgcVerifyDO get(@Param("caseId") long caseId, @Param("ugcType") int ugcType, @Param("ugcId") long ugcId);

    @DataSource(DS.CF_SLAVE)
    List<RiskUgcVerifyDO> listByAnchor(@Param("start")long start, @Param("limit")int limit);

    @DataSource(DS.CF_SLAVE)
    List<RiskUgcVerifyDO> listByCaseAndAnchor(@Param("caseId")long caseId, @Param("start") long start, @Param("limit") int limit);

    @DataSource(DS.CF_SLAVE)
    Set<Long> getHitUgcIdList(@Param("caseId") long caseId,
                              @Param("ugcType") int ugcType,
                              @Param("ugcIdList") List<Long> ugcIdList);

    @DataSource(DS.CF_SLAVE)
    Set<Long> getAllHitUgcIdList(@Param("caseId") long caseId,
                                @Param("ugcType") int ugcType);

    int save(RiskUgcVerifyDO trans2DO);

    int delete(@Param("caseId") long caseId, @Param("ugcType") int ugcType, @Param("ugcId") long ugcId);

    int deleteById(@Param("id") long id);

    @DataSource(DS.CF_SLAVE)
    List<RiskUgcVerifyDO> getByCaseIdAndUgcType(@Param("caseId") long caseId, @Param("ugcType") int ugcType);
}
