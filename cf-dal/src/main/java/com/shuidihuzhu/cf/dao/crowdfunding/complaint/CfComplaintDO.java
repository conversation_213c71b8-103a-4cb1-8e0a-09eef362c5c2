package com.shuidihuzhu.cf.dao.crowdfunding.complaint;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * cf_complaint
 */
@Data
public class CfComplaintDO implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 对外展示id
     */
    private String infoId;

    /**
     * 案例infoId
     */
    private String crowdFundingInfoId;

    /**
     * 业务表id
     */
    private Long bizId;

    /**
     * 业务枚举映射
     */
    private Integer bizType;

    /**
     * 投诉人id
     */
    private Long complaintUserId;

    /**
     * 投诉枚举类型 枚举映射1垃圾营销2涉黄信息3人身攻击4有害信息5违法信息6宣扬仇恨
     */
    private int complaintType;

    /**
     * 投诉结果 1有效0无效
     */
    private int complaintResult;

    /**
     * 工单id
     */
    private Long workOrderId;

    /**
     * 是否删除 0存在1删除
     */
    private Integer isDelete;

    /**
     * 是否可用 1是0否
     */
    private Integer isAvailable;

    /**
     * 投诉时间
     */
    private LocalDateTime complaintTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}