package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.UserRealInfo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;

@DataSource(DS.CF)
public interface UserRealInfoDao {

	int save(UserRealInfo userRealInfo);

	@DataSource(DS.CF_SLAVE)
	long getCountByCreateTime(@Param("userId")long userId, @Param("startTime") Timestamp startTime,
			@Param("endTime") Timestamp endTime);

	@DataSource(DS.CF_SLAVE)
	List<UserRealInfo> getByVerifyStatus(@Param("userId")long userId,
			@Param("idcardVerifyStatusList") List<Integer> idcardVerifyStatusList);

	@DataSource(DS.CF_SLAVE)
	UserRealInfo getLastOne(@Param("userId")long userId);

	int updateIdcardVerifyStatus(@Param("id") int id, @Param("idcardVerifyStatus") int idcardVerifyStatus,
			@Param("resultCode") String resultCode, @Param("resultMsg") String resultMsg);

	UserRealInfo getById(@Param("id") int id);

	UserRealInfo getByCryptoIdCard(@Param("cryptoIdCard") String cryptoIdCard);

	@DataSource(DS.CF_SLAVE)
	List<UserRealInfo> getSuccessByUserIds(@Param("userIds") List<Long> userIds);

	@DataSource(DS.CF_SLAVE)
	List<UserRealInfo> getHandlingInfo(@Param("from") int from, @Param("limit") int limit);

	@DataSource(DS.CF)
	List<UserRealInfo> getByUserId(@Param("userId") long userId);

	@DataSource(DS.CF)
	int updateMedicalStatus(@Param("userId")long userId, @Param("medicalStatus") int medicalStatus);

	@DataSource(DS.CF)
	int updateMedicalImageUrl(@Param("userId")long userId, @Param("medicalImageUrl") String medicalImageUrl);

	@DataSource(DS.CF)
	int updateIsMedicalById(@Param("id") long id, @Param("medicalStatus") int medicalStatus);

	int deleteById(@Param("id") int id);

	@DataSource(DS.CF_SLAVE)
	List<UserRealInfo> getOnceAllSuccess(@Param("userId") long userId);

	@DataSource(DS.CF_SLAVE)
	List<UserRealInfo> getByUserIdAndIdCard(@Param("userId") long userId, @Param("cryptoIdCard") String cryptoIdCard);

}
