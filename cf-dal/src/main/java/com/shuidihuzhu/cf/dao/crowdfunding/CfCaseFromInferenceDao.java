package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfCaseFromInference;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

import java.util.List;

/**
 *
 * 推断from字段的依据
 *
 * Created by wangsf on 17/3/17.
 */
@DataSource(DS.CF_SLAVE)
public interface CfCaseFromInferenceDao {

	List<CfCaseFromInference> getList();

}
