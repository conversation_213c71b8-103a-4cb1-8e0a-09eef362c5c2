package com.shuidihuzhu.cf.dao.crowdfunding.pay;

import com.shuidihuzhu.cf.model.crowdfunding.pay.CfDonorRefundRecord;
import com.shuidihuzhu.cf.model.crowdfunding.pay.CfDrawCashOffline;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

@DataSource(DS.CF)
public interface CfDonorRefundRecordDao {

    int add(CfDonorRefundRecord cfDonorRefundRecord);

    @DataSource(DS.CF_SLAVE)
    CfDonorRefundRecord getByUserId(long usrId);
}
