package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingIdCase;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by sven on 18/8/15.
 *
 * <AUTHOR>
 */
@DataSource(DS.CF)
public interface CrowdfundingIdCaseDao {

    CrowdfundingIdCase getByInfoId(@Param("caseId") int caseId);

    List<CrowdfundingIdCase> getByInfoIds(@Param("caseIds") List<Integer> caseIds);

    int updateStatus(@Param("caseId") int caseId, @Param("status") int status);

    int updateIdCase(@Param("caseId") int caseId, @Param("name") String name, @Param("cryptoIdCard") String cryptoIdCard);

    int insert(CrowdfundingIdCase crowdfundingIdCase);

    List<CrowdfundingIdCase> getByCryptoIdCard(@Param("cryptoIdCard") String cryptoIdCard);

}
