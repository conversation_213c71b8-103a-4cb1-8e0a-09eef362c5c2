package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplateRecord;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR> Ahrievil
 */
@DataSource(DS.CF)
public interface CfBaseInfoTemplateRecordDao {

    @DataSource(DS.CF_SLAVE)
    CfBaseInfoTemplateRecord selectByInfoUuid(@Param("infoUuid") String infoUuid);

    int insertOne(CfBaseInfoTemplateRecord cfBaseInfoTemplateRecord);
}
