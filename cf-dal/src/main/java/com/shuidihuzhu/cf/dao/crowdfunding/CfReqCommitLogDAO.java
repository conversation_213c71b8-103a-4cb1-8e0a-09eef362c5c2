package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.client.model.CfRequestCommitLogDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @time 2019/10/22 下午7:46
 * @desc
 */
@DataSource(DS.CF)
public interface CfReqCommitLogDAO {
    int insert(CfRequestCommitLogDO commitLogDO);

    @DataSource(DS.CF_SLAVE)
    CfRequestCommitLogDO queryById(Long id);

    @DataSource(DS.CF_SLAVE)
    CfRequestCommitLogDO queryByRequestId(String requestId);

    @DataSource(DS.CF_SLAVE)
    List<CfRequestCommitLogDO> query(Long offset, int limit, Timestamp before);

    int delete(Long id);
    int updateStatus(String requestId, int status);
}
