package com.shuidihuzhu.cf.dao.audit;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.domain.CfImageAiMaskDO;
import com.shuidihuzhu.client.model.CfPublicAuditAction;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 审核公示
 * @Author: panghairui
 * @Date: 2022/12/19 8:18 下午
 */
@DataSource(CfDataSource.ADMIN_DB_MASTER)
public interface CfPublicAuditDao {

    int insertPublicAudit(CfPublicAuditAction cfPublicAuditAction);

    int insertPublicAudits(@Param("cfPublicAuditActions") List<CfPublicAuditAction> cfPublicAuditActions);

    @DataSource(CfDataSource.ADMIN_DB_SLAVE)
    CfPublicAuditAction selectByCaseId(@Param("caseId") Integer caseId, @Param("auditType") Integer auditType);

    @DataSource(CfDataSource.ADMIN_DB_SLAVE)
    List<CfPublicAuditAction> selectAllByCaseId(@Param("caseId") Integer caseId);


}
