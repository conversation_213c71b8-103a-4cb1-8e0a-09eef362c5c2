package com.shuidihuzhu.cf.dao.toG;

import com.shuidihuzhu.client.cf.api.model.GovCooperationMark;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 政府合作标记记录DAO接口
 * 
 * <AUTHOR>
 * @date 2023/07/01
 */
@DataSource(DS.CF)
public interface GovCooperationMarkDao {
    /**
     * 添加记录
     * 
     * @param mark 标记记录
     * @return 影响行数
     */
    int add(GovCooperationMark mark);

    int update(GovCooperationMark mark);
    
    /**
     * 根据案例ID和政府代码查询记录
     * 
     * @param caseId 案例ID
     * @param govCode 政府项目代码
     * @return 标记记录
     */
    @DataSource(DS.CF_SLAVE)
    GovCooperationMark selectByCaseIdAndGovCode(@Param("caseId") Integer caseId, 
                                                @Param("govCode") Integer govCode);
    
    /**
     * 根据案例ID列表和政府代码查询记录列表
     * 
     * @param caseIdList 案例ID列表
     * @param govCode 政府项目代码
     * @return 标记记录列表
     */
    @DataSource(DS.CF_SLAVE)
    List<GovCooperationMark> listByCaseIdsAndGovCode(@Param("caseIdList") List<Integer> caseIdList, 
                                                     @Param("govCode") Integer govCode);

    @DataSource(DS.CF_SLAVE)
    List<GovCooperationMark> listMarkRecordsByAllGov(@Param("caseId") Integer caseId);
} 