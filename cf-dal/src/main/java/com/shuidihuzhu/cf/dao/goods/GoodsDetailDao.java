package com.shuidihuzhu.cf.dao.goods;

import com.shuidihuzhu.cf.model.goods.GoodsDetail;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.CF)
public interface GoodsDetailDao {
	
	int save(GoodsDetail goodsDetail);

	@DataSource(DS.CF_SLAVE)
	GoodsDetail getByInfoUuid(@Param("infoUuid") String infoUuid);

	@DataSource(DS.CF_SLAVE)
	List<GoodsDetail> getListByInfoUuids(@Param("infoUuids") List<String> infoUuids);

    int update(GoodsDetail goodsDetail);
}
