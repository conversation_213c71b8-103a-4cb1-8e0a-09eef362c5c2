package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfAppFirstOpen;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;

/**
 * Created by wangsf on 17/12/21.
 */
@DataSource(DS.CF)
public interface CfAppFirstOpenDao {

	@DataSource(DS.CF_SLAVE)
	List<CfAppFirstOpen> findByCreateTime(@Param("beginTime")Timestamp beginTime, @Param("endTime") Timestamp endTime,
	                                      @Param("anchorId") int anchorId, @Param("limit") int limit);

}
