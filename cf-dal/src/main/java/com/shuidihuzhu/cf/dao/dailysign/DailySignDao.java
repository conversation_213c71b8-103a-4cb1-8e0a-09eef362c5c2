package com.shuidihuzhu.cf.dao.dailysign;

import com.shuidihuzhu.cf.model.dailysign.DailySign;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by wangsf on 18/4/27.
 */
@DataSource(DS.CF)
public interface DailySignDao {

	int insert(DailySign dailySign);

	int insertUpdate(DailySign dailySign);

	@DataSource(DS.CF_SLAVE)
	DailySign getByUserIdAndDayKey(@Param("userId") long userId, @Param("dayKey") String dayKey);

	@DataSource(DS.CF_SLAVE)
	List<DailySign> getListByUserId(@Param("userId") long userId, @Param("anchorId") long anchorId,
	                                @Param("limit") int limit);

}
