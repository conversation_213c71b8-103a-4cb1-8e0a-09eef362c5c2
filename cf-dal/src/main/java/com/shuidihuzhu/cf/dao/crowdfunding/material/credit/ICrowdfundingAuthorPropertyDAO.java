package com.shuidihuzhu.cf.dao.crowdfunding.material.credit;

import com.shuidihuzhu.cf.domain.material.credit.CrowdfundingAuthorPropertyDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

/**
 * Created by sven on 18/11/16.
 *
 * <AUTHOR>
 */
@DataSource(DS.CF)
public interface ICrowdfundingAuthorPropertyDAO {

    int insertOrUpdate(CrowdfundingAuthorPropertyDO crowdfundingAuthorPropertyDO);

    @DataSource(DS.CF_SLAVE)
    CrowdfundingAuthorPropertyDO selectByCaseId(int caseId);
}
