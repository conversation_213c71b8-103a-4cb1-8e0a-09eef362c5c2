package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReportLabel;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by roy on 16/7/29.
 */

@DataSource(DS.CF)
public interface CrowdfundingReportDao {
    int add(CrowdfundingReport crowdfundingReport);
    int addRealNameReport(CrowdfundingReport crowdfundingReport);

    @DataSource(DS.CF_SLAVE)
    List<CrowdfundingReport> getListByInfoId(@Param("activityId") Integer activityId);
    //批量更新
    void updateReportStatusList(@Param("reportIds") List<Integer> reportIds);


    int addLabel(@Param("list")List<CrowdfundingReportLabel> list);

    @DataSource(DS.CF_SLAVE)
    List<CrowdfundingReport> getListByInfoIds(@Param("activityIds") List<Integer> activityIds);

    @DataSource(DS.CF_SLAVE)
    List<CrowdfundingReport> getListByInfoId(@Param("activityId") int activityId);

    @DataSource(DS.CF_SLAVE)
    List<Map<String, Object>> getCfReportCountsMapByInfoIds(@Param("activityIds") Set<Integer> activityIds);

    @DataSource(DS.CF_SLAVE)
    List<CrowdfundingReport> queryReportByUser(@Param("userId") long userId);

    @DataSource(DS.CF_SLAVE)
    List<CrowdfundingReportLabel> queryReportLabelByReportId(@Param("reportId") int reportId);

    @DataSource(DS.CF_SLAVE)
    List<CrowdfundingReportLabel> queryReportLabelByReportIds(@Param("reportIds") List<Integer> reportIds);

    @DataSource(DS.CF_SLAVE)
    List<CrowdfundingReport> getReportByConditions(@Param("userId") long userId, @Param("activityId") int activityId);

    @DataSource(DS.CF_SLAVE)
    CrowdfundingReport getById(@Param("id") int id);

    @DataSource(DS.CF_SLAVE)
    List<CrowdfundingReport> getListByIds(@Param("reportIds") List<Integer> reportIds, @Param("userId") long userId);

    @DataSource(DS.CF_SLAVE)
    CrowdfundingReport queryLastReportByUserWithReal(@Param("userId") long userId);
}
