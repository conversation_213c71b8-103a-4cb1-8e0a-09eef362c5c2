package com.shuidihuzhu.cf.dao.loverank;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.fulllink.measurement.annotation.Operate;
import com.shuidihuzhu.cf.fulllink.measurement.utils.OperateType;
import com.shuidihuzhu.cf.model.loverank.UserLoveRankLikeRecord;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 按照caseId分表
 */

@DataSource(CfDataSource.CF_NEW_CASE_DETAIL_SHADING)
public interface UserLoveRankLikeRecordDao {


    int insert(UserLoveRankLikeRecord record);

    int insertSelective(UserLoveRankLikeRecord record);


    @Operate(OperateType.READ)
    List<UserLoveRankLikeRecord> selectLikeRecordByUserId(@Param("userId") long userId, @Param("caseId") int caseId,
                                                          @Param("userIdSet") Set<Long> userIdSet);
    @Operate(OperateType.READ)
    UserLoveRankLikeRecord selectNewLikeByUserId(@Param("caseId") int caseId, @Param("userId") Long adminUserId, @Param("likeUserId") Long userId, @Param("anonymous") int nonymous);

}