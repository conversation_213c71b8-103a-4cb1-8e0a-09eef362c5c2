package com.shuidihuzhu.cf.dao.miniprogram;

import com.shuidihuzhu.cf.model.miniprogram.CfTopicShareCommentCount;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.FRAME)
public interface CfTopicShareCommentDao {
    int insertOne(@Param("cfTopicShareCommentCount") CfTopicShareCommentCount cfTopicShareCommentCount);

    @DataSource(DS.FRAME_SLAVE)
    CfTopicShareCommentCount selectByTopicId(@Param("topicId") int topicId);

    @DataSource(DS.FRAME_SLAVE)
    List<CfTopicShareCommentCount> listByTopicIds(@Param("topicIds") List<Integer> topicIds);

    int updateShareNumByTopicId(@Param("topicId") int topicId);

    int updateCommentNumByTopicId(@Param("topicId") int topicId);

    int incPraiseNum(@Param("topicId") int topicId);

    int decPraiseNum(@Param("topicId") int topicId);

    int updateByTopicId(@Param("topicId") int topicId, @Param("cfTopicShareCommentCount") CfTopicShareCommentCount cfTopicShareCommentCount);
}
