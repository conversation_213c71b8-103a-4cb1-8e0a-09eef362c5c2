package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

@DataSource(DS.CF)
public interface CfRefundRecordTaskDao {

    /**
     * 获取案例的未处理完成的任务数
     * 未完成的定义：status=0（未处理）, status=1 （处理中）
     * 已完成的定义：status=2 （处理成功）,status=3(处理失败)
     * @param infoUuid
     * @return
     */
    int getUnCompleteRefundRecordTask(@Param("infoUuid") String infoUuid);
}
