package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.fulllink.measurement.annotation.Operate;
import com.shuidihuzhu.cf.fulllink.measurement.utils.OperateType;
import com.shuidihuzhu.cf.model.crowdfunding.CfCaseEndWhiteList;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

import java.util.List;

@DataSource(DS.CF)
public interface CfCaseEndWhiteListDao {

    @Operate(OperateType.READ)
    @DataSource(DS.CF_SLAVE)
    List<CfCaseEndWhiteList> getList();
}
