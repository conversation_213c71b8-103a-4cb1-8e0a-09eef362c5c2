package com.shuidihuzhu.cf.dao.questionnaire;

import com.shuidihuzhu.cf.vo.questionnaire.WxQuestionVo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.CF)
public interface WxQuestionDao {

    @DataSource(DS.CF_SLAVE)
    List<WxQuestionVo> getByQnrId(@Param("qnrId") int qnrId);

    @DataSource(DS.CF_SLAVE)
    WxQuestionVo getById(@Param("qId") int qId);

}
