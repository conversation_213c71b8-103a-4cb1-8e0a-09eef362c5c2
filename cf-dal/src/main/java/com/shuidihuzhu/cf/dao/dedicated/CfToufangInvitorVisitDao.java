package com.shuidihuzhu.cf.dao.dedicated;

import com.shuidihuzhu.cf.domain.dedicated.CfToufangInvitorVisitDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

@DataSource(DS.CF)
public interface CfToufangInvitorVisitDao {
    int add(CfToufangInvitorVisitDO cfToufangInvitorVisitDO);

    @DataSource(DS.CF_SLAVE)
    CfToufangInvitorVisitDO getCfToufangInvitorVisitDOByUserId(@Param("userId") Long userId,
                                                               @Param("channel") String channel);
    @DataSource(DS.CF_SLAVE)
    int getCfToufangInvitorVisitDOCountByUserId(@Param("sourceUserId") long sourceUserId,
                                                @Param("inviteChannel") String inviteChannel,
                                                @Param("startTime") Date startTime,
                                                @Param("endTime") Date endTime);
}
