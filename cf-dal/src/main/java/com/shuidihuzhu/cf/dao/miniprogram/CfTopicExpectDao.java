package com.shuidihuzhu.cf.dao.miniprogram;

import com.shuidihuzhu.cf.model.miniprogram.CfTopicExpect;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

@DataSource(DS.FRAME)
public interface CfTopicExpectDao {
    int insertOne(@Param("cfTopicExpect") CfTopicExpect cfTopicExpect);

    int updateByPhaseIdAndUserId(@Param("phaseId") int phaseId, @Param("userId") long userId, @Param("status") int status);

    @DataSource(DS.FRAME_SLAVE)
    CfTopicExpect selectByPhaseIdAndUserId(@Param("phaseId") int phaseId, @Param("userId") long userId);
}
