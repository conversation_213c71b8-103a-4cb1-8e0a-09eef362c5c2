package com.shuidihuzhu.cf.dao.crowdfunding.combine;


import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.model.contribute.CfContributeOrder;
import com.shuidihuzhu.cf.model.contribute.CfContributeTotalInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord;
import com.shuidihuzhu.cf.response.NewPageVo;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@DataSource(CfDataSource.CF_ORDER_NEW_MASTER_DATASOURCE)
public interface CfContributeOrderDao {

    int addContributeOrder(CfContributeOrder contributeOrder);

    int updatePaySuccess(@Param("payUid") String payUid,
                         @Param("thirdPayUid") String thirdPayUid,
                         @Param("orderStatus") Integer orderStatus,
                         @Param("callbackTime") Date callbackTime,
                         @Param("realPayAmount") Integer realPayAmount,
                         @Param("feeAmount") Integer feeAmount);

    @DataSource(CfDataSource.CF_ORDER_NEW_MASTER_DATASOURCE)
    List<CfContributeOrder> selectByPayUids(@Param("payUids") List<String> payUids);

    @DataSource(CfDataSource.CF_ORDER_NEW_SLAVE_DATASOURCE)
    List<CfContributeOrder> selectByThirdPayUids(@Param("thirdPayUids") List<String> thirdPayUids);

    @DataSource(CfDataSource.CF_ORDER_NEW_SLAVE_DATASOURCE)
    List<CfContributeOrder> selectByUserIds(@Param("userIds") List<Long> userIds,
                                            @Param("statusList") List<Integer> statusList);

    @DataSource(CfDataSource.CF_ORDER_NEW_SLAVE_DATASOURCE)
    List<CfContributeOrder> selectByCaseId(@Param("caseId") int caseId);

    @DataSource(CfDataSource.CF_ORDER_NEW_SLAVE_DATASOURCE)
    List<CfContributeOrder> selectByCaseIdTimeRange(@Param("caseId") int caseId,
                                                    @Param("callBackBeginTime") Date callBackBeginTime,
                                                    @Param("callBackEndTime") Date callBackEndTime);

    @DataSource(CfDataSource.CF_ORDER_NEW_SLAVE_DATASOURCE)
    CfContributeTotalInfo selectTotalInfoByCaseIdTimeRange(@Param("caseId") int caseId,
                                                           @Param("callBackBeginTime") Date callBackBeginTime,
                                                           @Param("callBackEndTime") Date callBackEndTime);


    @DataSource(CfDataSource.CF_ORDER_NEW_SLAVE_DATASOURCE)
    List<CfContributeOrder> selectByIds(@Param("ids") List<Long> ids);


    int updateOrderStatus(@Param("payUid") String payUid,
                          @Param("toStatus") int toStatus);

    @DataSource(CfDataSource.CF_ORDER_NEW_SLAVE_DATASOURCE)
    List<CfContributeOrder> selectByUserIdLimit(@Param("userId") long userId,
                                                @Param("anchorId") long anchorId,
                                                @Param("limit") int limit);

    @DataSource(CfDataSource.CF_ORDER_NEW_SLAVE_DATASOURCE)
    List<CfContributeOrder> getList(@Param("callbackStart") Date callbackStart, @Param("callbackEnd") Date callbackEnd,
                                    @Param("contributeType") String contributeType, @Param("orderStatus") int orderStatus,
                                    @Param("caseId") int caseId, @Param("donateOrderId") long donateOrderId,
                                    @Param("payAmount") long payAmount, @Param("pageType") String pageType,
                                    @Param("id") long id, @Param("pageSize") int pageSize,
                                    @Param("thirdPayUid") String thirdPayUid);


    @DataSource(CfDataSource.CF_ORDER_NEW_SLAVE_DATASOURCE)
    List<CfContributeOrder> selectBySourceChannel(@Param("sourceChannel") String sourceChannel,
                                                  @Param("sourceIds") List<String> sourceIds);


    int updateUserByIds(@Param("ids") List<Long> ids, @Param("toUserId") long toUserId);

    /**
     * 全表扫描的sql，慎重使用 查找最近已捐的limit条 查找最近已退款的不用这个sql
     * @param orderStatus
     * @param limit
     * @return
     */
    @DataSource(CfDataSource.CF_ORDER_NEW_SLAVE_DATASOURCE)
    List<Long> selectContributeUserId(@Param("excludeUserIds") List<Long> excludeUserIds, @Param("orderStatus") int orderStatus, @Param("limit") int limit);
}
