package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfTouFangSign;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * Author: <PERSON>
 * Date: 2017/2/16 14:26
 */
@DataSource(DS.CF)
public interface CfTouFangSignDao {

	int insertList(List<CfTouFangSign> cfTouFangSigns);

	@DataSource(DS.CF_SLAVE)
	List<CfTouFangSign> selectByTime(@Param("start") Timestamp start, @Param("anchorId") int anchorId, @Param("limit") int limit);

	@DataSource(DS.CF_SLAVE)
	CfTouFangSign selectByMobile(@Param("cryptoMobile") String cryptoMobile);
}
