package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;


/**
 * <AUTHOR>
 * @since 2016/11/2
 */

@DataSource(DS.CF)
public interface CrowdfundingInvitationQrDao {

    /**
     * 插入用户邀请二维码信息
     *
     * @param userId
     * @param thirdId
     * @param qrString
     */
    public void insertQrInfo(long userId,int thirdId,String qrString);


    /**
     * 获取用户邀请二维码
     *
     * @param userId
     * @param thirdId
     * @return
     */
    @DataSource(DS.CF_SLAVE)
    public String getQrInfo(long userId, int thirdId);

}
