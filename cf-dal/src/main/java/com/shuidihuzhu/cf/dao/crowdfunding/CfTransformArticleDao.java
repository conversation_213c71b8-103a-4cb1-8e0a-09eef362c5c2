package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfTransformArticleDo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Author   : roy
 * Date     : 2018-05-07  15:59
 * Describe :
 * table    : cf_transform_article_info
 *
 * <AUTHOR>
 * @see CfTransformArticleDao
 */
@DataSource(DS.CF)
public interface CfTransformArticleDao {

    /**
     * 略
     *
     * @param cfTransformArticleDo
     * @return
     */
    int insert(CfTransformArticleDo cfTransformArticleDo);

    /**
     * 略
     *
     * @param id
     * @return
     */
    int delete(@Param("id") long id);

    @DataSource(DS.CF_SLAVE)
    List<CfTransformArticleDo> listByAnchor(@Param("start") long anchorId
            , @Param("size") int size);

    @DataSource(DS.CF_SLAVE)
    List<CfTransformArticleDo> listByPage();

    /**
     * 按标题搜索 分页显示
     *
     * @param titleKey
     * @return
     */
    @DataSource(DS.CF_SLAVE)
    List<CfTransformArticleDo> listByPageAndTitleKey(@Param("titleKey") String titleKey);

}
