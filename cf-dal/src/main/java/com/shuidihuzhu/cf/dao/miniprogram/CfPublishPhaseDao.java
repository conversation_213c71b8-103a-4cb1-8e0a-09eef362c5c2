package com.shuidihuzhu.cf.dao.miniprogram;

import com.shuidihuzhu.cf.model.miniprogram.CfPublishPhase;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;

@DataSource(DS.FRAME)
public interface CfPublishPhaseDao {
    int insertOne(@Param("cfPublishPhase") CfPublishPhase cfPublishPhase);

    int deleteById(@Param("id") int id);

    int deleteByPhaseId(@Param("phaseId") int phaseId);

    int updateByPhaseId(@Param("phaseId") int phaseId, @Param("cfPublishPhase") CfPublishPhase cfPublishPhase);

    @DataSource(DS.FRAME_SLAVE)
    List<CfPublishPhase> getList();

    @DataSource(DS.FRAME_SLAVE)
    CfPublishPhase selectByPhaseId(@Param("phaseId") int phaseId);

    @DataSource(DS.FRAME_SLAVE)
    CfPublishPhase selectNextByTime(@Param("start") String start, @Param("end") String end);

    @DataSource(DS.FRAME_SLAVE)
    List<CfPublishPhase> listByTimeLimit(@Param("time") Timestamp time, @Param("limit") int limit);

    @DataSource(DS.FRAME_SLAVE)
    List<CfPublishPhase> listByPhaseIds(@Param("phaseIds") List<Integer> phaseIds);
}
