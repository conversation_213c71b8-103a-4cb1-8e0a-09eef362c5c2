package com.shuidihuzhu.cf.dao.crowdfunding.user;

import com.shuidihuzhu.cf.model.crowdfunding.CfUserBindFeedbackCaseDO;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2019-10-12 13:18
 **/
@DataSource("cfUserDataSource")
public interface CfUserBindFeedbackCaseDao {

    void addList(@Param("bindFeedbackCaselist") List<CfUserBindFeedbackCaseDO> userBindFeedbackCaseDOList);

    Long getMaxFeedbackId();

    List<CfUserBindFeedbackCaseDO> getCaseIdList(@Param("userId") long userId, @Param("limit") int limit);

    List<CfUserBindFeedbackCaseDO> listByFeedbackId(@Param("feedbackId")long feedbackId);

    void batchUpdate(@Param("bindFeedbackCaselist") List<CfUserBindFeedbackCaseDO> userBindFeedbackCaseDOList);
}
