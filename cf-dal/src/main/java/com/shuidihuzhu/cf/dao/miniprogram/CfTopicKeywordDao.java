package com.shuidihuzhu.cf.dao.miniprogram;

import com.shuidihuzhu.cf.model.miniprogram.CfTopicKeyword;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.FRAME)
public interface CfTopicKeywordDao {
    int addOne(@Param("cfTopicKeyword") CfTopicKeyword cfTopicKeyword);

    int deleteById(@Param("id") int id);

    int updateById(@Param("id") int id, @Param("keyword") String keyword);

    @DataSource(DS.FRAME_SLAVE)
    CfTopicKeyword selectById(@Param("id") int id);

    @DataSource(DS.FRAME_SLAVE)
    CfTopicKeyword selectByKeyword(@Param("keyword") String keyword);

    @DataSource(DS.FRAME_SLAVE)
    List<CfTopicKeyword> listByIds(@Param("ids") List<Integer> ids);
}
