package com.shuidihuzhu.cf.dao.crowdfunding.freeservicecharge;

import com.shuidihuzhu.cf.model.crowdfunding.freeservicecharge.CfFreeServiceChargeDo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;


/**
 * <AUTHOR>
 * @date 2020/11/25  2:07 下午
 */
@DataSource(DS.CF)
public interface CfFreeServiceChargeDao {
    int insert(CfFreeServiceChargeDo cfFreeServiceChargeDo);

    int updateApplyById(@Param("id") long id,
                        @Param("apply") int apply);

    @DataSource(DS.CF_SLAVE)
    CfFreeServiceChargeDo getByCaseId(@Param("caseId") int caseId);
}
