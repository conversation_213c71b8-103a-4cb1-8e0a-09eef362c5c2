package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfWxReply;
import com.shuidihuzhu.cf.model.crowdfunding.CfWxReplyDomain;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.common.web.util.admin.BasicExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.CF)
public interface CfWxReplyDao {

    @DataSource(DS.CF_SLAVE)
    List<CfWxReply> getAll();

    int insert(CfWxReply cfWxReply);

    int update(CfWxReply cfWxReply);

    @DataSource(DS.CF_SLAVE)
    CfWxReply selectByKeyAndGroupId(@Param("keyWorld") String keyWorld, @Param("groupId") Integer groupId,
                                    @Param("replyContent") String replyContent, @Param("valid") Integer valid, @Param("msgType") Integer msgType);

    @DataSource(DS.CF_SLAVE)
    CfWxReply selectById(@Param("id") int id);

    @DataSource(DS.CF_SLAVE)
    List<CfWxReply> selectByExample(CfWxReplyDomain cfWxReplyDomain);
}
