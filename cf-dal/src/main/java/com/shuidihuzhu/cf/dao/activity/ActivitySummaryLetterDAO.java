package com.shuidihuzhu.cf.dao.activity;

import com.shuidihuzhu.cf.domain.activity.SummaryLetterDO;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@DataSource("cfUserInfoShardingCrowdfunding")
public interface ActivitySummaryLetterDAO {

    int insert(SummaryLetterDO summaryLetterDO);

    int updateById(SummaryLetterDO summaryLetterDO);

    List<SummaryLetterDO> listById(@Param("ids") List<Long> ids);

    List<SummaryLetterDO> listByAnchor(@Param("anchor") long anchor, @Param("size") int size);

    List<SummaryLetterDO> listHeadByArea(@Param("area") String area, @Param("size") int size);

    List<SummaryLetterDO> listByCaseIds(@Param("caseIds") List<Integer> caseIds, @Param("size") int size);

    SummaryLetterDO getByCaseId(@Param("caseId") int caseId);
}
