package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingMsgContent;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.CF_SLAVE)
public interface CrowdfundingMsgContentDao {
	
	CrowdfundingMsgContent getByKey(@Param("key") String key);

	List<CrowdfundingMsgContent> getAll();
}
