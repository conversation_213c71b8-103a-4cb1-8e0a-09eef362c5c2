package com.shuidihuzhu.cf.dao.crowdfunding.slave;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;

/**
 * Created by wangsf on 17/5/31.
 */
@DataSource(DS.CF_SLAVE)
public interface UserRealInfoSlaveDao {

	Integer countByMin(@Param("begin") Timestamp begin, @Param("end") Timestamp end);

    int countSuccess(@Param("begin") Timestamp begin, @Param("end") Timestamp end);
}
