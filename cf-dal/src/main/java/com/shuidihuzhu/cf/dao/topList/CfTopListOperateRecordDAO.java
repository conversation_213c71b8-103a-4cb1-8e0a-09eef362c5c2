package com.shuidihuzhu.cf.dao.topList;

import com.shuidihuzhu.cf.model.topList.CfTopListOperateRecord;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@DataSource("cfUserDataSource")
public interface CfTopListOperateRecordDAO {

    int insertRecordList(@Param("tableIndex") String tableIndex, @Param("list") List<CfTopListOperateRecord> records);

    @DataSource("cfUserDataSourceSlave")
    List<CfTopListOperateRecord> selectByTimeAndType(@Param("tableIndex") String tableIndex, @Param("caseId") int caseId,
                                                     @Param("operateTime") String operateTime,
                                                     @Param("operateType") int operateType,
                                                     @Param("userIds") List<Long> userIds);

}
