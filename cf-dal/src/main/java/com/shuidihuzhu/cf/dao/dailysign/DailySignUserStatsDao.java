package com.shuidihuzhu.cf.dao.dailysign;

import com.shuidihuzhu.cf.model.dailysign.DailySignUserStat;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * Created by wangsf on 18/4/27.
 */
@DataSource(DS.CF)
public interface DailySignUserStatsDao {

	int insertOrUpdate(DailySignUserStat stat);

	@DataSource(DS.CF_SLAVE)
	DailySignUserStat getByUserId(@Param("userId") long userId);

}
