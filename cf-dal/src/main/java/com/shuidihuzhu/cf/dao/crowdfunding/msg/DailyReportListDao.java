package com.shuidihuzhu.cf.dao.crowdfunding.msg;

import com.shuidihuzhu.cf.model.dailyreport.CfDailyReportList;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by ch<PERSON> on 2017/11/10.
 */
@DataSource(DS.CF)
public interface DailyReportListDao {
	void addBatch(@Param("list") List<CfDailyReportList> list);

	@DataSource(DS.CF_SLAVE)
	List<CfDailyReportList> getTop(@Param("infoUuid") String infoUuid,
	                                @Param("dateStr") String dateStr,
	                                @Param("limit") int limit);
}
