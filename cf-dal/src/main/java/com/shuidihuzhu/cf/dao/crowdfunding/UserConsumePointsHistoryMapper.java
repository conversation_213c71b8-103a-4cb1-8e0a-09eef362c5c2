package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.UserConsumePointsHistory;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

@DataSource(DS.CF)
public interface UserConsumePointsHistoryMapper {

    int insert(UserConsumePointsHistory record);

    int insertSelective(UserConsumePointsHistory record);

    @DataSource(DS.CF_SLAVE)
    UserConsumePointsHistory selectByPrimaryKey(Long id);

    @DataSource(DS.CF_SLAVE)
    UserConsumePointsHistory selectByTradeNo(String tradeNo);

    int updateByPrimaryKeySelective(UserConsumePointsHistory record);

    int updateByPrimaryKey(UserConsumePointsHistory record);

    int backByTradeNo(@Param("tradeNo") String tradeNo, @Param("userId")long userId, @Param("remark") String remark);
}