package com.shuidihuzhu.cf.dao.caseinfo;

import com.shuidihuzhu.cf.model.river.RiverReviewDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@DataSource(DS.CF)
public interface RiverReviewDAO {

    int insert(RiverReviewDO riverReviewDO);

    @DataSource(DS.CF_SLAVE)
    RiverReviewDO getLastByCaseIdAndUsageType(@Param("caseId") int caseId, @Param("usageType") int usageType);

    @DataSource(DS.CF_SLAVE)
    RiverReviewDO getById(@Param("id") long id);

    int updateById(@Param("id") long id,
                   @Param("status") int status,
                   @Param("rejectDetail") String rejectDetail);

    @DataSource(DS.CF_SLAVE)
    List<RiverReviewDO> getByCaseIdsAndUsageType(@Param("caseIds") List<Integer> caseIds, @Param("usageType") int usageType);

    @DataSource(DS.CF_SLAVE)
    RiverReviewDO getByCaseIdAndUsageType(@Param("caseId") Integer caseId, @Param("usageType") int usageType);

}
