package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.fulllink.measurement.annotation.Operate;
import com.shuidihuzhu.cf.fulllink.measurement.utils.OperateType;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.Collection;
import java.util.Date;
import java.util.List;

@DataSource(DS.CF)
public interface CfInfoExtDao {

    int add(CfInfoExt cfInfoExt);

    void updateCaseFrom(@Param("infoUuid") String infoUuid, @Param("fromType") int fromType,
                        @Param("fromDetail") String fromDetail);

    @Operate(OperateType.READ)
    CfInfoExt getByInfoUuid(@Param("infoUuid") String infoUuid);

    @Operate(OperateType.READ)
    @DataSource(DS.CF_SLAVE)
    CfInfoExt getByInfoUuidFromSlave(@Param("infoUuid") String infoUuid);

    int updateFinishStatus(@Param("infoUuid") String infoUuid, @Param("finishStatus") int finishStatus);

    int updateFinishStatusByList(@Param("list") List<String> list, @Param("finishStatus") int finishStatus);

    int updateRefundStatus(@Param("infoUuid") String infoUuid, @Param("refundStatus") int refundStatus);

    int updateTransferStatus(@Param("infoUuid") String infoUuid, @Param("transferStatus") int transferStatus);

    @DataSource(DS.CF_SLAVE)
    List<CfInfoExt> getListByUuids(@Param("infoUuids") List<String> infoUuids);

    int updateFromType(@Param("infoUuid") String infoUuid, @Param("fromType") int fromType);

    @DataSource(DS.CF_SLAVE)
    List<CfInfoExt> getListByProductNames(@Param("productNames") List<String> productNames);

//	@DataSource(DS.CF_SLAVE)
//	List<CfInfoExt> getByInfoUuidPrefix(@Param("infoUuidPrefix") String infoUuidPrefix);

    int updateFirstApproveStatus(@Param("infoUuid") String infoUuid, @Param("firstApproveStatus") int status);

    int updateFirstApproveStatusByCaseId(@Param("caseId") int caseId, @Param("firstApproveStatus") int status);

    /**
     * 通过首次过审时间查询
     *
     * @param startTime
     * @param endTime
     * @param start
     * @param size
     * @return
     */
    @DataSource(DS.CF_SLAVE)
    List<CfInfoExt> getByFirstApproveTime(@Param("startTime") Timestamp startTime,
                                          @Param("endTime") Timestamp endTime,
                                          @Param("start") int start, @Param("size") int size);

//	List<CfInfoExt> getByInfoUuidPrefix(@Param("infoUuidPCrowdfundingInfoBaseVo.javarefix") String infoUuidPrefix);

    int updateNeedCaseList(@Param("infoUuid") String infoUuid, @Param("needCaseList") String needCaseList);

    int updateUserRefund(@Param("infoUuid") String infoUuid, @Param("refundEndTime") String refundEndTime);

    int insertList(@Param("list") List<String> list);

    @DataSource(DS.CF_SLAVE_2)
    List<CfInfoExt> selectByInfoUuidList(@Param("list") List<String> list);

    /**
     * 根据时间段获取通过自愿者发起的所有案例
     *
     * @param startTime
     * @param endTime
     * @param volunteerUniqueCode
     * @return
     */
    @DataSource(DS.CF_SLAVE)
    List<CfInfoExt> selectAllVolunteerUniqueCodeWithTime(@Param("startTime") Date startTime,
                                                         @Param("endTime") Date endTime,
                                                         @Param("volunteerUniqueCode") String volunteerUniqueCode,
                                                         @Param("offset") Integer offset,
                                                         @Param("pageSize") Integer pageSize);

    /**
     * 根据时间段获取通过自愿者发起的且自愿者选择关注的案例
     *
     * @param startTime
     * @param endTime
     * @param volunteerUniqueCode
     * @return
     */
    @DataSource(DS.CF_SLAVE)
    List<CfInfoExt> selectFollowedVolunteerUniqueCodeWithTime(@Param("startTime") Date startTime,
                                                              @Param("endTime") Date endTime,
                                                              @Param("volunteerUniqueCode") String volunteerUniqueCode,
                                                              @Param("offset") Integer offset,
                                                              @Param("pageSize") Integer pageSize);

    /**
     * 更新自愿者关联案例的关注状态
     *
     * @param infoUuid
     * @param uniqueCode
     * @param bdFollowed
     * @return
     */
    int updateBdFollowed(@Param("infoUuid") String infoUuid,
                         @Param("uniqueCode") String uniqueCode,
                         @Param("bdFollowed") int bdFollowed);

    @DataSource(DS.CF_SLAVE)
    int getBdServiceCountByTime(@Param("startTime") String startTime,
                                @Param("endTime") String endTime,
                                @Param("uniqueCode") String uniqueCode);

    @DataSource(DS.CF_SLAVE)
    int getBdFollowedServiceCountByTime(@Param("startTime") String startTime,
                                        @Param("endTime") String endTime,
                                        @Param("uniqueCode") String uniqueCode);


    void updateFinishStr(@Param("caseUuid") String caseUuid, @Param("finishStr") String finishStr);

    @DataSource(DS.CF_SLAVE)
    CfInfoExt getByCaseId(@Param("caseId") int caseId);



    @DataSource(DS.CF_SLAVE)
    List<CfInfoExt> getByCaseIds(@Param("list") Collection<Integer> caseIds);

    /**
     * 查询最近3000个案例里面 符合捐款条件的随机N个案例id
     *
     * @return 案例id list
     */
    @DataSource(DS.CF_SLAVE)
    List<String> listInfoUuidForDonationTest(@Param("userId") long userId, @Param("size") int size);

    int updateNoHandlingFeeByInfoUuid(@Param("infoUuid") String infoUuid, @Param("noHandlingFee") int noHandlingFee);

}
