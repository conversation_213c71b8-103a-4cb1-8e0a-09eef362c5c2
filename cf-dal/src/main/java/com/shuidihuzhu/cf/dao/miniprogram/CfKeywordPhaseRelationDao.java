package com.shuidihuzhu.cf.dao.miniprogram;

import com.shuidihuzhu.cf.model.miniprogram.CfKeywordPhaseRelation;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

@DataSource(DS.FRAME)
public interface CfKeywordPhaseRelationDao {
    int addOne(@Param("cfKeywordPhaseRelation") CfKeywordPhaseRelation cfKeywordPhaseRelation);

    int updateById(@Param("id") int id, @Param("cfKeywordPhaseRelation") CfKeywordPhaseRelation cfKeywordPhaseRelation);

    int updateByPhaseId(@Param("phaseId") int phaseId, @Param("cfKeywordPhaseRelation") CfKeywordPhaseRelation cfKeywordPhaseRelation);

    int addExpectByPhaseId(@Param("phaseId") int phaseId);

    int addNotExpectByPhaseId(@Param("phaseId") int phaseId);

    int addExpectByPhaseIdOnly(@Param("phaseId") int phaseId);

    int addNotExpectByPhaseIdOnly(@Param("phaseId") int phaseId);

    @DataSource(DS.FRAME_SLAVE)
    CfKeywordPhaseRelation selectByPhaseId(@Param("phaseId") int phaseId);
}
