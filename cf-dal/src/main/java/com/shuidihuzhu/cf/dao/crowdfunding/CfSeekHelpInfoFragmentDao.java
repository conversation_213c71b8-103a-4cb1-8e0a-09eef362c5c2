package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingSeekHelpInfoFragment;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * Created by chao on 2017/8/6.
 */
@DataSource(DS.CF)
public interface CfSeekHelpInfoFragmentDao {
	@DataSource(DS.CF_SLAVE)
	CrowdfundingSeekHelpInfoFragment get(@Param("infoUuid") String infoUuid);

	void delete(@Param("infoUuid") String infoUuid);

	long add(CrowdfundingSeekHelpInfoFragment infoFragment);
}
