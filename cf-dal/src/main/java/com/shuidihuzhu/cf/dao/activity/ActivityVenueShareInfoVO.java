package com.shuidihuzhu.cf.dao.activity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 主会场配转详情VO
 * <AUTHOR>
 */
@Data
@ApiModel("主会场配转信息")
public class ActivityVenueShareInfoVO {

    @ApiModelProperty("是否可配转")
    private boolean canSubsidyShare;

    @ApiModelProperty("随机到的案例infouuid")
    private String infoUuid;

    @ApiModelProperty("案例标题")
    private String title;

    @ApiModelProperty("案例头图")
    private String headImageUrl;

    @ApiModelProperty("补贴数量")
    private double subsidyCount;

    @ApiModelProperty("补贴单位")
    private String unitName;
}
