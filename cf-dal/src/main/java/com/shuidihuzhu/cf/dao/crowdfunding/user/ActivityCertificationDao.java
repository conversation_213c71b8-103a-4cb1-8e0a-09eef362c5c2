package com.shuidihuzhu.cf.dao.crowdfunding.user;

import com.shuidihuzhu.cf.model.crowdfunding.ActivityCertificationDO;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * @author: fengxuan
 * @create 2019-10-17 14:45
 **/
@DataSource("cfUserInfoShardingCrowdfunding")
public interface ActivityCertificationDao {

    int add(ActivityCertificationDO certificationDO);

    String getDocumentId(@Param("userId") long userId);
}
