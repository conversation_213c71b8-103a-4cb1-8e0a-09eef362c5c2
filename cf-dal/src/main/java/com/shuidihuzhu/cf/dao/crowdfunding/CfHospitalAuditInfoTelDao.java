package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfHospitalAuditInfoTel;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-07-29
 **/
@DataSource(DS.CF)
public interface CfHospitalAuditInfoTelDao {

    int insert(@Param("cfHospitalAuditInfoTels") List<CfHospitalAuditInfoTel> cfHospitalAuditInfoTels);

    int deleteByCfHospitalAuditInfoId(@Param("cfHospitalAuditInfoId") long cfHospitalAuditInfoId);

    int deleteByCfHospitalAuditInfoIds(@Param("cfHospitalAuditInfoIds") List<Long> cfHospitalAuditInfoIds);

    @DataSource(DS.CF_SLAVE)
    List<CfHospitalAuditInfoTel> selectByCfHospitalAuditInfoId(@Param("cfHospitalAuditInfoId") long cfHospitalAuditInfoId);
}
