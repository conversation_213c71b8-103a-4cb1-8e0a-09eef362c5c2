package com.shuidihuzhu.cf.dao.crowdfunding.user;

import com.shuidihuzhu.cf.domain.activity.ActivityVenueDetail;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * Created by sven on 2020/3/24.
 *
 * <AUTHOR>
 */
@DataSource("cfUserDataSource")
public interface ActivityVenueDetailDao {

    @DataSource("cfUserDataSourceSlave")
    ActivityVenueDetail get(@Param("id") long id);

    @DataSource("cfUserDataSourceSlave")
    ActivityVenueDetail getByCooperateId(@Param("cooperateId") long cooperateId);

    int update(@Param("id") long id,
               @Param("status") boolean status,
               @Param("oldVersion") long oldVersion,
               @Param("newVersion") long newVersion);
}
