package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.fulllink.measurement.annotation.Operate;
import com.shuidihuzhu.cf.fulllink.measurement.utils.OperateType;
import com.shuidihuzhu.cf.model.crowdfunding.CfCaseVisitConfig;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * TABLE: cf_case_visit_config
 *
 * 案例访问记录
 *
 * Created by wangsf on 18/4/16.
 */
@DataSource(DS.CF)
public interface CfCaseVisitConfigDao {

	@DataSource(DS.CF_SLAVE)
	@Operate(OperateType.READ)
	CfCaseVisitConfig getByCaseId(@Param("caseId") int caseId);

	@DataSource(DS.CF_SLAVE)
	@Operate(OperateType.READ)
	List<CfCaseVisitConfig> getByCaseIds(List<Integer> caseIds);

	@DataSource(DS.CF)
	int updateShareAndDonate(@Param("caseId") int caseId, @Param("canShare") boolean canShare, @Param("canDonate") boolean canDonate);

	@DataSource(DS.CF)
	int insertShareAndDonate(@Param("caseId") int caseId, @Param("canShare") boolean canShare, @Param("canDonate") boolean canDonate);

	@DataSource(DS.CF)
	int updateHiddenAndHiddenTitle(@Param("caseId") int caseId,
							 @Param("hidden") boolean hidden,
							 @Param("hiddenTitle") String hiddenTitle);


    int updateForceStop(@Param("caseId") int caseId, @Param("forceStop") boolean forceStop);

	int insertHiddenAndHiddenTitle(@Param("caseId") int caseId, @Param("canShare") boolean canShare,
								   @Param("canDonate") boolean canDonate,
								   @Param("hidden") boolean hidden,
								   @Param("hiddenTitle") String hiddenTitle
								   );
}
