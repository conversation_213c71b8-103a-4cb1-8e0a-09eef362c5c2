package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfFaceIdLivingVerifyInfo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

@DataSource(DS.CF)
public interface CfFaceIdLivingVerifyInfoDao {

    //cf-admin 调用cf-api，用到了photoValid ,没啥作用
    CfFaceIdLivingVerifyInfo getByInfoUuid(@Param("infoUuid") String infoUuid);

}
