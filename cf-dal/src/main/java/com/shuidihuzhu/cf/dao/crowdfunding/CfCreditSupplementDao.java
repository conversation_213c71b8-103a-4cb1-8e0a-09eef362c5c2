package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.fulllink.measurement.annotation.Operate;
import com.shuidihuzhu.cf.fulllink.measurement.utils.OperateType;
import com.shuidihuzhu.cf.model.crowdfunding.CfCreditSupplement;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by ahrievil on 2017/4/6.
 */
@DataSource(DS.CF)
public interface CfCreditSupplementDao {
    int addList(@Param("list")List<CfCreditSupplement> list);
    @Operate(OperateType.READ)
    List<CfCreditSupplement> selectByInfoUuid(@Param("infoUuid") String infoUuid);
    int deleteFromInfoUuid(@Param("infoUuid") String infoUuid);
}
