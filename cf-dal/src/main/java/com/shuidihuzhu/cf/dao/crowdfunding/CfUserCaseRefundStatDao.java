package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.fulllink.measurement.annotation.Operate;
import com.shuidihuzhu.cf.fulllink.measurement.utils.OperateType;
import com.shuidihuzhu.cf.model.crowdfunding.CfUserCaseRefundStatDO;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * @Author: wangpeng
 * @Date: 2023/2/23 19:19
 * @Description:
 */
@DataSource(CfDataSource.CROWDFUNDING_USER_MANAGER)
public interface CfUserCaseRefundStatDao {

    int insert(@Param("baseStatDO") CfUserCaseRefundStatDO cfUserCaseRefundStatDO, @Param("tableSuffix")String tableSuffix);

    @Operate(OperateType.READ)
    @DataSource(CfDataSource.CROWDFUNDING_USER_Slave)
    CfUserCaseRefundStatDO selectByUserAndCaseId(@Param("userId") long userId, @Param("caseId") int caseId,
                                             @Param("tableSuffix")String tableSuffix);

    int updateRefundAmount(@Param("userId") long userId, @Param("caseId") int caseId, @Param("refundAmount") int refundAmount,
                           @Param("tableSuffix")String tableSuffix);
}
