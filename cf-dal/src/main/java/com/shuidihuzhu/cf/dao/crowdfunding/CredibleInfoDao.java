package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2020-10-20 19:46
 **/
@DataSource(DS.CF_SLAVE)
public interface CredibleInfoDao {

    int getAuditStatus(@Param("subId") long subId, @Param("type") int type);
}
