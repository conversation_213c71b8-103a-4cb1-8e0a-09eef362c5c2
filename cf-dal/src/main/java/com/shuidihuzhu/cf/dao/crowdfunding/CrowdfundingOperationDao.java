package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.fulllink.measurement.annotation.Operate;
import com.shuidihuzhu.cf.fulllink.measurement.utils.OperateType;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOperation;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;
import java.util.Set;

@DataSource(DS.CF)
public interface CrowdfundingOperationDao {

	int add(CrowdfundingOperation crowdfundingOperation);

	int update(CrowdfundingOperation crowdfundingOperation);

	int updateOperation(@Param("infoUuid") String infoUuid,
	           @Param("operation") int operation,
	           @Param("operatorId") int operatorId,
	           @Param("reason") String reason);

	@DataSource(DS.CF_SLAVE)
	@Operate(OperateType.READ)
	CrowdfundingOperation getByInfoId(@Param("infoId") String infoId);

	int insertOrUpdateCommitTime(@Param("infoId") String infoId, @Param("caseId") int caseId, @Param("commitTime") Timestamp commitTime);

    int updateReportStatus(@Param("reportStatus") int reportStatus, @Param("infoUuid") String infoUuid);

	@DataSource(DS.CF_SLAVE)
	Set<String> filterReportedInfoUuid(@Param("infoIds") Set<String> infoIds);

    @DataSource(DS.CF_SLAVE)
    List<CrowdfundingOperation> selectByInfoUuids(@Param("infoUuids") List<String> infoUuids);

	@DataSource(DS.CF_SLAVE)
	List<CrowdfundingOperation> getByInfoIds(@Param("infoIds")List<String> infoIds);
}
