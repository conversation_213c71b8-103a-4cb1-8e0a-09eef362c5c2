package com.shuidihuzhu.cf.dao.crowdfunding.user;

import com.shuidihuzhu.cf.model.crowdfunding.CfUserCaseInfoDO;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2019-09-30 11:50
 **/
@DataSource("cfUserInfoShardingCrowdfunding")
public interface NewCfUserCaseDao {

    int insertOrUpdate(CfUserCaseInfoDO cfUserCaseInfoDO);

    /**
     * 防止一次从数据库取的过多,通过caseId查询参考 @see CfUserCaseInfoService#getByCaseId
     */
    @DataSource("cfUserInfoShardingCrowdfundingSlave")
    List<CfUserCaseInfoDO> findByCaseIdRangeById(@Param("caseId") int caseId, @Param("id") int id);

    @DataSource("cfUserInfoShardingCrowdfundingSlave")
    List<CfUserCaseInfoDO> findByUniqueIdAndLimit(@Param("caseId") int caseId, @Param("limit") int limit
            , @Param("offset") int offset);

//    @DataSource("cfUserInfoShardingCrowdfundingSlave")
    CfUserCaseInfoDO findByCaseAndUserId(@Param("caseId") int caseId, @Param("userId") long userId);
}
