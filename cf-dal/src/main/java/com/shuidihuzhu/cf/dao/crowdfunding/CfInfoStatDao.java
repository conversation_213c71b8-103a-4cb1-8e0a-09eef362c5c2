package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfInfoStat;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Author: <PERSON>
 * Date: 2017/4/1 15:02
 */
@DataSource(DS.CF)
public interface CfInfoStatDao {

	public int incShareCount(@Param("id") Integer id);

	public int incDonationCount(@Param("id") Integer id);

	public int incVerifyUserCount(@Param("id") Integer id);

	public int incCommentCount(@Param("id") Integer id);


	public int addAmount(@Param("id") Integer id, @Param("amount") Integer amount);

	@DataSource(DS.CF_SLAVE)
	public List<CfInfoStat> listByIds(@Param("ids") List<Integer> ids);

	@DataSource(DS.CF_SLAVE)
	public CfInfoStat getById(@Param("id") Integer id);

	int subtractAmount(@Param("id") Integer id, @Param("amount") Integer amount);

	int add(CfInfoStat cfInfoStat);

	int incVerifyHospitalCount(@Param("id") Integer id);

	int incVerifyFriendCount(@Param("id") Integer id);

	public int discShareCount(@Param("id") Integer id);

	public int discDonationCount(@Param("id") Integer id);

	public int discVerifyUserCount(@Param("id") Integer id);

	public int discCommentCount(@Param("id") Integer id);

	int discVerifyHospitalCount(@Param("id") Integer id);

	int discVerifyFriendCount(@Param("id") Integer id);

	int discBlessingCount(@Param("id") Integer id);
	public int incBlessingCount(@Param("id") Integer id);

	int incRefundCount(@Param("id") Integer id);

	int updateDonatorCount(@Param("id") int id, @Param("donatorCount") int donatorCount);

	int incDonatorCount(@Param("id") Integer id);

	int decDonatorCount(@Param("id") Integer id);

	/**
	 * 洗数据用  业务不要用
	 * @param id
	 * @param refundCount
	 * @return
	 */
	int updateRefundCount(@Param("id") int id, @Param("refundCount") int refundCount);

}
