package com.shuidihuzhu.cf.dao.crowdfunding.share;

import com.shuidihuzhu.cf.model.crowdfunding.share.CfShareWeiboTemplate;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

import java.util.List;

/**
 * Created by wangsf on 18/3/13.
 */
@DataSource(DS.CF)
public interface CfShareWeiboTemplateDao {

	@DataSource(DS.CF_SLAVE)
	List<CfShareWeiboTemplate> getAll();

}
