package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.fulllink.measurement.annotation.Operate;
import com.shuidihuzhu.cf.fulllink.measurement.utils.OperateType;
import com.shuidihuzhu.cf.model.crowdfunding.CfUserCaseBaseStatDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2019/2/20 下午8:08
 * @desc
 */
@DataSource(CfDataSource.CROWDFUNDING_USER_MANAGER)
public interface CfUserCaseBaseStatDao {
    int insert(@Param("baseStatDO") CfUserCaseBaseStatDO baseStatDO,@Param("tableSuffix")String tableSuffix);

    @Operate(OperateType.READ)
    @DataSource(CfDataSource.CROWDFUNDING_USER_Slave)
    CfUserCaseBaseStatDO selectByUserAndCase(@Param("userId") long userId, @Param("caseId") int caseId,
                                             @Param("tableSuffix")String tableSuffix);

    @Operate(OperateType.READ)
    @DataSource(CfDataSource.CROWDFUNDING_USER_Slave)
    List<CfUserCaseBaseStatDO> selectByUserAndCases(@Param("userId") long userId, @Param("caseIds") List<Integer> caseIds,
                                                    @Param("tableSuffix") String tableSuffix);
    int updateCaseShareInfo(@Param("userId") long userId, @Param("caseId") int caseId,
                            @Param("tableSuffix")String tableSuffix);
    int updateShareBroughtAmount(@Param("userId") long userId, @Param("caseId") int caseId, @Param("shareBroughtAmountIncr") int shareBroughtAmountIncr,
                                 @Param("tableSuffix")String tableSuffix);
    int currentCaseDonateAmountIncr(@Param("userId") long userId, @Param("caseId") int caseId, @Param("currentCaseDonateAmountIncr") int currentCaseDonateAmountIncr,
                                    @Param("tableSuffix")String tableSuffix);
    int updateShareFirstDonateAmount(@Param("userId") long userId, @Param("caseId") int caseId, @Param("shareFirstAmount") int shareFirstAmount,
                                     @Param("tableSuffix")String tableSuffix);

}
