package com.shuidihuzhu.cf.dao.crowdfunding.user;

import com.shuidihuzhu.cf.model.crowdfunding.CfUserFirstDonateTimeInfo;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * @author: fengxuan
 * @create 2019-10-17 10:46
 **/
@DataSource("cfUserInfoShardingCrowdfunding")
public interface CfUserFirstDonateDao {

    int add(CfUserFirstDonateTimeInfo cfUserFirstDonateTimeInfo);

    CfUserFirstDonateTimeInfo findByUserId(@Param("userId")long userId);
}
