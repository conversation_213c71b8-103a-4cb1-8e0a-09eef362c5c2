package com.shuidihuzhu.cf.dao.crowdfundingshare;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CfSharePromoteOrder;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-07-05
 */
@DataSource(CfDataSource.SHUIDI_CROWDFUNDING_SHARE_SLAVE)
public interface CfSharePromoteOrderShardingDao {

    @DataSource(CfDataSource.SHUIDI_CROWDFUNDING_SHARE_MASTER)
    int insert(@Param("order") CfSharePromoteOrder cfSharePromoteOrder, @Param("sharding") String sharding);

    @DataSource(CfDataSource.SHUIDI_CROWDFUNDING_SHARE_MASTER)
    CfSharePromoteOrder findLast(@Param("sourceUserId") long sourceUserId, @Param("infoId") int infoId, @Param("sharding") String sharding);

    List<CfSharePromoteOrder> findByUserAndCase(@Param("sourceUserId") long sourceUserId, @Param("infoId") int infoId, @Param("sharding") String sharding);

    List<CfSharePromoteOrder> findByInfoId(@Param("infoId") int infoId, @Param("anchorId") int anchorId,
                                           @Param("limit") int limit, @Param("sharding") String sharding);

    Integer getSumAmountByInfoIdAndUserId(@Param("infoId") int infoId, @Param("userId")long userId, @Param("sharding") String sharding);

    /**
     * 退款修改退款状态
     * @param orderId
     */
    @DataSource(CfDataSource.SHUIDI_CROWDFUNDING_SHARE_MASTER)
    int refund(@Param("orderId") long orderId, @Param("infoId") int infoId, @Param("sharding") String sharding);

    @DataSource(CfDataSource.SHUIDI_CROWDFUNDING_SHARE_MASTER)
    CfSharePromoteOrder getByOrderId(@Param("infoId") int infoId, @Param("orderId") long orderId, @Param("sharding") String sharding);

    @DataSource(CfDataSource.SHUIDI_CROWDFUNDING_SHARE_MASTER)
    List<CfSharePromoteOrder> getByOrderIdBySouceIdAndInfoIdAndUserId(@Param("infoId") int infoId, @Param("orderIds") List<Long> orderIds,@Param("sourceUserId")long sourceUserId, @Param("sharding") String sharding);
}
