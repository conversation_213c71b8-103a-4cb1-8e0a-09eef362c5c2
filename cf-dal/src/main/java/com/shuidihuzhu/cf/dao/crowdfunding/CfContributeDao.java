package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfContribute;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 *Author：梁洪超
 *Date： 2017/10/20
 */
@DataSource(DS.CF)
public interface CfContributeDao {

	/** 保存爱心榜信息
	 * @param list
	 * @return
	 */
	int addContributeList(@Param("list") List<CfContribute> list);

	/**获取按案例的前十个
	 * @param infoId
	 * @return
	 */
	@DataSource(DS.CF_SLAVE)
	List<CfContribute> getTopList (@Param("infoId") int infoId ,@Param("limit") int limit );

	/**
	 * 批量更新is-delete
	 * @param list
	 * @return
	 */
	int deleteByIds(@Param("list") List<Integer> list);

	/**
	 * @param list
	 * @param start
	 * @param limit
	 * @return
	 */
	@DataSource(DS.CF_SLAVE)
	List<CfContribute> getBeachByInfoIds (@Param("list") List<Integer> list ,
	                                      @Param("offset") int offset , @Param("limit") int limit);

	int updateByIds(@Param("list")List<CfContribute>list);
	
	int update (@Param("cfContribute") CfContribute cfContribute);

	@DataSource(DS.CF_SLAVE)
	Date getMaxCreatTime ();

	@DataSource(DS.CF_SLAVE)
	int getCountInsert(@Param("startTime") Date startTime , @Param("endTime") Date endTime);

	@DataSource(DS.CF_SLAVE_2)
	int getCountUpdate(@Param("startTime") Date startTime , @Param("endTime") Date endTime);

	@DataSource(DS.CF_SLAVE_2)
	int getCountDelete(@Param("startTime") Date startTime , @Param("endTime") Date endTime);
}
