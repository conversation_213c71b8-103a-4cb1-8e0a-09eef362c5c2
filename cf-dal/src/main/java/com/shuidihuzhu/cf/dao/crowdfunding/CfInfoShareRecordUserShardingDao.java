package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareRecord;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;


/**
 * @author: wanghui
 * @time: 2019/7/8 3:47 PM
 * @description:
 * @param:  * @param null :
 * @return:  * @return : null
 */
@DataSource(CfDataSource.SHUIDI_CROWDFUNDING_SHARE_SLAVE)
public interface CfInfoShareRecordUserShardingDao {

	@DataSource(CfDataSource.SHUIDI_CROWDFUNDING_SHARE_MASTER)
	int add(@Param("record") CfInfoShareRecord cfInfoShareRecord, @Param("sharding") String sharding);



	/**
	 * 根据id查询用户是否分享案例
	 * @param userIds
	 * @param beforeTime
	 * @param newTime
	 * @return
	 */
	List<CfInfoShareRecord> selByUserIds(@Param("userIds") Set<Long> userIds,
                                         @Param("beforeTime") Timestamp beforeTime,
                                         @Param("newTime") Timestamp newTime, @Param("sharding") String sharding);

	/**
	 * 根据id查询用户是否分享案例
	 * @param userIds
	 * @param beforeTime
	 * @param newTime
	 * @return
	 */
	List<CfInfoShareRecord> selByUserIdsAndCaseId(@Param("userIds") Set<Long> userIds,@Param("caseId") int caseId,
										 @Param("beforeTime") Timestamp beforeTime,
										 @Param("newTime") Timestamp newTime, @Param("sharding") String sharding);

	List<CfInfoShareRecord> findCfInfoShareRecord(@Param("userId") Long userId, @Param("startTime") String startTime,
                                                  @Param("endTime") String endTime, @Param("sharding") String sharding);

}
