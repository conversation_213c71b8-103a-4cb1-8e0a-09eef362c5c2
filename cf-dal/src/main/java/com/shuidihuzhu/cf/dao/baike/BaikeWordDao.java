package com.shuidihuzhu.cf.dao.baike;

import com.shuidihuzhu.cf.model.baike.BaikeWord;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * Created by wangsf on 17/12/23.
 */

@DataSource(DS.CF)
public interface BaikeWordDao {

	@DataSource(DS.CF_SLAVE)
	BaikeWord findByWord(@Param("word") String word);

}
