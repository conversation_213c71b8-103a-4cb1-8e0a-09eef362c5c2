package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.fulllink.measurement.annotation.Operate;
import com.shuidihuzhu.cf.fulllink.measurement.utils.OperateType;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * @author: wanghui
 * @time: 2019/3/12 8:56 PM
 * @description:  以orderid分表的dao
 */

@DataSource(CfDataSource.SHARDING_DONATION_BIG_TABLE_SLAVE)
public interface CrowdfundingPayRecordShardingOrderIdDao {
	@DataSource(CfDataSource.SHARDING_DONATION_BIG_TABLE_MASTER)
	int addPayRecord(CrowdfundingPayRecord payRecord);
	@DataSource(CfDataSource.SHARDING_DONATION_BIG_TABLE_MASTER)
	int updatePayStatus(@Param("crowdfundingOrderId") Long crowdfundingOrderId,@Param("payUid") String payUid,
						@Param("payStatus") Integer payStatus,@Param("callbackTime") Date callbackTime,
						@Param("realPayAmount") Integer realPayAmount);
	@DataSource(CfDataSource.SHARDING_DONATION_BIG_TABLE_MASTER)
	int updateRefundStatus(@Param("crowdfundingOrderId") Long crowdfundingOrderId,@Param("payUid") String payUid,
						   @Param("refundStatus") Integer refundStatus, @Param("refundTime") Date refundTime,
						   @Param("refundAmount") Integer refundAmount, @Param("refundReason") String refundReason);
	@DataSource(CfDataSource.SHARDING_DONATION_BIG_TABLE_MASTER)
	int updateRefundStatusByOrderIds(@Param("crowdfundingOrderIds") List<Long> orderIds,@Param("refundStatus") int refundStatus );
	@DataSource(CfDataSource.SHARDING_DONATION_BIG_TABLE_MASTER)
	CrowdfundingPayRecord getByOrderId(@Param("crowdfundingOrderId") Long orderId);
	List<CrowdfundingPayRecord> listSuccessByOrderIds(@Param("crowdfundingOrderIds") List<Long> orderIds);

	List<CrowdfundingPayRecord> getPaySuccessByPayUidsAndOrderIds(@Param("crowdfundingOrderIds") List<Long> orderIds,@Param("payUids") List<String> payUids);
	@DataSource(CfDataSource.SHARDING_DONATION_BIG_TABLE_MASTER)
	List<CrowdfundingPayRecord> getPaySuccessByOrderIds(@Param("orderIds") List<Long> orderIds,@Param("valid") Integer valid);

	@DataSource(CfDataSource.DONATION_BIGTABLE_MASTER)
	List<CrowdfundingPayRecord> getPaySuccessByOrderIdsWithSuffixTableName(@Param("sharding") String sharding, @Param("orderIds") List<Long> orderIds,@Param("valid") Integer valid);
	@DataSource(CfDataSource.DONATION_BIGTABLE_MASTER)
	@Operate(OperateType.READ)
	CrowdfundingPayRecord getByOrderIdWithPayUidAndSuffixTableName(@Param("sharding") String sharding, @Param("crowdfundingOrderId") Long crowdfundingOrderId,@Param("payUid") String payUid);
	List<CrowdfundingPayRecord> selectByOrderIdList(@Param("list") List<Long> list, @Param("start") int start, @Param("size") int size);
	List<CrowdfundingPayRecord> getPaySuccessByOrderIdsAndRefundStatus(@Param("orderIds")List<Long> orderIds,
																	   @Param("refundStatusList") List<Integer> refundStatusList);
	@DataSource(CfDataSource.DONATION_BIGTABLE_SLAVE_1)
	List<CrowdfundingPayRecord> getPaySuccessByOrderIdsAndRefundStatusWithSuffixTableName(@Param("sharding") String sharding,
																	   @Param("orderIds")List<Long> orderIds,
																	   @Param("refundStatusList") List<Integer> refundStatusList);
	@Operate(OperateType.READ)
	List<CrowdfundingPayRecord> getByOrderIdsAndPayUids(@Param("orderIds")List<Long> orderIds,@Param("payUids") List<String> payUids);

	List<CrowdfundingPayRecord> getValidByOrderIds(@Param("orderIds") List<Long> orderIds);
	@DataSource(CfDataSource.DONATION_BIGTABLE_SLAVE_1)
	List<CrowdfundingPayRecord> getByOrderIdsWithSuffixTableName(@Param("sharding") String sharding,@Param("orderIds") List<Long> orderIds);



}
