package com.shuidihuzhu.cf.dao.topList.dailyActivity;

import com.shuidihuzhu.cf.model.topList.DailyActivityTopList;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;
import org.springframework.context.annotation.Bean;

import java.util.List;

@DataSource("cfUserInfoShardingCrowdfunding")
public interface DailyActivityUserInfoDao {

    @DataSource("cfUserDataSourceSlave")
    List<DailyActivityTopList.UserInfoSummary> selectTopDonateCountList(@Param("activityPeriod") int activityPeriod,
                                                                        @Param("size") int size,
                                                                        @Param("index") int index);

    @DataSource("cfUserDataSourceSlave")
    List<DailyActivityTopList.UserInfoSummary> selectTopBringVisitCountList(@Param("activityPeriod") int activityPeriod,
                                                                            @Param("size") int size,
                                                                            @Param("index") int index);


    @DataSource("cfUserInfoShardingCrowdfundingSlave")
    DailyActivityTopList.UserInfoSummary selectUserSummaryFromSlave(@Param("userId") long userId,@Param("activityPeriod") int activityPeriod);

    DailyActivityTopList.UserInfoSummary selectUserSummary(@Param("userId") long userId,@Param("activityPeriod") int activityPeriod);


    int insertOrUpdateDonateCount(DailyActivityTopList.UserInfoSummary param);
    int insertOrUpdateBringVisit(DailyActivityTopList.UserInfoSummary param);
    int insertOrUpdateVisitType(@Param("userId") long userId, @Param("visitVenueType") int visitVenueType,
                                @Param("sourceBringVisit") String sourceBringVisit,
                                @Param("activityPeriod") int activityPeriod);

}
