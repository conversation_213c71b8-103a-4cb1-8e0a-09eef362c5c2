package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.dto.UserSatisfactionDTO;
import com.shuidihuzhu.cf.model.crowdfunding.UserSatisfactionDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author：liuchangjun
 * @Date：2021/9/28
 */
@DataSource(DS.CF)
public interface UserSatisfactionDao {

    List<UserSatisfactionDO> getByUserIdAndCaseId(@Param("userId") long userId,
                                                  @Param("caseId") int caseId,
                                                  @Param("testType") Integer testType,
                                                  @Param("informationNames") List<String> informationNames);

    int addUserSatisfaction(@Param("userSatisfactionDTO") UserSatisfactionDTO userSatisfactionDTO);
}
