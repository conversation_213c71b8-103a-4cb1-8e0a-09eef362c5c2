package com.shuidihuzhu.cf.dao.crowdfunding;


import com.shuidihuzhu.cf.model.crowdfunding.material.CfPatientCaseInfo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.CF)
public interface CfPatientCaseInfoDao {

    @DataSource(DS.CF_SLAVE)
    List<CfPatientCaseInfo> selectByIdCardList(@Param("list") List<String> patientIdCards);

    int addOrUpdate(@Param("patientCryptoIdcard") String patientCryptoIdcard,
                    @Param("patientCaseInfo") String patientCaseInfo);


    @DataSource(DS.CF_SLAVE)
    List<CfPatientCaseInfo> selectByIdLimit(@Param("id") long id, @Param("limit") int limit);
}
