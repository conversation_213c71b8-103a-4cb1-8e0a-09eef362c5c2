package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTitleTemplate;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by Ahrievil on 2017/12/6
 */
@DataSource(DS.CF)
public interface CfBaseInfoTitleTemplateDao {

    int insertOne(CfBaseInfoTitleTemplate cfBaseInfoTitleTemplate);

    @DataSource(DS.CF_SLAVE)
    List<CfBaseInfoTitleTemplate> selectLimit(@Param("start") int start, @Param("size") int size);

    @DataSource(DS.CF_SLAVE)
    List<CfBaseInfoTitleTemplate> selectTypeLimit(@Param("titleType") int titleType, @Param("start") int start, @Param("size") int size);
}
