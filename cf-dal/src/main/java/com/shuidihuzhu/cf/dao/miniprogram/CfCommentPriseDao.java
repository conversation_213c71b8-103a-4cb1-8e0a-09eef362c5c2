package com.shuidihuzhu.cf.dao.miniprogram;

import com.shuidihuzhu.cf.model.miniprogram.CfCommentPrise;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.FRAME)
public interface CfCommentPriseDao {
    int add(@Param("topicId") int topicId, @Param("userId") long userId, @Param("commentId") long commentId);

    @DataSource(DS.FRAME_SLAVE)
    CfCommentPrise selectByUserAndComment(@Param("userId") long userId, @Param("commentId") long commentId);

    int updateByUserAndComment(@Param("userId") long userId, @Param("commentId") long commentId, @Param("status") int status);

    @DataSource(DS.FRAME_SLAVE)
    List<CfCommentPrise> listByUserAndCommentIds(@Param("userId") long userId, @Param("commentIds") List<Long> commentIds);
}
