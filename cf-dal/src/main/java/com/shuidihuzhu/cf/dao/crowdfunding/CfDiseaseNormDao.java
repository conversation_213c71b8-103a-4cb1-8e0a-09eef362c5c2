package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.DiseaseLabel;
import com.shuidihuzhu.cf.model.disease.DiseaseNorm;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <AUTHOR>
 */
@DataSource(DS.CF)
public interface CfDiseaseNormDao {

    int insert(@Param("caseId") int caseId, @Param("infoUuid") String infoUuid,
               @Param("diseaseName") String diseaseName, @Param("diseaseNorm") String diseaseNorm,
               @Param("diseaseNormJson") String diseaseNormJson);

    int update(@Param("caseId") int caseId, @Param("diseaseName") String diseaseName,
               @Param("diseaseNorm") String diseaseNorm, @Param("diseaseNormJson") String diseaseNormJson);

    @DataSource(DS.CF_SLAVE)
    int selectCount(@Param("caseId") int caseId);

    @DataSource(DS.CF_SLAVE)
    DiseaseLabel selectByCaseId(@Param("caseId") int caseId);

    @DataSource(DS.CF_SLAVE)
    DiseaseNorm getByCaseId(@Param("caseId") int caseId);

    @DataSource(DS.CF_SLAVE)
    List<DiseaseNorm> selectByCaseIds(@Param("caseIds") List<Integer> caseId);

}
