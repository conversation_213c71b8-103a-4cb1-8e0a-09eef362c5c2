package com.shuidihuzhu.cf.dao.starrocks;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.model.starrocks.RptCfCaseFeaturesDo;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/18  14:43
 */
@DataSource(CfDataSource.STAR_ROCKS_DATASOURCE)
public interface CfCaseFeaturesDao {

    RptCfCaseFeaturesDo getByInfoId(@Param("infoId") Long infoId);

    List<RptCfCaseFeaturesDo> listByInfoIds(@Param("infoIds") List<Long> infoIds);

    List<RptCfCaseFeaturesDo> listBigSpaceDonateByVolunteerCode(@Param("volunteerCode") String volunteerCode);


}
