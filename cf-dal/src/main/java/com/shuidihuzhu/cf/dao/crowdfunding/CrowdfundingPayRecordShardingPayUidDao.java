package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.fulllink.measurement.annotation.Operate;
import com.shuidihuzhu.cf.fulllink.measurement.utils.OperateType;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecordShardingModel;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * @author: wanghui
 * @time: 2019/3/12 8:56 PM
 * @description:  以payuid分表的dao
 */

@DataSource(CfDataSource.SHARDING_DONATION_BIG_TABLE_MASTER)
public interface CrowdfundingPayRecordShardingPayUidDao {

	int addPayRecord(CrowdfundingPayRecord payRecord);

	@Operate(OperateType.READ)
	CrowdfundingPayRecordShardingModel getPayRecordShardingModelByPayUid(@Param("payUid") String payUid);

	List<CrowdfundingPayRecordShardingModel> getPayRecordShardingModelListByPayUids(@Param("payUids") List<String> payUids);

}
