package com.shuidihuzhu.cf.dao.ugc;


import com.shuidihuzhu.cf.domain.ugc.CfUgcOperateRecord;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.springframework.web.bind.annotation.RequestParam;
@DataSource(DS.CF)
public interface CfUgcOperateRecordDao {

    int insert(CfUgcOperateRecord record);

    int insertSelective(CfUgcOperateRecord record);

    CfUgcOperateRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CfUgcOperateRecord record);

    int updateByPrimaryKey(CfUgcOperateRecord record);

    CfUgcOperateRecord getByCaseIdAndBizIdAndBizType(@RequestParam("caseId") long caseId,
                                                     @RequestParam("bizId") long bizId,
                                                     @RequestParam("bizType") int bizType);
}