package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfPropertyTransferHistory;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by ch<PERSON> on 2017/10/13.
 */
@DataSource(DS.CF)
public interface CfPropertyTransferHistoryDao {
	void addBatch(@Param("historyList") List<CfPropertyTransferHistory> historyList);
	List<CfPropertyTransferHistory> get(@Param("fromUserId")long fromUserId,
	                                    @Param("toUserId") long toUserId,
	                                    @Param("bizId") int bizId,
	                                    @Param("bizType") int bizType);
}
