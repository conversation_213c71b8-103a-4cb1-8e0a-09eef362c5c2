package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfSendProve;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@DataSource(DS.CF)
public interface CfProveDao {

    @DataSource(DS.CF_SLAVE)
    CfSendProve getlastOne(@Param("caseId") int caseId);

    int updateProveById(@Param("id") long id,
                        @Param("pictureUrl") String pictureUrl,
                        @Param("pictureAuditStatus") int pictureAuditStatus);

    int updateCancelReason(@Param("id") long id, @Param("cancelReason") String cancelReason);

    @DataSource(DS.CF_SLAVE)
    List<CfSendProve> getListByCaseId(@Param("caseId") int caseId);

    CfSendProve getById(@Param("id") long id);

}
