package com.shuidihuzhu.cf.dao.crowdfunding;


import com.shuidihuzhu.cf.model.crowdfunding.CfPageGlobalConfig;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.CF)
public interface AdminCfPageGlobalConfigDao {

     int add(List<CfPageGlobalConfig> configs);

     int delete(@Param("globalType") int globalType);

     @DataSource(DS.CF_SLAVE)
     List<CfPageGlobalConfig> getListByType(@Param("globalType") int globalType);

}
