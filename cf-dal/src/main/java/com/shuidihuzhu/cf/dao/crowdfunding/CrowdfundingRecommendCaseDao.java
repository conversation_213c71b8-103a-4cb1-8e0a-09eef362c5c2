package com.shuidihuzhu.cf.dao.crowdfunding;


import com.shuidihuzhu.cf.CrowdfundingRecommendCase;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by wangsf on 17/2/12.
 */
@DataSource(DS.CF_SLAVE)
public interface CrowdfundingRecommendCaseDao {

	List<String> getInfoIdList(@Param("limit") int limit, @Param("type") int type,@Param("size") int size);

	int update(@Param("patientName") String patientName, @Param("infoId") String infoId);

	List<String> getListByPatientName(@Param("type") int type, @Param("patientName") String patientName);

	List<String> getListByInfoIds(@Param("infoIds") List<String> infoIds);

}
