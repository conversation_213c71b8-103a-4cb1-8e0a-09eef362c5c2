package com.shuidihuzhu.cf.dao.topList.dailyActivity;


import com.shuidihuzhu.cf.model.topList.DailyActivityTopList;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource("cfUserDataSource")
public interface DailyActivityUserInfoBackupDao {

    List<DailyActivityTopList.UserInfoSummary> selectAllByLimit(@Param("id")long id, @Param("limit") int limit, @Param("index") int index);

    int insertBackUpHistory(@Param("list") List<DailyActivityTopList.UserInfoSummary> list);

}
