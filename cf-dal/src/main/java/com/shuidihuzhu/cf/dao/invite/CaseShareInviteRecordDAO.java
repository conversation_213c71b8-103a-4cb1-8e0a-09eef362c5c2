package com.shuidihuzhu.cf.dao.invite;

import com.shuidihuzhu.cf.domain.ShareInviteRecordDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-12-13  18:20
 */
@DataSource(DS.CF)
public interface CaseShareInviteRecordDAO {

    @DataSource(DS.CF_SLAVE)
    ShareInviteRecordDO getById(@Param("id") long id);

    @DataSource(DS.CF_SLAVE)
    List<ShareInviteRecordDO> listByCondition(@Param("userId") long userId,
                                              @Param("caseId") long caseId,
                                              @Param("startTime") Date startTime,
                                              @Param("endTime") Date endTime);

    int insert(ShareInviteRecordDO shareInviteRecord);

    int insertFriendUserIds(@Param("userId") long userId,
                            @Param("caseId") int caseId,
                            @Param("friendUserIds") List<Long> friendUserIds);
}
