//package com.shuidihuzhu.cf.dao.message;
//
//import com.shuidihuzhu.cf.fulllink.measurement.annotation.Operate;
//import com.shuidihuzhu.cf.fulllink.measurement.utils.OperateType;
//import com.shuidihuzhu.common.datasource.DS;
//import com.shuidihuzhu.common.datasource.annotation.DataSource;
//import com.shuidihuzhu.common.web.model.UserAddress;
//import org.apache.ibatis.annotations.Param;
//
///**
// *
// * Created by lixuan on 2017/03/29.
// *
// */
//@DataSource(DS.SD)
//public interface UserAddressDao {
//
//	int save(UserAddress userAddress);
//
//	int updateForClearDefault(@Param("userId") long userId, @Param("id") long id);
//
//	@Operate(OperateType.READ)
//	UserAddress getById(@Param("id") long id);
//
//	UserAddress getDefaultByUserId(@Param("userId") long userId);
//}
