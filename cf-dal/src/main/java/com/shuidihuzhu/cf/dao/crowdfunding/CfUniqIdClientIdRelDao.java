package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfUniqIdClientIdRel;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by wangsf on 17/12/21.
 */
@DataSource(DS.CF)
public interface CfUniqIdClientIdRelDao {

	int insert(CfUniqIdClientIdRel cfUniqIdClientIdRel);

	@DataSource(DS.CF_SLAVE)
	CfUniqIdClientIdRel findByAppUniqId(@Param("appUniqId") String appUniqId);

	int deleteById(@Param("id") int id);

	@DataSource(DS.CF_SLAVE)
	List<CfUniqIdClientIdRel> findByUniqIds(@Param("appUniqIds") List<String> appUniqIds,
	                                        @Param("anchorId") int anchorId,
	                                        @Param("limit") int limit);

}
