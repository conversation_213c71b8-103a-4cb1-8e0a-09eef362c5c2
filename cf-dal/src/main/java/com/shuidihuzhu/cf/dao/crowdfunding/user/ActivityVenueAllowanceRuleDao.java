package com.shuidihuzhu.cf.dao.crowdfunding.user;

import com.shuidihuzhu.cf.domain.activity.ActivityVenueAllowanceRule;
import com.shuidihuzhu.cf.domain.activity.ActivityVenueDetail;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by sven on 2020/3/25.
 *
 * <AUTHOR>
 */
@DataSource("cfUserDataSource")
public interface ActivityVenueAllowanceRuleDao {

    @DataSource("cfUserDataSourceSlave")
    List<ActivityVenueAllowanceRule> get(@Param("activityId") long activityId);
}
