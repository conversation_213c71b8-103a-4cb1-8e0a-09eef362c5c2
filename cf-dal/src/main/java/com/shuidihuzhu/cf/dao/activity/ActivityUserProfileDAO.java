package com.shuidihuzhu.cf.dao.activity;

import com.shuidihuzhu.cf.domain.activity.ActivityUserProfileDO;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@DataSource("cfUserInfoShardingCrowdfunding")
public interface ActivityUserProfileDAO {

    int insert(ActivityUserProfileDO activityUserProfileDO);

    ActivityUserProfileDO getByUserIdAndType(@Param("userId") long userId, @Param("profileType") int profileType);
}
