package com.shuidihuzhu.cf.dao.mina;

import com.shuidihuzhu.cf.model.mina.CfMinaQuizSort;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * Created by dongcf on 2017/12/20
 */
@DataSource(DS.FRAME)
public interface CfMinaQuizSortDao {

    int insert(CfMinaQuizSort cfMinaQuizSort);

    @DataSource(DS.FRAME_SLAVE)
    CfMinaQuizSort getByAppIdWithUserId(@Param("userId") long userId, @Param("appId")String appId);

    @DataSource(DS.FRAME_SLAVE)
    CfMinaQuizSort getByAppIdWithSelfTag(@Param("selfTag") String selfTag, @Param("appId")String appId);
}
