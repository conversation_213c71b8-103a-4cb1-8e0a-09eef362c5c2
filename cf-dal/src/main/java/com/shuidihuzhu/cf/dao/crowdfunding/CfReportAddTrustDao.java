package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfReportAddTrust;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/10/17.
 */
@DataSource(DS.CF)
public interface CfReportAddTrustDao {

    int save(CfReportAddTrust cfReportAddTrust);

    int update(CfReportAddTrust cfReportAddTrust);

    @DataSource(DS.CF_SLAVE)
    CfReportAddTrust getNewestByInfoUuid(@Param("infoUuid")String infoUuid);


    int delete(@Param("infoUuid") String infoUuid);

    @DataSource(DS.CF_SLAVE)
    CfReportAddTrust getTrustById(@Param("id")long id);

}
