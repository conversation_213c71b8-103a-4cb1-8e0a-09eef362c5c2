package com.shuidihuzhu.cf.dao.crowdfunding.visitconfig;

import com.shuidihuzhu.cf.domain.visitconfig.VisitConfigLogDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-09-19  20:31
 */
@DataSource(DS.CF)
public interface VisitConfigLogDAO {

    int insert(VisitConfigLogDO visitConfigLogDO);

    /**
     * 获取某案例 某操作类型的所有操作记录
     *
     * @param infoUuid
     * @return
     */
    @DataSource(DS.CF_SLAVE)
    List<VisitConfigLogDO> listByInfoUuidAndActionType(@Param("infoUuid") String infoUuid,
                                                       @Param("actionType") Integer actionType);
    @DataSource(DS.CF_SLAVE)
    List<VisitConfigLogDO> listByCondition(@Param("infoUuid") String infoUuid,
                                           @Param("actionTypes") List<Integer> actionTypes,
                                           @Param("sources") List<Integer> sources);


}
