package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderBase;
import com.shuidihuzhu.client.cf.workorder.read.BasicWorkOrder;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

@DataSource(CfDataSource.TD_CF_ADMIN_DS)
public interface TdWorkOrderDao {
    WorkOrderBase getLastWorkOrderByType(@Param("caseId") int caseId, @Param("type") int type);
    WorkOrderBase getWorkOrderById(@Param("id") long id);
}
