package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.IdcardVerifyWhiteList;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: duchao
 * @Date: 2018/8/16 下午5:18
 */
@DataSource(DS.CF_SLAVE)
public interface CfFirstApproveIdCardWhiteListDao {
	IdcardVerifyWhiteList getByNameAndIdcard(@Param("name") String name,
											 @Param("cryptoIdCard") String cryptoIdCard);


	@DataSource(DS.CF)
	int add(IdcardVerifyWhiteList idcardVerifyWhiteList);

	@DataSource(DS.CF)
	int deleteFirstApproveWhiteIdById(@Param("id") int id);

	List<IdcardVerifyWhiteList> selectAllWhiteIdCardList();

	List<IdcardVerifyWhiteList> selectAllWhiteIdCardListAll(@Param("name") String name, @Param("idCard") String idCard);
}
