package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfRecommendInfoTagRelation;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.CF)
public interface CfRecommendInfoTagRelationDao {

	int insert(CfRecommendInfoTagRelation record);

	@DataSource(DS.CF_SLAVE)
	CfRecommendInfoTagRelation selectByPrimaryKey(Integer id);

	@DataSource(DS.CF_SLAVE)
	List<CfRecommendInfoTagRelation> selectByInfoIds(@Param("infoIds") List<Integer> infoIds);

	int updateByPrimaryKey(CfRecommendInfoTagRelation record);
}
