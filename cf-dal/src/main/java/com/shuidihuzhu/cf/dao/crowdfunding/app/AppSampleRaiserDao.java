package com.shuidihuzhu.cf.dao.crowdfunding.app;

import com.shuidihuzhu.cf.model.crowdfunding.app.AppSampleRaiser;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by wangsf on 18/2/1.
 */

@DataSource(DS.CF)
public interface AppSampleRaiserDao {

	@DataSource(DS.CF_SLAVE)
	List<AppSampleRaiser> findByAppName(@Param("name") String name);

}
