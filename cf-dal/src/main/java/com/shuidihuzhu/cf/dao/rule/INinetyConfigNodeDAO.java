package com.shuidihuzhu.cf.dao.rule;

import com.shuidihuzhu.cf.fulllink.measurement.annotation.Operate;
import com.shuidihuzhu.cf.fulllink.measurement.utils.OperateType;
import com.shuidihuzhu.cf.model.rule.NinetyDaysConfigNode;
import com.shuidihuzhu.cf.model.rule.RuleCondition;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by sven on 2020/3/27.
 *
 * <AUTHOR>
 */
@DataSource("cfUserDataSource")
public interface INinetyConfigNodeDAO {

    @DataSource("cfUserDataSourceSlave")
    @Operate(OperateType.READ)
    List<NinetyDaysConfigNode> getAll();
}
