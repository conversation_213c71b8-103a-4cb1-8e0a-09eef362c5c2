package com.shuidihuzhu.cf.dao.activity;

import com.shuidihuzhu.cf.domain.activity.ActivityDonatorRedPocketHistory;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by sven on 2020/5/10.
 *
 * <AUTHOR>
 */
@DataSource(DS.CF)
public interface ActivityDonatorRedPocketHistoryDAO {

    int insert(ActivityDonatorRedPocketHistory history);

    @DataSource(DS.CF_SLAVE)
    List<ActivityDonatorRedPocketHistory> getByCaseIdUserId(@Param("caseId") int caseId, @Param("userId") long userId);

    @DataSource(DS.CF_SLAVE)
    List<ActivityDonatorRedPocketHistory> getByCaseId(@Param("caseId") int caseId);

    @DataSource(DS.CF_SLAVE)
    List<ActivityDonatorRedPocketHistory> getByCaseIdLastThree(@Param("caseId") int caseId);

    @DataSource(DS.CF_SLAVE)
    List<Long> listUserIdsByCaseIdAndPocketId(@Param("caseId") int caseId, @Param("pocketId") long pocketId);

    @DataSource(DS.CF_SLAVE)
    List<Long> listUserIdsByCaseIdAndPocketIdList(@Param("caseId") int caseId, @Param("pocketIdList") List<Long> pocketIdList);

    void cleanUserSubsidyBackdoor(@Param("activityId") long activityId, @Param("caseId") int caseId, @Param("userId") long userId);
}
