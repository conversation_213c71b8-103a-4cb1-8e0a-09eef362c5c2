package com.shuidihuzhu.cf.dao.crowdfunding.complaint;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * complaint_verify_result
 * <AUTHOR>
@Data
public class ComplaintVerifyResultDO implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 业务id
     */
    private Long bizId;

    /**
     * 业务type ComplaintBizTypeEnum枚举映射
     */
    private Integer bizType;

    /**
     * 是否审核 1是0否
     */
    private Integer isCheck;

    /**
     * 是否删除 0存在1删除
     */
    private Integer isDelete;

    /**
     * 是否可用 1是0否
     */
    private Integer isAvailable;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}