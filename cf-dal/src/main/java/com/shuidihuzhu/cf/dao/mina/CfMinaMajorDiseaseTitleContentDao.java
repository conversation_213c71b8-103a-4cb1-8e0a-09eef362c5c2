package com.shuidihuzhu.cf.dao.mina;

import com.shuidihuzhu.cf.model.mina.CfMinaMajorDiseaseTitleContent;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * Created by ahrievil on 2017/7/5.
 */
@DataSource(DS.CF)
public interface CfMinaMajorDiseaseTitleContentDao {
    @DataSource(DS.CF_SLAVE)
    List<CfMinaMajorDiseaseTitleContent> selectByMenuId(@Param("anchorId") int anchorId, @Param("size") int size);

    @DataSource(DS.CF_SLAVE)
    int selectIdByMenuId(@Param("menuId") int menuId);
    @DataSource(DS.CF_SLAVE)
    String selectByMenuIdAndOrder(@Param("menuId") int menuId, @Param("titleOrder") int titleOrder);
    @DataSource(DS.CF_SLAVE)
    List<CfMinaMajorDiseaseTitleContent> selectByMenuIdList(@Param("set") Set<Integer> set);
    int insert(CfMinaMajorDiseaseTitleContent cfMinaMajorDiseaseTitleContent);
    int insertList(@Param("list") List<CfMinaMajorDiseaseTitleContent> list);
}
