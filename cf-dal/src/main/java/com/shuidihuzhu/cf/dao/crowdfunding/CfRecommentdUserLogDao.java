package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfRecommentdUserLog;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

@DataSource(DS.CF)
public interface CfRecommentdUserLogDao {

	int insert(CfRecommentdUserLog record);

	@DataSource(DS.CF_SLAVE)
	CfRecommentdUserLog selectByUniqUnionKey(@Param("selfTag") String selfTag, @Param("fromInfoId") String fromInfoId,
	                                         @Param("infoId") String infoId);

	int updateByUniqUnionKey(CfRecommentdUserLog record);
}
