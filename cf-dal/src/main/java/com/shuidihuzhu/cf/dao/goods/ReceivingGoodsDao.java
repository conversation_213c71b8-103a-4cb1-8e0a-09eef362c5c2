package com.shuidihuzhu.cf.dao.goods;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.client.model.ReceivingGoodsDto;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/6/8 3:30 PM
 */
@DataSource(CfDataSource.ADMIN_DB_MASTER)
public interface ReceivingGoodsDao {

    int saveReceivingGoods(ReceivingGoodsDto receivingGoodsDto);

    int queryTotalCount();

    List<ReceivingGoodsDto> queryReceivingByPhoneAndUserId(long userId, String receivingPhone);

    List<ReceivingGoodsDto> queryReceivingGoods(String receivingName, String receivingPhone, String receivingAddress,
                                                Timestamp beginTime, Timestamp endTime, int offset);

    int queryReceivingGoodsCount(String receivingName, String receivingPhone, String receivingAddress);

    List<ReceivingGoodsDto> queryReceivingGoodsByTime(Timestamp beginTime, Timestamp endTime);

}
