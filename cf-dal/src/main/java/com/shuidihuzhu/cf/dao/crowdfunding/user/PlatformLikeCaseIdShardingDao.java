package com.shuidihuzhu.cf.dao.crowdfunding.user;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.model.crowdfunding.PlatformLikeCaseIdSharding;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * Created by dongcf on 2020/10/20
 */
@DataSource(CfDataSource.CF_USER_INFO_SHARDING_CROWDFUNDING)
public interface PlatformLikeCaseIdShardingDao {

    int insert(PlatformLikeCaseIdSharding sharding);

    @DataSource(CfDataSource.CF_USER_INFO_SHARDING_CROWDFUNDING_SLAVE)
    PlatformLikeCaseIdSharding getByUserId(@Param("caseId") int caseId, @Param("userId") long userId);
}
