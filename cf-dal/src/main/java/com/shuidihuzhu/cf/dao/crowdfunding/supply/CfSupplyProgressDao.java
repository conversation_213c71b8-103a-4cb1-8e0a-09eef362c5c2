package com.shuidihuzhu.cf.dao.crowdfunding.supply;

import com.shuidihuzhu.cf.model.CfInfoSupplyProgress;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: fengxuan
 * @create 2020-01-09 21:16
 **/
@DataSource(DS.CF)
public interface CfSupplyProgressDao {

    @DataSource(DS.CF_SLAVE)
    CfInfoSupplyProgress getById(@Param("id")long id);


    List<CfInfoSupplyProgress> getByActionId(@Param("progressActionId") long progressActionId);

}
