package com.shuidihuzhu.cf.dao.topList.dailyActivity;

import com.shuidihuzhu.cf.model.topList.DailyActivityTopList;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;
import org.springframework.context.annotation.Bean;

@DataSource("cfUserDataSource")
public interface DailyActivityTopListDao {

    int insertOrUpdate(DailyActivityTopList.TopListSummary param);

    @DataSource("cfUserDataSourceSlave")
    DailyActivityTopList.TopListSummary selectByType(@Param("activityPeriod")int activityPeriod);
}
