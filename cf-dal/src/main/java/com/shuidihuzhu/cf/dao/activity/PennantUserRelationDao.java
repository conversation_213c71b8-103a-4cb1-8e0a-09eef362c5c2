package com.shuidihuzhu.cf.dao.activity;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.model.activity.PennantUserRelation;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by dongcf on 2020/3/16
 */
@DataSource(CfDataSource.DONATION_BIGTABLE_MASTER)
public interface PennantUserRelationDao {

    int insert(PennantUserRelation pennantUserRelation);

    @DataSource(CfDataSource.DONATION_BIGTABLE_SLAVE_1)
    List<PennantUserRelation> findByInfoIdAndAwardType(@Param("userId") Long userId, @Param("infoId") String infoId, @Param("awardType") Integer awardType);

    @DataSource(CfDataSource.DONATION_BIGTABLE_SLAVE_1)
    List<PennantUserRelation> findByUserId(@Param("userId") Long userId);

    @DataSource(CfDataSource.DONATION_BIGTABLE_SLAVE_1)
    PennantUserRelation getByUserIdAndPayUid(@Param("userId") Long userId, @Param("payUid") String payUid);
}
