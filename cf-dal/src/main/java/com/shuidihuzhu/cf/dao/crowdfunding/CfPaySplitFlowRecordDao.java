package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfPaySplitFlowRecord;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource("shardingCfFinanceMaster")
public interface CfPaySplitFlowRecordDao {

	int save(CfPaySplitFlowRecord cfPaySplitFlowRecord);

	List<CfPaySplitFlowRecord> findByUserIds(List<Long> userIds);

	CfPaySplitFlowRecord findLastByUserId(@Param("userId") long userId);

}
