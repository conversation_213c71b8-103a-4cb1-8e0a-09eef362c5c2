package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.fulllink.measurement.annotation.Operate;
import com.shuidihuzhu.cf.fulllink.measurement.utils.OperateType;
import com.shuidihuzhu.cf.model.crowdfunding.CfUserBaseStatDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @time 2019/2/20 下午7:21
 * @desc
 */
@DataSource(CfDataSource.CROWDFUNDING_USER_MANAGER)
public interface CfUserBaseStatDao {
    int insert(@Param("baseStatDO")CfUserBaseStatDO baseStatDO, @Param("tableSuffix")String tableSuffix);

    @Operate(OperateType.READ)
    @DataSource(CfDataSource.CROWDFUNDING_USER_Slave)
    CfUserBaseStatDO selectByUser(@Param("userId") long userId ,@Param("tableSuffix")String tableSuffix);
    int updateUserShareInfo(@Param("userId") long userId, @Param("shareCaseIncr") int shareCaseIncr,@Param("tableSuffix")String tableSuffix);
    int updateDonateStatInfo(@Param("userId") long userId, @Param("donateCaseIncr") int donateCaseIncr, @Param("donateAmountIncr") int donateAmountIncr,
                             @Param("tableSuffix")String tableSuffix);
}
