package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.fulllink.measurement.annotation.Operate;
import com.shuidihuzhu.cf.fulllink.measurement.utils.OperateType;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoStatus;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@DataSource(DS.CF)
public interface CrowdfundingInfoStatusDao {

	int add(CrowdfundingInfoStatus crowdfundingInfoStatus);

	int updateByType(@Param("infoUuid") String infoUuid, @Param("type") int type, @Param("status") int status);

	int update(@Param("infoUuid") String infoUuid, @Param("status") int status);

	@Operate(OperateType.READ)
	@DataSource(DS.CF_SLAVE)
	List<CrowdfundingInfoStatus> getByInfoUuid(@Param("infoUuid") String infoUuid);

	@Operate(OperateType.READ)
	List<CrowdfundingInfoStatus> getByInfoUuidMaster(@Param("infoUuid") String infoUuid);



	@DataSource(DS.CF_SLAVE)
	List<CrowdfundingInfoStatus> getStatusByInfoUuid(@Param("infoUuid") String infoUuid,
	                                                 @Param("typeList") List<Integer> typeList);

	/**
	 * TODO:
	 * 这个先走主，可能要待定，先记下来。给sea后台的场景用主，详情页的场景用从。
	 * @param infoUuid
	 * @param type
	 * @return
	 */
	CrowdfundingInfoStatus getByInfoUuidAndType(@Param("infoUuid") String infoUuid, @Param("type") int type);

	@DataSource(DS.CF_SLAVE)
	List<CrowdfundingInfoStatus> getByInfoUuids(@Param("infoUuids") List<String> infoUuids);

    int updateByTypes(@Param("infoUuid") String infoUuid, @Param("status") int status, @Param("set") Set<Integer> set);
}
