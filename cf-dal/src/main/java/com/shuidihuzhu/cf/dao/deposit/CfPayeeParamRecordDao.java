package com.shuidihuzhu.cf.dao.deposit;

import com.shuidihuzhu.cf.model.deposit.CfPayeeParamRecord;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019年12月13日15:37:47
 * <p>
 * C端 收款人填写日志记录信息
 */
@DataSource(DS.CF)
public interface CfPayeeParamRecordDao {
    /**
     * 保存参数信息
     *
     * @param cfPayeeParamRecord
     * @return
     */
    int insertPayeeParamRecord(CfPayeeParamRecord cfPayeeParamRecord);

    /**
     * 根据四要素获取案例最后填写的一条记录
     *
     * @param caseId
     * @param name                  持卡人姓名
     * @param encryptIdCard         加密证件号码
     * @param encryptBankCardNo     银行卡号码
     * @return
     */
    CfPayeeParamRecord getByFourElementsLastOne(@Param("caseId") int caseId, @Param("name") String name,
                                                @Param("encryptIdCard") String encryptIdCard,
                                                @Param("encryptBankCardNo") String encryptBankCardNo);

    List<CfPayeeParamRecord> getCfPayeeParamRecord(@Param("caseId") int caseId);
}
