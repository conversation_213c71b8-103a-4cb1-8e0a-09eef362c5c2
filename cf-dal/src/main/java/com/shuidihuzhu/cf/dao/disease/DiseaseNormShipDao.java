package com.shuidihuzhu.cf.dao.disease;

import com.shuidihuzhu.cf.model.disease.DiseaseKnowledge;
import com.shuidihuzhu.cf.model.disease.DiseaseNormShip;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author：liuchangjun
 * @Date：2021/8/25
 */
@DataSource(DS.CF)
public interface DiseaseNormShipDao {

    DiseaseNormShip getBycaseId(@Param("caseId") int caseId);

    void insertDiseaseNormShip(@Param("infoUuid") String infoUuid,
                               @Param("caseId") int caseId,
                               @Param("diseaseName") String diseaseName,
                               @Param("diseaseNormShip") String diseaseNormShip);
}
