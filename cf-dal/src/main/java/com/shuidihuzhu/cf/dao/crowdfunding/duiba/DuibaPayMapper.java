package com.shuidihuzhu.cf.dao.crowdfunding.duiba;

import com.shuidihuzhu.cf.model.crowdfunding.DuibaPayModel;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * @package: com.shuidihuzhu.cf.dao.crowdfunding.duiba
 * @Author: liujiawei
 * @Date: 2018/9/3  13:41
 */
@DataSource("userPointsDataSource")
public interface DuibaPayMapper {

    int insert(DuibaPayModel duibaPayModel);

    List<DuibaPayModel> selectByUserId(@Param("userId") Long userId);

    DuibaPayModel selectByOrderNum(@Param("orderNum") String orderNum);

    DuibaPayModel selectByTradeNo(@Param("tradeNo") String tradeNo);

    int setPaySuccess(@Param("tradeNo") String tradeNo, @Param("payStatus") int payStatus, @Param("payTime")Date payTime);

    int setPayInfoStauts(@Param("tradeNo") String tradeNo, @Param("payStatus") int payStatus);

    int setOtherConfirm(@Param("tradeNo") String tradeNo, @Param("status") int status);

    int setRefundSuccess(@Param("tradeNo") String tradeNo, @Param("refundStatus") int refundStatus, @Param("refundTime")Date refundTime);

    int setRefundStartStatus(@Param("tradeNo") String tradeNo, @Param("refundStatus") int refundStatus, @Param("refundStartTime")Date refundStartTime);

    int setRefundFailedReason(@Param("tradeNo") String tradeNo, @Param("refundFailedReason") String refundFailedReason);

    int setRefundReason(@Param("tradeNo") String tradeNo, @Param("refundReason") String refundReason);

    int setRefundStatus(@Param("tradeNo") String tradeNo, @Param("refundStatus") int refundStatus);

    int setThirdType(@Param("tradeNo") String tradeNo, @Param("thirdType") int thirdType);
}
