package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.vo.CfFirsApproveMaterialVO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * @Author: duchao
 * @Date: 2018/8/4 下午4:04
 */
@DataSource(DS.CF)
public interface CfFirstApproveMaterialDao {
	int add(CfFirsApproveMaterial cfFirsApproveMaterial);

	@DataSource(DS.CF_SLAVE)
	CfFirsApproveMaterial getByInfoUuid(@Param("infoUuid") String infoUuid);

	@DataSource(DS.CF_SLAVE)
	CfFirsApproveMaterial getByInfoId(@Param("infoId") int infoId);

	int updateStatusByCaseId(@Param("caseId") int caseId, @Param("status") int status);


	@DataSource(DS.CF_SLAVE)
	List<CfFirsApproveMaterial> getByParam(CfFirsApproveMaterial cfFirsApproveMaterial);

	@DataSource(DS.CF_SLAVE)
	List<CfFirsApproveMaterial> getByInfoIds(@Param("infoIds") List<Integer> infoIds);

	int updateAfterRejectForSubmit(CfFirsApproveMaterialVO material);

	@Deprecated
	int updateRejectTypeByInfoId(@Param("infoId") int infoId, @Param("rejectType") int rejectType,
	                             @Param("rejectMessage") String rejectMessage);

	@DataSource(DS.CF_SLAVE)
	CfFirsApproveMaterial getById(@Param("id") int id);

	CfFirsApproveMaterial getLastByUserId(@Param("userId") long userId);


	@DataSource(DS.CF_SLAVE)
	int countWorkOrderByCaseIdAndApproveStatus(@Param("caseId") int caseId, @Param("contentType") int contentType,
	                                           @Param("approveStatus") int approveStatus);

	@DataSource(DS.CF_SLAVE)
	List<CfFirsApproveMaterial> getByUserId(@Param("userId") long userId);


	@DataSource(DS.CF_SLAVE)
	List<CfFirsApproveMaterial> getByPatientCryptoIdCard(@Param("list") List<String> patientCryptoIdCards);

	@DataSource(DS.CF_SLAVE)
	List<CfFirsApproveMaterial> getListByPatientBornCard(@Param("list") List<String> patientBornCards);

	@DataSource(DS.CF_SLAVE)
	List<CfFirsApproveMaterial> findByInfoUuids(@Param("infoUuids") List<String> infoIds);


	int updateUserIdByIds(@Param("ids") List<Integer> ids, @Param("userId") long userId);

	@DataSource(DS.CF_SLAVE)
	List<CfFirsApproveMaterial> getSingleByPatientCryptoIdCard(@Param("patientCryptoIdCard") String patientCryptoIdCard);

	@DataSource(DS.CF_SLAVE)
	List<CfFirsApproveMaterial> getSingleByPatientBornCard(@Param("patientBornCard") String patientBornCard);

	@DataSource(DS.CF_SLAVE)
    List<CfFirsApproveMaterial> getListByCaseIdList(@Param("caseIdList") List<Integer> caseIdList);

	@DataSource(DS.CF_SLAVE)
	List<CfFirsApproveMaterial> getBySelfRealNameAndCreateTime(@Param("selfRealName") String selfRealName, @Param("createTime") Date createTime);
}
