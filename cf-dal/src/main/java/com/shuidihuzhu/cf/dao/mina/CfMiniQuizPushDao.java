package com.shuidihuzhu.cf.dao.mina;

import com.shuidihuzhu.cf.model.mina.CfMiniQuizPush;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by dongcf on 2018/3/9
 */
@DataSource(DS.FRAME)
public interface CfMiniQuizPushDao {

    Integer insert(CfMiniQuizPush cfMinaQuizPush);

    List<CfMiniQuizPush> findByQuizIdWithUserId(@Param("minaQuizId")Integer minaQuizId, @Param("userIds")List<Long> userIds);

}
