package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * Created by lgj on 16/6/21.
 */

@DataSource(CfDataSource.CF_ORDER_NEW_MASTER_DATASOURCE)
public interface CrowdfundingPayRecordNewDao {

//	int addPayRecord(CrowdfundingPayRecord payRecord);
//
//	int updatePayStatus(@Param("payUid") String payUid, @Param("payStatus") Integer payStatus,
//						@Param("callbackTime") Date callbackTime, @Param("realPayAmount") Integer realPayAmount);
//
//	int updateRefundStatus(@Param("payUid") String payUid, @Param("refundStatus") Integer refundStatus,
//						   @Param("refundTime") Date refundTime, @Param("refundAmount") Integer refundAmount,
//						   @Param("refundReason") String refundReason);
//
//	int updateRefundStatusByOrderIds(@Param("refundStatus") int refundStatus, @Param("orderIds") List<Long> orderIds);

}
