package com.shuidihuzhu.cf.dao.crowdfunding.handler;

import com.google.common.base.Preconditions;
import com.shuidihuzhu.cf.enums.crowdfunding.SceneMessageEnum;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.TypeHandler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 * @date 2018-05-11  17:13
 */
public class SceneMessageEnumTypeHandler implements TypeHandler<SceneMessageEnum> {
    @Override
    public void setParameter(PreparedStatement ps, int i, SceneMessageEnum parameter, JdbcType jdbcType) throws SQLException {
        Preconditions.checkNotNull(parameter);
        ps.setInt(i, parameter.getVal());
    }

    @Override
    public SceneMessageEnum getResult(ResultSet rs, String columnName) throws SQLException {
        int val = rs.getInt(columnName);
        return SceneMessageEnum.decode(val);
    }

    @Override
    public SceneMessageEnum getResult(ResultSet rs, int columnIndex) throws SQLException {
        int val = rs.getInt(columnIndex);
        return SceneMessageEnum.decode(val);
    }

    @Override
    public SceneMessageEnum getResult(CallableStatement cs, int columnIndex) throws SQLException {
        int val = cs.getInt(columnIndex);
        return SceneMessageEnum.decode(val);
    }
}
