package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.domain.CfCaseRiskDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-08-04  10:32
 */
@DataSource(DS.CF)
public interface CfCaseRiskDAO {

    int insert(CfCaseRiskDO cfCaseRiskDO);

    /**
     * 更新风险信息
     *
     * @return
     */
    int updateRiskById(CfCaseRiskDO cfCaseRiskDO);

    /**
     * 批量修改状态
     * @param handleStatus
     * @param ids
     * @return
     */
    int updateStatusByIds(@Param("handleStatus")int handleStatus, Collection<Long> ids);

    @DataSource(DS.CF_SLAVE)
    CfCaseRiskDO getByInfoUuid(@Param("infoUuid") String infoUuid,
                               @Param("riskType") int riskType);

    @DataSource(DS.CF_SLAVE)
    List<CfCaseRiskDO> listByCondition(@Param("verified") int verified,
                                       @Param("passedList") List<Integer> passedList,
                                       @Param("riskType") int riskType,
                                       @Param("start") long start,
                                       @Param("size") int size);

    @DataSource(DS.CF_SLAVE)
    List<CfCaseRiskDO> listByConditionAll(@Param("verified") int verified,
                                          @Param("passedList") List<Integer> passedList,
                                          @Param("riskType") int riskType,
                                          @Param("handleStatusList")List<Integer> handleStatusList);

    @DataSource(DS.CF_SLAVE)
    List<CfCaseRiskDO> listByCreateTime(@Param("startTime") Date startTime,
                                        @Param("endTime") Date endTime,
                                        @Param("start")long start,
                                        @Param("size")int size);

}
