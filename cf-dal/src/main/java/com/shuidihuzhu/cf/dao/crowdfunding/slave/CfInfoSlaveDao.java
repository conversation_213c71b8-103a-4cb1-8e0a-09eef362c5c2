package com.shuidihuzhu.cf.dao.crowdfunding.slave;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;

/**
 * Created by wangsf on 17/1/16.
 */
@DataSource(DS.CF_SLAVE)
public interface CfInfoSlaveDao {

    Integer selectCountByMin(@Param("begin") Timestamp begin, @Param("end") Timestamp end);
}
