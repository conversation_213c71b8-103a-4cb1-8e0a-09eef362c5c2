package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfHonestInfo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2019/6/26 下午5:33
 * @desc
 */
@DataSource(DS.CF)
public interface CfHonestDAO {

    int insert(CfHonestInfo honestInfo);

    int updateById(CfHonestInfo honestInfo);

    @DataSource(DS.CF_SLAVE)
    CfHonestInfo query(@Param("caseId") int caseId);

    @DataSource(DS.CF_SLAVE)
    CfHonestInfo queryById(@Param("id") int id);

    @DataSource(DS.CF_SLAVE)
    List<CfHonestInfo> queryByCaseId(@Param("caseId") int caseId);
}
