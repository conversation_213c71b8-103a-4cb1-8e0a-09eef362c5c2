package com.shuidihuzhu.cf.dao.miniprogram;

import com.shuidihuzhu.cf.model.miniprogram.CfKeywordTopicRelation;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

@DataSource(DS.FRAME)
public interface CfKeywordTopicRelationDao {
    int addOne(@Param("cfKeywordTopicRelation") CfKeywordTopicRelation cfKeywordTopicRelation);

//    int deleteById(@Param("id") int id);

    int updateById(@Param("id") int id, @Param("cfKeywordTopicRelation") CfKeywordTopicRelation cfKeywordTopicRelation);

    int updateByDate(@Param("date") Date date, @Param("cfKeywordTopicRelation") CfKeywordTopicRelation cfKeywordTopicRelation);

    int addExpectByDate(@Param("date") Date date);

    int addNotExpectByDate(@Param("date") Date date);

    int addExpectByDateOnly(@Param("date") Date date);

    int addNotExpectByDateOnly(@Param("date") Date date);

    @DataSource(DS.FRAME_SLAVE)
    CfKeywordTopicRelation selectByDate(@Param("date") Date date);

//    @DataSource(DS.FRAME_SLAVE)
//    List<CfKeywordTopicRelation> listByDate(@Param("start") Date start, @Param("end") Date end);
//
//    @DataSource(DS.FRAME_SLAVE)
//    List<CfKeywordTopicRelation> listByKeywordId(@Param("keywordId") int keywordId);
}
