package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareSourceUserRecord;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;



/**
 * @author: wanghui
 * @time: 2019/7/8 3:47 PM
 * @description:
 * @param:  * @param null :
 * @return:  * @return : null
 */
@DataSource(CfDataSource.SHUIDI_CROWDFUNDING_SHARE_SLAVE)
public interface CfInfoShareRecordSourceUserShardingDao {

	@DataSource(CfDataSource.SHUIDI_CROWDFUNDING_SHARE_MASTER)
	int add(@Param("record") CfInfoShareSourceUserRecord cfInfoShareSourceUserRecord, @Param("sharding") String sharding);

}
