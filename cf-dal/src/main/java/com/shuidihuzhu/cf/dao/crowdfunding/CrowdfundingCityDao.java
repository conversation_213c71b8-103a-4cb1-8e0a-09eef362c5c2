package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCity;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.CF_SLAVE)
public interface CrowdfundingCityDao {

    List<CrowdfundingCity> getAll();

    List<CrowdfundingCity> getByIds(@Param("ids") List<Integer> ids);

    List<CrowdfundingCity> getByParentId(@Param("parentId") int parentId);

    CrowdfundingCity getByCode(@Param("code") String code);

    List<CrowdfundingCity> getListByCodes(@Param("codes") List<String> codes);

    CrowdfundingCity getById(@Param("id") Integer id);

    List<CrowdfundingCity> listByCityName(@Param("cityName") String cityName, @Param("limit") int limit);

    List<CrowdfundingCity> getAllLevel1City();

    List<CrowdfundingCity> getByRealParentId(@Param("realParentId") int realParentId);

    CrowdfundingCity findByIdAndDelete(@Param("id") int id);

    List<CrowdfundingCity> listByCityNameAndLevel(@Param("cityName") String cityName, @Param("levels") List<Integer> levels, @Param("limit") Integer limit);

    List<CrowdfundingCity> getByProvinceAndCityNameByLike(@Param("city") String city, @Param("parentId") int parentId);

    List<CrowdfundingCity> getCityByFetchId(int fetchId, int limit);

}
