package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfFastDrawVersion;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

@DataSource(DS.CF)
public interface CfFastDrawVersionDao {
    int save(CfFastDrawVersion cfFastDrawVersion);

    @DataSource(DS.CF_SLAVE)
    CfFastDrawVersion getByCaseId(@Param("caseId") int caseId);
}
