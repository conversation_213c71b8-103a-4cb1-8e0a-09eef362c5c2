package com.shuidihuzhu.cf.dao.topList;

import com.shuidihuzhu.cf.model.topList.CfTopListSummary;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;
import org.springframework.context.annotation.Bean;

import java.util.Date;
import java.util.List;

@DataSource("cfUserDataSource")
public interface CfTopListSummaryDAO {

    CfTopListSummary selectSummaryByInfoUuid(@Param("infoUuid") String infoUuid);

//    @DataSource("cfUserDataSourceSlave")
    CfTopListSummary selectSummaryByCaseId(@Param("caseId") int caseId);

//    @DataSource("cfUserDataSourceSlave")
    List<CfTopListSummary> selectSummaryByCaseIds(@Param("caseIds") List<Integer> caseIs);

    int insertSummary(CfTopListSummary summary);

    int updateAllAndFriendTopList(CfTopListSummary summary);

    int updatePrizeUserIds(@Param("caseId") int caseId, @Param("canPrizeUsers") String canPrizeUsers);

    @DataSource(DS.CF_SLAVE)
    Date selectFirstInitialPassTime(@Param("caseId") int caseId, @Param("materialId") int materialId,
                                    @Param("handleType") int handleType);

}
