package com.shuidihuzhu.cf.dao.miniprogram;

import com.shuidihuzhu.cf.model.miniprogram.CfTopic;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.FRAME)
public interface CfTopicDao {
    int addOne(@Param("cfTopic") CfTopic cfTopic);

    int deleteById(@Param("id") int id);

    int updateById(@Param("id") int id, @Param("cfTopic") CfTopic cfTopic);

    @DataSource(DS.FRAME_SLAVE)
    CfTopic selectById(@Param("id") int id);

    @DataSource(DS.FRAME_SLAVE)
    List<CfTopic> selectByPhaseId(@Param("phaseId") int phaseId);

    @DataSource(DS.FRAME_SLAVE)
    List<CfTopic> listByPhaseIds(@Param("phaseIds") List<Integer> phaseIds);

    @DataSource(DS.FRAME_SLAVE)
    List<CfTopic> listByPhaseId(@Param("phaseId") int phaseId);

    @DataSource(DS.FRAME_SLAVE)
    List<CfTopic> listByPhaseIdLimit(@Param("phaseIds") List<Integer> phaseId, @Param("limit") int limit);

    @DataSource(DS.FRAME_SLAVE)
    List<CfTopic> listByIds(@Param("topicIds") List<Integer> topicIds);
}
