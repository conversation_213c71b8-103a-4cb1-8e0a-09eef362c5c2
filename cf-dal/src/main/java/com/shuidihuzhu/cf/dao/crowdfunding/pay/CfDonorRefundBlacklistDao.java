package com.shuidihuzhu.cf.dao.crowdfunding.pay;

import com.shuidihuzhu.cf.model.crowdfunding.pay.CfDonorRefundBlacklist;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2019/3/18 16:41
 */
@DataSource(DS.CF_SLAVE)
public interface CfDonorRefundBlacklistDao {
    CfDonorRefundBlacklist getByUserId(long userId);
}
