package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfPatientEvaluationRecord;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.CF)
public interface CfPatientEvaluationRecordDao {

    int insertSelective(CfPatientEvaluationRecord record);


    int updateByPrimaryKeySelective(CfPatientEvaluationRecord record);

    @DataSource(DS.CF_SLAVE)
    CfPatientEvaluationRecord selectRecordByUserIdAndCaseId(@Param("userId")long userId,@Param("caseId")long caseId);

    @DataSource(DS.CF_SLAVE)
    List<CfPatientEvaluationRecord> selectRecordByCaseId(long caseId);

}