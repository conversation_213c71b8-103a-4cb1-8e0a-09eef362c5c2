package com.shuidihuzhu.cf.dao.type.handler;

import com.google.common.base.Preconditions;
import com.shuidihuzhu.cf.enums.activity.Activity111CaseTypeEnum;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.TypeHandler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Created by sven on 2020/4/4.
 *
 * <AUTHOR>
 */
public class Activity111CaseTypeEnumTypeHandler implements TypeHandler<Activity111CaseTypeEnum> {

    @Override
    public void setParameter(PreparedStatement ps, int i, Activity111CaseTypeEnum parameter, JdbcType jdbcType) throws SQLException {
        Preconditions.checkNotNull(parameter);
        ps.setInt(i, parameter.getCode());
    }

    @Override
    public Activity111CaseTypeEnum getResult(ResultSet rs, String columnName) throws SQLException {
        int type = rs.getInt(columnName);
        return Activity111CaseTypeEnum.parse(type);
    }

    @Override
    public Activity111CaseTypeEnum getResult(ResultSet rs, int columnIndex) throws SQLException {
        int type = rs.getInt(columnIndex);
        return Activity111CaseTypeEnum.parse(type);
    }

    @Override
    public Activity111CaseTypeEnum getResult(CallableStatement cs, int columnIndex) throws SQLException {
        int type = cs.getInt(columnIndex);
        return Activity111CaseTypeEnum.parse(type);
    }
}
