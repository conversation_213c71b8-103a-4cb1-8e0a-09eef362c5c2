package com.shuidihuzhu.cf.dao.rule;

import com.shuidihuzhu.cf.model.rule.RuleCondition;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by sven on 18/8/7.
 *
 * <AUTHOR>
 */
@DataSource(DS.CF)
public interface IRuleConditionDAO {

    /**
     * 根据规则集id来获取规则
     *
     * @param collectionId
     * @return
     */
    @DataSource(DS.CF_SLAVE)
    List<RuleCondition> getByCollectionId(@Param("collectionId") long collectionId);

    @DataSource(DS.CF_SLAVE)
    RuleCondition getById(@Param("id") long id);

    /**
     * 插入一条规则
     * @return
     */
    int insert(RuleCondition ruleCondition);

    /**
     * 修改一条规则的value和操作符
     *
     * @param id
     * @param fieldName
     * @param operator
     * @param targetValue
     * @return
     */
    int updateRule(@Param("id")long id,
                   @Param("filedName") String fieldName,
                   @Param("operator") int operator,
                   @Param("targetValue") String targetValue);

    int update(RuleCondition condition);

    /**
     * 修改数据的外置数据源
     *
     * @param id
     * @param fieldName
     * @param operator
     * @param externDataSource
     * @return
     */
    int updateRuleExtDataSouce(@Param("id")long id,
                   @Param("filedName") String fieldName,
                   @Param("operator") int operator,
                   @Param("externDataSource") String externDataSource);
}
