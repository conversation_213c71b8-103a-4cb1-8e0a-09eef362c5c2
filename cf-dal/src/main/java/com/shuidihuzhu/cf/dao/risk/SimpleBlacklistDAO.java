package com.shuidihuzhu.cf.dao.risk;

import com.shuidihuzhu.cf.model.risk.SimpleBlacklistDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@DataSource(DS.CF)
public interface SimpleBlacklistDAO {

    int insert(SimpleBlacklistDO simpleBlackListDO);

    @DataSource(DS.CF_SLAVE)
    SimpleBlacklistDO getByCaseId(@Param("actionType") int actionType, @Param("caseId") int caseId);

    int removeByCaseId(@Param("actionType") int actionType, @Param("caseId") int caseId);

    int removeById(@Param("id") long id);
}
