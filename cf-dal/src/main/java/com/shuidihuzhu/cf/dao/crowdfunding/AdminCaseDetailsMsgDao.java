package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.client.cf.admin.model.AdminCaseDetailsMsg;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;


/**
 * <AUTHOR>
 * @date 2020/11/25  2:07 下午
 */
@DataSource(DS.CF)
public interface AdminCaseDetailsMsgDao {
    @DataSource(DS.CF_SLAVE)
    AdminCaseDetailsMsg getByInfoUuid(@Param("infoUuid") String infoUuid);

    @DataSource(DS.CF_SLAVE)
    AdminCaseDetailsMsg getByCaseId(@Param("caseId") int caseId);

    @DataSource(DS.CF_SLAVE)
    AdminCaseDetailsMsg getCaseDetailsMsgByInfoUuid(@Param("infoUuid") String infoUuid);

    int updateDiseaseName(@Param("id") long id, @Param("caseLabel") String caseLabel);
}
