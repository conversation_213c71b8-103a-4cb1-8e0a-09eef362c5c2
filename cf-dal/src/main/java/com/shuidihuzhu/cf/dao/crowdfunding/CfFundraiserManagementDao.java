package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfCapitalTotal;
import com.shuidihuzhu.cf.model.crowdfunding.CfFundraiserManagement;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@DataSource(DS.CF)
public interface CfFundraiserManagementDao {

    @DataSource(DS.CF_SLAVE)
    CfFundraiserManagement getCfFundraiserManagementList(@Param("date") String date, @Param("infoUuid") String infoUuid);

    int insert(CfFundraiserManagement cfFundraiserManagement);

    int update(CfFundraiserManagement cfFundraiserManagement);

    @DataSource(DS.CF_SLAVE)
    List<CfFundraiserManagement> getLastSevenDaysCfFundraiserManagementList(@Param("infoUuid") String infoUuid, @Param("dates") List<String> dates);

    @DataSource(DS.CF_SLAVE)
    List<CfFundraiserManagement> getCfFundraiserManagementListByInfoUuid(@Param("infoUuid") String infoUuid);

}
