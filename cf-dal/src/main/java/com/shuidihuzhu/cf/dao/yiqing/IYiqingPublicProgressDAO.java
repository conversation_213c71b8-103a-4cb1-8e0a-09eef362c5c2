package com.shuidihuzhu.cf.dao.yiqing;

import com.shuidihuzhu.cf.domain.yiqing.YiqingPublicProgress;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

import java.util.List;

/**
 * Created by sven on 2020/1/26.
 *
 * <AUTHOR>
 */
@DataSource(DS.CF)
public interface IYiqingPublicProgressDAO {

    @DataSource(DS.CF_SLAVE)
    YiqingPublicProgress getLatestOne();

    @DataSource(DS.CF_SLAVE)
    List<YiqingPublicProgress> getAll();

    int update(YiqingPublicProgress yiqingPublicProgress);

    int insert(YiqingPublicProgress yiqingPublicProgress);

    int delete(long id);
}
