package com.shuidihuzhu.cf.dao.starrocks;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.model.starrocks.RptCfCaseFriendShareDo;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/18  14:43
 */
@DataSource(CfDataSource.STAR_ROCKS_DATASOURCE)
public interface CfCaseFriendShareDao {

    List<RptCfCaseFriendShareDo> listByInfoId(@Param("infoId") Long infoId);

}
