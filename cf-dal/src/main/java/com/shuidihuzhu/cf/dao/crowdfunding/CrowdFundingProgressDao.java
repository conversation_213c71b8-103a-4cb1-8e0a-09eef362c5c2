package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingProgress;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by chao on 16/7/12.
 */

@DataSource(DS.CF)
public interface CrowdFundingProgressDao {

	/**
	 * 资金用途审核以后不使用该接口
	 * 已新建表admin_crowdfunding_fund_use_audit用来存储 资金进展
	 * 以后使用CrowdfundingFundUseAuditProgressBiz insertFundUseAuditProgress接口来存储
	 * @param progress
	 * @return
	 */
	int add(CrowdFundingProgress progress);

	@DataSource(DS.CF_SLAVE)
	List<CrowdFundingProgress> getActivityProgress(@Param("activityId") Integer activityId,
			@Param("types") List<Integer> types, @Param("offset") Integer offset, @Param("limit") Integer limit);

	@DataSource(DS.CF_SLAVE)
	List<CrowdFundingProgress> queryProgressByUserId(@Param("userId") Long userId);

	@DataSource(DS.CF_SLAVE)
	CrowdFundingProgress getActivityProgressById(@Param("id") Integer id);

	@DataSource(DS.CF_SLAVE)
	CrowdFundingProgress getActivityProgressByIdAndCaseId(@Param("id") Integer id, @Param("caseId") Integer caseId);

	@DataSource(DS.CF_SLAVE)
	List<CrowdFundingProgress> queryByType(@Param("caseId") int caseId, @Param("pType") int pType);

	int updateForDelete(@Param("crowdfundingId") int crowdfundingId, @Param("id") int id);

	int deleteProgress(@Param("crowdfundingId") int crowdfundingId, @Param("progressId") int progressId);

	int updateType(@Param("id") int id, @Param("fromType") int fromType, @Param("toType") int toType);

	int updateImageUrls(@Param("id")int id, @Param("imageUrls")String imageUrls);

	@DataSource(DS.CF_SLAVE)
	CrowdFundingProgress getFirstProgress(@Param("crowdfundingId") int id);

	List<CrowdFundingProgress> getListByType(@Param("caseId") int caseId, @Param("type") int type);

	@DataSource(DS.CF_SLAVE)
	List<CrowdFundingProgress> queryByCaseIdAndTypes(@Param("caseId") int caseId, @Param("pTypeList") List<Integer> pTypeList);
}
