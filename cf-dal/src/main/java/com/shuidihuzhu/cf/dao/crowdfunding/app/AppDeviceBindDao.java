package com.shuidihuzhu.cf.dao.crowdfunding.app;

import com.shuidihuzhu.cf.model.crowdfunding.app.vo.AppDeviceBindVo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2019-06-24
 */
@DataSource(DS.CF)
public interface AppDeviceBindDao {

    /**
     * 根据app唯一ID获取设备绑定关系
     * @param appUnqiueId
     * @return
     */
    @DataSource(DS.CF_SLAVE)
    AppDeviceBindVo getAppDeviceBindVoByAppUnqiueId(@Param("appUnqiueId") String appUnqiueId);

    int update(AppDeviceBindVo appDeviceBindVo);

    int insert(AppDeviceBindVo appDeviceBindVo);
}
