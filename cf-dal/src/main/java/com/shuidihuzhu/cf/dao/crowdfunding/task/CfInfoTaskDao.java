package com.shuidihuzhu.cf.dao.crowdfunding.task;

import com.shuidihuzhu.cf.model.task.CfInfoTask;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: lixuan
 * @date: 2018/3/16 18:00
 */
@DataSource(DS.CF)
public interface CfInfoTaskDao {

    int save(CfInfoTask cfInfoTask);

    int saveBatch(@Param("cfInfoTaskList") List<CfInfoTask> cfInfoTaskList);

    @DataSource(DS.CF_SLAVE)
    List<CfInfoTask> getByInfoUuidAndDate(@Param("infoUuid") String infoUuid, @Param("date") String date);

    @DataSource(DS.CF_SLAVE)
    CfInfoTask getByInfoUuidAndDateAndTaskRuleId(@Param("infoUuid") String infoUuid, @Param("date") String date,
                                                 @Param("taskRuleId") long taskRuleId);

    @DataSource(DS.CF_SLAVE)
    List<CfInfoTask> getByInfoUuidAndTaskRuleId(@Param("infoUuid") String infoUuid, @Param("taskRuleId") long taskRuleId);

    int updateTimesOnce(@Param("infoUuid") String infoUuid, @Param("date") String date, @Param("taskRuleId") long taskRuleId);

    int updateTimes(@Param("infoUuid") String infoUuid, @Param("date") String date, @Param("taskRuleId") long taskRuleId,
                    @Param("times") int times);

    int deleteTaskByInfoUuidAndRuleId(@Param("infoUuid") String infoUuid, @Param("taskRuleId") long taskRuleId);

    int updateFinish(@Param("id") long id);

}
