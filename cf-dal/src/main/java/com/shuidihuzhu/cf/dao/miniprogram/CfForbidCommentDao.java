package com.shuidihuzhu.cf.dao.miniprogram;

import com.shuidihuzhu.cf.model.miniprogram.CfForbidComment;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.FRAME)
public interface CfForbidCommentDao {
    int addOne(@Param("cfForbidComment") CfForbidComment cfForbidComment);

    int deleteById(@Param("id") int id);

    @DataSource(DS.FRAME_SLAVE)
    CfForbidComment selectById(@Param("id") int id);

    @DataSource(DS.FRAME_SLAVE)
    List<CfForbidComment> listByUserId(@Param("userId") long userId);

    @DataSource(DS.FRAME_SLAVE)
    List<CfForbidComment> listByTopicId(@Param("topicId") int topicId);
}
