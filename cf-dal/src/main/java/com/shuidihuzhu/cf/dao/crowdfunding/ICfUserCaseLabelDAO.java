package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfUserCaseLabelDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @time 2019/3/21 下午5:42
 * @desc
 */
@DataSource(DS.CF)
public interface ICfUserCaseLabelDAO {

    int insertCaseLabel(CfUserCaseLabelDO cfUserCaseLabelDO);

    int batchInsertCaseLabel(@Param("lists") List<CfUserCaseLabelDO> lists);

    @DataSource(DS.CF_SLAVE)
    List<CfUserCaseLabelDO> queryCaseLabel(@Param("userId") long userId);

    @DataSource(DS.CF_SLAVE)
    CfUserCaseLabelDO queryCaseLabelByUserAndCase(@Param("userId") long userId, @Param("caseId") int caseId);

    int updateEnd(@Param("userId") long userId, @Param("caseId") int caseId);

    int updateEndTimeAndStatus(@Param("userId") long userId, @Param("caseId") int caseId, @Param("endTime")Date endTime, @Param("approveStatus") int approveStatus, @Param("approveTime") Date approveTime);

    int updateRejectAndStatus(@Param("userId") long userId, @Param("caseId") int caseId, @Param("approveStatus") int approveStatus);
}
