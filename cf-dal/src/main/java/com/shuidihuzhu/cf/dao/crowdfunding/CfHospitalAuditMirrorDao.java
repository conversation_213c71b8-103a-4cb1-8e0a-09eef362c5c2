package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfHospitalAuditMirror;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/11/16.
 */
@DataSource(DS.CF)
public interface CfHospitalAuditMirrorDao {

    int save(CfHospitalAuditMirror cfHospitalAuditMirror);

    @DataSource(DS.CF_SLAVE)
    CfHospitalAuditMirror getLastOneByType(@Param("operatingRecordId") long operatingRecordId,
                                           @Param("type") int type);
}
