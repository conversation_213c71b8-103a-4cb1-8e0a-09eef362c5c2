package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfWxSubscribeReply;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by wangsf on 17/3/10.
 */

@DataSource(DS.CF)
public interface CfWxSubscribeReplyDao {

	/**
	 *
	 * 获取回复消息
 	 *
	 * @param eventKey
	 *
	 * @return
	 */
	@DataSource(DS.CF_SLAVE)
	CfWxSubscribeReply getByEventKey(@Param("eventKey") String eventKey, @Param("thirdType") int thirdType);

	/**
	 * 根据处理方式获取可回复的消息
	 *
	 * @param method
	 *
	 * @return
	 */
	@DataSource(DS.CF_SLAVE)
	List<CfWxSubscribeReply> getByMethod(@Param("method") int method);
}
