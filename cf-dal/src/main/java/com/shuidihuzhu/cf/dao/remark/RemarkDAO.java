package com.shuidihuzhu.cf.dao.remark;

import com.shuidihuzhu.cf.domain.RemarkDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@DataSource(DS.CF)
public interface RemarkDAO {

    @DataSource(DS.CF)
    int insert(RemarkDO remarkDO);

    @DataSource(DS.CF_SLAVE_2)
    List<RemarkDO> listByCaseId(@Param("caseId") long caseId);

    @DataSource(DS.CF_SLAVE_2)
    List<RemarkDO> listByCaseIdAndRemarkTypes(@Param("caseId") long caseId,
                                              @Param("remarkTypes") List<Integer> remarkTypes);

    @DataSource(DS.CF_SLAVE_2)
    RemarkDO getLastByCaseIdAndRemarkTypes(@Param("caseId") long caseId,
                                              @Param("remarkTypes") List<Integer> remarkTypes);

    @DataSource(DS.CF_SLAVE)
    RemarkDO getById(@Param("id") long id);
}
