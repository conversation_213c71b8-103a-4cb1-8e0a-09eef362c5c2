package com.shuidihuzhu.cf.dao.mina;

import com.shuidihuzhu.cf.model.mina.CfMinaQuizQuestion;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by dongcf on 2017/12/12
 */
@DataSource(DS.FRAME)
public interface CfMinaQuizQuestionDao {

    @DataSource(DS.FRAME_SLAVE)
    List<CfMinaQuizQuestion> findByMinaQuizId(@Param("minaQuizId") Integer minaQuizId);

    Integer insert(CfMinaQuizQuestion cfMinaQuizQuestion);

    Integer updateDeleteByMinaQuizId(@Param("isDelete") Integer isDelete, @Param("minaQuizId") Integer minaQuizId);
}
