package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.EvaluateLabelDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.CF_SLAVE)
public interface EvaluateLabelDao {

    EvaluateLabelDO getSingleByAge(@Param("age") int age);

    List<EvaluateLabelDO> getListByAge(@Param("age") int age);

    List<EvaluateLabelDO> getByPriority(@Param("priority") int priority);

    int getLabelId(@Param("label") String label);

    EvaluateLabelDO getByLabelId(@Param("labelId") int labelId);

    List<EvaluateLabelDO> getByCharacterId(@Param("characterId") int characterId);

    EvaluateLabelDO getByCharacterId1(@Param("characterId") int characterId);

    List<EvaluateLabelDO> getAll();

}
