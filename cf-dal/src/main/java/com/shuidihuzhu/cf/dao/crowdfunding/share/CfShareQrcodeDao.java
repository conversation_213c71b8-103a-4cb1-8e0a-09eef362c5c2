package com.shuidihuzhu.cf.dao.crowdfunding.share;

import com.shuidihuzhu.cf.model.crowdfunding.share.CfShareQrcode;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * Created by wangsf on 18/3/13.
 */
@DataSource(DS.CF)
public interface CfShareQrcodeDao {

	@DataSource(DS.CF_SLAVE)
	CfShareQrcode getByInfoUuidAndChannel(@Param("infoUuid") String infoUuid, @Param("channel") String channel);

	int insert(CfShareQrcode cfShareQrcode);
}
