package com.shuidihuzhu.cf.dao.crowdfunding.slave;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingProgress;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;

/**
 * Created by chao on 16/7/12.
 */

@DataSource(DS.CF_SLAVE)
public interface CrowdFundingProgressSlaveDao {

    Integer selectCountByMin(@Param("begin") Timestamp begin, @Param("end") Timestamp end);

    List<CrowdFundingProgress> selectById(@Param("ids")List<Long> ids);

}
