package com.shuidihuzhu.cf.dao.activity;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.model.activity.PennantInfo;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

import java.util.List;

/**
 * Created by dongcf on 2020/3/16
 */
@DataSource(CfDataSource.DONATION_BIGTABLE_MASTER)
public interface PennantInfoDao {

    @DataSource(CfDataSource.DONATION_BIGTABLE_MASTER)
    List<PennantInfo> findAll();
}
