package com.shuidihuzhu.cf.dao.mina;


import com.shuidihuzhu.cf.model.mina.CfMinaQuizShare;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * Created by dongcf on 2018/3/18
 */
@DataSource(DS.FRAME)
public interface CfMinaQuizShareDao {

    Integer insert(CfMinaQuizShare cfMinaQuizShare);

    @DataSource(DS.FRAME_SLAVE)
    CfMinaQuizShare getByUserIdAndQuizId(@Param("minaQuizId")Integer minaQuizId,@Param("userId")long userId);

    @DataSource(DS.FRAME_SLAVE)
    CfMinaQuizShare getById(@Param("id")Integer id);
}
