package com.shuidihuzhu.cf.dao.activity;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@DataSource(DS.CF)
public interface ActivitySubsidyUserCaseShareCountDAO {

    int save(@Param("userId") long userId, @Param("caseId") int caseId, @Param("shareCount") int shareCount);

    @DataSource(DS.CF_SLAVE)
    Integer getCount(@Param("userId") long userId, @Param("caseId") int caseId);

}
