package com.shuidihuzhu.cf.dao.crowdfunding.comment;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.domain.comment.CrowdfundingCommentIdToCrowdfundingIdDO;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(CfDataSource.SHUIDI_CF_COMMENT_SHARE)
public interface CrowdfundingCommentIdToCrowdfundingIdDao {
    int deleteByCommentId(Long commentId);

    int insert(CrowdfundingCommentIdToCrowdfundingIdDO record);

    @DataSource(CfDataSource.SHUIDI_CF_COMMENT_SLAVE_SHARE)
    CrowdfundingCommentIdToCrowdfundingIdDO selectByCommentId(Long commentId);

    @DataSource(CfDataSource.SHUIDI_CF_COMMENT_SLAVE_SHARE)
    List<CrowdfundingCommentIdToCrowdfundingIdDO> selectByCommentIdsNoCareDeleted(@Param("commentIds") List<Long> commentIds);
}