package com.shuidihuzhu.cf.dao.crowdfunding;


import com.shuidihuzhu.cf.model.crowdfunding.CfStatData;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@DataSource(DS.CF)
public interface CfStatDataDao {


    int insertOrUpdate(CfStatData cfStatData);

    @DataSource(DS.CF_SLAVE)
    List<CfStatData> getCfStatDataByUpdateTime(@Param("batchId")int batchId,@Param("limit") int limit);

    @DataSource(DS.CF_SLAVE)
    List<CfStatData> getCfStatDataById(@Param("id")long id ,@Param("batchId") int batchId,@Param("limit") int limit);

    @DataSource(DS.CF_SLAVE)
    List<CfStatData> getCfStatDataBy24H(@Param("begin") int begin ,@Param("end") int end, @Param("caseId")long caseId);

    @DataSource(DS.CF_SLAVE)
    CfStatData getCfStatDataBycaseId(@Param("caseId") long caseId, @Param("batchId") int batchId);

    @DataSource(DS.CF_SLAVE)
    List<CfStatData> getCfStatDataByDate(@Param("begin") int begin ,@Param("end") int end, @Param("caseIds") List<Long> caseIds);


}
