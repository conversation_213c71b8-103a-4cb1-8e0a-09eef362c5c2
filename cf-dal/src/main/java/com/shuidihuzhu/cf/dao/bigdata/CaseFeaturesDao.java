package com.shuidihuzhu.cf.dao.bigdata;


import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.model.bigdata.CfCaseFeaturesDo;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/26  20:54
 */
@DataSource(CfDataSource.CF_STAR_ROCKS_DATASOURCE)
public interface CaseFeaturesDao {

    CfCaseFeaturesDo getByInfoId(@Param("infoId") Long infoId, @Param("dt") String dt);

    List<CfCaseFeaturesDo> listByInfoIds(@Param("infoIds") List<Long> infoIds, @Param("dt") String dt);

    List<CfCaseFeaturesDo> listBigSpaceDonateByVolunteerCode(@Param("volunteerCode") String volunteerCode, @Param("dt") String dt);

}
