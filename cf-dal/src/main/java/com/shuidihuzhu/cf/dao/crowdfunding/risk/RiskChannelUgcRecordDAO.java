package com.shuidihuzhu.cf.dao.crowdfunding.risk;

import com.shuidihuzhu.cf.domain.risk.RiskChannelUgcRecordDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2018-08-30  13:59
 */
@DataSource(DS.CF)
public interface RiskChannelUgcRecordDAO {

    @DataSource(DS.CF_SLAVE)
    RiskChannelUgcRecordDO getByCodeAndBizId(@Param("code")int code, @Param("bizId")long bizId);

    int insert(RiskChannelUgcRecordDO riskChanncelUgcRecordDO);

}
