package com.shuidihuzhu.cf.dao.crowdfunding;


import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingSuccessCase;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * Created by lenn on 17/6/15.
 */
@DataSource(DS.CF)
public interface CrowdfundingSuccessCaseDao {

	@DataSource(DS.CF_SLAVE)
	List<CrowdfundingSuccessCase> getIdsByType(@Param("limit") int limit, @Param("type") int type);

	@DataSource(DS.CF_SLAVE)
	List<String> getIds(@Param("limit") int limit);

	@DataSource(DS.CF_SLAVE)
	List<String> getIdsAfterTime(@Param("time") Date time);

	int addSuccessCase(CrowdfundingSuccessCase successCase);

	@DataSource(DS.CF_SLAVE)
	CrowdfundingSuccessCase getByShortId(@Param("shortId") String shortId);
}
