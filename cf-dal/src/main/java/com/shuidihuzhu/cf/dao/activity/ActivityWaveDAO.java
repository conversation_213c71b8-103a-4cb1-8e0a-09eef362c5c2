package com.shuidihuzhu.cf.dao.activity;

import com.shuidihuzhu.cf.domain.activity.ActivityWaveDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@DataSource(DS.CF)
public interface ActivityWaveDAO {

    int insert(ActivityWaveDO activityWaveDO);

    @DataSource(DS.CF_SLAVE)
    ActivityWaveDO getWaveById(@Param("id") long id);

    @DataSource(DS.CF_SLAVE)
    ActivityWaveDO getWaveByTypeAndNowTime(@Param("waveType") int waveType);

    @DataSource(DS.CF_SLAVE)
    ActivityWaveDO getWaveByTypeAndNowTimeAndCity(@Param("waveType") int waveType, @Param("cityId") int cityId);

    @DataSource(DS.CF_SLAVE)
    ActivityWaveDO getWaveByCondition(@Param("waveType") int waveType, @Param("joinType") int joinType,
                                      @Param("scope") int scope, @Param("cityId") int cityId,
                                      @Param("activityId") Long activityId);

    @DataSource(DS.CF_SLAVE)
    ActivityWaveDO getWaveByHospital(@Param("waveType") int waveType, @Param("joinType") int joinType,
                                     @Param("scope") int scope, @Param("hospitalCode") String hospitalCode,
                                     @Param("activityId") Long activityId);

    @DataSource(DS.CF_SLAVE)
    List<ActivityWaveDO> getListByCondition(@Param("status") Boolean status, @Param("current") int current, @Param("pageSize") int pageSize);


    @DataSource(DS.CF_SLAVE)
    int count(@Param("status") Boolean status);

    int updatePlace(@Param("id") long id, @Param("currentPlace") int currentPlace);

}
