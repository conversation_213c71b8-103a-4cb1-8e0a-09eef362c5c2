package com.shuidihuzhu.cf.dao.miniprogram;

import com.shuidihuzhu.cf.model.miniprogram.CfCommentDynamic;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.FRAME)
public interface CfCommentDynamicDao {
    int addOne(@Param("cfCommentDynamic") CfCommentDynamic cfCommentDynamic);

    int deleteById(@Param("id") long id);

    int deleteByCommentId(@Param("commentId") long commentId);

    int updateById(@Param("id") long id, @Param("cfCommentDynamic") CfCommentDynamic cfCommentDynamic);

    int updatePariseNum(@Param("id") long id);

    int updateUnPariseNum(@Param("id") long id);

    int updateByCommentId(@Param("commentId") long commentId, @Param("cfCommentDynamic") CfCommentDynamic cfCommentDynamic);

    @DataSource(DS.FRAME_SLAVE)
    CfCommentDynamic selectById(@Param("id") long id);

    @DataSource(DS.FRAME_SLAVE)
    CfCommentDynamic selectByCommentId(@Param("commentId") long commentId);

    @DataSource(DS.FRAME_SLAVE)
    List<CfCommentDynamic> listByCommentIds(@Param("commentIds") List<Long> commentIds);

    @DataSource(DS.FRAME_SLAVE)
    List<CfCommentDynamic> listByIdAndStatusLimit(@Param("topicId") int topicId, @Param("topStauts") int topStauts, @Param("limit") int limit);

    @DataSource(DS.FRAME_SLAVE)
    List<CfCommentDynamic> listByIdsAndStatus(@Param("topicIds") List<Integer> topicIds, @Param("topStatus") int topStauts);

    @DataSource(DS.FRAME_SLAVE)
    List<CfCommentDynamic> listByIdAndStatus(@Param("topicId") int topicId, @Param("topStauts") int topStauts);
}
