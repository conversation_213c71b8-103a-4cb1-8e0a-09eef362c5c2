package com.shuidihuzhu.cf.dao.activity;

import com.shuidihuzhu.cf.domain.activity.ActivityKeyAreaDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@DataSource(DS.CF)
public interface ActivityKeyAreaSubsidyCaseDAO {

    int insert(ActivityKeyAreaDO activityKeyAreaDO);

    int setReachedThreshold(@Param("caseId") int caseId, @Param("subsidyType") int subsidyType);

    @DataSource(DS.CF_SLAVE)
    ActivityKeyAreaDO getByCaseId(@Param("caseId") int caseId);

    @DataSource(DS.CF_SLAVE)
    List<Integer> getSameWaveCaseListOfRandomExceptSelf(@Param("waveId") long waveId,
                                                        @Param("exceptCaseId") int exceptCaseId,
                                                        @Param("size") int size);

    int addSubsidyCount(@Param("caseId") int caseId, @Param("activityId") long activityId);

    @DataSource(DS.CF_SLAVE)
    int getCurrentCountInThisMonth(@Param("waveId") long waveId);
}
