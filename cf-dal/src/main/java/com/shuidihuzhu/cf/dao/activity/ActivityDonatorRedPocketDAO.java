package com.shuidihuzhu.cf.dao.activity;

import com.shuidihuzhu.cf.domain.activity.ActivityDonatorRedPocketDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by sven on 2020/5/7.
 *
 * <AUTHOR>
 */
@DataSource(DS.CF)
public interface ActivityDonatorRedPocketDAO {

    int insert(ActivityDonatorRedPocketDO redPocketDO);

    int updateStatus(@Param("caseId") int caseId,@Param("pocketId") long pocketId,
                     @Param("oldStatus") int oldStatus,@Param("updateStatus") int updateStatus);

    int reduceCount(@Param("caseId") int caseId,@Param("pocketId") long pocketId, @Param("reduceCount") int reduceCount);

    @DataSource(DS.CF_SLAVE)
    List<ActivityDonatorRedPocketDO> getAllByCaseId(@Param("caseId") int caseId);

    @DataSource(DS.CF_SLAVE)
    ActivityDonatorRedPocketDO getDoingByCaseId(@Param("caseId") int caseId);

    @DataSource(DS.CF_SLAVE)
    List<Long> listDonePocketIdByCaseId(@Param("caseId") int caseId);
}
