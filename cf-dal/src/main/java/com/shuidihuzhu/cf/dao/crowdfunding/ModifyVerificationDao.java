package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.ModifyVerificationDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * @Description:
 * @Author: pangh<PERSON><PERSON>
 * @Date: 2023/6/28 2:46 PM
 */
@DataSource(DS.CF)
public interface ModifyVerificationDao {

    int insertModifyVerificationRecord(ModifyVerificationDO modifyVerificationDO);

    @DataSource(DS.CF_SLAVE)
    ModifyVerificationDO selectByVerifyId(@Param("verifyId") Integer verifyId);

}
