package com.shuidihuzhu.cf.dao.user;

import com.shuidihuzhu.cf.domain.user.UserMobileDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@DataSource(DS.CF)
public interface UserMobileMapper {

    int save(UserMobileDO userMobileDO);

    @DataSource(DS.CF_SLAVE)
    String getEncryptMobileByUserId(@Param("userId") long userId);
}
