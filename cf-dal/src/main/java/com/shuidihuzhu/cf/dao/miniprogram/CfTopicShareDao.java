package com.shuidihuzhu.cf.dao.miniprogram;

import com.shuidihuzhu.cf.model.miniprogram.CfTopicShare;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.FRAME)
public interface CfTopicShareDao {
    int addOne(@Param("cfTopicShare") CfTopicShare cfTopicShare);

    @DataSource(DS.FRAME_SLAVE)
    List<CfTopicShare> listByTopicId(@Param("topicId") int topicId);
}
