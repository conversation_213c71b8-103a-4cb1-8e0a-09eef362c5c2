package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingSummary;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;

/**
 * Created by wangsf on 17/1/16.
 */
@DataSource(DS.CF)
public interface CrowdfundingSummaryDao {

    /**
     * 插入一条汇总信息
     * @param summary
     * @return
     */
    int add(CrowdfundingSummary summary);

    /**
     * 更新汇总信息
     * @param summary
     * @return
     */
    void update(CrowdfundingSummary summary);

    /**
     * 获取指定类型的汇总信息
     * @param type
     * @return
     */
    @DataSource(DS.CF_SLAVE)
    CrowdfundingSummary getByTypeAndSummaryTime(@Param("type") int type, @Param("summaryTime")Timestamp summaryTime);
}
