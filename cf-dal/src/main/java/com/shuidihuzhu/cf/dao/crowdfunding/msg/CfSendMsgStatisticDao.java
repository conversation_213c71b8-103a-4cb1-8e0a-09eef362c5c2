package com.shuidihuzhu.cf.dao.crowdfunding.msg;

import com.shuidihuzhu.cf.model.crowdfunding.CfSendMsgStatistic;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

@DataSource(DS.CF)
public interface CfSendMsgStatisticDao {

    public int save(CfSendMsgStatistic cfSendMsgStatistic);

    @DataSource(DS.CF_SLAVE)
    public Integer selectSendCount(@Param("userId") long userId, @Param("thirdType") int thirdType);
}
