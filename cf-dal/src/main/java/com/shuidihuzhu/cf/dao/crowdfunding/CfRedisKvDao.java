package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.app.AppUpgradeInfo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.common.web.model.RedisKv;
import org.apache.ibatis.annotations.Param;

/**
 * Created by wangsf on 17/2/16.
 */
@DataSource(DS.CF)
public interface CfRedisKvDao {

	@DataSource(DS.CF_SLAVE)
	RedisKv queryByKey(@Param("key") String key);

	@DataSource(DS.CF_SLAVE)
	String queryValueByKey(@Param("key") String key);

	int saveRedisKv (RedisKv redisKv);

	int updateRedisKv(@Param("key") String key, @Param("value") String value);

}
