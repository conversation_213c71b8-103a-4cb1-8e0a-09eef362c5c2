package com.shuidihuzhu.cf.dao.goods;

import com.shuidihuzhu.cf.fulllink.measurement.annotation.Operate;
import com.shuidihuzhu.cf.fulllink.measurement.utils.OperateType;
import com.shuidihuzhu.cf.model.goods.GoodsGear;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.CF)
public interface GoodsGearDao {
	
	int save(GoodsGear gear);

	@DataSource(DS.CF_SLAVE)
	List<GoodsGear> getByInfoUuid(@Param("infoUuid") String infoUuid);
	
	int updateNum(@Param("id") long id, @Param("goodsCount") int goodsCount);

	@Operate(OperateType.READ)
	@DataSource(DS.CF_SLAVE)
	GoodsGear getById(@Param("id") long id);

	@DataSource(DS.CF_SLAVE)
	List<GoodsGear> getListByIds(@Param("ids") List<Long> ids);

    int updateValidByInfoUuid(@Param("infoUuid") String infoUuid, @Param("valid") int valid);

    int updateByGoodsGear(GoodsGear goodsGear);
}
