package com.shuidihuzhu.cf.dao.crowdfunding.msg;

import com.shuidihuzhu.cf.model.dailyreport.CfDailyReportSummary;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * Created by chao on 2017/11/10.
 */
@DataSource(DS.CF)
public interface DailyReportSummaryDao {
	@DataSource(DS.CF_SLAVE)
	CfDailyReportSummary getSummary(@Param("infoUuid") String infoUuid,
	                                @Param("dateStr") String dateStr);

	int add(CfDailyReportSummary summary);

	void addBatch(@Param("list") List<CfDailyReportSummary> summaryList);

	@DataSource(DS.CF_SLAVE)
	List<CfDailyReportSummary> getOfStatus(@Param("dateStr") String dateStr,
	                                       @Param("statusSet") Set<Integer> status,
	                                       @Param("offset") int offset,
	                                       @Param("limit") int limit);

	void updateStatusBatch(@Param("list") List<CfDailyReportSummary> summaryList,
	                       @Param("status") int status);
}
