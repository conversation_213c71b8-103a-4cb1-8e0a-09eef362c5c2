package com.shuidihuzhu.cf.dao.crowdfunding.InitialAudit;

import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.CrowdfundingInitialAuditInfo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

@DataSource(DS.CF)
public interface CrowdfundingInitialAuditInfoDao {

    int insert(CrowdfundingInitialAuditInfo param);

    CrowdfundingInitialAuditInfo selectById(@Param("id") int id);

    int updateInitialAuditInfo(CrowdfundingInitialAuditInfo param);

    @DataSource(DS.CF_SLAVE)
    CrowdfundingInitialAuditInfo selectByCaseId(@Param("caseId") int caseId);

    int updateState(@Param("caseId") Integer caseId,
                    @Param("baseInfo") Integer baseInfo,
                    @Param("firstApproveInfo") Integer firstApproveInfo,
                    @Param("creditInfo") Integer creditInfo,
                    @Param("rejectDetail") String rejectDetail);
}
