package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingProgress;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/07/17
 */
@DataSource(DS.CF)
public interface CrowdfundingFundUseAuditDao {

    int insertFundUseAuditProgress(CrowdFundingProgress progress);

    @DataSource(DS.CF_SLAVE)
    List<CrowdFundingProgress> selectByCaseIdAndStatus(@Param("caseId") int caseId, @Param("status") int status);

    @DataSource(DS.CF_SLAVE)
    List<CrowdFundingProgress> selectByCaseIdAndStatusNew(@Param("caseId") int caseId, @Param("status") int status);

    @DataSource(DS.CF_SLAVE)
    CrowdFundingProgress selectById(@Param("id") long id);
}
