package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfCharityPayee;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE 2018/11/15
 */
@DataSource(DS.CF)
public interface CfCharityPayeeDao {

    @DataSource(DS.CF_SLAVE)
    CfCharityPayee getByCaseUUid(@Param("uuid") String uuid);

    List<CfCharityPayee> getCfCharityPayeeByUUids(@Param("uuids") List<String> uuids);

    int insert(CfCharityPayee cfCharityPayee);


    int update(CfCharityPayee cfCharityPayee);

    CfCharityPayee getCfCharityPayeeBycaseId(@Param("caseId") Integer caseId);
}
