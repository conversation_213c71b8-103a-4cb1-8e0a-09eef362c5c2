package com.shuidihuzhu.cf.dao.tdsql;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.model.crowdfunding.OrderStatisticsPo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfCountSumVo;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;


@DataSource(CfDataSource.TD_CF_ORDER_DS)
public interface TdCrowdfundingOrderDao {

    CfCountSumVo selectCountByMin(@Param("begin") Timestamp begin, @Param("end") Timestamp end,@Param("payStatus") Integer payStatus);

    List<CrowdfundingOrder> getListBetweenTime(@Param("start")Timestamp start,
                                               @Param("end")Timestamp end,
                                               @Param("offset") int offset,
                                               @Param("limit") int limit);


    List<CrowdfundingOrder> getByOffset(@Param("offset") long offset,
                                        @Param("limit") int limit);


    int getOrderCountBetweenTime(@Param("start") Timestamp start, @Param("end") Timestamp end);

    Timestamp getLatestOrderTime();

    List<CrowdfundingOrder> selectByPayTimeLimit(@Param("begin") Timestamp begin, @Param("end") Timestamp end,
                                                 @Param("start") int start, @Param("size") int size);


    List<CrowdfundingOrder> getValidPayedSuccessListByPayTimeLimit(@Param("begin") Timestamp begin,
                                                                   @Param("end") Timestamp end, @Param("id") Long id,
                                                                   @Param("size") int size);

    List<CrowdfundingOrder> selectByUserIdAndThirdType(@Param("userId") Long userId, @Param("caseId") Integer caseId,
                                                       @Param("thirdType") Integer thirdType);


    List<CrowdfundingOrder> findCrowdfundingOrder(@Param("userId") Long userId, @Param("startTime") String startTime,
                                                  @Param("endTime") String endTime);

    List<Map<String, Object>> getCountByTime(@Param("startTime") String startTime, @Param("endTime") String endTime);

    List<OrderStatisticsPo> getCountByTimeNew(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 限制有效且支付为 0
     * @param startDate
     * @param endDate
     * @param id
     * @param limit
     * @return
     */
    List<CrowdfundingOrder> getNoPayListByDate(@Param("startDate")Date startDate, @Param("endDate")Date endDate,
                                               @Param("id")Long id,@Param("limit") Integer limit);

    CrowdfundingOrder getCrowdfundingOrderById(@Param("id") Long id);

    CrowdfundingOrder getCrowdfundingOrderByCode(@Param("code") String code);

    List<CrowdfundingOrder> getCrowdfundingOrderByActivity(@Param("caseIds") List<Integer> caseIds, @Param("activityId") long activityId);

}

