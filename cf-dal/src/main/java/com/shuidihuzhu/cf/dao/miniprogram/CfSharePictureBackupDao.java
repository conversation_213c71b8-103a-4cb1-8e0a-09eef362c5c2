package com.shuidihuzhu.cf.dao.miniprogram;

import com.shuidihuzhu.cf.domain.miniprogram.CfSharePictureBackupDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

/**
 * <AUTHOR>
 */
@DataSource(DS.CF)
public interface CfSharePictureBackupDao {

    int insert(CfSharePictureBackupDO pictureBackupDO);

    int update(CfSharePictureBackupDO pictureBackupDO);
    
    @DataSource(DS.CF_SLAVE)
    CfSharePictureBackupDO getByUserIdAndCaseIdAndTypeAndUuid(Long userId, Integer caseId, Integer type);

}
