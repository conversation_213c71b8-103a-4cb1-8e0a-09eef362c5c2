package com.shuidihuzhu.cf.dao.audit;

import com.shuidihuzhu.cf.client.apipure.model.audit.CfAuditInfoSupplement;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: wangpeng
 * @Date: 2023/5/17 19:30
 * @Description:
 */
@DataSource(DS.CF)
public interface CfAuditInfoSupplementDao {

    int insert(CfAuditInfoSupplement cfAuditInfoSupplement);

    int updateContentByCaseIdAndType(CfAuditInfoSupplement cfAuditInfoSupplement);

    @DataSource(DS.CF_SLAVE)
    List<CfAuditInfoSupplement> selectByCaseId(@Param("caseId") int caseId);

    @DataSource(DS.CF_SLAVE)
    CfAuditInfoSupplement selectByCaseIdAndType(@Param("caseId") int caseId, @Param("supplementType") int supplementType);
}
