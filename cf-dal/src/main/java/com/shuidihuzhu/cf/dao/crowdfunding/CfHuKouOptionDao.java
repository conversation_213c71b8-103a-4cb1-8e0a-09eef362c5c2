package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.HuKouOptionDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

/**
 * @Description:
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024/6/11 2:37 PM
 */
@DataSource(DS.CF)
public interface CfHuKouOptionDao {

    int saveHuKouOption(HuKouOptionDO huKouOptionDO);

    int updateHuKouOption(HuKouOptionDO huKouOptionDO);

    @DataSource(DS.CF_SLAVE)
    HuKouOptionDO selectByCaseId(Integer caseId);

}
