package com.shuidihuzhu.cf.dao.crowdfunding.app;

import com.shuidihuzhu.cf.model.crowdfunding.app.vo.AppEventVo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2019-06-24
 */
@DataSource(DS.CF)
public interface AppEventDao {

    @DataSource(DS.CF_SLAVE)
    AppEventVo getAppEventDo(@Param("appUnqiueId") String appUnqiueId,
                             @Param("eventId") int eventId);

    int insert(AppEventVo appEventVo);
}
