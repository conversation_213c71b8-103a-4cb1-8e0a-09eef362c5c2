package com.shuidihuzhu.cf.dao.crowdfunding.casematerial;

import com.shuidihuzhu.cf.model.crowdfunding.casematerial.CfRefuseReasonTagSlave;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@DataSource(DS.CF_SLAVE)
public interface CfRefuseReasonTagSlaveDao {
    List<CfRefuseReasonTagSlave> selectAll();

    List<CfRefuseReasonTagSlave> selectByType(@Param("type") Integer type);
}
