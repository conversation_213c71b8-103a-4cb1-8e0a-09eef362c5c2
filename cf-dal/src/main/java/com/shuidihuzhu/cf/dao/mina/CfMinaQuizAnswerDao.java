package com.shuidihuzhu.cf.dao.mina;

import com.shuidihuzhu.cf.model.mina.CfMinaQuizAnswer;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by dongcf on 2017/12/12
 */
@DataSource(DS.FRAME)
public interface CfMinaQuizAnswerDao {

    @DataSource(DS.FRAME_SLAVE)
    List<CfMinaQuizAnswer> findByQuestionIds(@Param("questionIds") List<Integer> questionIds);

    @DataSource(DS.FRAME_SLAVE)
    List<CfMinaQuizAnswer> findByIds(@Param("ids") List<Integer> ids);

    Integer insertBatch(@Param("list") List<CfMinaQuizAnswer> list);

    Integer updateDeleteByQuestionIds(@Param("isDelete") Integer isDelete, @Param("questionIds") List<Integer> questionIds);
}
