package com.shuidihuzhu.cf.dao.crowdfunding.task;

import com.shuidihuzhu.cf.model.task.CfInfoTaskRule;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: lixuan
 * @date: 2018/3/16 17:57
 */
@DataSource(DS.CF)
public interface CfInfoTaskRuleDao {

    @DataSource(DS.CF_SLAVE)
    List<CfInfoTaskRule> getAllValid();

    @DataSource(DS.CF_SLAVE)
    CfInfoTaskRule getById(@Param("id") long id);
}
