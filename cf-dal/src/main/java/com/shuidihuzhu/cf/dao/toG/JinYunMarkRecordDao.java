package com.shuidihuzhu.cf.dao.toG;

import com.shuidihuzhu.cf.domain.toG.CfJinYunMarkRecord;
import com.shuidihuzhu.cf.domain.toG.JinYunListPageQuery;
import com.shuidihuzhu.cf.enums.toG.FundraisingListParam;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 缙云打标记录
 * @Author: panghairui
 * @Date: 2022/3/18 3:40 下午
 */
@DataSource(DS.CF)
public interface JinYunMarkRecordDao {

    @DataSource(DS.CF_SLAVE)
    CfJinYunMarkRecord selectByCaseId(@Param("caseId") int caseId);

    int updateVerifyStatus(@Param("verifyStatus") int verifyStatus, @Param("caseId") int caseId);

    int updateFinishStatus(@Param("finishStatus") int finishStatus, @Param("caseId") int caseId);

    int updateFirstApproveStatus(@Param("firstApproveStatus") int firstApproveStatus, @Param("caseId") int caseId);

    int add(CfJinYunMarkRecord cfJinYunMarkRecord);

    List<CfJinYunMarkRecord> selectPage(JinYunListPageQuery jinYunListPageQuery);

    int countByPage(JinYunListPageQuery jinYunListPageQuery);

    @DataSource(DS.CF_SLAVE)
    List<CfJinYunMarkRecord> listByParam(FundraisingListParam fundraisingListParam);

    int removeByCaseId(@Param("caseId") int caseId);

    @DataSource(DS.CF_SLAVE)
    List<CfJinYunMarkRecord> getListByCaseIdList(@Param("caseIdList") List<Integer> caseIdList);
}
