package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.IdcardVerifyWhiteListRecord;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

import java.util.List;

@DataSource(DS.CF_SLAVE)
public interface CfFirstApproveIdCardWhiteListRecordDao {

    @DataSource(DS.CF)
    int save(IdcardVerifyWhiteListRecord record);

    List<IdcardVerifyWhiteListRecord> findByVerifyId(int verifyId);
}
