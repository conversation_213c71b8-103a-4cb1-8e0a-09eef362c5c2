package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfSuspectedCaseInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CfSuspectedCaseInfoModel;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;
import java.util.List;


/**
 * Created by wangsf on 17/4/27.
 */

@DataSource(DS.CF)
public interface CfSuspectedCaseInfoDao {

	@DataSource(DS.CF_SLAVE)
	List<CfSuspectedCaseInfo> getList(@Param("limit") int limit, @Param("size") int size);

	@DataSource(DS.CF_SLAVE)
	List<CfSuspectedCaseInfo> getListBySerch(CfSuspectedCaseInfoModel cfSuspectedCaseInfoModel);

	int update(CfSuspectedCaseInfo cfSuspectedCaseInfo);

	int delete(@Param("id") int id);

	int add(CfSuspectedCaseInfo cfSuspectedCaseInfo);

	@DataSource(DS.CF_SLAVE)
	CfSuspectedCaseInfo getById(@Param("id") int id);

	@DataSource(DS.CF_SLAVE)
	List<CfSuspectedCaseInfo> getAllAndIdNumNotEmpty();

	@DataSource(DS.CF_SLAVE_2)
	int total();

}
