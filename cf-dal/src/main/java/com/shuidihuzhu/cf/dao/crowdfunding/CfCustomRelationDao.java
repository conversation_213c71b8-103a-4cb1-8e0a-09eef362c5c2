package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.CfCustomRelation;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by dongcf on 2020/7/21
 */
@DataSource(DS.CF)
public interface CfCustomRelationDao {

    @DataSource(DS.CF_SLAVE)
    CfCustomRelation getLast(@Param("caseId")long caseId);

    int insert(CfCustomRelation cfCustomRelation);

    @DataSource(DS.CF_SLAVE)
    List<CfCustomRelation> findByCaseId(@Param("caseId")long caseId);
}
