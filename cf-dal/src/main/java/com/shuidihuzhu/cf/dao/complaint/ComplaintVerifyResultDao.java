package com.shuidihuzhu.cf.dao.complaint;

import com.shuidihuzhu.cf.dao.crowdfunding.complaint.ComplaintVerifyResultDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

@DataSource(DS.CF)
public interface ComplaintVerifyResultDao {

    int insert(ComplaintVerifyResultDO record);

    int insertSelective(ComplaintVerifyResultDO record);

    ComplaintVerifyResultDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ComplaintVerifyResultDO record);

    int updateByPrimaryKey(ComplaintVerifyResultDO record);

    ComplaintVerifyResultDO getByBizIdAndBizType(@Param("bizId") long bizId,@Param("bizType") int bizType);

}