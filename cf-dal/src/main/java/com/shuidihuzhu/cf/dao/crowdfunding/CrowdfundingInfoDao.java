package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.BankCardVerifyStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingRelationType;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.fulllink.measurement.annotation.Operate;
import com.shuidihuzhu.cf.fulllink.measurement.utils.OperateType;
import com.shuidihuzhu.cf.model.CfInfoSimpleModelV2;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Set;


/**
 * Created by wuxinlong on 6/21/16.
 */

@DataSource(DS.CF)
public interface CrowdfundingInfoDao {
    void add(CrowdfundingInfo crowdfundingInfo);

    int update(CrowdfundingInfo crowdfundingInfo);

    int updatePayeeInfo(CrowdfundingInfo crowdfundingInfo);

    int updateTitle(CrowdfundingInfo crowdfundingInfo);

    int updateBaseInfo(CrowdfundingInfo crowdfundingInfo);

    int updateRelationType(CrowdfundingInfo crowdfundingInfo);

    int updateUserId(CrowdfundingInfo crowdfundingInfo);

    int updateBaseInfoV2(CrowdfundingInfo crowdfundingInfo);

    int updateFromStageInfo(CrowdfundingInfo crowdfundingInfo);

    int adjustAmountFromOrders(@Param("id") int id,
                               @Param("amount") int amount);

    int doApprove(@Param("crowdfundingId") int crowdfundingId,
                  @Param("status") CrowdfundingStatus status,
                  @Param("beginTime") Date beginTime,
                  @Param("endTime") Date endTime);

    List<CrowdfundingInfo> getApproveList(@Param("size") int size,
                                          @Param("offset") int offset,
                                          @Param("status") CrowdfundingStatus status,
                                          @Param("applicantName") String applicantName,
                                          @Param("title") String title);


    @Operate(OperateType.READ)
    CrowdfundingInfo getFundingInfo(String infoId);

    List<CrowdfundingInfo> getFundingInfoList();

    /**
     * 此方法不要使用从库
     *
     * @param id
     * @return
     */
    @Operate(OperateType.READ)
    CrowdfundingInfo getFundingInfoById(int id);

    @DataSource(DS.CF_SLAVE)
    CrowdfundingInfo getFundingInfoByIdFromSlave(int caseId);

    @DataSource(DS.CF_SLAVE)
    List<CrowdfundingInfo> getFundingInfoByIds(@Param("ids") List<Integer> ids);

    @DataSource(DS.CF_SLAVE)
    List<CrowdfundingInfo> getFundingInfoByInfoIds(@Param("infoIds") List<String> infoIds);

    @DataSource(DS.CF_SLAVE)
    List<CrowdfundingInfo> getByUserId(long userId);

    @DataSource(DS.CF_SLAVE)
    @Operate(OperateType.READ)
    int getCountByUserId(long userId);

    int addAmount(@Param("id") int id, @Param("amount") int amount);

    int subtractAmount(@Param("id") int id, @Param("amount") int amount);

    int updateEndTime(@Param("id") int id, @Param("endTime") Date endTime);

    int updateDataStatus(@Param("id") int id, @Param("dataStatus") int dataStatus);

    int updateType(@Param("id") int id, @Param("type") int type);


    int updateStatus(@Param("id") int id, @Param("newStatus") CrowdfundingStatus newStatus, @Param("oldStatus") CrowdfundingStatus oldStatus);

    int updateVerifyStatus(@Param("id") int id,
                           @Param("verifyStatus") BankCardVerifyStatus verifyStatus,
                           @Param("bankCardVerifyMessage") String bankCardVerifyMessage,
                           @Param("bankCardVerifyMessage2") String bankCardVerifyMessage2);

    /**
     * 获取指定时间周期内，同一个用户提交的次数
     *
     * @param userId
     * @param startTime
     * @param endTime
     * @return
     */
    int getApplyCount(@Param("userId")long userId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    @DataSource(DS.CF_SLAVE)
    List<CrowdfundingInfo> getByCreateTime(@Param("startTime") Timestamp startTime, @Param("endTime") Timestamp endTime, @Param("start") int start, @Param("size") int size);

    int insertIntoFromExcel(CrowdfundingInfo crowdfundingInfo);

    int updateFromByInfoUuid(@Param("infoUuid") String infoUuid, @Param("from") String from);

    /**
     * 根据目标金额、已筹金额获取对应的资金筹集记录
     *
     * @param targetAmount
     * @param endInfoIds
     * @param limit
     * @return
     */
    List<String> getSuccessCaseIds(@Param("targetAmount") int targetAmount, @Param("endInfoIds") List<String> endInfoIds, @Param("limit") int limit);

    List<CrowdfundingInfo> getInfoIdsByCreateTime(@Param("startTime") Timestamp startTime, @Param("endTime") Timestamp endTime, @Param("id") int id);

    @DataSource(DS.CF_SLAVE)
    int getId(@Param("startTime") Timestamp startTime, @Param("endTime") Timestamp endTime);

    @DataSource(DS.CF_SLAVE)
    List<CrowdfundingInfo> findByAnchorId(@Param("anchorId") int anchorId, @Param("limit") int limit);

    List<CrowdfundingInfo> findByEndTime(@Param("start") Timestamp start, @Param("end") Timestamp end, @Param("offset") int offset,
                                         @Param("limit") int limit);

    //  查询案例表
    @DataSource(DS.CF_SLAVE)
    List<Integer> getInfoIdsInEndTime(@Param("startId") int startId, @Param("endTime") Date endTime,
                                      @Param("offset") int offset, @Param("limit") int limit);

    CrowdfundingInfo selectLatestInfoByUserId(@Param("userId")long userId);

    @DataSource(DS.CF_SLAVE)
    List<Integer> getIdsByCreateTime(@Param("begin") Timestamp begin, @Param("end") Timestamp end,
                                     @Param("offset") int offset, @Param("limit") int limit);

    int updateBeginAndEndTime(@Param("id") int id, @Param("beginTime")Date beginTime, @Param("endTime")Date endTime);

    @DataSource(DS.CF_SLAVE)
    List<CrowdfundingInfo> selectIdAndUuidAndTypeById(@Param("set") Set<Integer> set);

    @DataSource(DS.CF_SLAVE)
    List<CrowdfundingInfo> queryById(@Param("id") int id);

    int updateTitleImgById(@Param("id") int id, @Param("titleImg") String titleImg);

    @DataSource(DS.CF_SLAVE)
    List<CrowdfundingInfo> listByPayeeMobile(String cryptoMobile);

    int updatePayeeRelation(@Param("id") int id, @Param("relationType") CrowdfundingRelationType relationType,
                            @Param("relation") String relation);

    int setMaterialsVersion(@Param("caseId") int caseId,
                            @Param("mId") int mId);

    CfInfoSimpleModelV2 getSimpleInfo(String infoId);

    @DataSource(DS.CF_SLAVE)
    CrowdfundingInfo getSimpleInfoById(Integer id);

    @DataSource(DS.CF_SLAVE)
    List<CrowdfundingInfo> getCrowdfundingInfoListByAmount(@Param("minAmount") Integer minAmount, @Param("maxAmount") Integer maxAmount,
                                                           @Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("limit") int limit);
}
