package com.shuidihuzhu.cf.dao.loverank;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.fulllink.measurement.annotation.Operate;
import com.shuidihuzhu.cf.fulllink.measurement.utils.OperateType;
import com.shuidihuzhu.cf.model.loverank.UserLoveRankLikeStat;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 按照caseId分表
 */
@DataSource(CfDataSource.CF_NEW_CASE_DETAIL_SHADING)
public interface UserLoveRankLikeStatDao {

    int insert(UserLoveRankLikeStat record);

    int insertSelective(UserLoveRankLikeStat record);

    @Operate(OperateType.READ)
    List<UserLoveRankLikeStat> selectLikeStatByUserIds(@Param("userIds") Set<Long> userIds, @Param("caseId") int caseId);

    int minusLikeById(@Param("id") Long id,@Param("userId")long userId,@Param("caseId")int caseId);

    int addLikeById(@Param("id") Long id,@Param("userId")long userId,@Param("caseId")int caseId);

    int updateViewUserIdsById(@Param("id") Long id, @Param("userId") long userId, @Param("loginUserId") long loginUserId, @Param("caseId") int caseId);

    @Operate(OperateType.READ)
    UserLoveRankLikeStat selectLikeByUserIdAndCaseIdAndAnonymous(@Param("userId") long userId,
                                                                 @Param("caseId") int caseId,
                                                                 @Param("anonymous") int anonymous);

}