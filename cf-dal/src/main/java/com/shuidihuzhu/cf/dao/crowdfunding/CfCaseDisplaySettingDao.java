package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.CaseDisplaySettingDo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

@DataSource(DS.CF_SLAVE)
public interface CfCaseDisplaySettingDao {
    @DataSource(DS.CF)
    int addOne(@Param("setting") CaseDisplaySettingDo caseDisplaySetting);

    @DataSource(DS.CF)
    int update(@Param("setting") CaseDisplaySettingDo caseDisplaySettingDo);

    CaseDisplaySettingDo select(@Param("caseId") int caseId);
}
