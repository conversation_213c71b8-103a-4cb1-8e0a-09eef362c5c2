package com.shuidihuzhu.cf.dao.yiqing;

import com.shuidihuzhu.cf.domain.yiqing.YiqingHistory;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by sven on 2020/1/24.
 *
 * <AUTHOR>
 */
@DataSource(DS.CF)
public interface IYiqingHistoryDAO {

    int replace(YiqingHistory yiqingHistory);

    @DataSource(DS.CF_SLAVE)
    List<YiqingHistory> getYesterDayAndToday(@Param("today") int today,
                                             @Param("yesterday") int yesterday );

    @DataSource(DS.CF_SLAVE)
    List<YiqingHistory> getHistory(@Param("today") int today);
}
