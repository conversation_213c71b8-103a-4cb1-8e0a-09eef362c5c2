package com.shuidihuzhu.cf.dao.toG;

import com.shuidihuzhu.client.cf.api.model.GuangzhouMarkRecord;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;

@DataSource(DS.CF)

public interface GuangzhouMarkRecordDAO {

    int saveGuangzhouLabel(GuangzhouMarkRecord record);

    int updateGuangzhouLabel(GuangzhouMarkRecord record);

    GuangzhouMarkRecord selectByCaseId(int caseId);

    GuangzhouMarkRecord selectByInfoId(String infoId);

    int deleteByCaseId(int caseId);
}
