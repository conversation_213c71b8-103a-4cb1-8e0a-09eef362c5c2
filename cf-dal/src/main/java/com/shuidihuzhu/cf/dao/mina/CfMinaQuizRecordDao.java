package com.shuidihuzhu.cf.dao.mina;

import com.shuidihuzhu.cf.model.mina.CfMinaQuizRecord;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by dongcf on 2017/12/12
 */
@DataSource(DS.FRAME)
public interface CfMinaQuizRecordDao {

    int insert(CfMinaQuizRecord cfMinaQuizRecord);

    int updateAnswerAndScore(CfMinaQuizRecord cfMinaQuizRecord);

    @DataSource(DS.FRAME_SLAVE)
    List<CfMinaQuizRecord> listByUserIdAndQuizIdList(@Param("userId") long userId, @Param("quizIdList") List<Integer> quizIdList);

    @DataSource(DS.FRAME_SLAVE)
    List<CfMinaQuizRecord> listBySelfTagAndQuizIdList(@Param("selfTag") String selfTag, @Param("quizIdList") List<Integer> quizIdList);

    @DataSource(DS.FRAME_SLAVE)
    CfMinaQuizRecord findByUserIdAndQuizId(@Param("userId") long userId, @Param("quizId") int quizId);

    @DataSource(DS.FRAME_SLAVE)
    CfMinaQuizRecord findBySelfTagAndQuizId(@Param("quizId") int quizId, @Param("selfTag") String selfTag);

    @DataSource(DS.FRAME_SLAVE)
    List<Long> findUserIdByQuizIds(@Param("quizIds")List<Integer> quizIds,@Param("anchorId")Integer anchorId,@Param("limit")Integer limit);


    List<CfMinaQuizRecord> findByUserIds(@Param("minaQuizId")Integer minaQUizId,@Param("userIds")List<Long> userIds);

    @DataSource(DS.FRAME_SLAVE)
    CfMinaQuizRecord getLastQuizByUserId(@Param("userId")long userId,@Param("minaQuizId")Integer minaQuizId,@Param("minaQuizShareId")Integer minaQuizShareId);

    @DataSource(DS.FRAME_SLAVE)
    List<CfMinaQuizRecord> findByQuizShareId(@Param("minaQuizShareId")Integer minaQuizShareId,@Param("offset")Integer offset,@Param("limit")Integer limit);
}
