package com.shuidihuzhu.cf.dao.disease;

import com.shuidihuzhu.cf.model.disease.DiseaseKnowledge;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * @Author：liuchangjun
 * @Date：2021/8/26
 */
@DataSource(DS.CF)
public interface DiseaseKnowledgeDao {

    List<DiseaseKnowledge> getNorms();

    List<DiseaseKnowledge> getDiseaseKnowledge(@Param("ids") Set<Long> ids);
}
