package com.shuidihuzhu.cf.dao.dedicated;

import com.shuidihuzhu.cf.domain.dedicated.CfToufangInviteCaseRelationDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.CF)
public interface CfToufangInviteCaseRelationDao {
    int addInviteRelation(CfToufangInviteCaseRelationDO caseRelationDO);

    @DataSource(DS.CF_SLAVE)
    List<CfToufangInviteCaseRelationDO> getInviteCaseRelation( @Param("invitorUniqueCode") String invitorUniqueCode);
}
