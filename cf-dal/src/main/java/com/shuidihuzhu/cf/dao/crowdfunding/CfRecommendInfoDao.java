package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfRecommendInfo;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.CF)
public interface CfRecommendInfoDao {

	int insert(CfRecommendInfo record);

	@DataSource(DS.CF_SLAVE)
	CfRecommendInfo selectByPrimaryKey(Integer id);

	@DataSource(DS.CF_SLAVE)
	List<CfRecommendInfo> selectRandomInfos(@Param("limit") Integer limit);

	int updateByPrimaryKey(CfRecommendInfo record);
}
