package com.shuidihuzhu.cf.dao.miniprogram;

import com.shuidihuzhu.cf.model.miniprogram.CfMiniStoreModel;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @DATE 2020/1/5
 */
@DataSource(DS.CF)
public interface CfMiniProgramDao {


    int save(CfMiniStoreModel miniStoreModel);

    @DataSource(DS.CF_SLAVE)
    CfMiniStoreModel getByUserId(@Param("userId") long userId, @Param("key") String key);

}
