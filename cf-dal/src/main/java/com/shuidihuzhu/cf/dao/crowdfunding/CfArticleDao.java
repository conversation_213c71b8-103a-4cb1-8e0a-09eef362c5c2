package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfArticle;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * 文章列表
 *
 * Created by wangsf on 17/2/22.
 */
@DataSource(DS.CF_SLAVE)
public interface CfArticleDao {

	List<CfArticle> getListByAnchorId(@Param("start") int start, @Param("limit") int limit);

}
