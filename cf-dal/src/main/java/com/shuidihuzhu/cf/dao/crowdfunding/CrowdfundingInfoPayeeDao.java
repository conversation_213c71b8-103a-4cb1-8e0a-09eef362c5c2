package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoPayee;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DataSource(DS.CF)
public interface CrowdfundingInfoPayeeDao {

	int add(CrowdfundingInfoPayee crowdfundingInfoPayee);

	int update(CrowdfundingInfoPayee crowdfundingInfoPayee);

    @DataSource(DS.CF_SLAVE)
    CrowdfundingInfoPayee getByInfoUuid(@Param("infoUuid") String infoUuid);

    int updatePayeeRelation(@Param("caseId") int caseId, @Param("relationType") int relationType);

    @DataSource(DS.CF_SLAVE)
    List<CrowdfundingInfoPayee> selectByPayeeName(@Param("name") String name);
    @DataSource(DS.CF_SLAVE)
    List<CrowdfundingInfoPayee> selectByInfoUuidList(@Param("list") List<String> list);
    @DataSource(DS.CF_SLAVE)
    List<CrowdfundingInfoPayee> selectByPayeeIdCard(@Param("idCard") String idCard);
}
