package com.shuidihuzhu.cf.dao.caseinfo;

import com.shuidihuzhu.cf.domain.caseinfo.CaseInfoApproveStageDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@DataSource(DS.CF)
public interface CaseInfoApproveStageDAO {

    int insert(CaseInfoApproveStageDO caseInfoApproveStageDO);

    int update(CaseInfoApproveStageDO caseInfoApproveStageDO);

    CaseInfoApproveStageDO getByCaseId(@Param("caseId") long caseId);
    List<CaseInfoApproveStageDO> getByCaseIdBatch(@Param("caseIdList") List<Integer> caseIdList);
}
