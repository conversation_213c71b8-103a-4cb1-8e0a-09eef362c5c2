package com.shuidihuzhu.cf.dao.dedicated;

import com.shuidihuzhu.cf.domain.dedicated.CfUserVolunteerRelationDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

@DataSource(DS.CF)
public interface CfUserVolunteerRelationDao {
    @DataSource(DS.CF_SLAVE)
    CfUserVolunteerRelationDO getAccountUserAndVolunteerRelation(@Param("openId") String openId,@Param("phone") String phone);

    int addUserVolunteerRelationDO(CfUserVolunteerRelationDO volunteerRelationDO);
}
