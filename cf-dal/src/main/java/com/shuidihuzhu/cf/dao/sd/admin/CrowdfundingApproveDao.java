package com.shuidihuzhu.cf.dao.sd.admin;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingApprove;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@DataSource("shuidiCfAdminDataSource")
public interface CrowdfundingApproveDao {

    int insert(CrowdfundingApprove record);

    int insertSelective(CrowdfundingApprove record);

//    @DataSource("shuidiCfAdminSlaveDataSource")
    CrowdfundingApprove selectByPrimaryKey(Integer id);

//    @DataSource("shuidiCfAdminSlaveDataSource")
    List<CrowdfundingApprove> getListByCrowdfundingId(Integer crowdfundingId);

//    @DataSource("shuidiCfAdminSlaveDataSource")
    CrowdfundingApprove getLastWithCommentByCrowdfundingId(Integer crowdfundingId);

    int updateByPrimaryKeySelective(CrowdfundingApprove record);

    int updateByPrimaryKey(CrowdfundingApprove record);

    List<CrowdfundingApprove> getCrowdfundingApprovesByCfIds(@Param("cfIds") List<Integer> cfIds);
}