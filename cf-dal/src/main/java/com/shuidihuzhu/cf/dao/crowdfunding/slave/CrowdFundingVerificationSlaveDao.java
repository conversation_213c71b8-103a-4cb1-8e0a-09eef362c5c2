package com.shuidihuzhu.cf.dao.crowdfunding.slave;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/10/18.
 */
@DataSource(DS.CF_SLAVE)
public interface CrowdFundingVerificationSlaveDao {

    Integer selectCountByMin(@Param("begin") Timestamp begin, @Param("end") Timestamp end);
}
