package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfUserLabelDO;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @time 2019/3/21 下午5:02
 * @desc
 */
@DataSource(DS.CF)
public interface ICfUserLabelDAO {
    int insertLabel(CfUserLabelDO labelDO);
    @DataSource(DS.CF_SLAVE)
    CfUserLabelDO queryLabelByUser(@Param("userId") long userId);
    int incrCaseNum(@Param("userId") long userId);
    int updateDraft(@Param("draft") int draft, @Param("userId") long userId);
}
