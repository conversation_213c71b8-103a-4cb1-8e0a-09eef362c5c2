package com.shuidihuzhu.cf.dao.miniprogram;

import com.shuidihuzhu.cf.model.miniprogram.CfTopicComment;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;

@DataSource(DS.FRAME)
public interface CfTopicCommentDao {
    int addOne(@Param("cfTopicComment") CfTopicComment cfTopicComment);

    int deleteById(@Param("id") long id);

    @DataSource(DS.FRAME_SLAVE)
    CfTopicComment selectById(@Param("id") long id);

    @DataSource(DS.FRAME_SLAVE)
    List<CfTopicComment> selectByIds(@Param("ids") List<Long> ids);

    @DataSource(DS.FRAME_SLAVE)
    List<CfTopicComment> selectByUserId(@Param("userId") long userId);

    @DataSource(DS.FRAME_SLAVE)
    List<CfTopicComment> listByTopicId(@Param("topicId") int topicId);

    @DataSource(DS.FRAME_SLAVE)
    List<CfTopicComment> listByUserIds(@Param("userIds") List<Long> userIds);

    @DataSource(DS.FRAME_SLAVE)
    List<CfTopicComment> listByTimeAndSensitive(@Param("begin") Timestamp begin,
                                                @Param("end") Timestamp end,
                                                @Param("sensitive") int sensitive);

    @DataSource(DS.FRAME_SLAVE)
    List<CfTopicComment> listByCommentParentIds(@Param("parentIds") List<Long> commentIds);

    @DataSource(DS.FRAME_SLAVE)
    List<CfTopicComment> listByCommentGroupId(@Param("commentGroupId") Long commentGroupId,
                                              @Param("anchorId") Long anchorId,
                                              @Param("limit") Integer limit);
}
