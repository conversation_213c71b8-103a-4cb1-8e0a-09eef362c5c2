package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.CfContentStyle;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 案例文章样式表交互（目前只支持加粗，后续加样式可以在表里加字段区分样式）
 * @Author: panghairui
 * @Date: 2023/11/8 11:33 AM
 */
@DataSource(DS.CF)
public interface CfCaseContentStyleDao {

    void insertCfContentStyle(CfContentStyle cfContentStyle);

    void updateCfContentStyle(CfContentStyle cfContentStyle);

    @DataSource(DS.CF_SLAVE)
    CfContentStyle getByCaseId(@Param("caseId") Integer caseId);

}
