package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfSendProveTemplate;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@DataSource(DS.CF)
public interface CfProveTemplateDao {

    @DataSource(DS.CF)
    List<CfSendProveTemplate> findByCaseIdAndProveId(@Param("caseId") int caseId,
                                                     @Param("proveId") long proveId,
                                                     @Param("auditStatusList") List<Integer> auditStatusList);

    @DataSource(DS.CF_SLAVE)
    List<CfSendProveTemplate> getAllProve(@Param("caseId") int caseId,
                                          @Param("proveId") long proveId);

    @DataSource(DS.CF_SLAVE)
    List<CfSendProveTemplate> getAuditProve(@Param("caseId") int caseId,
                                          @Param("proveIdList") List<Long> proveIdList);

    CfSendProveTemplate getByAuditProve(@Param("caseId") int caseId, @Param("proveId") long proveId);

    int updateContentById(@Param("id") long id, @Param("content") String content, @Param("auditStatus") int auditStatus);

    int delete(@Param("caseId") int caseId, @Param("proveId") long proveId, @Param("auditStatus") int auditStatus);

}
