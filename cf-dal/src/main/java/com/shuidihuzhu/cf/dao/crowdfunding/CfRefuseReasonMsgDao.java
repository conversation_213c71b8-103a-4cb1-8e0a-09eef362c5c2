package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonMsg;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * Created by Ahrievil on 2017/7/31
 */
@DataSource(DS.CF)
public interface CfRefuseReasonMsgDao {
	@DataSource(DS.CF_SLAVE)
	CfRefuseReasonMsg selectByInfoUuidAndType(@Param("infoUuid") String infoUuid, @Param("type") int type);

	@DataSource(DS.CF_SLAVE)
	List<String> selectWithTimeLimit(@Param("begin") Timestamp begin, @Param("end") Timestamp end,
	                                 @Param("start") int start, @Param("size") int size);

	int deleteByInfoUuid(@Param("infoUuid") String infoUuid);

	@DataSource(DS.CF_SLAVE)
	List<CfRefuseReasonMsg> selectByInfoUuidLast(@Param("infoUuid") String infoUuid);

	int updateCaseRefuseMsg(@Param("id") long id, @Param("itemReason") String itemReason);

	int updateCaseModifySuggest(@Param("id") long id, @Param("suggestModify") String suggestModify);
}
