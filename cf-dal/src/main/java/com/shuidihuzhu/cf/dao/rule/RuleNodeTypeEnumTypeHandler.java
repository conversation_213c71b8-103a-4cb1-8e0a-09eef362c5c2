package com.shuidihuzhu.cf.dao.rule;

import com.google.common.base.Preconditions;
import com.shuidihuzhu.cf.enums.rule.RuleNodeTypeEnum;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.TypeHandler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

public class RuleNodeTypeEnumTypeHandler implements TypeHandler<RuleNodeTypeEnum> {

    @Override
    public void setParameter(PreparedStatement ps, int i, RuleNodeTypeEnum parameter, JdbcType jdbcType) throws SQLException {
        Preconditions.checkNotNull(parameter);
        ps.setInt(i, parameter.getCode());
    }

    @Override
    public RuleNodeTypeEnum getResult(ResultSet rs, String columnName) throws SQLException {
        int type = rs.getInt(columnName);
        return RuleNodeTypeEnum.parse(type);
    }

    @Override
    public RuleNodeTypeEnum getResult(ResultSet rs, int columnIndex) throws SQLException {
        int type = rs.getInt(columnIndex);
        return RuleNodeTypeEnum.parse(type);
    }

    @Override
    public RuleNodeTypeEnum getResult(CallableStatement cs, int columnIndex) throws SQLException {
        int type = cs.getInt(columnIndex);
        return RuleNodeTypeEnum.parse(type);
    }
}