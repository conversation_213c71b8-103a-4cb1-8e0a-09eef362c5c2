package com.shuidihuzhu.cf.dao.activity;

import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.model.activity.PennantUserStat;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * Created by dongcf on 2020/3/16
 */
@DataSource(CfDataSource.DONATION_BIGTABLE_MASTER)
public interface PennantUserStatDao {

    int insert(PennantUserStat pennantUserStat);

    PennantUserStat getByUserId(@Param("userId") Long userId);

    int updateAwardById(@Param("pennantIdStr")String pennantIdStr,@Param("id")Long id);
}
