package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfUserStat;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@DataSource(DS.CF)
public interface CfUserStatDao {

	@DataSource(DS.CF_SLAVE)
	CfUserStat selectByUserId(@Param("userId")long userId);

	@DataSource(DS.CF_SLAVE)
	List<CfUserStat> getByUserIds(@Param("userIds")Set<Long> userIds);

	int addScoreByInc(@Param("userId")long userId,
	                  @Param("score") Integer score,
	                  @Param("valueFiled") String valueFiled);


	int minusScore(@Param("userId") long userId,
	                  @Param("score") Integer score);

	int addScore(@Param("userId") long userId,
	               @Param("score") Integer score);

	int insert(CfUserStat cfUserStat);

	int insertContainStatus(CfUserStat cfUserStat);


	int updateIniting(@Param("userId")long userId);

	int updateInited(@Param("userId")long userId);

	int updateForInit(CfUserStat cfUserStat);

	int addAmountAndScore(@Param("userId")long userId, @Param("score") Integer score, @Param("amount") Integer amount);

	int followAndScore(@Param("userId")long userId, @Param("score") Integer score);

	void updateBatch(@Param("items") List<CfUserStat> cfUserStats);

}
