package com.shuidihuzhu.cf.dao.crowdfunding.toufang;

import com.shuidihuzhu.cf.model.crowdfunding.toufang.CfLandingPageConfig;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by ch<PERSON> on 2017/11/14.
 */
@DataSource(DS.CF)
public interface CfLandingPageConfigDao {
	@DataSource(DS.CF_SLAVE)
	CfLandingPageConfig get(@Param("account") String account,
	                        @Param("campaign") String campaign,
	                        @Param("adgroup") String adgroup);

	@DataSource(DS.CF_SLAVE)
	List<CfLandingPageConfig> getByPage(@Param("offset") int offset,
	                                    @Param("limit") int limit);
	
	void update(CfLandingPageConfig config);
	
	//限制100
	@DataSource(DS.CF_SLAVE)
	List<CfLandingPageConfig> getCfLandingPageConfigList(@Param("campaign") String campaign,
	                                                     @Param("adgroup") String adgroup);
}
