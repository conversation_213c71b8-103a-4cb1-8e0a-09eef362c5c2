package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfPosterWxMsgRel;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

/**
 * Created by wangsf on 17/11/13.
 */
@DataSource(DS.CF)
public interface CfPosterWxMsgRelDao {

	@DataSource(DS.CF_SLAVE)
	CfPosterWxMsgRel findByActivityIdAndThirdType(@Param("activityId") int activityId, @Param("thirdType") int thirdType,
	                                              @Param("msgType") int msgType);


}
