package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 * <AUTHOR>
 */
@DataSource(DS.CF)
public interface ApplyForFeeFreeDao {
    int add(@Param("fillPhone") String fillPhone, @Param("caseId") long caseId, @Param("caseInitiatedTimeDifference") int caseInitiatedTimeDifference,
            @Param("amountRaised") long amountRaised, @Param("organizationalRelationships") String organizationalRelationships, @Param("caseCreateTime") Date caseCreateTime);

}
