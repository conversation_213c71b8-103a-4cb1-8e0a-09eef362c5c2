package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfSuspectedCaseInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CfSuspectedCaseReportAttachment;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * Created by wangsf on 17/4/27.
 */

@DataSource(DS.CF)
public interface CfSuspectedCaseReportAttachmentDao {

	int insert(List<CfSuspectedCaseReportAttachment> attachments);

}
