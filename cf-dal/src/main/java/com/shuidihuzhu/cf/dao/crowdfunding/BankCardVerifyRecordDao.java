package com.shuidihuzhu.cf.dao.crowdfunding;

import com.shuidihuzhu.cf.enums.crowdfunding.BankCardVerifyStatus;
import com.shuidihuzhu.cf.model.crowdfunding.BankCardVerifyRecord;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Author: <PERSON> Wu Date: 16/9/9 19:54
 */
@DataSource(DS.CF)
public interface BankCardVerifyRecordDao {

	BankCardVerifyRecord get(int id);

	@DataSource(DS.CF_SLAVE)
	BankCardVerifyRecord getByThreeElements(@Param("cryptoHolderName") String cryptoHolderName,
			@Param("cryptoIdCard") String cryptoIdCard, @Param("cryptoBankCard") String cryptoBankCard,
			@Param("verifyStatus") BankCardVerifyStatus verifyStatus);

	int add(BankCardVerifyRecord bankCardVerifyRecord);

	@DataSource(DS.CF_SLAVE)
	List<BankCardVerifyRecord> listByVerifyStatus(@Param("verifyStatus") BankCardVerifyStatus verifyStatus,
			@Param("offset") int offset, @Param("limit") int limit);

    /**
     * 将 认证结果更新为认证通过
     *
     * @param holderName
     * @param cryptoIdCard
     * @param cryptoBankCard
     * @param cryptoMobile
     * @param caseId
     * @return
     */
    int updatePassed(@Param("holderName") String holderName, @Param("cryptoIdCard") String cryptoIdCard,
                     @Param("cryptoBankCard") String cryptoBankCard, @Param("cryptoMobile") String cryptoMobile,
                     @Param("caseId") int caseId);

	/**
	 * 将 认证结果更新为认证通过
	 *
	 * @param holderName
	 * @param cryptoIdCard
	 * @param cryptoBankCard
	 * @param caseId
	 * @return
	 */
	@DataSource(DS.CF_SLAVE)
	List<BankCardVerifyRecord> getByCaseIdAndElements(@Param("holderName") String holderName,
													  @Param("cryptoIdCard") String cryptoIdCard,
													  @Param("cryptoBankCard") String cryptoBankCard,
													  @Param("caseId") int caseId);
}
