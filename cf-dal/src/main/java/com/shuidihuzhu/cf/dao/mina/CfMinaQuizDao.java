package com.shuidihuzhu.cf.dao.mina;

import com.shuidihuzhu.cf.model.mina.CfMinaQuiz;
import com.shuidihuzhu.common.datasource.DS;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * Created by dongcf on 2017/12/12
 */
@DataSource(DS.FRAME)
public interface CfMinaQuizDao {

    @DataSource(DS.FRAME_SLAVE)
    List<CfMinaQuiz> findByAppId(@Param("appId") String appId, @Param("offset") int offset, @Param("limit") int limit);

    @DataSource(DS.FRAME_SLAVE)
    List<CfMinaQuiz> findByAppIdAndType(@Param("appId") String appId, @Param("showType") Integer showType, @Param("offset") int offset, @Param("limit") int limit);

    @DataSource(DS.FRAME_SLAVE)
    List<CfMinaQuiz> findByShowType(@Param("showType") Integer showType, @Param("offset") int offset, @Param("limit") int limit);

    @DataSource(DS.FRAME_SLAVE)
    Integer countByAppId(@Param("appId") String appId);

    @DataSource(DS.FRAME_SLAVE)
    CfMinaQuiz getById(@Param("id") Integer id);

    int updateTimesById(@Param("id") Integer id);

    @DataSource(DS.FRAME_SLAVE)
    List<CfMinaQuiz> findByTitle(@Param("title") String title, @Param("offset") Integer offset, @Param("limit") Integer pageSize);

    Integer countByTitle(@Param("title") String title);

    int insert(CfMinaQuiz cfMinaQuiz);

    CfMinaQuiz getBySortAndIsNew(@Param("isNew") Integer isNew, @Param("sort") Integer sort);

    Integer updateIsNewById(@Param("isNew") Integer isNew, @Param("id") Integer id);

    Integer updateSortById(@Param("sort") Integer isNew, @Param("id") Integer id);

    Integer updateReleaseTimeById(@Param("releaseTime") Date releaseTime, @Param("id") Integer id);

    Integer updateShowTypeById(@Param("showType") Integer showType, @Param("id") Integer id);

    Integer updateIsDeleteById(@Param("isDelete") Integer isDelete, @Param("id") Integer id);

    Integer updateInfoById(CfMinaQuiz cfMinaQuiz);
}
