<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>
    <artifactId>cf-biz</artifactId>
  <version>3.6.657-SNAPSHOT</version>

	<parent>
		<groupId>com.shuidihuzhu.cf</groupId>
		<artifactId>cf-api-parent</artifactId>
		<version>3.6.657-SNAPSHOT</version>
	</parent>

	<properties>
		<maven.source.skip>false</maven.source.skip>
	</properties>

    <dependencies>
        <dependency>
            <groupId>com.shuidihuzhu.account</groupId>
            <artifactId>account-relation-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.account</groupId>
            <artifactId>account-wecom-client</artifactId>
        </dependency>


        <dependency>
            <groupId>com.shuidihuzhu.data</groupId>
            <artifactId>servicelog-sdk-java</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.infra</groupId>
            <artifactId>mail-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.client</groupId>
            <artifactId>ai-push-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.ai-alps</groupId>
            <artifactId>ai-cupid-client</artifactId>
        </dependency>

        <dependency>
            <groupId>io.prometheus</groupId>
            <artifactId>simpleclient_pushgateway</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.ai-alps</groupId>
            <artifactId>ai-ocean-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-dal</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-rpc-delegate</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>xstream</artifactId>
                    <groupId>com.thoughtworks.xstream</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-context</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.baseservice</groupId>
            <artifactId>baseservice-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-all</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu</groupId>
            <artifactId>ds_util</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.hz</groupId>
            <artifactId>huzhu-client</artifactId>
        </dependency>
		<dependency>
            <groupId>com.shuidihuzhu.dataservice</groupId>
            <artifactId>dataservice-client</artifactId>
		</dependency>
        <dependency>
            <groupId>com.shuidihuzhu.data</groupId>
            <artifactId>servicelog-sdk-java</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.zookeeper</groupId>
                    <artifactId>zookeeper</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.data</groupId>
            <artifactId>servicelog-meta-cf</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.pf</groupId>
            <artifactId>pf-rpc-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.googlecode.aviator</groupId>
            <artifactId>aviator</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.eb</groupId>
            <artifactId>schedule-mq-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-client</artifactId>
            <version>${cf-client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.shuidihuzhu.common</groupId>
                    <artifactId>web-model</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.frame</groupId>
            <artifactId>frame-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.account</groupId>
            <artifactId>verifycode-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-finance-common-model</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-finance-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-finance-feign-client</artifactId>
        </dependency>

        <dependency>
            <groupId>org.skyscreamer</groupId>
            <artifactId>jsonassert</artifactId>
            <version>1.5.0</version>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.op</groupId>
            <artifactId>shuidi-auth-saas-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-enhancer-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.ai-service</groupId>
            <artifactId>ai-service-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.common</groupId>
            <artifactId>shuidi-wx-provider</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>xstream</artifactId>
                    <groupId>com.thoughtworks.xstream</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-material-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-event-center-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-ugc-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>shuidi-cipher</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.infra</groupId>
            <artifactId>spring-cloud-shuidi-deprecated</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.shuidihuzhu.msg</groupId>-->
<!--            <artifactId>msg-server-client</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-stat-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-activity-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.frame</groupId>
            <artifactId>charity-rpc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.msg</groupId>
            <artifactId>msg-rpc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.datawarehouse</groupId>
            <artifactId>shuidi-es-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.elasticsearch.plugin</groupId>
                    <artifactId>x-pack-sql-jdbc</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch</groupId>
            <artifactId>elasticsearch</artifactId>
        </dependency>

        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-high-level-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-wx-coupon-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.pay</groupId>
            <artifactId>pay-rpc-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.account</groupId>
            <artifactId>verify-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-api-pure-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.ai-algo</groupId>
            <artifactId>ai-service-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-store</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.infra</groupId>
            <artifactId>spring-cloud-starter-shuidi-cos</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qcloud</groupId>
            <artifactId>cos_api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.tencent.cloud</groupId>
            <artifactId>cos-sts-java</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-admin-api-pure-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuidihuzhu.cf</groupId>
            <artifactId>cf-tog-client</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok-mapstruct-binding</artifactId>
        </dependency>

    </dependencies>
</project>
