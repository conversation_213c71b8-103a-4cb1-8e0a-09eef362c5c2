package com.shuidihuzhu.cf.configuration;

import brave.Tracing;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.shuidihuzhu.cf.constants.AsyncPoolConstants;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * Created by sven on 2019/3/11.
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class AsyncConfiguration {

    @Bean("writeSimpleInfoPool")
    public Executor refreshUserInfoExecutor() {
        return getExecutor(AsyncPoolConstants.WRITE_SIMPLE_INFO_POOL, 5, 10);
    }

    @Autowired
    private Tracing tracing;

    private Executor getExecutor(String poolName, int coreSize, int maxSize) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(coreSize);
        executor.setMaxPoolSize(maxSize);
        executor.setQueueCapacity(1000);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix(poolName + "-");
        executor.setRejectedExecutionHandler(new RejectedExecutionHandler() {
            @Override
            public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                log.info("The {} is discarded", poolName);
            }
        });
        return executor;
    }

    @Bean(AsyncPoolConstants.SYNC_TOP_LIST_CHANGE_POOL)
    public Executor refreshTopListExecutor() {
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                10, 10, 1, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(), new ThreadFactoryBuilder().setNameFormat("syncTopListChangePool").build());
        return executor;
    }

    @Bean(AsyncPoolConstants.SYNC_VERIFICATE_TOP_LIST_CHANGE_POOL)
    public Executor refreshVerificateTopListExecutor() {
        return getExecutor(AsyncPoolConstants.SYNC_VERIFICATE_TOP_LIST_CHANGE_POOL, 10, 10);
    }

    @Bean(AsyncPoolConstants.ASYNC_CASE_DEPOSIT_POOL)
    public Executor caseDepositExecutor() {
        return getExecutor(AsyncPoolConstants.ASYNC_CASE_DEPOSIT_POOL, 10, 10);
    }

    @Bean(AsyncPoolConstants.ASYNC_VENUE_DONATE_POOL)
    public Executor venueDonateExecutor() {
        return getExecutor(AsyncPoolConstants.ASYNC_VENUE_DONATE_POOL, 10, 10);
    }

    @Bean(AsyncPoolConstants.SEND_PAY_SUCCESS_OLAP)
    public Executor casePayOlapExecutor() {
        return getExecutor(AsyncPoolConstants.SEND_PAY_SUCCESS_OLAP, 10, 20);
    }

    @Bean(AsyncPoolConstants.UPDATE_SUBSIDY_SHARE_ON_CREATE_ORDER)
    public Executor updateSubsidyShareOnCreateOrder() {
        return getExecutor(AsyncPoolConstants.UPDATE_SUBSIDY_SHARE_ON_CREATE_ORDER, 10, 20);
    }

    @Bean(AsyncPoolConstants.OLAP_THREAD_POOL)
    public Executor olapThreadPool() {
        return getExecutor(AsyncPoolConstants.UPDATE_SUBSIDY_SHARE_ON_CREATE_ORDER, 10, 20);
    }

    @Bean(AsyncPoolConstants.ORDER_ADD)
    public Executor orderAdd() {
        return getExecutor(AsyncPoolConstants.ORDER_ADD, 10, 20);
    }


    @Bean(AsyncPoolConstants.HOT_CASE)
    public Executor hotCase() {
        return getExecutor(AsyncPoolConstants.HOT_CASE, 5, 10);
    }

}
