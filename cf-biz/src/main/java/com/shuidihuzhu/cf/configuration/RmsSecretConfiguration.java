package com.shuidihuzhu.cf.configuration;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * rms secret 秘钥配置
 *
 * <AUTHOR>
 */
@Configuration
public class RmsSecretConfiguration {

    public static final String FAKE_ID_CONSUSION_V1 = "FAKE_ID_CONFUSION_V1";
    public static final String FAKE_ID_CONSUSION = "FAKE_ID_CONFUSION";

    @Setter
    @Getter
    public static class RmsSecret {
        private String value;
        private String key;
    }

    @Bean(FAKE_ID_CONSUSION_V1)
    @ConfigurationProperties(prefix = "rms.secret.fake.id.key-consusion-key-v1.cf-api")
    public RmsSecret fakeIdConfusionV1() {
        return new RmsSecret();
    }

    @Bean(FAKE_ID_CONSUSION)
    @ConfigurationProperties(prefix = "rms.secret.fake.id.key-consusion-key.cf-api")
    public RmsSecret fakeIdConfusion() {
        return new RmsSecret();
    }

}
