package com.shuidihuzhu.cf.configuration;

import com.shuidihuzhu.esdk.EsClient;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class EsConfig {

    public static final String CLUSTER_NAME = "es-common";
    public static final String ES_BEAN_NAME = "es-common-client";


    @Bean(ES_BEAN_NAME)
    @ConfigurationProperties("rms.es.cf-api.cf-api")
    public EsClient esClient() {
        return new EsClient();
    }
}
