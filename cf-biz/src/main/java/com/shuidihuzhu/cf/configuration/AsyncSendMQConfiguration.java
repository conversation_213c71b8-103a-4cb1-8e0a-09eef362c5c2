package com.shuidihuzhu.cf.configuration;

import com.shuidihuzhu.cf.constants.AsyncPoolConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * Created by z<PERSON><PERSON> on 2019/04/08
 */

@Slf4j
@Configuration
public class AsyncSendMQConfiguration {

    @Bean("sendCryptoRealtionMQPool")
    public Executor sendRelationMQExecutor() {
        return getExecutor(AsyncPoolConstants.SEND_CRYPTO_RELATION_MQ_POOL, 5, 10);
    }

    private Executor getExecutor(String poolName, int coreSize, int maxSize) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(coreSize);
        executor.setMaxPoolSize(maxSize);
        executor.setQueueCapacity(1000);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix(poolName + "-");
        executor.setRejectedExecutionHandler(new RejectedExecutionHandler() {
            @Override
            public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                log.info("The {} is discarded", poolName);
            }
        });
        return executor;
    }

}
