package com.shuidihuzhu.cf.duibasdk;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @package: com.shuidihuzhu.cf.duibasdk
 * @Author: liujiawei
 * @Date: 2018/9/3  14:51
 */

@AllArgsConstructor
@NoArgsConstructor
@Data
public class CreditOrderResult {

    private String errorMessage = "";
    private String data = "";

    public CreditOrderResult(boolean success, String bizId, String errorMessage) {
        if (success) {
            this.data = "'url':'https://www.shuidichou.com/mine/duiba-pay?bizId=" + bizId + "','bizid':'" + bizId + "'";
        } else {
            this.data = "'url':'https://www.shuidichou.com/mine/duiba-pay-fail','bizid':'" + bizId + "'";
            this.errorMessage = errorMessage;
        }
    }

    @Override
    public String toString() {
        return "{'status':'ok','errorMessage':'" + errorMessage + "','data':{" + data + "}}";
    }

}
