package com.shuidihuzhu.cf.duibasdk;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @package: com.shuidihuzhu.cf.duibasdk
 * @Author: liujiawei
 * @Date: 2018/9/3  14:24
 */

@ToString
@NoArgsConstructor
@AllArgsConstructor
@Data
public class OrderParams {
    private String appKey;//应用标示appKey
    private String cash;//订单金额
    private String uid="";//用户id
    private String bizId="";//内部订单号
    private String orderNum="";//兑吧订单号
    private Date timestamp;//时间戳,当前时间毫秒值
    public Map<String, String> toRequestMap(String appSecret){
        Map<String, String> map=new HashMap<String, String>();
        map.put("uid", uid);
        map.put("appKey", appKey);
        map.put("bizId", bizId);
        map.put("appSecret", appSecret);
        map.put("timestamp",  System.currentTimeMillis()+"");
        map.put("orderNum", orderNum);
        putIfNotEmpty(map, "cash", cash);
        String sign=SignTool.sign(map);
        map.remove("appSecret");
        map.put("sign", sign);
        return map;
    }

    private void putIfNotEmpty(Map<String, String> map,String key,String value){
        if(value==null || value.length()==0){
            return;
        }
        map.put(key, value);
    }
}
