package com.shuidihuzhu.cf.biz.crowdfunding;

import com.shuidihuzhu.cf.model.crowdfunding.CfInfoBlessing;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSimpleModel;

import java.util.List;

/**
 * Author: <PERSON>
 * Date: 2017/6/23 16:01
 */
public interface CfInfoBlessingBiz {

	CfInfoBlessing selectByUserIdOrSelfTag(CfInfoBlessing record);

	int blessing(CfInfoBlessing record, CfInfoSimpleModel simpleModel);

	int unBlessing(CfInfoBlessing record);

	List<CfInfoBlessing> getByInfoUUid(String infoUuid, int anchorId, int limit);

	List<CfInfoBlessing> selectBlessByUserId(long userId);

	int updateUserIdByIds(List<Integer> ids, long toUserId);
}
