package com.shuidihuzhu.cf.delegate.biapi.impl;

import com.shuidihuzhu.cf.delegate.biapi.BiApiClientDelegate;
import com.shuidihuzhu.client.dataservice.bi.v1.BiApiClient;
import com.shuidihuzhu.client.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

@Slf4j
@Service
public class BiApiClientDelegateImpl implements BiApiClientDelegate {

    @Resource
    private BiApiClient biApiClient;


    @Override
    public Optional<Long> queryUserNum(String portraitId) {
        if (StringUtils.isBlank(portraitId)) {
            return Optional.empty();
        }

        Response<Long> response = biApiClient.queryUserNum(portraitId);

        log.debug("biApiClient.queryUserNum portraitId:{}, response:{}", portraitId, response);
        if (response != null && response.getData() != null) {
            return Optional.of(response.getData());
        }

        return Optional.empty();
    }
}
