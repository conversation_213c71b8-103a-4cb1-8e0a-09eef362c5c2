package com.shuidihuzhu.cf.delegate.risk;

import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.risk.client.aegis.EngineAnalysisClient;
import com.shuidihuzhu.cf.risk.model.aegis.RiskAnalysisSceneDto;
import com.shuidihuzhu.cf.risk.model.aegis.RiskInfo;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class RiskEngineDelegate {

    private static final String TAG = "RiskEngineDelegate";

    @Resource
    private EngineAnalysisClient engineAnalysisClient;

    @Autowired
    private ApplicationService applicationService;

    public void handleOrderAdd(int caseId, long userId, Long orderId, String channel){
//        RiskAnalysisSceneDto v = new RiskAnalysisSceneDto();
//        long scene = applicationService.isDevelopment() ? 59L : 5L;
//        v.setScene(scene);
//        v.setReqId(caseId + userId + String.valueOf(System.currentTimeMillis()));
//        HashMap<String, Object> mapOrderAdd = Maps.newHashMap();
//        mapOrderAdd.put("caseId", caseId);
//        mapOrderAdd.put("userId", userId);
//        mapOrderAdd.put("channel", channel);
//        mapOrderAdd.put("orderId", orderId);
//
//        HashMap<String, Object> map = Maps.newHashMap();
//        map.put("orderAdd", mapOrderAdd);
//
//        v.setEventInfo(map);
//        Response<Map<String, RiskInfo>> response = engineAnalysisClient.analyzeScene(v);
//        log.info("{} data {}", TAG, response);
    }

}
