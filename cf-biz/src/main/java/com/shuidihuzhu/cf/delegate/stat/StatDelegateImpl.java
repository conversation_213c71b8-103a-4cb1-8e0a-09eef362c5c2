package com.shuidihuzhu.cf.delegate.stat;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.stat.enums.StatDataEnum;
import com.shuidihuzhu.cf.client.stat.feign.ICfStatClient;
import com.shuidihuzhu.cf.client.stat.model.CfStatResult;
import com.shuidihuzhu.cf.client.stat.model.CfUserCaseDonateShareInfo;
import com.shuidihuzhu.cf.enhancer.utils.HelpResponseUtils;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class StatDelegateImpl implements StatDelegate{


    @Resource
    private ICfStatClient cfStatClient;

    @Override
    public Optional<List<CfStatResult>> getResults(long userId, int caseId, List<StatDataEnum> dataEnums) {
        List<Integer> types = dataEnums.stream().map(StatDataEnum::getType).collect(Collectors.toList());
        RpcResult<List<CfStatResult>> listRpcResult = cfStatClient.listCfStatResult(types, userId, caseId);
        Response<List<CfStatResult>> listResponse = HelpResponseUtils.mapRpcResponse(listRpcResult);
        log.info("StatDelegateImpl dataEnum:{}, userId:{}, res:{}", dataEnums, userId, listResponse);
        if (listResponse.notOk()) {
            log.info("StatDelegateImpl fail");
            return Optional.empty();
        }
        List<CfStatResult> data = listResponse.getData();
        return Optional.ofNullable(data);
    }

    @Override
    public Optional<Long> getLong(long contextUserId, int caseId, StatDataEnum dataEnum) {
        Optional<List<CfStatResult>> results = getResults(contextUserId, caseId, Lists.newArrayList(dataEnum));
        if (results.isEmpty()) {
            return Optional.empty();
        }
        return getLong(results.get(), dataEnum);
    }

    @Override
    public Optional<Long> getLong(List<CfStatResult> results, StatDataEnum dataEnum) {
        return findValue(results, dataEnum, Long::valueOf);
    }

    @Override
    public Optional<Integer> getInt(long contextUserId, int caseId, StatDataEnum dataEnum) {
        Optional<List<CfStatResult>> results = getResults(contextUserId, caseId, Lists.newArrayList(dataEnum));
        if (results.isEmpty()) {
            return Optional.empty();
        }
        return getInt(results.get(), dataEnum);
    }

    @Override
    public Optional<Integer> getInt(List<CfStatResult> results, StatDataEnum dataEnum) {
        return findValue(results, dataEnum, Integer::valueOf);
    }

    @Override
    public Optional<String> getString(long contextUserId, int caseId, StatDataEnum dataEnum) {
        Optional<List<CfStatResult>> results = getResults(contextUserId, caseId, Lists.newArrayList(dataEnum));
        if (results.isEmpty()) {
            return Optional.empty();
        }
        return getString(results.get(), dataEnum);
    }

    @Override
    public Optional<String> getString(List<CfStatResult> results, StatDataEnum dataEnum) {
        return findValue(results, dataEnum, String::valueOf);
    }

    private <T> Optional<T> findValue(List<CfStatResult> results, StatDataEnum dataEnum, Function<String, T> valueOf) {
        for (CfStatResult s : results) {
            if (s.getType() == dataEnum.getType()) {
                return Optional.of(s).map(CfStatResult::getValue).map(valueOf);
            }
        }
        return Optional.empty();
    }

}
