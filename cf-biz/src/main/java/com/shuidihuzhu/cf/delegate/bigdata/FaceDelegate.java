package com.shuidihuzhu.cf.delegate.bigdata;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.client.dataservice.faceApi.v1.FaceApiClient;
import com.shuidihuzhu.client.model.TagExtDO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class FaceDelegate {

    @Autowired
    private FaceApiClient faceApiClient;
    private static final SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /**
     * https://wiki.shuiditech.com/pages/viewpage.action?pageId=767855480
     * 获取最近一次访问案例详情页时间
     * @param userId
     * @param caseId
     * @return
     */
    public Response<Date> getLastViewCaseDetailTime(Long userId, Integer caseId) {
        ParamLastViewCaseDetailTime v = new ParamLastViewCaseDetailTime();
        v.setDimension(27);
        v.setTagForeignName("cf_case_last_visit_case_time");
        HashMap<String, Object> extMap = Maps.newHashMap();
        extMap.put("caseId", "=," + caseId);
        v.setExtInfo(JSON.toJSONString(extMap));
        v.setUniqueKey(1);
        String properties = JSON.toJSONString(Lists.newArrayList(v));
        Map<TagExtDO, Object> resp = faceApiClient.uniformQuery(userId, null, null, properties, null, null);
        log.info("faceApiClient result getLastViewCaseDetailTime userId {}, caseId {}, resp {}", userId, caseId, resp);
        if (resp == null) {
            log.error("faceApiClient error getLastViewCaseDetailTime userId {}, caseId {}", userId, caseId);
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_ERROR);
        }
        Object o = resp.values().stream().findFirst().orElse(null);
        if (o == null) {
            log.info("faceApiClient data null getLastViewCaseDetailTime userId {}, caseId {}, resp {}", userId, caseId, resp);
            return NewResponseUtil.makeSuccess(null);
        }
        String s = String.valueOf(o);
        if (StringUtils.isBlank(s)) {
            log.info("faceApiClient data blank getLastViewCaseDetailTime userId {}, caseId {}, resp {}", userId, caseId, resp);
            return NewResponseUtil.makeSuccess(null);
        }
        Date date = null;
        try {
            date = parseDateTime(s);
        } catch (Exception e) {
            log.warn("getLastViewCaseDetailTime date {}, error {}", s, e);
        }

        return NewResponseUtil.makeSuccess(date);
    }

    public static void main(String[] args) throws ParseException {
        String s = "2021-03-15 12:35:56.669";
        Date date = parseDateTime(s);
        System.out.println("date = " + date);

        int caseId = 3168623;
        ParamLastViewCaseDetailTime v = new ParamLastViewCaseDetailTime();
        v.setDimension(27);
        v.setTagForeignName("cf_case_last_visit_case_time");
        HashMap<String, Object> extMap = Maps.newHashMap();
        extMap.put("caseId", caseId);
        v.setExtInfo(JSON.toJSONString(extMap));
        v.setUniqueKey(1);
        String properties = JSON.toJSONString(Lists.newArrayList(v));
        System.out.println("properties = " + properties);

    }

    public static Date parseDateTime(String str) throws ParseException {
        if (StringUtils.isBlank(str)) {
            return null;
        }
        return dateTimeFormat.parse(str);
    }

    @Data
    public static class ParamLastViewCaseDetailTime {
        private int dimension;
        private String extInfo;
        private String tagForeignName;
        private int uniqueKey;
    }
}
