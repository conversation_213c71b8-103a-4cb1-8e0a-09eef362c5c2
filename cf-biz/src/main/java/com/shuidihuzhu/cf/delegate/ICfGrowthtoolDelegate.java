package com.shuidihuzhu.cf.delegate;

import com.shuidihuzhu.client.cf.api.model.enums.ParamTypeEnum;
import com.shuidihuzhu.client.cf.growthtool.model.*;
import com.shuidihuzhu.client.cf.growthtool.model.lovepartner.CfPartnerCaseRelationDo;
import com.shuidihuzhu.client.cf.growthtool.model.lovepartner.CfPartnerInfoDo;
import com.shuidihuzhu.client.model.CheckUserScanOrInvitedResultModel;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;

/**
 * @author: wanghui
 * @create: 2019/7/23 4:23 PM
 */
public interface ICfGrowthtoolDelegate {
    Response registerAdd(CfTouFangSignDto cfTouFangSignDto);

    Response toutiaoRegister(CfTouFangSignDto cfTouFangSignDto);

    Response registerMobile(CfTouFangSignDto cfTouFangSignDto);

    Response registerByJson(String param);

    Response<CrowdfundingVolunteer> getVolunteerByUniqueCode(String uniqueCode);

    Integer syncInviteLog(Long currentUserId, Long sourceUserId);

    Integer insertVolunteerCreateCaseRecord(Long userId, String volunteerUnique);

    boolean checkIsVolunteerCreateCase(long userId);

    CheckUserScanOrInvitedResultModel checkUserScan(Long userId, String infoUuid);

    CheckUserScanOrInvitedResultModel checkUserIsInvite(Long userId, Integer checkUserScan);

    CheckUserScanOrInvitedResultModel checkUserIsInviteByCrowdfundUser(Long userId, Integer checkUserScan);

    void transferCfVolunteer(long fromUserId, long toUserId);

    CrowdfundingVolunteer getCfVolunteerDOByUniqueCode(String uniqueCode);

    CfVolunteerMaterialDO getVolunteerMateriByUniqueCode(String uniqueCode);

    String checkExistQrCodeAndReturnMobileByMobile(String mobile);

	void checkAndPushFangbiandaiForUser(String openId, String deviceId);

    List<BdCrmVolunteerOrgnizationSimpleModel> getBdCrmVolunteerOrgnizationSimpleModelByUniqueCode(String uniqueCode);

    int updateSaoMaNoClewCreateDesc(Integer scanId, String desc);

    CfPartnerInfoDo getCfPartnerInfoByUniqueCode(String uniqueCode);

    Integer createPartnerCaseRelation(CfPartnerCaseRelationDo cfPartnerCaseRelationDo);

    CfVolunteerSimpleVo getVolunteerSimpleByUniqueCode(String volunteerUniqueCode);
}
