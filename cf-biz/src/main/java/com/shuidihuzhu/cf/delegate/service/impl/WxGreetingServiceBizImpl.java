package com.shuidihuzhu.cf.delegate.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.delegate.service.WxGreetingServiceBiz;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.wx.grpc.client.common.GreetingMatchRule;
import com.shuidihuzhu.wx.grpc.client.feign.WxGreetingServiceClient;
import com.shuidihuzhu.wx.grpc.enums.GreetingMatchRuleEnum;
import com.shuidihuzhu.wx.grpc.model.GreetingThirdTypeAttach;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/5/9 2:54 PM
 */
@Slf4j
@Service
@RefreshScope
public class WxGreetingServiceBizImpl implements WxGreetingServiceBiz {

    @Resource
    private WxGreetingServiceClient wxGreetingServiceClient;

    @Override
    public Response<Boolean> sendGreetingAsync(String openId, String key, int thirdType) {
        return wxGreetingServiceClient.sendGreetingAsync(openId, key, thirdType);
    }

    @Override
    public List<GreetingThirdTypeAttach> getGreeting(int thirdType, String eventKey, GreetingMatchRule greetingMatchRule) {
        return wxGreetingServiceClient.getGreeting(thirdType, eventKey, GreetingMatchRuleEnum.of(greetingMatchRule.getNumber()));
    }

}
