package com.shuidihuzhu.cf.delegate;

import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.BatchUserIdGetParam;
import com.shuidihuzhu.account.model.UserRpcResponse;
import com.shuidihuzhu.account.model.po.WxUserDetailSimplifyPo;
import com.shuidihuzhu.account.model.wx.WxMpSubscribeModel;
import com.shuidihuzhu.client.grpc.account.v1.feign.WxUserDetailServiceClient;
import com.shuidihuzhu.client.service.UserEventWhaleServicce;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023-05-16 8:12 PM
 **/
@Component
public class WxUserDetailDelegate {

    @Resource
    private WxUserDetailServiceClient wxUserDetailServiceClient;

    @Resource
    private UserEventWhaleServicce userEventWhaleServicce;

    public WxUserDetailServiceClient getClient() {
        return wxUserDetailServiceClient;
    }

    public void subscribe(String openId, int thirdType) {
        getClient().subscribe(openId, thirdType);
    }

    public void unsubscribe(String openId) {
        getClient().unsubscribe(openId);
    }


    @Deprecated
    public List<WxUserDetailSimplifyPo> getSimplifyWxUserDetail(HashSet<Long> userIds, List<Integer> thirdTypeList, boolean needMobile) {
        return getClient().getSimplifyWxUserDetail(userIds, thirdTypeList, needMobile);
    }

    public List<WxMpSubscribeModel> listByUserIdsAndTypes(Collection<Long> userIds, List<Integer> thirdTypeList) {
        return  userEventWhaleServicce.listByUserIdsAndTypes(Lists.newArrayList(userIds), thirdTypeList);
    }
}
