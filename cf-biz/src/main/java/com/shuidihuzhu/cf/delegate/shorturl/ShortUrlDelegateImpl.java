package com.shuidihuzhu.cf.delegate.shorturl;

import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.client.baseservice.zqurl.v1.TokenLinkClient;
import com.shuidihuzhu.client.baseservice.zqurl.v1.model.UrlMatch;
import com.shuidihuzhu.client.baseservice.zqurl.v1.param.ZqurlParam;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * 短链服务封装
 */
@Slf4j
@Service
public class ShortUrlDelegateImpl implements ShortUrlDelegate {

    @Autowired
    private TokenLinkClient tokenLinkClient;

    @Override
    public String process(String url) {
        String r = p(url);
        if (r == null) {
            return url;
        }
        return r;
    }

    @Override
    public String processWithException(String url) throws ShortUrlException {
        String r = p(url);
        if (r == null) {
            throw new ShortUrlException();
        }
        return r;
    }

    @Override
    public String processNullable(String url) {
        return p(url);
    }

    @Nullable
    private String p(String url) {
        try {
            ZqurlParam zqurlParam = new ZqurlParam(url, (Date) null);
            zqurlParam.setDomainKey("cf");
            com.shuidihuzhu.client.baseservice.zqurl.v1.model.Response<UrlMatch> req = tokenLinkClient.createOne(zqurlParam);
            if (req.notOk()) {
                log.error("zqurl-api service has error ; error code {} , error msg {}", req.getCode(), req.getMsg());
                return null;
            }
            log.info("短链 result {}", JSONObject.toJSON(req));
            UrlMatch data = req.getData();
            if (data == null) {
                return null;
            }
            return data.getZqUrl();
        } catch (Exception e) {
            log.error("ShortUrlDelegate error msg url: {}", url, e);
            return null;
        }
    }
}
