package com.shuidihuzhu.cf.delegate.impl;

import com.shuidihuzhu.cf.delegate.IPayDelegate;
import com.shuidihuzhu.client.baseservice.pay.enums.BizType;
import com.shuidihuzhu.client.baseservice.pay.model.CallbackResult;
import com.shuidihuzhu.client.baseservice.pay.model.PayRpcResponse;
import com.shuidihuzhu.client.baseservice.pay.v1.PayCallBackClientV1;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2018-07-09  20:21
 */
@Service
@Slf4j
public class PayDelegateImpl implements IPayDelegate {

    @Resource
    private PayCallBackClientV1 payCallBackClientV1;

    @Override
    public CallbackResult getCallbackResultByOrderId(String orderId) {
        CallbackResult callbackResult = getCallbackResultByOrderIdAndBiz(orderId, BizType.HEALTH_SECURITY);
        if (callbackResult == null) {
            callbackResult = getCallbackResultByOrderIdAndBiz(orderId, BizType.CF_FUND);
        }
        if (callbackResult == null) {
            callbackResult = getCallbackResultByOrderIdAndBiz(orderId, BizType.WISH);
        }
        if (callbackResult == null) {
            callbackResult = getCallbackResultByOrderIdAndBiz(orderId, BizType.CF);
        }
        if (callbackResult == null) {
            callbackResult = getCallbackResultByOrderIdAndBiz(orderId, BizType.CF_DREAM);
        }
        if (callbackResult == null) {
            log.warn("{}", orderId);
        }
        return callbackResult;
    }

    /**
     * 根据业务订单id和bizTye获取支付回调
     * @param orderId
     * @param bizType
     * @return
     */
    private CallbackResult getCallbackResultByOrderIdAndBiz(String orderId, BizType bizType){
        log.info("orderId: {}, bizType: {}", orderId, bizType);

        PayRpcResponse<CallbackResult> callbackResultPayRpcResponse = payCallBackClientV1.getByOrderId(orderId, bizType);

        log.info("orderId: {}, bizType: {}, callbackResultPayRpcResponse: {}",
                orderId, bizType, callbackResultPayRpcResponse);

        if (!callbackResultPayRpcResponse.isSuccess()) {
            return null;
        }
        return callbackResultPayRpcResponse.getResult();
    }
}
