package com.shuidihuzhu.cf.delegate.clew;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackFeignClient;
import com.shuidihuzhu.client.cf.clewtrack.model.ClewCallRecordModel;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ClewCallDelegate {

    @Autowired
    private CfClewtrackFeignClient cfClewtrackFeignClient;

    @Nullable
    public ClewCallRecordModel getByUniqueId(String callUniqueId) {
        Response<List<ClewCallRecordModel>> listResp =
                cfClewtrackFeignClient.getClewCallRecordsByUniqueIds(Lists.newArrayList(callUniqueId));
        if (listResp == null ||
                listResp.getCode() != 0 ||
                listResp.getData() == null ||
                CollectionUtils.isEmpty(listResp.getData())
        ) {
            log.error("getByUniqueId resp error callUniqueId:{}, response:{}", callUniqueId, JSON.toJSON(listResp));
            return null;
        }
        return listResp.getData().get(0);
    }
}
