package com.shuidihuzhu.cf.delegate;

import com.shuidihuzhu.cf.model.clewtrack.ClewReceiveModel;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.client.cf.growthtool.model.lovepartner.CfPartnerInfoDo;
import com.shuidihuzhu.msg.model.SmsRecord;

/**
 * 统一处理与筹款登记相关交互的面板
 */
public interface IRegisterClewFacade {
    /**
     * 登记成功后，延时30分钟发送短信提醒
     * @param smsRecord
     */
    void sendRegisterSuccessDelayMessage(SmsRecord smsRecord);

    /**
     * 登记成功之后，发送登记线索消息给线索系统(cf-clewtrack-api)
     * @return
     */
    OpResult<ClewReceiveModel> sendNewClewMsg(ClewReceiveModel clewReceiveModel);

    /**
     * //  用户扫描志愿者二维码发起后  通知cf-clewtrack 建立bdcrm线索
     * @param crowdfundingInfo
     * @param volunteerUniqueCode
     * @param clientIp
     * @param cfPartnerInfoDo
     * @return
     */
    OpResult sendMq2CfClewtrack(CrowdfundingInfo crowdfundingInfo, String volunteerUniqueCode, String clientIp, CfPartnerInfoDo cfPartnerInfoDo);

    /**
     * 用户扫描志愿者二维码  通知cf-clewtrack 建立bdcrm线索 , 带上channel
     * @param userId
     * @param mobile
     * @param volunteerUniqueCode
     * @param channel
     */
    OpResult sendMq2CfClewtrackUseChannel(long userId, String mobile, String volunteerUniqueCode, String channel,String scanId,String scanTime, CfPartnerInfoDo cfPartnerInfoDo);
}
