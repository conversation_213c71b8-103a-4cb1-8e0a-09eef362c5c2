package com.shuidihuzhu.cf.delegate.stat;

import com.shuidihuzhu.cf.client.stat.enums.StatDataEnum;
import com.shuidihuzhu.cf.client.stat.model.CfStatResult;
import com.shuidihuzhu.cf.client.stat.model.CfUserCaseDonateShareInfo;
import com.shuidihuzhu.common.web.model.Response;

import javax.annotation.Nullable;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface StatDelegate {

    Optional<List<CfStatResult>> getResults(long contextUserId, int caseId, List<StatDataEnum> dataEnum);

    Optional<Long> getLong(long contextUserId, int caseId, StatDataEnum dataEnum);

    Optional<Long> getLong(List<CfStatResult> results, StatDataEnum dataEnum);

    Optional<Integer> getInt(long contextUserId, int caseId, StatDataEnum dataEnum);

    Optional<Integer> getInt(List<CfStatResult> results, StatDataEnum dataEnum);

    Optional<String> getString(long contextUserId, int caseId, StatDataEnum dataEnum);

    Optional<String> getString(List<CfStatResult> results, StatDataEnum dataEnum);
}
