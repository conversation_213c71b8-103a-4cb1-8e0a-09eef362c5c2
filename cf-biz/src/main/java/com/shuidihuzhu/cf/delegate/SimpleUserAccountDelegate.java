package com.shuidihuzhu.cf.delegate;

import com.shuidihuzhu.account.model.service.MobileUserIdModel;
import com.shuidihuzhu.account.model.service.OpenIdUserIdModel;
import com.shuidihuzhu.client.grpc.account.v1.feign.SimpleUserAccountServiceClient;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.cf.enhancer.utils.AlarmBotService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-05-16 8:01 PM
 **/
@Slf4j
@Component
public class SimpleUserAccountDelegate {

    @Resource
    private SimpleUserAccountServiceClient simpleUserAccountServiceClient;

    @Resource
    private ApplicationService applicationService;

    private SimpleUserAccountServiceClient getClient() {
        return this.simpleUserAccountServiceClient;
    }

    public OpenIdUserIdModel getUserIdByOpenId(String openId) {
        OpenIdUserIdModel result = getClient().getUserIdByOpenId(openId);
        if (result == null || result.getUserId() <= 0) {
            String temp = "【openId查询用户信息失败提醒】\n" + "openId：%s";
            String content = String.format(temp, openId);
            sentText(content);
        }
        return result;
    }

    public List<MobileUserIdModel> getUserIdsByMobiles(List<String> mobiles) {
        return getClient().getUserIdsByMobiles(mobiles);
    }

    public MobileUserIdModel getUserIdByMobile(String mobile) {
        return getClient().getUserIdByMobile(mobile);
    }

    public List<OpenIdUserIdModel> getUserIdsByOpenIds(List<String> openIdList) {
        return getClient().getUserIdsByOpenIds(openIdList);
    }

    public void sentText(String content) {
        if (applicationService.isDevelopment()) {
            content = "【测试环境】\n" + content;
        }
        AlarmBotService.sentText("195f1864-b753-4dda-8b15-88e9b78a8e86", content, null, null);
    }
}