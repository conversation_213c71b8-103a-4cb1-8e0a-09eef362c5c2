package com.shuidihuzhu.cf.delegate.finance.impl;

import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixProperty;
import com.shuidihuzhu.cf.delegate.finance.IFinanceFeignClientV2Delegate;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceFeignClientV2;
import com.shuidihuzhu.cf.finance.client.response.model.FeignResponse;
import com.shuidihuzhu.cf.finance.model.vo.CfFinanceCapitalStatusVo;
import com.shuidihuzhu.cf.finance.model.vo.DonationAmountInFenVo;
import com.shuidihuzhu.client.cf.risk.model.CfRiskQueryOperateParam;
import com.shuidihuzhu.client.cf.risk.model.result.UserOperatorValidMultiUnit;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

/**
 * @Author: lianghongchao
 * @Date: 2020/5/26 17:41
 */
@Service
@Slf4j
@RefreshScope
public class FinanceFeignClientV2Delegate implements IFinanceFeignClientV2Delegate {
	@Autowired
	private CfFinanceFeignClientV2 cfFinanceFeignClientV2;
	@Value("${apollo.finance.status-new:true}")
	private boolean financeStatusNew;

	@Override
	public Response<DonationAmountInFenVo> getDonationAmountInFen(String infoUuid) {
		if (StringUtils.isEmpty(infoUuid)) {
			return NewResponseUtil.makeFail("参数为空");
		}
		try {
			FeignResponse<DonationAmountInFenVo> fenVoFeignResponse = cfFinanceFeignClientV2.getDonationAmountInFen(infoUuid);
			if (fenVoFeignResponse == null || fenVoFeignResponse.notOk()) {
				log.warn("infoUuid:{} 查询异常", infoUuid);
				return NewResponseUtil.makeFail("获取失败");
			}
			if (null == fenVoFeignResponse.getData()) {
				log.warn("infoUuid:{} 查询异常，结果为null", infoUuid);
				return NewResponseUtil.makeFail("获取成功，但结果为 null");
			}
			return NewResponseUtil.makeSuccess(fenVoFeignResponse.getData());
		} catch (Exception e) {
			log.error("finance-feign-api服务异常,infoUuid:{}", infoUuid, e);
		}
		return NewResponseUtil.makeFail("查询异常");
	}

	@Override
	public Response<CfFinanceCapitalStatusVo> getFinanceCapitalStatusV2(String infoUuid, int caseId) {
		if (StringUtils.isEmpty(infoUuid) || caseId <= 0) {
			return NewResponseUtil.makeFail("参数为空");
		}
		try {
			FeignResponse<CfFinanceCapitalStatusVo> fenVoFeignResponse = cfFinanceFeignClientV2.getFinanceCapitalStatusV2(
					infoUuid, caseId);
			if (fenVoFeignResponse == null || fenVoFeignResponse.notOk()) {
				log.warn("infoUuid:{} 查询异常", infoUuid);
				return NewResponseUtil.makeFail("获取失败");
			}
			if (null == fenVoFeignResponse.getData()) {
				log.warn("infoUuid:{} 查询异常，结果为null", infoUuid);
				return NewResponseUtil.makeFail("获取成功，但结果为 null");
			}
			return NewResponseUtil.makeSuccess(fenVoFeignResponse.getData());
		} catch (Exception e) {
			log.error("finance-feign-api服务异常,infoUuid:{}", infoUuid, e);
		}
		return NewResponseUtil.makeFail("查询异常");
	}

	@Override
	@Deprecated
	public Response<CfFinanceCapitalStatusVo> getFinanceCapitalStatusV1(String infoUuid) {
		if (StringUtils.isEmpty(infoUuid)) {
			return NewResponseUtil.makeFail("参数为空");
		}
		try {
			FeignResponse<CfFinanceCapitalStatusVo> fenVoFeignResponse = cfFinanceFeignClientV2.getFinanceCapitalStatus(infoUuid);
			if (fenVoFeignResponse == null || fenVoFeignResponse.notOk()) {
				log.warn("infoUuid:{} 查询异常", infoUuid);
				return NewResponseUtil.makeFail("获取失败");
			}
			if (null == fenVoFeignResponse.getData()) {
				log.warn("infoUuid:{} 查询异常，结果为null", infoUuid);
				return NewResponseUtil.makeFail("获取成功，但结果为 null");
			}
			return NewResponseUtil.makeSuccess(fenVoFeignResponse.getData());
		} catch (Exception e) {
			log.error("finance-feign-api服务异常,infoUuid:{}", infoUuid, e);
		}
		return NewResponseUtil.makeFail("查询异常");
	}

	@Override
	public Response<CfFinanceCapitalStatusVo> getFinanceCapitalStatusV1(String infoUuid, int caseId) {
		if (financeStatusNew) {
			return this.getFinanceCapitalStatusV2(infoUuid, caseId);
		} else {
			return this.getFinanceCapitalStatusV1(infoUuid);
		}
	}
}
