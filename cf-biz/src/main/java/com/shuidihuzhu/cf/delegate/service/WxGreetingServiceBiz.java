package com.shuidihuzhu.cf.delegate.service;

import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.wx.grpc.client.common.GreetingMatchRule;
import com.shuidihuzhu.wx.grpc.model.GreetingThirdTypeAttach;

import java.util.List;

/**
 * @Description:
 * @Author: panghair<PERSON>
 * @Date: 2023/5/9 2:53 PM
 */
public interface WxGreetingServiceBiz {

    Response<Boolean> sendGreetingAsync(String openId, String key, int thirdType);

    List<GreetingThirdTypeAttach> getGreeting(int thirdType, String eventKey, GreetingMatchRule greetingMatchRule);

}
