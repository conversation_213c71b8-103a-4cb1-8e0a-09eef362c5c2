package com.shuidihuzhu.cf.delegate.finance.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.delegate.finance.IFinanceDrawCashFeignDelegate;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.CfDrawCashConstant;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceDrawCashFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.enums.CfDrawCashCommonEnum;
import com.shuidihuzhu.cf.finance.model.CfReminderWord;
import com.shuidihuzhu.cf.finance.model.drawcash.CfDrawExpectCapitationModel;
import com.shuidihuzhu.cf.finance.model.finance.drawcash.CfDrawCashApplyV2;
import com.shuidihuzhu.cf.finance.model.vo.CfDrawCashApplyVo;
import com.shuidihuzhu.cf.finance.model.vo.DrawApplyCheckResult;
import com.shuidihuzhu.cf.finance.model.vo.drawcash.CfApplyDrawWithAuditVo;
import com.shuidihuzhu.cf.finance.model.vo.drawcash.CrowdfundingInfoApplyDrawVo;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.model.AnchorPageVO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: wanghui
 * @create: 2019/6/12 5:03 PM
 */
@Slf4j
@Service
@RefreshScope
public class FinanceDrawCashFeignDelegate implements IFinanceDrawCashFeignDelegate {
	@Autowired
	private CfFinanceDrawCashFeignClient cfFinanceDrawCashFeignClient;
	@Autowired
	private ShuidiCipher shuidiCipher;

	@Override
	public Response<Map<Integer, DrawApplyCheckResult>> applyCheckList(List<Integer> infoIdList, boolean checkEndTime) {
		if (CollectionUtils.isEmpty(infoIdList)) {
			return NewResponseUtil.makeSuccess(Maps.newHashMap());
		}
		try {
			FeignResponse<Map<Integer, DrawApplyCheckResult>> response = cfFinanceDrawCashFeignClient.applyCheckList(infoIdList,
					checkEndTime);
			if (response == null || response.notOk()) {
				log.error("finance服务异常");
				return NewResponseUtil.makeError(CfErrorCode.USER_IN_BLACKLIST, Maps.newHashMap());
			}
			return NewResponseUtil.makeSuccess(response.getData());
		} catch (Exception e) {
			log.error("finance服务异常,", e);
			return NewResponseUtil.makeError(CfErrorCode.USER_IN_BLACKLIST, Maps.newHashMap());
		}
	}

	@Override
	public Response<CfDrawCashApplyVo> getApplyInfo(int caseId) {
		try {
			FeignResponse<CfDrawCashApplyVo> response = cfFinanceDrawCashFeignClient.getApplyInfo(caseId);
			log.debug("caseId:{} response:{}", caseId, JSON.toJSONString(response));
			if (response == null || response.notOk()) {
				log.error("finance服务异常");
				return NewResponseUtil.makeError(CfErrorCode.USER_IN_BLACKLIST, null);
			} else {
				return NewResponseUtil.makeSuccess(response.getData());
			}
		} catch (Exception e) {
			log.error("finance服务异常,", e);
			return NewResponseUtil.makeError(CfErrorCode.USER_IN_BLACKLIST, null);
		}
	}

	@Override
	public Response<Map<Integer, CfDrawCashApplyVo>> getApplyInfoMap(List<Integer> caseIds) {
		if (CollectionUtils.isEmpty(caseIds)) {
			return NewResponseUtil.makeSuccess(Maps.newHashMap());
		}
		try {
			FeignResponse<List<CfDrawCashApplyVo>> response = cfFinanceDrawCashFeignClient.getApplyInfoList(caseIds);
			if (response == null || response.notOk()) {
				log.error("finance服务异常");
				return NewResponseUtil.makeError(CfErrorCode.USER_IN_BLACKLIST, Maps.newHashMap());
			}
			List<CfDrawCashApplyVo> cfDrawCashApplyVoList = response.getData();
			Map<Integer, CfDrawCashApplyVo> drawCashApplyVoMap = cfDrawCashApplyVoList.stream()
					.collect(Collectors.toMap(CfDrawCashApplyVo::getCaseId, Function.identity(), (a, b) -> b));
			return NewResponseUtil.makeSuccess(drawCashApplyVoMap);
		} catch (Exception e) {
			log.error("finance服务异常,", e);
			return NewResponseUtil.makeError(CfErrorCode.USER_IN_BLACKLIST, Maps.newHashMap());
		}
	}

	@Override
	public Response<DrawApplyCheckResult> drawSplitApplyDrawCash(CrowdfundingInfoApplyDrawVo crowdfundingInfoApplyDrawVo) {
		try {
			FeignResponse<DrawApplyCheckResult> response = cfFinanceDrawCashFeignClient.drawSplitApplyDrawCash(crowdfundingInfoApplyDrawVo);
			if (response == null) {
				log.error("finance服务异常");
				return NewResponseUtil.makeError(CfErrorCode.USER_IN_BLACKLIST);
			}
			return NewResponseUtil.makeResponse(response.getCode(), response.getMsg(), response.getData());
		} catch (Exception e) {
			log.error("finance服务异常,", e);
			return NewResponseUtil.makeError(CfErrorCode.USER_IN_BLACKLIST);
		}
	}

	public Response<List<CfDrawCashApplyV2>> getCaseApplyList(String infoUuid) {
		if (StringUtils.isEmpty(infoUuid)) {
			return NewResponseUtil.makeSuccess(Lists.newArrayList());
		}
		try {
			FeignResponse<List<CfDrawCashApplyV2>> feignResponse = cfFinanceDrawCashFeignClient.getCaseApplyList(infoUuid);
			if (feignResponse == null || feignResponse.notOk()) {
				log.error("finance服务异常 infoUuid:{}", infoUuid);
				return NewResponseUtil.makeError(CfErrorCode.USER_IN_BLACKLIST, Lists.newArrayList());
			}
			return NewResponseUtil.makeSuccess(feignResponse.getData());
		} catch (Exception e) {
			log.error("finance服务异常,infoUuid:{}", infoUuid, e);
			return NewResponseUtil.makeError(CfErrorCode.USER_IN_BLACKLIST, Lists.newArrayList());
		}
	}

	@Override
	public Response<CfApplyDrawWithAuditVo> getLastValidApply(String infoUuid) {
		try {
			Response<List<CfDrawCashApplyV2>> response = this.getCaseApplyList(infoUuid);
			List<CfDrawCashApplyV2> drawCashApplyV2List = response.getData();
			if (CollectionUtils.isEmpty(drawCashApplyV2List)) {
				return NewResponseUtil.makeSuccess(null);
			}

			CfDrawCashApplyV2 cfDrawCashApplyV2 = drawCashApplyV2List.stream()
					.filter(item -> item.getIsClose() == CfDrawCashCommonEnum.CloseStatusEnum.UN_CLOSE.getCode())
					.max(Comparator.comparing(CfDrawCashApplyV2::getId)).orElse(null);
			if (cfDrawCashApplyV2 == null) {
				return NewResponseUtil.makeSuccess(null);
			}
			return NewResponseUtil.makeSuccess(convertToDrawWithAuditVo(cfDrawCashApplyV2));
		} catch (Exception e) {
			log.error("infoUuid:{}", infoUuid, e);
		}
		return null;
	}

	private CfApplyDrawWithAuditVo convertToDrawWithAuditVo(CfDrawCashApplyV2 cfDrawCashApplyV2) {
		CfApplyDrawWithAuditVo cfApplyDrawWithAuditVo = new CfApplyDrawWithAuditVo();
		// caseId 忽略
		cfApplyDrawWithAuditVo.setInfoUuid(cfDrawCashApplyV2.getInfoUuid());
		cfApplyDrawWithAuditVo.setTraceNo(cfDrawCashApplyV2.getTraceNo());
		cfApplyDrawWithAuditVo.setDrawCashType(cfDrawCashApplyV2.getDrawCashType());

		cfApplyDrawWithAuditVo.setStopCfReason(cfDrawCashApplyV2.getStopCfReason());
		cfApplyDrawWithAuditVo.setUseOfFunds(cfDrawCashApplyV2.getUseOfFunds());
		cfApplyDrawWithAuditVo.setPatientConditionNow(cfDrawCashApplyV2.getPatientConditionNow());
		// 用户填写的图片
		if (CollectionUtils.isNotEmpty(cfDrawCashApplyV2.getUrlAddressIdList())) {
			List<Map<Integer, String>> urlAddressIdList = cfDrawCashApplyV2.getUrlAddressIdList();
			List<String> attachments = Lists.newArrayList();
			for (Map<Integer, String> entry : urlAddressIdList) {
				attachments.addAll(entry.values());
			}
			cfApplyDrawWithAuditVo.setAttachments(attachments);
		} else {
			cfApplyDrawWithAuditVo.setAttachments(Lists.newArrayList());
		}
		cfApplyDrawWithAuditVo.setPatientRegion(cfDrawCashApplyV2.getPatientRegion());
		cfApplyDrawWithAuditVo.setPatientAddress(cfDrawCashApplyV2.getPatientAddress());
		cfApplyDrawWithAuditVo.setEmergencyContactName(cfDrawCashApplyV2.getEmergencyContactName());
		cfApplyDrawWithAuditVo.setEmergencyContactPhone(shuidiCipher.decrypt(cfDrawCashApplyV2.getEmergencyContactPhone()));
		cfApplyDrawWithAuditVo.setEmergencyContactRelation(cfDrawCashApplyV2.getEmergencyContactRelation());

		cfApplyDrawWithAuditVo.setDrawCashAmount(cfDrawCashApplyV2.getAmount());
		cfApplyDrawWithAuditVo.setDeadlineOrderTime(cfDrawCashApplyV2.getDeadlineOrderTime());
		cfApplyDrawWithAuditVo.setMedicalBillList(cfDrawCashApplyV2.getMedicalBillList());

		// 审核状态等
		cfApplyDrawWithAuditVo.setApplyStatus(CfDrawCashConstant.ApplyStatus.getByCode(cfDrawCashApplyV2.getApplyStatus()));
		cfApplyDrawWithAuditVo.setApplyTime(cfDrawCashApplyV2.getApplyTime());
		cfApplyDrawWithAuditVo.setAuditTime(cfDrawCashApplyV2.getAuditTime());
		cfApplyDrawWithAuditVo.setReason(cfDrawCashApplyV2.getReason());
		return cfApplyDrawWithAuditVo;
	}

	/**
	 * 计算提现预期到账金额
	 *
	 * @param caseId
	 * @param applyAmountInFen
	 * @param applyTime
	 * @return
	 */
	@Override
	public CfReminderWord<CfDrawExpectCapitationModel> getExpectDrawAmountInfo(int caseId, int applyAmountInFen, long applyTime) {
		FeignResponse<CfReminderWord<CfDrawExpectCapitationModel>> response =
				cfFinanceDrawCashFeignClient.getExpectDrawAmountInfo(caseId, applyAmountInFen, applyTime);
		return Optional.ofNullable(response)
				.filter(FeignResponse::ok)
				.map(FeignResponse::getData)
				.orElse(new CfReminderWord<>("系统繁忙，请稍后再试"));
	}

	@Override
	public Response<CfDrawCashApplyVo> getRecentApplyInfo(int caseId) {
		try {
			FeignResponse<CfDrawCashApplyVo> response = cfFinanceDrawCashFeignClient.getRecentApplyInfo(caseId);
			log.debug("caseId:{} response:{}", caseId, JSON.toJSONString(response));
			if (response == null || response.notOk()) {
				log.error("finance服务异常");
				return NewResponseUtil.makeError(CfErrorCode.USER_IN_BLACKLIST, null);
			} else {
				CfDrawCashApplyVo applyVo = response.getData();
				if (Objects.nonNull(applyVo)) {
					applyVo.setCaseId(0);
				}
				return NewResponseUtil.makeSuccess(applyVo);
			}
		} catch (Exception e) {
			log.error("finance服务异常,", e);
			return NewResponseUtil.makeError(CfErrorCode.USER_IN_BLACKLIST, null);
		}
	}

	@Override
	public Response<AnchorPageVO<CfDrawCashApplyVo>> getDrawLifeCycle(int caseId, long applyId, int pageSize) {
		try {
			FeignResponse<AnchorPageVO<CfDrawCashApplyVo>> response = cfFinanceDrawCashFeignClient.getDrawLifeCycle(caseId, applyId, pageSize);
			log.debug("caseId:{} response:{}", caseId, JSON.toJSONString(response));
			if (response == null || response.notOk()) {
				log.error("finance服务异常");
				return NewResponseUtil.makeError(CfErrorCode.USER_IN_BLACKLIST, null);
			} else {
				AnchorPageVO<CfDrawCashApplyVo> applyVoAnchorPageVO = response.getData();
				if (Objects.nonNull(applyVoAnchorPageVO) && CollectionUtils.isNotEmpty(applyVoAnchorPageVO.getList())) {
					applyVoAnchorPageVO.getList().forEach(r -> r.setCaseId(0));
				}
				return NewResponseUtil.makeSuccess(applyVoAnchorPageVO);
			}
		} catch (Exception e) {
			log.error("finance服务异常,", e);
			return NewResponseUtil.makeError(CfErrorCode.USER_IN_BLACKLIST, null);
		}
	}
}
