package com.shuidihuzhu.cf.delegate.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.delegate.IRegisterClewFacade;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.model.clewtrack.ClewReceiveModel;
import com.shuidihuzhu.cf.model.clewtrack.ScanQRCodeNoticeEnum;
import com.shuidihuzhu.cf.model.clewtrack.ScanQRCodeNoticeModel;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.mq.producer.CommonMessageHelperService;
import com.shuidihuzhu.cf.mq.producer.IClewTrackProducer;
import com.shuidihuzhu.cf.mq.producer.MessageBuilder;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.client.cf.growthtool.model.lovepartner.CfPartnerInfoDo;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import com.shuidihuzhu.msg.model.SmsRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
@RefreshScope
public class RegisterClewFacadeImpl implements IRegisterClewFacade {
    @Autowired
    private CommonMessageHelperService commonMessageHelperService;

    @Autowired(required = false)
    private IClewTrackProducer clewTrackProducer;
    @Autowired
    ApplicationService applicationService;
    @Autowired(required = false)
    private Producer producer;

    @Override
    public void sendRegisterSuccessDelayMessage(SmsRecord smsRecord) {
        //触发延迟信息
        DelayLevel delayLevel = DelayLevel.M30;
        if (applicationService.isDevelopment()) {
            delayLevel = DelayLevel.M2;
        }
        Message message = new Message(MQTopicCons.CF, MQTagCons.CLICK_ON_DELAY_PAGE_THR_MIN_MSG, smsRecord.getMobile() + "_" + System.currentTimeMillis(), smsRecord, delayLevel);
        producer.send(message);
    }

    @Override
    public OpResult<ClewReceiveModel> sendNewClewMsg(ClewReceiveModel clewReceiveModel) {
        return clewTrackProducer.sendNewClewMsg(clewReceiveModel);
    }

    @Override
    public OpResult sendMq2CfClewtrack(CrowdfundingInfo crowdfundingInfo, String volunteerUniqueCode, String clientIp, CfPartnerInfoDo cfPartnerInfoDo) {
        ScanQRCodeNoticeModel scanQRCodeNoticeModel = new ScanQRCodeNoticeModel();
        try{
            Map<String,String> map = Maps.newHashMap();
            map.put("crowdfundingInfo", JSON.toJSONString(crowdfundingInfo));
            map.put("volunteerUniqueCode",volunteerUniqueCode);
            map.put("clientIp",clientIp);
            map.put("partnerUniqueCode", Optional.ofNullable(cfPartnerInfoDo).map(CfPartnerInfoDo::getUniqueCode).orElse(""));
            scanQRCodeNoticeModel.setType(ScanQRCodeNoticeEnum.USER_SCAN_AFTER_LAUNCH);
            scanQRCodeNoticeModel.setExtData(map);
            // 发送案例结束MQ
            Message<ScanQRCodeNoticeModel> message = MessageBuilder
                    .createWithPayload(scanQRCodeNoticeModel)
                    .addKey(crowdfundingInfo.getInfoId()+"--"+volunteerUniqueCode)
                    .setTags(MQTagCons.NOTICE_CLEW_TRACK_ADD_BD_CRM_CLEW)
                    .build();
            commonMessageHelperService.send(message);
        }catch (Exception e){
            return OpResult.createFailResult(CfErrorCode.SYSTEM_UNRECOGNIZED_ERROR);
        }
        return OpResult.createSucResult(scanQRCodeNoticeModel);
    }

    @Override
    public OpResult sendMq2CfClewtrackUseChannel(long userId, String mobile, String volunteerUniqueCode, String channel,String scanId,String scanTime, CfPartnerInfoDo cfPartnerInfoDo) {
        log.debug("sendMq2CfClewtrackUseChannel_userId:{},volunteerUniqueCode:{},channel:{}",userId,volunteerUniqueCode,channel);
        ScanQRCodeNoticeModel scanQRCodeNoticeModel = new ScanQRCodeNoticeModel();
        try{
            Map<String,String> map = Maps.newHashMap();
            map.put("userId",String.valueOf(userId));
            map.put("volunteerUniqueCode",volunteerUniqueCode);
            map.put("phone",mobile);
            map.put("channel",channel);
            map.put("scanId",scanId);
            map.put("scanTime",scanTime);
            map.put("partnerUniqueCode", Optional.ofNullable(cfPartnerInfoDo).map(CfPartnerInfoDo::getUniqueCode).orElse(""));
            scanQRCodeNoticeModel.setType(ScanQRCodeNoticeEnum.FOLLOW_OFFICIAL_ACCOUNT);
            scanQRCodeNoticeModel.setExtData(map);
            // 发送案例结束MQ
            Message<ScanQRCodeNoticeModel> message = MessageBuilder
                    .createWithPayload(scanQRCodeNoticeModel)
                    .addKey()
                    .setTags(MQTagCons.NOTICE_CLEW_TRACK_ADD_BD_CRM_CLEW)
                    .build();
            commonMessageHelperService.send(message);
        }catch (Exception e){
            log.error(this.getClass().getSimpleName()+"  sendMq2CfClewtrack err:",e);
            return OpResult.createFailResult(CfErrorCode.SYSTEM_UNRECOGNIZED_ERROR);
        }
        return OpResult.createSucResult(scanQRCodeNoticeModel);
    }
}
