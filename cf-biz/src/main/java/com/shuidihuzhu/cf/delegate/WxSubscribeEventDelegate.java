package com.shuidihuzhu.cf.delegate;

import com.shuidihuzhu.wx.grpc.enums.EventType;
import com.shuidihuzhu.wx.grpc.enums.MsgType;
import com.shuidihuzhu.wx.grpc.enums.WxMpBizType;
import com.shuidihuzhu.wx.grpc.model.WxSubscribeModel;
import com.shuidihuzhu.wx.grpc.model.WxSubscribeResponseDto;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2018-12-26  15:02
 */
public interface WxSubscribeEventDelegate {

    /**
     * 批量查询是否关注主号中的任意一个
     * 返回关注的用户列表
     * @param userIds
     * @return
     */
    Stream<Long> filterSubscribedMajor(Collection<Long> userIds);

    void saveEvent(String fromUserName, String toUserName, WxMpBizType wxMpBizType, MsgType byValue, EventType byValue1, String s, Date date);

    List<WxSubscribeModel> batchCheckSubscribeByUserIds(List<Long> userIds, int thirdType);

    WxSubscribeResponseDto getSubscribeOnlyOne(long userId, int mpType, int eventType, long time, long time1);

    List<WxSubscribeModel> getSubscribesByUserId(long userId);
}
