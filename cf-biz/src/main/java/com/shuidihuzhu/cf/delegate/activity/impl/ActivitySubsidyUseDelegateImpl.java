package com.shuidihuzhu.cf.delegate.activity.impl;

import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.activity.feign.CaseCountFeignClient;
import com.shuidihuzhu.cf.activity.feign.CfActivityFeignClient;
import com.shuidihuzhu.cf.activity.feign.v3.ActivityReadFeignClient;
import com.shuidihuzhu.cf.activity.model.SubsidyUseResult;
import com.shuidihuzhu.cf.activity.model.param.ActivitySubsidyParam;
import com.shuidihuzhu.cf.activity.model.v2.ActivityScopeBO;
import com.shuidihuzhu.cf.activity.model.v2.ActivityScopeVo;
import com.shuidihuzhu.cf.biz.crowdfunding.CfInfoExtBiz;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.delegate.activity.ActivitySubsidyUseDelegate;
import com.shuidihuzhu.cf.domain.activity.ActivityKeyAreaDO;
import com.shuidihuzhu.cf.domain.activity.ActivityVenueDetail;
import com.shuidihuzhu.cf.domain.activity.ActivityWaveNameDO;
import com.shuidihuzhu.cf.enhancer.utils.HelpResponseUtils;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.service.activity.subsidy.ActivityKeyAreaService;
import com.shuidihuzhu.cf.service.activity.subsidy.ActivitySubsidyOrderService;
import com.shuidihuzhu.cf.service.activity.IActivityVenueService;
import com.shuidihuzhu.cf.service.activity.subsidy.ActivityWaveService;
import com.shuidihuzhu.cf.service.activity.subsidy.impl.ActivitySubsidyServiceImpl;
import com.shuidihuzhu.cf.service.crowdfunding.IPService;
import com.shuidihuzhu.cf.service.msg.MsgClientV2Service;
import com.shuidihuzhu.cf.util.CaseUtils;
import com.shuidihuzhu.cf.util.activity.SubsidyUtils;
import com.shuidihuzhu.charity.client.api.BonfireClient;
import com.shuidihuzhu.charity.client.enums.activity.ActivityDetailType;
import com.shuidihuzhu.common.web.model.Response;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ActivitySubsidyUseDelegateImpl implements ActivitySubsidyUseDelegate {

    @Autowired
    private CfActivityFeignClient cfActivityFeignClient;

    @Autowired
    private ActivitySubsidyOrderService activitySubsidyOrderService;

    @Autowired
    private IActivityVenueService activityVenueService;

    @Autowired
    private CfInfoExtBiz cfInfoExtBiz;

    @Autowired
    private CaseCountFeignClient caseCountFeignClient;

    @Autowired
    private IPService ipService;

    @Autowired
    private ActivityKeyAreaService activityKeyAreaService;

    @Autowired
    private ActivityReadFeignClient activityReadFeignClient;
    @Autowired
    private ActivityWaveService activityWaveService;

    @Override
    public boolean onCfPaySuccess(CrowdfundingOrder order) {
        log.info("onCfPaySuccess caseId:{}, orderId:{}", order.getCrowdfundingId(), order.getId());
        Integer caseId = order.getCrowdfundingId();
        long userId = order.getUserId();
        Long orderId = order.getId();
        ActivitySubsidyOrderService.PaySuccessSubsidyModelV2 orderSubsidyModel = activitySubsidyOrderService.getOrderSubsidyModel(orderId);
        if (orderSubsidyModel == null) {
            log.warn("ActivitySubsidyUseDelegateImpl model null orderId:{}, caseId:{}, userId:{}", orderId, caseId, userId);
            return true;
        }

        int shareDv = orderSubsidyModel.getShareDv();
        int channelType = orderSubsidyModel.getChannelType();
        String feeId = orderSubsidyModel.getFeeId();
        int activityId = orderSubsidyModel.getActivityId();
        // 是否是会场内捐款
        int cooperateActivityId = 0;
        boolean onVenue = false;
        if (allMeetingplace().contains(activityId)) {
            onVenue = activityId > 0;
            //如果是会场的活动id进行转成参加活动的id
            ActivityVenueDetail activityDetail = activityVenueService.getActivityDetail(activityId);
            cooperateActivityId = Optional.ofNullable(activityDetail).map(ActivityVenueDetail::getCooperateActivityId).orElse(0);
        }else {
            //如果活动id不等于0，判断活动是否结束，结束设置cooperateActivityId = 0
            if (activityId >0) {
                OperationResult<ActivityScopeBO> activity = activityReadFeignClient.getActivityById(activityId);
                if (activity.isSuccess() && activity.getData() != null){
                    Date endTime = activity.getData().getBaseInfoBO().getEndTime();
                    if (endTime.before(new Date())){
                        cooperateActivityId = 0 ;
                    }
                }
            }
            cooperateActivityId = activityId;
        }

        ActivitySubsidyParam param = ActivitySubsidyParam.createCFPaySuccess(
                cooperateActivityId,
                caseId,
                userId,
                orderId,
                shareDv,
                channelType,
                feeId,
                onVenue
        );
        param.setFromQQ(orderSubsidyModel.isFromQQ());

        param = processCommonParam(param);
        //todo（优化） 如果案例没有加入活动，不调用配捐接口
        RpcResult<SubsidyUseResult> resp = cfActivityFeignClient.getCooperateMoneyV3(param);
        log.info("ActivitySubsidyUseDelegateImpl getCooperateMoneyV3 param:{}, resp:{}", param, resp);
        if (resp == null || resp.isFail()) {
            log.info("ActivitySubsidyUseDelegateImpl subsidy fail caseId:{}", caseId);
            return false;
        }
        SubsidyUseResult subsidyUseResult = resp.getData();
        if (subsidyUseResult == null) {
            log.error("ActivitySubsidyUseDelegateImpl subsidy error caseId:{}", caseId);
            return true;
        }
        boolean success = subsidyUseResult.isSuccess();
        if (success) {
            activityKeyAreaService.onPreSubsidy(caseId, subsidyUseResult.getActivityId());
        }
        activityKeyAreaService.onPaySuccess(caseId);
        return resp.isSuccess();
    }

    private ActivitySubsidyParam processCommonParam(ActivitySubsidyParam param) {
        CfInfoExt ext = cfInfoExtBiz.getByCaseId(param.getCaseId());
        // 生命周期天数
        int caseLifecycleDayCount = CaseUtils.getCaseLifecycleDayCount(ext);
        param.setCaseLifecycleDayCount(caseLifecycleDayCount);

        // 发起省份
        String provinceFromIP = ipService.getProvinceFromIP(ext.getClientIp());
        param.setCaseRaiseProvince(provinceFromIP);

        int caseId = param.getCaseId();
        activityKeyAreaService.processSubsidyParam(param, caseId);

        //        // 用户历史捐款次数
//        Integer userDonateCount = statDelegate.getInt(param.getUserId(), caseId, StatDataEnum.DONATE_COUNT).orElse(0);

        return param;
    }

    @NotNull
    public Response<Void> onShareSubsidyUse(long viewUserId, long sourceUserId, int caseId, ActivitySubsidyServiceImpl.Model model, boolean eagelValue) {
        int shareDv = model.getShareDv();
        int userDonateCountOnCaseInActivity = model.getUserDonateCountOnCaseInActivity();

        // 调用配转成功
        int channel = SubsidyUtils.parseChannel(shareDv).getCode();

        ActivitySubsidyParam param = ActivitySubsidyParam.createShareSuccess(
                caseId, sourceUserId, model.getShareRecordId(), model.getShareIp(), shareDv, channel,
                model.getShareSourceId(), model.getShareNum(), userDonateCountOnCaseInActivity);
        param.setEagleValue(eagelValue);
        param = processCommonParam(param);

        RpcResult<Void> resp = caseCountFeignClient.usefulOneV4(param);
        log.info("subsidy share view usefulOne viewUserId={}, caseId={}, sourceUserId={}, shareDv={}, model={}, resp={}",
                viewUserId, caseId, sourceUserId, shareDv, model, resp);
        if (resp.isFail()) {
            log.error("ActivityShareViewServiceImpl usefulOne fail");
        }
        return HelpResponseUtils.makeSuccess();
    }

    @Override
    public RpcResult<SubsidyUseResult> onBonfireSubsidy(CrowdfundingOrder order,int teamNum) {
        log.info("onBonfireSubsidy orderid:{} teamNum:{}",order.getId(),teamNum);
        //获取活动id
        Optional<ActivityKeyAreaDO> activityKey = activityKeyAreaService.get(order.getCrowdfundingId());
        if (activityKey.isEmpty()){
            log.info("onBonfireSubsidy have not bonfire activity caseId:{}",order.getCrowdfundingId());
            return RpcResult.createSucResult(createResult(null,false,0));
        }
        ActivityKeyAreaDO activityKeyAreaDO = activityKey.get();
        long activityId = activityKeyAreaDO.getActivityId();
        Integer caseId = order.getCrowdfundingId();
        Long orderId = order.getId();
        ActivitySubsidyOrderService.PaySuccessSubsidyModelV2 orderSubsidyModel = activitySubsidyOrderService.getOrderSubsidyModel(orderId);
        if (orderSubsidyModel == null) {
            log.warn("onBonfireSubsidy model null orderId:{}, caseId:{}", orderId, caseId);
            return RpcResult.createSucResult(createResult(activityId,false,0));
        }
        ActivitySubsidyParam param = ActivitySubsidyParam.createCFPaySuccess(
                activityId,
                caseId,
                order.getUserId(),
                order.getId(),
                orderSubsidyModel.getShareDv(),
                orderSubsidyModel.getChannelType(),
                orderSubsidyModel.getFeeId(),
                false
        );
//        ActivitySubsidyParam param = ActivitySubsidyParam.createCFPaySuccess(
//                activityId,
//                caseId,
//                order.getUserId(),
//                order.getId(),
//                order.getShareDv(),
//                1,
//               null,
//                false
//        );
        //获取案例顾问的城市
        ActivityWaveNameDO newWaveName = activityWaveService.getNewWaveName(activityKeyAreaDO);
        if (ObjectUtils.isEmpty(newWaveName) || StringUtils.isEmpty(newWaveName.getWaveName())){
            log.info("onBonfireSubsidy have not get volunteer city,param:{},data:{}",activityKeyAreaDO,newWaveName);
            return RpcResult.createSucResult(createResult(activityId,false,0));
        }
        //复用配捐字段，team-1 原因是在配捐的时候+1(捐单)
        param.setKeyAreaSubsidyCount(teamNum-1);
        param.setKeyAreaType(newWaveName.getWaveName());
        RpcResult<SubsidyUseResult> resp = cfActivityFeignClient.getCooperateMoneyV3(param);
        log.info("onBonfireSubsidy param:{},data:{}", JSON.toJSONString(param),resp);
        if (resp.isSuccess() && resp.getData()!=null){
            SubsidyUseResult subsidyUseResult = resp.getData();
            //补贴成功，记录补贴次数
            if (subsidyUseResult.isSuccess()){
                activityKeyAreaService.onPreSubsidy(caseId, subsidyUseResult.getActivityId());
            }
        }
        return resp;
    }

    private SubsidyUseResult createResult(Long activityId, boolean success, int amount) {
        SubsidyUseResult result = new SubsidyUseResult();
        result.setActivityId(activityId);
        result.setSuccess(success);
        result.setCooperateAmount(amount);
        return result;
    }

    /**
     * 会场活动的id集合
     * @return
     */
    public List<Integer> allMeetingplace() {
        return Lists.newArrayList(ActivityDetailType.CF_LOVE_HOME.getCode(),
                ActivityDetailType.CF_WX.getCode(),
                ActivityDetailType.PF_WX.getCode());
    }

}
