package com.shuidihuzhu.cf.delegate;

import com.shuidihuzhu.account.model.UserThirdModel;
import com.shuidihuzhu.client.grpc.account.v1.feign.UserThirdServiceClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-05-16 7:58 PM
 **/
@Component
public class UserThirdDelegate {

    @Resource
    private UserThirdServiceClient userThirdServiceClient;

    private UserThirdServiceClient getClient() {
        return this.userThirdServiceClient;
    }

    public UserThirdModel getThirdModelWithUserId(long userId, int type) {
        return getClient().getThirdModelWithUserId(userId, type);
    }

    public UserThirdModel getThirdModelWithOpenId(String openId) {
        return getClient().getThirdModelWithOpenId(openId);
    }

    public List<UserThirdModel> getThirdModelsByUserIdsAndType(List<Long> userIds, int type) {
        return getClient().getThirdModelsByUserIdsAndType(userIds, type);
    }

    public List<UserThirdModel> getThirdModelByOpenids(List<String> openIds) {
        return getClient().getThirdModelByOpenids(openIds);
    }

    public List<UserThirdModel> getByUserId(long userId) {
        return getClient().getByUserId(userId);
    }
}
