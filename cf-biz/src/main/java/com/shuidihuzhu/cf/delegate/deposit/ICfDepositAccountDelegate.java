package com.shuidihuzhu.cf.delegate.deposit;

import com.shuidihuzhu.cf.enums.crowdfunding.CfDrawCashConstant;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.enums.deposit.DepositStatusEnum;
import com.shuidihuzhu.cf.finance.model.deposit.CfPayeeDepositAccount;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @Author: lian<PERSON><PERSON><PERSON>o
 * @Date: 2019/12/15 18:17
 */
public interface ICfDepositAccountDelegate {
    /**
     * 注册案例 三方托管账户
     *
     * @param caseId
     * @param infoUuid
     * @param productName
     * @return
     */
    void createCaseAccount(int caseId, String infoUuid, String productName);


    /**
     * 获取收款账户--在资金
     *
     * @param encryptCardNo
     * @return
     */
    List<CfPayeeDepositAccount> getPayeeAccount(String encryptCardNo);
}
