package com.shuidihuzhu.cf.delegate.impl;

import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.delegate.ICfFinanceFundStateDelegate;
import com.shuidihuzhu.cf.enums.CfPayeeMirrorRemarkEnum;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceFundStateFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.model.CfFundState;
import com.shuidihuzhu.cf.finance.model.StateMachine;
import com.shuidihuzhu.cf.finance.mq.FinanceMQTagCons;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author: lianghongchao
 * @Date: 2019-11-15 19:04
 */
@Service
@Slf4j
public class CfFinanceFundStateDelegateImpl implements ICfFinanceFundStateDelegate {
    @Autowired
    private CfFinanceFundStateFeignClient cfFinanceFundStateFeignClient;

    @Autowired
    private ApplicationService applicationService;

    @Autowired(required = false)
    private Producer producer;

    /**
     * @param cfId
     * @param cfInfoUuid
     * @param state
     * @param stateStop
     * @param cfPayeeMirrorRemarkEnum
     * @return
     */
    @Override
    public Integer addFundState(int cfId, String cfInfoUuid, int state, int stateStop,
                                CfPayeeMirrorRemarkEnum cfPayeeMirrorRemarkEnum, String productName) {
        Integer count = 0;
        // 添加状态表
        try {
            FeignResponse<Integer> feignResponse = cfFinanceFundStateFeignClient.addFundState(cfId, cfInfoUuid,
                    StateMachine.State.INITIAL.getCode(), StateMachine.StateStop.NONE.getCode(), productName,
                    CfPayeeMirrorRemarkEnum.CF_STATE_ADD);
            count = feignResponse.getData();
        } catch (Exception e) {
            log.error("finance服务异常,", e);
        }

        try {
            CfFundState cfFundState = new CfFundState(cfId, cfInfoUuid, StateMachine.State.valueOf(state));
            cfFundState.setStop(stateStop);
            DelayLevel delayLevel = DelayLevel.M5;
            if (!applicationService.isProduction()) {
                delayLevel = DelayLevel.M2;
            }
            Message msg = new Message(MQTopicCons.CF, FinanceMQTagCons.CF_STATE_MSG, System.currentTimeMillis() + "_" + cfId, cfFundState, delayLevel);
            producer.send(msg);
        } catch (Exception e) {
            log.error("addFundState.send", e);
        }
        return count;
    }
}
