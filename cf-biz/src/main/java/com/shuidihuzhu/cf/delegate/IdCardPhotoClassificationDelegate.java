package com.shuidihuzhu.cf.delegate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.alps.feign.ocean.OceanApiClient;
import com.shuidihuzhu.alps.feign.ocean.OceanApiRequest;
import com.shuidihuzhu.alps.feign.ocean.OceanApiResponse;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * @Author: wangpeng
 * @Date: 2022/12/21 11:22
 * @Description:
 */
@Slf4j
@Service
public class IdCardPhotoClassificationDelegate {

    @Resource
    private OceanApiClient oceanApiClient;

    /**
     * 身份证识别成功或fallback（fallback 业务上也看做是调用成功）
     */
    private static final int ID_CARD_INVOKE_SUCCESS_OR_FALLBACK = 0;

    /**
     * 人和身份证合照识别成功或fallback（fallback 业务上也看做是调用成功）
     */
    private static final int HUMAN_AND_ID_CARD_INVOKE_SUCCESS_OR_FALLBACK = 1;
    /**
     *
     * @param url 图片
     * @param infoUuid 案例id
     * @return '身份证': 0, '人和身份证合照': 1, '其他证件': 2
     */
    public int agent(String url, String infoUuid, int useScene) {
        int defaultResult = useScene == 0 ? ID_CARD_INVOKE_SUCCESS_OR_FALLBACK : HUMAN_AND_ID_CARD_INVOKE_SUCCESS_OR_FALLBACK;
        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("pid", infoUuid);
        bodyMap.put("url", url);
        String userId = "10007";
        String token = "84b6808b1d289051";
        String tag = "ai-chou-certificate-classification";
        String body = JSON.toJSONString(bodyMap);

        OceanApiRequest oceanApiRequest = new OceanApiRequest(userId, token, tag, body, null);
        Response<OceanApiResponse> response = oceanApiClient.agent(oceanApiRequest);
//        log.info("身份证证件ai识别结果 {} {}", oceanApiRequest, response);
        String s = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .map(OceanApiResponse::getBody)
                .orElse("");
        if (StringUtils.isEmpty(s)) {
            return defaultResult;
        }
        JSONObject jsonObject = JSONObject.parseObject(s);
        if (MapUtils.isEmpty(jsonObject)) {
            return defaultResult;
        }
        String valueOf = String.valueOf(jsonObject.get("label_id"));
        if (StringUtils.isEmpty(valueOf)) {
            return defaultResult;
        }
        return Integer.parseInt(valueOf);
    }
}
