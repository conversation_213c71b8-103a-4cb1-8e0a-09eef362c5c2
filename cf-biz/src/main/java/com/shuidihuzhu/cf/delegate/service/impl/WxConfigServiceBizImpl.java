package com.shuidihuzhu.cf.delegate.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.delegate.service.WxConfigServiceBiz;
import com.shuidihuzhu.wx.grpc.client.WxConfigsResponse;
import com.shuidihuzhu.wx.grpc.client.common.WxConfig;
import com.shuidihuzhu.wx.grpc.client.feign.WxConfigServiceClient;
import com.shuidihuzhu.wx.grpc.model.SimpleWxConfig;
import com.shuidihuzhu.wx.grpc.model.WxConfigModel;
import com.shuidihuzhu.wx.grpc.util.WxRpcUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/5/9 2:17 PM
 */
@Slf4j
@Service
@RefreshScope
public class WxConfigServiceBizImpl implements WxConfigServiceBiz {

    @Resource
    private WxConfigServiceClient wxConfigServiceClient;

    @Override
    public SimpleWxConfig getSimpleWxConfigByOriginId(String userName) {
        return wxConfigServiceClient.getSimpleWxConfigByOriginId(userName);
    }

    @Override
    public WxConfigModel getWxConfig(int thirdType) {
        return wxConfigServiceClient.getWxConfig(thirdType);
    }
}
