package com.shuidihuzhu.cf.delegate;

import com.shuidihuzhu.cf.finance.model.CfDonorRefundApply;
import com.shuidihuzhu.cf.finance.model.CfFinanceBcpModel;
import com.shuidihuzhu.cf.finance.model.CfRefundRecord;
import com.shuidihuzhu.cf.finance.model.req.CfDrawInfoReq;
import com.shuidihuzhu.cf.finance.model.vo.CfFinanceCapitalStatusVo;
import com.shuidihuzhu.cf.finance.model.vo.CfFinanceDrawApplyVo;
import com.shuidihuzhu.cf.finance.model.vo.DonationAmountInFenVo;
import com.shuidihuzhu.cf.finance.model.vo.draw.CfAmountInfoVo;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefund;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfRefundApplyVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingOrderVo;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;
import java.util.Map;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;

public interface ICFFinanceFeignDelegate {

    /**
     * 获取退款信息
     *
     * @param infoUuid
     * @return
     */
    CfRefund getRefundByInfoUuid(String infoUuid);

    /**
     * 获取案例退款订单记录
     *
     * @param infoUuid
     * @param anchorId
     * @param size
     * @return
     */
    List<CfRefundRecord> getRefundRecordByUuidAndAnchorId(String infoUuid, long anchorId, int size);

    /**
     * 自助下发退款
     *
     * @param userId
     * @param infoUuid
     * @param orderId
     * @return
     */
    Response cfRefundLaunchHelper(long userId, String infoUuid, long orderId);

    /**
     * 筹款人看到的金额详情
     *
     * @param infoUuid
     * @return
     */
    CfAmountInfoVo getFundraiserAmountInfo(String infoUuid);


    /**
     * 获取单笔退款订单状态
     *
     * @param orderIds
     * @return
     */
    Map<Long, CfDonorRefundApply.StatusEnum> getOrderRefundStatus(List<Long> orderIds);

    /**
     * 已筹金额
     *
     * @param infoUuid
     * @return
     */
    DonationAmountInFenVo getDonationAmountInFen(String infoUuid);

    /**
     * 资金基础状态
     *
     * @param infoUuid
     * @return
     */
    CfFinanceCapitalStatusVo getFinanceCapitalStatus(String infoUuid, int caseId);

    /**
     * 获取提现信息
     *
     * @param infoUuid
     * @return
     */
    CfFinanceDrawApplyVo getFinanceDrawApply(String infoUuid);

    /**
     * 资金系统下单
     *
     * @param cfFinanceBcpModel
     * @return
     */
    boolean addOrderToFinance(CfFinanceBcpModel cfFinanceBcpModel);


    /**
     * 账号转移
     *
     * @param orderList
     * @param fromUserId
     * @return
     */
    int transferOrder(List<CrowdfundingOrder> orderList, long fromUserId);

    Map<Long, CrowdfundingOrderVo.PublicContent> getOrderPublicContent(List<CfDrawInfoReq> orders, long userId);

    /**
     * 获取退款申请信息
     *
     * @param caseId -
     * @return -
     */
    CfRefundApplyVo getRefundApplyVoByCaseId(int caseId);

    /**
     * 取消退款申请
     *
     * @param caseId    -
     * @param userId    -
     * @return -
     */
    Response<Void> cancelRefundApply(int caseId, long userId);
}
