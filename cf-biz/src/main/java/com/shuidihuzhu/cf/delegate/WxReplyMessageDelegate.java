package com.shuidihuzhu.cf.delegate;

import com.shuidi.weixin.mp.bean.WxMpXmlMessage;
import com.shuidihuzhu.wx.grpc.client.feign.WxReplyMessageServiceClient;
import com.shuidihuzhu.wx.grpc.enums.WxMpBizType;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023-05-16 8:18 PM
 **/
@Component
public class WxReplyMessageDelegate {

    @Resource
    private WxReplyMessageServiceClient wxReplyMessageServiceClient;

    public WxReplyMessageServiceClient getClient() {
        return wxReplyMessageServiceClient;
    }

    public void saveMessage(WxMpXmlMessage wxMessage, WxMpBizType wxMpBizType) {
        getClient().saveMessage(wxMessage, wxMpBizType);
    }
}
