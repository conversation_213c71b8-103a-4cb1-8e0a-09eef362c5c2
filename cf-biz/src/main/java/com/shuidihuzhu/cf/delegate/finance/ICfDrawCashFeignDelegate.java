package com.shuidihuzhu.cf.delegate.finance;

import com.shuidihuzhu.cf.finance.model.vo.drawcash.CfApplyDrawWithAuditVo;
import com.shuidihuzhu.cf.finance.model.vo.drawcash.CfDrawCashPublicVo;
import com.shuidihuzhu.cf.finance.model.vo.drawcash.CfPublicMessageVo;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;

/**
 * @author: lian<PERSON><PERSON><PERSON><PERSON>
 * @create: 2020年06月22日17:05:05
 * <p>
 * 非筹款人使用接口，支持案例详情页量级
 */
public interface ICfDrawCashFeignDelegate {
	/**
	 * 公示信息展示
	 *
	 * @param infoUuid
	 * @return
	 */
	Response<List<CfDrawCashPublicVo>> getDrawPublicMessage(String infoUuid);

	/**
	 * 公示信息展示
	 *
	 * @param infoUuid
	 * @return
	 */
	Response<CfPublicMessageVo> getDrawPublicMessageV2(String infoUuid);

	/**
	 * 获取最新的提现申请参数
	 *
	 * @param caseId
	 * @return
	 */
	Response<CfApplyDrawWithAuditVo> getDrawCashLastParam(int caseId);

	/**
	 * 获取案例是否被分流到新的流程
	 *
	 * @param caseId
	 * @return
	 */
	boolean getFundingABTest(int caseId);

	/**
	 * 获取案例是否存在有效的草稿数据
	 *
	 * @param caseId
	 * @return
	 */
	boolean getFundingValidParam(int caseId);

	/**
	 * 获取案例是否证实完成
	 *
	 * @param caseId
	 * @return
	 */
	boolean getFundingVerifyData(int caseId);
}
