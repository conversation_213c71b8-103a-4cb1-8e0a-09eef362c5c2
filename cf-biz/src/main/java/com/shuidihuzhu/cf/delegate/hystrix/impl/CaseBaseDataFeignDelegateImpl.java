package com.shuidihuzhu.cf.delegate.hystrix.impl;

import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixProperty;
import com.shuidihuzhu.cf.delegate.hystrix.CaseBaseDataFeignDelegate;
import com.shuidihuzhu.client.cf.olap.client.CaseBaseDataFeignClient;
import com.shuidihuzhu.client.cf.olap.model.CfCaseBaseData;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/6/13  20:30
 */
@Slf4j
@Service
public class CaseBaseDataFeignDelegateImpl implements CaseBaseDataFeignDelegate {


    @Resource
    private CaseBaseDataFeignClient cfCaseBaseDataFeignClient;

    @HystrixCommand(fallbackMethod = "getCaseBaseDataFallback", commandProperties =
            {
                    @HystrixProperty(name = "execution.isolation.thread.timeoutInMilliseconds", value = "300")
            })
    @Override
    public CfCaseBaseData getCaseBaseData(int caseId) {

        Response<CfCaseBaseData> response = cfCaseBaseDataFeignClient.getCaseBaseData(caseId);

        return Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null);
    }

    public CfCaseBaseData getCaseBaseDataFallback(int caseId) {
        return null;
    }

}
