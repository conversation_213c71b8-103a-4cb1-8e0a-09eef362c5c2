package com.shuidihuzhu.cf.delegate.finance.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingAttachmentBiz;
import com.shuidihuzhu.cf.delegate.finance.ICfDrawCashFeignDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.CfDrawCashConstant;
import com.shuidihuzhu.cf.finance.client.feign.CfDrawCashFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.model.drawcash.CfDrawCashApplyParam;
import com.shuidihuzhu.cf.finance.model.vo.drawcash.CfApplyDrawWithAuditVo;
import com.shuidihuzhu.cf.finance.model.vo.drawcash.CfDrawCashPublicVo;
import com.shuidihuzhu.cf.finance.model.vo.drawcash.CfPublicMessageVo;
import com.shuidihuzhu.cf.finance.model.vo.refund.CfCaseRefundPublicMessageVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.Optional;

/**
 * @author: wanghui
 * @create: 2019/6/12 5:03 PM
 */
@Slf4j
@Service
@RefreshScope
public class CfDrawCashFeignDelegate implements ICfDrawCashFeignDelegate {
	@Autowired
	private CfDrawCashFeignClient cfDrawCashFeignClient;
	@Autowired
	private ShuidiCipher shuidiCipher;
	@Autowired
	private CrowdfundingAttachmentBiz crowdfundingAttachmentBiz;

	@Override
	public Response<List<CfDrawCashPublicVo>> getDrawPublicMessage(String infoUuid) {
		if (StringUtils.isEmpty(infoUuid)) {
			return NewResponseUtil.makeSuccess(Lists.newArrayList());
		}
		try {
			FeignResponse<List<CfDrawCashPublicVo>> feignResponse = cfDrawCashFeignClient.getDrawPublicMessage(infoUuid);
			log.debug("feignResponse:{}", JSON.toJSONString(feignResponse));
			if (feignResponse == null || feignResponse.notOk()) {
				log.error("finance服务异常 infoUuid:{}", infoUuid);
				return NewResponseUtil.makeSuccess(Lists.newArrayList());
			}
			return NewResponseUtil.makeSuccess(feignResponse.getData());
		} catch (Exception e) {
			log.error("finance服务异常,infoUuid:{}", infoUuid, e);
			return NewResponseUtil.makeSuccess(Lists.newArrayList());
		}
	}

	@Override
	public Response<CfPublicMessageVo> getDrawPublicMessageV2(String infoUuid) {
		if (StringUtils.isEmpty(infoUuid)) {
			return NewResponseUtil.makeSuccess(new CfPublicMessageVo());
		}
		try {
			FeignResponse<CfPublicMessageVo> feignResponse = cfDrawCashFeignClient.getDrawPublicMessageV2(infoUuid);
			log.debug("feignResponse:{}", JSON.toJSONString(feignResponse));
			if (feignResponse == null || feignResponse.notOk()) {
				log.error("finance服务异常 infoUuid:{}", infoUuid);
				return NewResponseUtil.makeSuccess(new CfPublicMessageVo());
			}

			CfPublicMessageVo cfPublicMessageVo = feignResponse.getData();
			if (Objects.isNull(cfPublicMessageVo)) {
				return NewResponseUtil.makeSuccess(feignResponse.getData());
			}

			List<CfCaseRefundPublicMessageVo> caseRefundPublicMessageVoList = cfPublicMessageVo.getCaseRefundPublicMessageVoList();
			if (CollectionUtils.isEmpty(caseRefundPublicMessageVoList)) {
				return NewResponseUtil.makeSuccess(feignResponse.getData());
			}

			for (CfCaseRefundPublicMessageVo cfCaseRefundPublicMessageVo : caseRefundPublicMessageVoList) {
				Date applyTime = cfCaseRefundPublicMessageVo.getApplyTime();
				if (Objects.isNull(applyTime)) {
					continue;
				}
				// 计算当前时间与申请时间的差值是否超过2分钟
				long timeDifference = System.currentTimeMillis() - applyTime.getTime();
				boolean isApplyTimeOverTwoMinutes = timeDifference > (2 * 60 * 1000); // 2分钟 = 2 * 60 * 1000毫秒

				cfCaseRefundPublicMessageVo.setIsApplyTimeOverTwoMinutes(isApplyTimeOverTwoMinutes);
			}
			
			return NewResponseUtil.makeSuccess(feignResponse.getData());
		} catch (Exception e) {
			log.error("finance服务异常,infoUuid:{}", infoUuid, e);
			return NewResponseUtil.makeSuccess(new CfPublicMessageVo());
		}
	}

	@Override
	public Response<CfApplyDrawWithAuditVo> getDrawCashLastParam(int caseId) {
		try {
			FeignResponse<CfDrawCashApplyParam> feignResponse = cfDrawCashFeignClient.getDrawCashLastParam(caseId);
			log.debug("feignResponse:{}", JSON.toJSONString(feignResponse));
			if (feignResponse == null || feignResponse.notOk()) {
				log.error("finance服务异常 caseId:{}", caseId);
				return NewResponseUtil.makeSuccess(null);
			}
			CfDrawCashApplyParam drawCashApplyParam = feignResponse.getData();
			if (drawCashApplyParam == null) {
				return NewResponseUtil.makeSuccess(null);
			}
			CfApplyDrawWithAuditVo cfApplyDrawWithAuditVo = new CfApplyDrawWithAuditVo();
			cfApplyDrawWithAuditVo.setInfoUuid(drawCashApplyParam.getInfoUuid());
			cfApplyDrawWithAuditVo.setDrawCashType(drawCashApplyParam.getDrawCashType());
			cfApplyDrawWithAuditVo.setStopCfReason(drawCashApplyParam.getStopCfReason());

			cfApplyDrawWithAuditVo.setUseOfFunds(drawCashApplyParam.getUseOfFunds());
			cfApplyDrawWithAuditVo.setPatientConditionNow(drawCashApplyParam.getPatientConditionNow());
			cfApplyDrawWithAuditVo.setPatientAddress(drawCashApplyParam.getPatientAddress());

			cfApplyDrawWithAuditVo.setApplyTime(drawCashApplyParam.getApplySubmitTime());

			if (StringUtils.isNotBlank(drawCashApplyParam.getMedicalBillUrls())) {
				cfApplyDrawWithAuditVo.setMedicalBillList(JSON.parseArray(drawCashApplyParam.getMedicalBillUrls(), String.class));
			}
			cfApplyDrawWithAuditVo.setPatientRegion(drawCashApplyParam.getPatientRegion());
			cfApplyDrawWithAuditVo.setPatientAddress(drawCashApplyParam.getPatientAddress());
			cfApplyDrawWithAuditVo.setEmergencyContactName(drawCashApplyParam.getEmergencyContactName());
			cfApplyDrawWithAuditVo.setEmergencyContactPhone(shuidiCipher.decrypt(drawCashApplyParam.getEmergencyContactPhone()));
			cfApplyDrawWithAuditVo.setEmergencyContactRelation(drawCashApplyParam.getEmergencyContactRelation());


			cfApplyDrawWithAuditVo.setDrawCashAmount(drawCashApplyParam.getAmount());
			cfApplyDrawWithAuditVo.setDeadlineOrderTime(drawCashApplyParam.getDeadlineOrderTime());

			cfApplyDrawWithAuditVo.setApplyStatus(CfDrawCashConstant.ApplyStatus.UNSUBMIT);
			cfApplyDrawWithAuditVo.setApplyTime(drawCashApplyParam.getApplySubmitTime());
			cfApplyDrawWithAuditVo.setAuditTime(null);
			cfApplyDrawWithAuditVo.setReason(StringUtils.EMPTY);
			return NewResponseUtil.makeSuccess(cfApplyDrawWithAuditVo);
		} catch (Exception e) {
			log.error("finance服务异常, caseId:{}", caseId, e);
			return NewResponseUtil.makeSuccess(null);
		}
	}

	@Override
	public boolean getFundingABTest(int caseId) {
		FeignResponse<Boolean> feignResponse = cfDrawCashFeignClient.getFundingABTest(caseId);
		return Optional.ofNullable(feignResponse).
				filter(FeignResponse::ok).
				map(FeignResponse::getData).
				orElse(false);
	}

	@Override
	public boolean getFundingValidParam(int caseId) {
		FeignResponse<Boolean> feignResponse = cfDrawCashFeignClient.getFundingValidParam(caseId);
		return Optional.ofNullable(feignResponse).
				filter(FeignResponse::ok).
				map(FeignResponse::getData).
				orElse(false);
	}

	@Override
	public boolean getFundingVerifyData(int caseId) {
		FeignResponse<Boolean> feignResponse = cfDrawCashFeignClient.getFundingVerifyData(caseId);
		return Optional.ofNullable(feignResponse).
				filter(FeignResponse::ok).
				map(FeignResponse::getData).
				orElse(false);
	}
}
