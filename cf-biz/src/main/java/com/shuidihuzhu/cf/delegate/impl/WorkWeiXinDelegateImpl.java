package com.shuidihuzhu.cf.delegate.impl;

import com.shuidihuzhu.cf.delegate.IWorkWeiXinDelegate;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.client.baseservice.alarm.v1.AlarmClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-06-14  18:18
 * https://wiki.shuiditech.com/pages/viewpage.action?pageId=12396185
 * @see com.shuidihuzhu.cf.constants.WorkWeiXinGroupConstants
 */
@Slf4j
@Service
public class WorkWeiXinDelegateImpl implements IWorkWeiXinDelegate {

    private static final String ERROR_MSG = "企业微信消息发送失败";

    @Resource
    private AlarmClient alarmClient;

    @Resource
    private ApplicationService applicationService;

    @Override
    public OpResult sendByUser(List<String> userList, String content) {
//        if(applicationService.isDevelopment()){
//            return OpResult.createSucResult();
//        }
        log.info("userList: {}, content: {}", userList, content);

        if (CollectionUtils.isEmpty(userList)) {
            log.info("empty userList: {}, content: {}", userList, content);
            return OpResult.createSucResult();
        }
        try {
            alarmClient.sendByUser(userList, content);
        } catch (Exception e) {
            log.error(ERROR_MSG + content, e);
            return OpResult.createFailResult(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        return OpResult.createSucResult();
    }

    /**
     * @param groupId
     * @see com.shuidihuzhu.cf.constants.WorkWeiXinGroupConstants
     * @param content
     * @return
     */
    @Override
    public OpResult sendByGroup(String groupId , String content) {
//        if(applicationService.isDevelopment()){
//                return OpResult.createSucResult();
//        }
        log.info("groupId: {}, content: {}", groupId, content);
        try {
            alarmClient.sendByGroup(groupId, content);
        } catch (Exception e) {
            log.error(ERROR_MSG + content, e);
            return OpResult.createFailResult(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        return OpResult.createSucResult();
    }
}
