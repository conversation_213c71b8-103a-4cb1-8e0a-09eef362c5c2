package com.shuidihuzhu.cf.delegate.impl;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.delegate.ICaseChannelDelegate;
import com.shuidihuzhu.client.cf.olap.client.CaseTagFeignClient;
import com.shuidihuzhu.client.cf.olap.model.CfCaseServiceClewTag;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Created by sven on 2020/4/23.
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class CaseChannelDelegateImpl implements ICaseChannelDelegate {


    //100线下，200是线上，无服务300

    @Resource
    private CaseTagFeignClient caseTagFeignClient;

    @Resource(name="cfRedissonHandler")
    private RedissonHandler redissonHandler;

    @Override
    public CaseChannelEnum getCaseRaiseChannel(int caseId) {

        Response<CfCaseServiceClewTag> response = caseTagFeignClient.getCaseServiceAndClewTag(caseId);
        log.info("getCaseServiceAndClewTag {}, response: {}", caseId, JSON.toJSONString(response));

        if(response == null || response.getData() == null){
            return CaseChannelEnum.OFFLINE;
        }
        int value = response.getData().getServiceTag();


//        int value = redissonHandler.get("case_channel_test_code", Integer.class);
//        log.info("getCaseServiceAndClewTag {}, response: {}", caseId, value);

        switch (value){
            case 100:
                return CaseChannelEnum.OFFLINE;
            case 200:
                return CaseChannelEnum.ONLINE;
            case 300:
                return CaseChannelEnum.NON_SERVICE;
            default:
                return CaseChannelEnum.OFFLINE;
        }
    }
}
