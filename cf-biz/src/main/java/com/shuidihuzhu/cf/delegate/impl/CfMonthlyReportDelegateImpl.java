package com.shuidihuzhu.cf.delegate.impl;

import com.shuidihuzhu.cf.delegate.ICfMonthlyReportDelegate;
import com.shuidihuzhu.charity.client.api.MonthlyReportClient;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/6/1  16:55
 */
@Slf4j
@Service
public class CfMonthlyReportDelegateImpl implements ICfMonthlyReportDelegate {

    @Autowired
    private MonthlyReportClient monthlyReportClient;

    public boolean getShowMonthlyReport(long userId) {

        if (userId <= 0) {
            return false;
        }

        Response<Boolean> response = monthlyReportClient.getShowMonthlyReport(userId);

        return Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(false);
    }

}
