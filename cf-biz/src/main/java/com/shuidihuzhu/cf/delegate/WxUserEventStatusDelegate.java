package com.shuidihuzhu.cf.delegate;

import com.shuidihuzhu.wx.grpc.client.feign.WxUserEventStatusServiceClient;
import com.shuidihuzhu.wx.grpc.model.WxMpSubscribeModel;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-05-16 8:03 PM
 **/
@Component
public class WxUserEventStatusDelegate {

    @Resource
    private WxUserEventStatusServiceClient wxUserEventStatusServiceClient;

    public WxUserEventStatusServiceClient getClient() {
        return wxUserEventStatusServiceClient;
    }


    public WxMpSubscribeModel getSubscribeByUserId(Long userId, int thirdType) {
        return getClient().getSubscribeByUserId(userId, thirdType);
    }

    public boolean checkSubscribeByUserId(long userId, int thirdType) {
        return getClient().checkSubscribeByUserId(userId, thirdType);
    }

    public boolean checkSubscribeByOpenId(String openId, int thirdType) {
        return getClient().checkSubscribeByOpenId(openId, thirdType);
    }

    public boolean checkHasSubscribedByUserId(long userId, List<Integer> thirdTypes) {
        return getClient().checkHasSubscribedByUserId(userId, thirdTypes);
    }

    public List<WxMpSubscribeModel> listSubscribeByUserId(long userId, List<Integer> thirdTypes) {
        return getClient().listSubscribeByUserId(userId, thirdTypes);
    }

    public WxMpSubscribeModel getSubscribeByOpenId(String openId, int thirdType) {
        return getClient().getSubscribeByOpenId(openId, thirdType);
    }
}
