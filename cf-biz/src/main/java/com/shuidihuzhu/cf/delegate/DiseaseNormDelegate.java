package com.shuidihuzhu.cf.delegate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Maps;
import com.shuidihuzhu.alps.feign.ocean.DiseaseNormReqParams;
import com.shuidihuzhu.alps.feign.ocean.OceanApiClient;
import com.shuidihuzhu.alps.feign.ocean.OceanApiRequest;
import com.shuidihuzhu.alps.feign.ocean.OceanApiResponse;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/4/19  3:52 下午
 */
@Service
@Slf4j
public class DiseaseNormDelegate {

    @Resource
    private OceanApiClient oceanApiClient;

    public Map<String, Set<String>> agent(List<String> diseaseList) {

        DiseaseNormReqParams diseaseNormReqParams = new DiseaseNormReqParams();
        diseaseNormReqParams.setDiseaseList(diseaseList);

        String userId = "10007";
        String token = "cc47459628d6ed3b";
        String tag = "ai-disease-norm";
        String body = JSON.toJSONString(diseaseNormReqParams);

        OceanApiRequest oceanApiRequest = new OceanApiRequest(userId, token, tag, body, null);
        Response<OceanApiResponse> response = oceanApiClient.agent(oceanApiRequest);

        log.info("新版疾病归一AI返回值 response:{}", JSON.toJSONString(response));

        String result = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .map(OceanApiResponse::getBody)
                .orElse(null);

        if (StringUtils.isEmpty(result)) {
            return Maps.newHashMap();
        }
        return JSON.parseObject(result, new TypeReference<Map<String, Set<String>>>() {});
    }
}
