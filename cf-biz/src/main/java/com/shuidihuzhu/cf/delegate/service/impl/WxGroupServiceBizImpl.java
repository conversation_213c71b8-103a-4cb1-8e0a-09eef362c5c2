package com.shuidihuzhu.cf.delegate.service.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.delegate.service.WxGroupServiceBiz;
import com.shuidihuzhu.wx.grpc.client.WebAppInfoListResponse;
import com.shuidihuzhu.wx.grpc.client.WxAppInfo;
import com.shuidihuzhu.wx.grpc.client.feign.WxGroupServiceClient;
import com.shuidihuzhu.wx.grpc.model.WxGroupInfoModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/5/9 2:33 PM
 */
@Slf4j
@Service
@RefreshScope
public class WxGroupServiceBizImpl implements WxGroupServiceBiz {

    @Resource
    private WxGroupServiceClient wxGroupServiceClient;

    @Override
    public List<WxGroupInfoModel> getByGroupId(int groupId) {
        return wxGroupServiceClient.getByGroupId(groupId);
    }
}
