package com.shuidihuzhu.cf.delegate;

import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.notice.workwx.WorkWeiXinContentBuilder;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-06-14  18:18
 * 拼接内容可以使用下面工具
 * @see WorkWeiXinContentBuilder
 * @see com.shuidihuzhu.cf.service.notice.workwx.WorkWeiXinService
 */
public interface IWorkWeiXinDelegate {
    OpResult sendByUser(List<String> operatorNameList, String s);

    OpResult sendByGroup(String groupId, String content);
}
