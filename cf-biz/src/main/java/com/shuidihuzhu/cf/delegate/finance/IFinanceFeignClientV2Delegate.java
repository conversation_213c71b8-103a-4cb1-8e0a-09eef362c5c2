package com.shuidihuzhu.cf.delegate.finance;

import com.shuidihuzhu.cf.finance.model.vo.CfFinanceCapitalStatusVo;
import com.shuidihuzhu.cf.finance.model.vo.DonationAmountInFenVo;
import com.shuidihuzhu.common.web.model.Response;


public interface IFinanceFeignClientV2Delegate {
	Response<DonationAmountInFenVo> getDonationAmountInFen(String infoUuid);

	Response<CfFinanceCapitalStatusVo> getFinanceCapitalStatusV2(String infoUuid, int caseId);

	Response<CfFinanceCapitalStatusVo> getFinanceCapitalStatusV1(String infoUuid);

	Response<CfFinanceCapitalStatusVo> getFinanceCapitalStatusV1(String infoUuid, int caseId);
}
