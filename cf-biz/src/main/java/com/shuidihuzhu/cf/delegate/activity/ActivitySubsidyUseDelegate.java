package com.shuidihuzhu.cf.delegate.activity;

import com.shuidihuzhu.cf.activity.model.SubsidyUseResult;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.service.activity.subsidy.impl.ActivitySubsidyServiceImpl;
import com.shuidihuzhu.common.web.model.Response;

/**
 * <AUTHOR>
 */
public interface ActivitySubsidyUseDelegate {

    boolean onCfPaySuccess(CrowdfundingOrder order);

    Response<Void> onShareSubsidyUse(long viewUserId, long sourceUserId, int caseId, ActivitySubsidyServiceImpl.Model model, boolean eagelValue);

    RpcResult<SubsidyUseResult> onBonfireSubsidy(CrowdfundingOrder order, int teamNum);
}
