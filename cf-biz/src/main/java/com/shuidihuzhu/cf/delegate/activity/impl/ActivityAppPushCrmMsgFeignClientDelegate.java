package com.shuidihuzhu.cf.delegate.activity.impl;

import com.shuidihuzhu.cf.activity.feign.v2.ActivityAppPushCrmMsgFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2020/7/15
 */
@Slf4j
@Service
public class ActivityAppPushCrmMsgFeignClientDelegate {

    @Resource
    private ActivityAppPushCrmMsgFeignClient activityAppPushCrmMsgFeignClient;

    public void sendReachEdMoneyLimit(long activityId, int caseId, int moneyLimit) {

        try {
            activityAppPushCrmMsgFeignClient.sendReachEdMoneyLimit(activityId, caseId, moneyLimit);
        } catch (Exception e) {
            log.error("activityAppPushCrmMsgFeignClient.sendReachEdMoneyLimit error. activityId:{}, caseId:{}, moneyLimit:{}", activityId, caseId, moneyLimit, e);
        }
    }
}
