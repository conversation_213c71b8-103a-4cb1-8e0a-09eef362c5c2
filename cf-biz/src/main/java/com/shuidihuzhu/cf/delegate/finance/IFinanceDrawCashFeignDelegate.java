package com.shuidihuzhu.cf.delegate.finance;

import com.shuidihuzhu.cf.finance.model.CfReminderWord;
import com.shuidihuzhu.cf.finance.model.drawcash.CfDrawExpectCapitationModel;
import com.shuidihuzhu.cf.finance.model.vo.CfDrawCashApplyVo;
import com.shuidihuzhu.cf.finance.model.vo.DrawApplyCheckResult;
import com.shuidihuzhu.cf.finance.model.vo.drawcash.CfApplyDrawWithAuditVo;
import com.shuidihuzhu.cf.finance.model.vo.drawcash.CrowdfundingInfoApplyDrawVo;
import com.shuidihuzhu.cf.vo.AnchorPageVo;
import com.shuidihuzhu.client.model.AnchorPageVO;
import com.shuidihuzhu.common.web.model.Response;

import java.util.List;
import java.util.Map;

/**
 * @author: wanghui
 * @create: 2019/6/12 5:02 PM
 */
public interface IFinanceDrawCashFeignDelegate {
	Response<Map<Integer, DrawApplyCheckResult>> applyCheckList(List<Integer> infoIdList, boolean checkEndTime);

	// 筹款人 量级请求  建议使用 CfDrawCashFeignClient.getApplyInfo 支持捐款人量级
	Response<CfDrawCashApplyVo> getApplyInfo(int caseId);

	Response<Map<Integer, CfDrawCashApplyVo>> getApplyInfoMap(List<Integer> caseIds);

	/**
	 * 边筹边取 提现申请
	 *
	 * @param crowdfundingInfoApplyDrawVo
	 * @return
	 */
	Response<DrawApplyCheckResult> drawSplitApplyDrawCash(CrowdfundingInfoApplyDrawVo crowdfundingInfoApplyDrawVo);

	/**
	 * 获取最新的一条提现申请信息，不含关闭的
	 * v1 v2 流程兼容
	 *
	 * @param infoUuid
	 * @return 提现申请对象
	 */
	Response<CfApplyDrawWithAuditVo> getLastValidApply(String infoUuid);
	/**
	 * 计算提现预期到账金额
	 *
	 * @param caseId
	 * @param applyAmountInFen
	 * @param applyTime
	 * @return
	 */
	CfReminderWord<CfDrawExpectCapitationModel> getExpectDrawAmountInfo(int caseId, int applyAmountInFen, long applyTime);

	/**
	 * 获取最近一次提现信息
	 *
	 * @param caseId -
	 * @return -
	 */
    Response<CfDrawCashApplyVo> getRecentApplyInfo(int caseId);

	/**
	 * 获取案例提现生命周期
	 *
	 *
	 * @param pageSize
	 * @param applyId
	 * @param caseId    -
	 * @return  -
	 */
	Response<AnchorPageVO<CfDrawCashApplyVo>> getDrawLifeCycle(int caseId, long applyId, int pageSize);
}
