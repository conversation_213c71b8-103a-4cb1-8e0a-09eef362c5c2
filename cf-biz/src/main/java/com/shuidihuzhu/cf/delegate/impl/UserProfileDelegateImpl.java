package com.shuidihuzhu.cf.delegate.impl;

import com.shuidihuzhu.cf.delegate.IUserProfileDelegate;
import com.shuidihuzhu.data.analytics.javasdk.core.UserProfile;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * Created by sven on 2019/9/16.
 *
 * <AUTHOR>
 */
@RefreshScope
@Slf4j
@Service
public class UserProfileDelegateImpl implements IUserProfileDelegate {

    @Autowired
    private UserProfile userProfile;

    @Async
    @Override
    public <T> void setProfile(long userId, String profileKey, T value) {

        try {
            log.info("start set user profile {} {} {}", userId, profileKey, value);

            userProfile.profileSet(CF_TOKEN, String.valueOf(userId), CF_BIZ, profileKey, value);

            log.info("end set user profile {} {} {}", userId, profileKey, value);
        }  catch (Throwable e) {
            log.warn("上报大数据错误。profileKey:{} userId:{}", profileKey, userId);
        }
    }

    @Async
    @Override
    public void deleteProfile(long userId, String profileKey){
        try {
            log.info("start delete user profile {} {}", userId, profileKey);

            userProfile.profileDelete(CF_TOKEN, String.valueOf(userId), CF_BIZ, profileKey);

            log.info("end delete user profile {} {} {}", userId, profileKey);
        }  catch (Throwable e) {
            log.warn("上报大数据错误。profileKey:{} userId:{}", profileKey, userId);
        }
    }
}
