package com.shuidihuzhu.cf.delegate.impl;

import brave.Tracing;
import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.po.WxUserDetailSimplifyPo;
import com.shuidihuzhu.account.model.wx.WxMpSubscribeModel;
import com.shuidihuzhu.cf.constants.WxConstants;
import com.shuidihuzhu.cf.delegate.WxSubscribeEventDelegate;
import com.shuidihuzhu.cf.delegate.WxUserDetailDelegate;
import com.shuidihuzhu.wx.grpc.client.feign.WxSubscribeEventServiceClient;
import com.shuidihuzhu.wx.grpc.enums.EventType;
import com.shuidihuzhu.wx.grpc.enums.MsgType;
import com.shuidihuzhu.wx.grpc.enums.WxMpBizType;
import com.shuidihuzhu.wx.grpc.model.WxSubscribeModel;
import com.shuidihuzhu.wx.grpc.model.WxSubscribeResponseDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Collection;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2018-12-26  15:02
 */
@Service
@Slf4j
public class WxSubscribeEventDelegateImpl implements WxSubscribeEventDelegate {

    @Autowired
    private Tracing tracing;
    private static ExecutorService executorService;

    @PostConstruct
    public void init() {
        executorService = tracing.currentTraceContext().executorService(Executors.newFixedThreadPool(10));
    }
    @Resource
    private WxUserDetailDelegate wxUserDetailDelegate;

    @Resource
    private WxSubscribeEventServiceClient wxSubscribeEventServiceClient;

    public WxSubscribeEventServiceClient getClient() {
        return wxSubscribeEventServiceClient;
    }

    @Override
    public Stream<Long> filterSubscribedMajor(Collection<Long> userIds) {

        List<WxMpSubscribeModel> result = parallelQuerySimplifyWxUserDetail(userIds);

        return result.stream()
                .filter(WxMpSubscribeModel::isSubscribe)
                .map(WxMpSubscribeModel::getUserId)
                .distinct();
    }

    private List<WxMpSubscribeModel> parallelQuerySimplifyWxUserDetail(Collection<Long> userIds){
        if (CollectionUtils.isEmpty(userIds)) {
            return Lists.newArrayList();
        }
        List<Future<List<WxMpSubscribeModel>>> futureList = Lists.newArrayList();

        List<List<Long>> userIdLists = Lists.partition(Lists.newArrayList(userIds.stream().distinct().collect(Collectors.toList())), 500);

        for (List<Long> userIdList : userIdLists){
            Future<List<WxMpSubscribeModel>> future = executorService.submit(() ->
                    wxUserDetailDelegate.listByUserIdsAndTypes(new HashSet<>(userIdList), WxConstants.MAJOR));

            futureList.add(future);
        }

        List<WxMpSubscribeModel> result = Lists.newArrayList();

        for (Future<List<WxMpSubscribeModel>> futures : futureList){
            try {
                List<WxMpSubscribeModel> simplifyPos = futures.get();
                if(CollectionUtils.isNotEmpty(simplifyPos)){
                    result.addAll(simplifyPos);
                }

            } catch (Exception e) {
                log.error("parallelQuerySimplifyWxUserDetail Exception", e);
            }
        }

        return result;
    }

    @Override
    public void saveEvent(String fromUserName, String toUserName, WxMpBizType wxMpBizType
            , MsgType msgType, EventType eventType, String eventKey, Date eventTime) {
        getClient().saveEvent(fromUserName, toUserName, wxMpBizType, msgType, eventType, eventKey, eventTime);
    }

    @Override
    public List<WxSubscribeModel> batchCheckSubscribeByUserIds(List<Long> userIds, int thirdType) {
        return getClient().batchCheckSubscribeByUserIds(userIds, thirdType);
    }

    @Override
    public WxSubscribeResponseDto getSubscribeOnlyOne(long userId, int mpBizType, int eventType, long start, long end) {
        return getClient().getSubscribeOnlyOne(userId, mpBizType, eventType, start, end);
    }

    @Override
    public List<WxSubscribeModel> getSubscribesByUserId(long userId) {
        return getClient().getSubscribesByUserIdV2(userId);
    }
}
