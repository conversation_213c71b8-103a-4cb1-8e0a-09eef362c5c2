package com.shuidihuzhu.cf.delegate;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.client.baseservice.ad.v1.AdClient;
import com.shuidihuzhu.client.baseservice.ad.v1.model.AdActiveParam;
import com.shuidihuzhu.client.baseservice.ad.v1.model.AdActiveVO;
import com.shuidihuzhu.client.baseservice.ad.v1.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2020/11/24
 */

@Slf4j
@Service
public class AdClientDelegate {

    @Resource
    private AdClient adClient;

    @Autowired
    private ApplicationService applicationService;

    public Optional<String> getGoodsTag(long userId, int thirdType) {
        AdActiveParam activeParam = new AdActiveParam();
        activeParam.setUserId(userId);
        activeParam.setThirdType(thirdType);
        activeParam.setAdPositionId(applicationService.isDevelopment() ? "posad16074091889853304" : "posad16086312657519855");

        try {
            Response<AdActiveVO> active = adClient.getActive(activeParam);
            log.debug("调用广告系统获取goodsTag activeParam:{}, response:{}", JSON.toJSONString(activeParam), JSON.toJSONString(active));
            if (active == null || active.getData() == null) {
                return Optional.empty();
            }
            return Optional.ofNullable(active.getData().getGoodsTag());
        } catch (Exception e) {
            log.error("调用广告系统获取goodsTag异常！userId:{}, thirdType:{}", userId, thirdType, e);
        }
        return Optional.empty();
    }

}
