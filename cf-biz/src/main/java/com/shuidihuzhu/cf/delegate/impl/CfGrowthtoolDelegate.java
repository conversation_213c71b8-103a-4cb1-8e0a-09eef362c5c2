package com.shuidihuzhu.cf.delegate.impl;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.delegate.ICfGrowthtoolDelegate;
import com.shuidihuzhu.client.cf.growthtool.client.CfBdPartnerCaseRelationFeignClient;
import com.shuidihuzhu.client.cf.growthtool.client.CfBdPartnerInfoFeignClient;
import com.shuidihuzhu.client.cf.growthtool.client.CfGrowthtoolFeginClient;
import com.shuidihuzhu.client.cf.growthtool.client.CfGrowthtoolVolunteerFeignClient;
import com.shuidihuzhu.client.cf.growthtool.model.*;
import com.shuidihuzhu.client.cf.growthtool.model.lovepartner.CfPartnerCaseRelationDo;
import com.shuidihuzhu.client.cf.growthtool.model.lovepartner.CfPartnerInfoDo;
import com.shuidihuzhu.client.model.CheckUserScanOrInvitedResultModel;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * @author: wanghui
 * @create: 2019/7/23 4:24 PM
 */
@Slf4j
@Service
public class CfGrowthtoolDelegate implements ICfGrowthtoolDelegate {
    @Autowired
    private CfGrowthtoolFeginClient cfGrowthtoolFeginClient;
    @Autowired
    private CfGrowthtoolVolunteerFeignClient cfGrowthtoolVolunteerFeignClient;

    @Autowired
    private CfBdPartnerInfoFeignClient partnerInfoFeignClient;

    @Autowired
    private CfBdPartnerCaseRelationFeignClient partnerCaseRelationFeignClient;

    @Override
    public Response registerAdd(CfTouFangSignDto cfTouFangSignDto) {
        return cfGrowthtoolFeginClient.registerAdd(cfTouFangSignDto);
    }

    @Override
    public Response toutiaoRegister(CfTouFangSignDto cfTouFangSignDto) {
        return cfGrowthtoolFeginClient.toutiaoRegister(cfTouFangSignDto);
    }

    @Override
    public Response registerMobile(CfTouFangSignDto cfTouFangSignDto) {
        return cfGrowthtoolFeginClient.registerMobile(cfTouFangSignDto);
    }

    @Override
    public Response registerByJson(String param) {
        return cfGrowthtoolFeginClient.registerByJson(param);
    }

    @Override
    public Response<CrowdfundingVolunteer> getVolunteerByUniqueCode(String uniqueCode) {
        return cfGrowthtoolFeginClient.getVolunteerByUniqueCode(uniqueCode);
    }

    @Override
    public Integer syncInviteLog(Long currentUserId, Long sourceUserId){
        Response<Integer> response = cfGrowthtoolFeginClient.syncInviteLog(currentUserId, sourceUserId);
        return response.ok()?response.getData():0;
    }

    @Override
    public Integer insertVolunteerCreateCaseRecord(Long userId, String volunteerUnique){
        Response<Integer> response = cfGrowthtoolFeginClient.insertVolunteerCreateCaseRecord(userId, volunteerUnique);
        return response.ok()?response.getData():0;
    }

    @Override
    public boolean checkIsVolunteerCreateCase(long userId) {
        Response<Boolean> response = cfGrowthtoolFeginClient.checkIsVolunteerCreateCase(userId);
        return Optional.ofNullable(response).map(Response::getData).orElse(false);
    }

    @Override
    public CheckUserScanOrInvitedResultModel checkUserScan(Long userId, String infoUuid){
        if (infoUuid==null){
            infoUuid = "";
        }
        Response<CheckUserScanOrInvitedResultModel> response = cfGrowthtoolFeginClient.checkUserScan(userId, infoUuid);
        return response.ok()?response.getData():new CheckUserScanOrInvitedResultModel();
    }

    @Override
    public CheckUserScanOrInvitedResultModel checkUserIsInvite(Long userId, Integer checkUserScan){
        Response<CheckUserScanOrInvitedResultModel> response = cfGrowthtoolFeginClient.checkUserIsInvite(userId, checkUserScan);
        return response.ok()?response.getData():new CheckUserScanOrInvitedResultModel();
    }

    @Override
    public CheckUserScanOrInvitedResultModel checkUserIsInviteByCrowdfundUser(Long userId, Integer checkUserScan){
        Response<CheckUserScanOrInvitedResultModel> response = cfGrowthtoolFeginClient.checkUserIsInviteByCrowdfundUser(userId, checkUserScan);
        return response.ok()?response.getData():new CheckUserScanOrInvitedResultModel();
    }
    @Override
    public void transferCfVolunteer( long fromUserId,  long toUserId){
        cfGrowthtoolVolunteerFeignClient.transferCfVolunteer(fromUserId,toUserId);
    }
    @Override
    public CrowdfundingVolunteer getCfVolunteerDOByUniqueCode(String uniqueCode){
        Response<CrowdfundingVolunteer> response = cfGrowthtoolVolunteerFeignClient.getCfVolunteerDOByUniqueCode(uniqueCode);
        return response.ok()?response.getData():null;
    }
    @Override
    public CfVolunteerMaterialDO getVolunteerMateriByUniqueCode(String uniqueCode) {
        Response<CfVolunteerMaterialDO> response = cfGrowthtoolVolunteerFeignClient.getVolunteerMateriByUniqueCode(uniqueCode);
        return response.ok()?response.getData():null;
    }
    @Override
    public String checkExistQrCodeAndReturnMobileByMobile(String mobile){
        Response<String> response = cfGrowthtoolVolunteerFeignClient.checkExistQrCodeAndReturnMobileByMobile(mobile);
        return response.ok()?response.getData():null;
    }

    @Override
    public void checkAndPushFangbiandaiForUser(String openId, String deviceId) {
        Response<Boolean> booleanResponse = cfGrowthtoolFeginClient.checkAndPushFangbiandaiForUser(openId, deviceId);
        log.info(this.getClass().getName()+" checkAndPushFangbiandaiForUser result:{}", JSON.toJSONString(booleanResponse));
    }

    @Override
    public List<BdCrmVolunteerOrgnizationSimpleModel> getBdCrmVolunteerOrgnizationSimpleModelByUniqueCode(String uniqueCode) {
        Response<List<BdCrmVolunteerOrgnizationSimpleModel>> response = cfGrowthtoolFeginClient.getBdCrmVolunteerOrgnizationSimpleModelByUniqueCode(uniqueCode);
        return response.ok() ? response.getData() : null;
    }

    @Override
    public int updateSaoMaNoClewCreateDesc(Integer scanId, String desc) {
        Response<Integer> response = cfGrowthtoolFeginClient.updateScanVolunteerCodeNoClewCreateDesc(Long.valueOf(scanId),desc);
        return response.ok() ? response.getData() : 0;
    }

    @Override
    public CfPartnerInfoDo getCfPartnerInfoByUniqueCode(String uniqueCode) {
        Response<CfPartnerInfoDo> response = partnerInfoFeignClient.getPartnerInfoByUniqueCode(uniqueCode);
        return response.ok() ? response.getData() : null;
    }

    @Override
    public Integer createPartnerCaseRelation(CfPartnerCaseRelationDo cfPartnerCaseRelationDo) {
        Response<Integer> response = partnerCaseRelationFeignClient.createPartnerCaseRelation(cfPartnerCaseRelationDo);
        return response.ok() ? response.getData() : 0;
    }

    @Override
    public CfVolunteerSimpleVo getVolunteerSimpleByUniqueCode(String volunteerUniqueCode) {
        Response<CfVolunteerSimpleVo> resp = cfGrowthtoolVolunteerFeignClient.getVolunteerSimpleByUniqueCode(volunteerUniqueCode);
        if (NewResponseUtil.isNotOk(resp)){
            return null;
        }
        return resp.getData();
    }
}
