package com.shuidihuzhu.cf.constants;

/**
 * <AUTHOR>
 * @date 2018-05-31  11:00
 */
public interface NoticeActionConstants {

    /**
     * 捐款关注欢迎语
     */
    String DONOR_GREET = "CF_DONOR_GREET";


    // 延时场景问答消息
    /**
     * 发送问题
     */
    String DELAY_QUESTION = "CF_SCENE_DELAY_QUESTION";
    /**
     * 2小时未点击发送
     */
    String DELAY_ANSWER_DEFAULT = "CF_SCENE_DELAY_ANSWER_DEFAULT";

    /**
     * 点击时发送
     */
    String DELAY_ANSWER_VISIT = "CF_SCENE_DELAY_ANSWER_VISIT";

    /**
     * 已发送过时 再次点击发送
     */
    String DELAY_ANSWER_HAS_SEND = "CF_SCENE_DELAY_ANSWER_HAS_SEND";

    /**
     * 催
     * 保存基本信息后 没有发起的用户
     * 发起
     * 923 消息
     * https://wiki.shuiditech.com/pages/viewpage.action?pageId=19957097
     */
    String SAVE_BASIC_INFO_DELAY_TRANSFORM_RAISE = "SAVE_BASIC_INFO_DELAY_TRANSFORM_RAISE";

    /**
     * 催
     * 保存基本信息后 没有发起的用户
     * 发起
     * 客服消息 失败 发模板消息
     * 924 消息
     * https://wiki.shuiditech.com/pages/viewpage.action?pageId=19957097
     */
    String SAVE_BASIC_INFO_DELAY_TRANSFORM_RAISE_RETRY = "SAVE_BASIC_INFO_DELAY_TRANSFORM_RAISE_RETRY";

    /**
     * 提醒发起人验证身份
     * https://wiki.shuiditech.com/pages/viewpage.action?pageId=19961370
     */
    String PROMOTE_VERIFY_USER_IDENTIFY = "PROMOTE_VERIFY_USER_IDENTIFY";

    /**
     * 身份验证失败 提醒重新填写
     * https://wiki.shuiditech.com/pages/viewpage.action?pageId=19961370
     */
    String PROMOTE_VERIFY_USER_IDENTIFY_FAIL_RETRY = "PROMOTE_VERIFY_USER_IDENTIFY_FAIL_RETRY";

    /**
     * 一度好友捐款后给好友发送消息
     * https://wiki.shuiditech.com/pages/viewpage.action?pageId=57934180
     */
    String DONATE_THEN_NOTICE_FRIEND = "DONATE_THEN_NOTICE_FRIEND";

}
