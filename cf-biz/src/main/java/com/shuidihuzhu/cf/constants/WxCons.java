package com.shuidihuzhu.cf.constants;

import com.shuidihuzhu.cf.util.crowdfunding.CfUrlConfig;

/**
 * Created by yao on 16/4/21.
 */
public class WxCons {

    /**
     * 测试
     * */
//    public static final String PAY_CALLBACK = "http://www.shuidihuzhu.com/test/api/order/callback";

    /**
     * 线上
     * */
//    public static final String PAY_CALLBACK = "http://www.shuidihuzhu.com/api/order/callback";
//    public static final String PAY_CALLBACK_CROWDFUNDING = "http://www.shuidihuzhu.com/api/cf/order/callback";
//    public static final String PAY_CALLBACK_CLOCKIN = "http://www.shuidihuzhu.com/api/clockin/wxpay/callback";
//    public static final String PAY_CALLBACK = UrlConfig.apiDomain() + "/api/order/callback";
//    public static final String PAY_CALLBACK_CROWDFUNDING = UrlConfig.apiDomain() + "/api/cf/order/callback";
//    public static final String PAY_CALLBACK_CLOCKIN = UrlConfig.apiDomain() + "/api/clockin/wxpay/callback";
    public static final String PAY_CALLBACK = CfUrlConfig.apiDomain() + "/api/order/callback";
    public static final String PAY_CALLBACK_CROWDFUNDING = CfUrlConfig.apiDomain() + "/api/cf/order/callback";
    public static final String PAY_CALLBACK_CLOCKIN = CfUrlConfig.apiDomain() + "/api/clockin/wxpay/callback";
    public static final String TRADE_TYPE_JSAPI = "JSAPI";
    public static final String TRADE_TYPE_NATIVE = "APP";
}
