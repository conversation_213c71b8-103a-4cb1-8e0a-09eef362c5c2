package com.shuidihuzhu.cf.service.huzhugrpc;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.verification.CrowdFundingVerificationBiz;
import com.shuidihuzhu.cf.dao.crowdfunding.UserRealInfoDao;
import com.shuidihuzhu.cf.dao.crowdfunding.slave.UserRealInfoSlaveDao;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.IdcardVerifyStatus;
import com.shuidihuzhu.cf.model.crowdfunding.UserRealInfo;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.common.web.util.IdGenUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class HzUserRealInfoService {

    @Autowired
    private UserRealInfoDao userRealInfoDao;
    @Autowired
    private CrowdFundingVerificationBiz verificationBiz;
    @Autowired
    private UserRealInfoSlaveDao userRealInfoSlaveDao;
    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;
    @Autowired
    private UserInfoDelegate userInfoDelegate;

    public UserRealInfo save(long userId, String name, String idCard) {
        UserRealInfo userRealInfo = new UserRealInfo();
        userRealInfo.setUserId(userId);
        userRealInfo.setName(name);
        userRealInfo.setOrderId(IdGenUtil.tradeNo());
        userRealInfo.setCryptoIdCard(oldShuidiCipher.aesEncrypt(idCard));
        userRealInfo.setIdcardVerifyStatus(IdcardVerifyStatus.HANDLING.getCode());
        this.userRealInfoDao.save(userRealInfo);
        return userRealInfo;
    }

    public long getCountByCreateTime(long userId, Timestamp startTime, Timestamp endTime) {
        return this.userRealInfoDao.getCountByCreateTime(userId, startTime, endTime);
    }

    public List<UserRealInfo> getByVerifyStatus(long userId, List<IdcardVerifyStatus> idcardVerifyStatusList) {
        List<Integer> list = Lists.newArrayListWithCapacity(idcardVerifyStatusList.size());
        idcardVerifyStatusList.forEach(idcardVerifyStatus -> {
            list.add(idcardVerifyStatus.getCode());
        });
        return this.userRealInfoDao.getByVerifyStatus(userId, list);
    }

    public UserRealInfo getLastOne(long userId) {
        return this.userRealInfoDao.getLastOne(userId);
    }

    public int updateIdcardVerifyStatus(int id, IdcardVerifyStatus idcardVerifyStatus, String resultCode,
                                        String resultMsg) {
        if (id <= 0) {
            return -1;
        }
        this.userRealInfoDao.updateIdcardVerifyStatus(id, idcardVerifyStatus.getCode(), resultCode, resultMsg);
        if (idcardVerifyStatus == IdcardVerifyStatus.HANDLE_SUCCESS) {
            UserRealInfo userRealInfo = this.getById(id);
            this.verificationBiz.updateUserName(userRealInfo.getUserId(), userRealInfo.getName());
        }
        return 1;
    }

    public UserRealInfo getById(int id) {
        UserRealInfo userRealInfo = this.userRealInfoDao.getById(id);
        if (userRealInfo != null) {
            userRealInfo.setIdCard(shuidiCipher.decrypt(userRealInfo.getCryptoIdCard()));
        }
        return userRealInfo;
    }

    public Map<Long, UserRealInfo> getSuccessByUserIds(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyMap();
        }
        List<UserRealInfo> userRealInfos = this.userRealInfoDao.getSuccessByUserIds(userIds);
        Map<Long, UserRealInfo> userRealInfoMap = Maps.newHashMap();
        userRealInfos.forEach(userRealInfo -> {
            userRealInfoMap.put(userRealInfo.getUserId(), userRealInfo);
        });
        return userRealInfoMap;
    }

    public int countByMin(Timestamp start, Timestamp end) {
        if (start == null || end == null || start.after(end)) {
            return 0;
        }
        Integer count = this.userRealInfoSlaveDao.countByMin(start, end);
        return (count == null ? 0 : count);
    }

    public int countSuccess(Timestamp start, Timestamp end) {
        return userRealInfoSlaveDao.countSuccess(start, end);
    }

    public boolean isMedical(Long userId) {

        List<UserRealInfo> list = userRealInfoDao.getByUserId(userId);

        long count = 0L;
        if (CollectionUtils.isNotEmpty(list)) {
            // 实名认证通过且是医护
            count = list.stream()
                    .filter(e -> 2 == e.getIdcardVerifyStatus() && 1 == e.getMedicalStatus()).count();
        }

        return count > 0;
    }

    public int updateIsMedical(long userId, int medicalStatus) {
        return userRealInfoDao.updateMedicalStatus(userId, medicalStatus);
    }

    public int updateIsMedicalById(long userId, int medicalStatus) {
        return userRealInfoDao.updateIsMedicalById(userId, medicalStatus);
    }

    public int updateMedicalImageUrl(long userId, String medicalVerifyImageUrl) {
        return userRealInfoDao.updateMedicalImageUrl(userId, medicalVerifyImageUrl);
    }

    public UserRealInfo getByUserId(Long userId) {
        return userRealInfoDao.getLastOne(userId);
    }

    public int deleteById(int id) {
        return userRealInfoDao.deleteById(id);
    }

    public List<UserRealInfo> getOnceAllSuccess(long userId) {
        return userRealInfoDao.getOnceAllSuccess(userId);
    }

    public List<UserRealInfo> getByUserIdAndIdCard(String mobile, String idCard) {

        long userId = 0;
        if (StringUtils.isNotBlank(mobile)) {
            UserInfoModel userModel = userInfoDelegate.getUserInfoByMobile(mobile);
            if (userModel != null) {
                userId = userModel.getUserId();
            }
        }

        String cryptoIdCard = oldShuidiCipher.aesEncrypt(idCard);
        if (userId == 0 && StringUtils.isBlank(cryptoIdCard)) {
            return Lists.newArrayList();
        }

        return userRealInfoDao.getByUserIdAndIdCard(userId, cryptoIdCard);
    }


}
