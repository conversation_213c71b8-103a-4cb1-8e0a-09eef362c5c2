package com.shuidihuzhu.cf.service.crowdfunding;

import com.shuidihuzhu.cf.service.risk.dark.DarkListService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by Ahrievil on 2017/11/1
 */
@Service
public class CfBlackListCacheService {

    @Autowired
    private DarkListService darkListService;

    public boolean isContainsByUgc(long userId) {
        //this is very important
        if (userId <= 0) {
            return false;
        }
        boolean passed = darkListService.checkUGCPassed(userId);
        return !passed;
    }

}
