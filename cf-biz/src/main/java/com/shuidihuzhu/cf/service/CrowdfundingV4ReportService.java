package com.shuidihuzhu.cf.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.CfReportDisposeActionBiz;
import com.shuidihuzhu.cf.client.adminpure.enums.AddTrustAuditStatusEnum;
import com.shuidihuzhu.cf.client.adminpure.feign.AdminCredibleInfoFeignClient;
import com.shuidihuzhu.cf.client.adminpure.model.report.CfCredibleInfoVo;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.dao.crowdfunding.*;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.CfReportTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoStatusEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.IdcardVerifyStatus;
import com.shuidihuzhu.cf.model.ReportPageInfoVO;
import com.shuidihuzhu.cf.model.ReportTagVO;
import com.shuidihuzhu.cf.model.UserReportVO;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfReportAddTrustDisposeVo;
import com.shuidihuzhu.cf.model.getfundinginfo.CrowdfundingUserInfo;
import com.shuidihuzhu.cf.model.risk.DarkListResult;
import com.shuidihuzhu.cf.risk.client.risk.BlacklistVerifyClient;
import com.shuidihuzhu.cf.risk.client.rpc.CfReportClient;
import com.shuidihuzhu.cf.risk.model.ReportHitStrategyRecord;
import com.shuidihuzhu.cf.risk.model.enums.risk.BlacklistVerifyTypeEnum;
import com.shuidihuzhu.cf.risk.model.enums.risk.LimitActionEnum;
import com.shuidihuzhu.cf.risk.model.risk.BlacklistVerifyDto;
import com.shuidihuzhu.cf.service.crowdfunding.CrowdfundingInfoDetailService;
import com.shuidihuzhu.cf.service.huzhugrpc.HzUserRealInfoService;
import com.shuidihuzhu.cf.vo.CfProveInfoVo;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.ext.WorkOrderExtFeignClient;
import com.shuidihuzhu.client.cf.workorder.ext.WorkOrderExtVO;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.util.DateUtil;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * @author: lixiaoshuang
 * @create: 2020-09-22 09:13
 **/
@Service
@Slf4j
public class CrowdfundingV4ReportService {
    @Autowired
    private AdminCredibleInfoFeignClient credibleInfoFeignClient;
    @Autowired
    private CfProveTemplateDao cfProveTemplateDao;
    @Autowired
    private CfReportDisposeActionBiz cfReportDisposeActionBiz;
    @Autowired
    private CfProveSnapshotDao cfProveSnapshotDao;
    @Autowired
    private CredibleInfoDao credibleInfoDao;
    @Autowired
    private CrowdFundingProgressDao crowdFundingProgressDao;
    @Autowired
    private CrowdfundingReportDao crowdfundingReportDao;
    @Autowired
    private AdminCfReportRiskTagLabelDao adminCfReportRiskTagLabelDao;
    @Autowired
    private CfProveDao cfProveDao;
    @Autowired
    private ReportInsteadWorkOrderRelateDAO reportInsteadWorkOrderRelateDAO;
    @Autowired
    private CfWorkOrderClient cfWorkOrderClient;
    @Autowired
    private BlacklistVerifyClient blacklistVerifyClient;
    @Autowired
    private HzUserRealInfoService userRealInfoBiz;
    @Autowired
    private UserInfoDelegate userInfoDelegate;
    @Autowired
    private WorkOrderExtFeignClient workOrderExtFeignClient;
    @Autowired
    private CfReportClient cfReportClient;
    @Autowired(required = false)
    private Producer producer;
    @Autowired
    private CrowdfundingInfoDetailService crowdfundingInfoDetailService;
    @Resource
    private IOssToCosTransService ossToCosTransService;
    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;

    private static final Integer OFFICIAL_TYPE = 3;
    private static final Integer HIGH_REPORT_PEOPLE = 10;
    private static final Integer IS_SHOW = 0;
    private static final Integer IS_NOT_SHOW = 1;


    public CfProveInfoVo getProveInfo(String infoUuid, CrowdfundingInfo fundingInfo) {

        if (Objects.isNull(fundingInfo)) {
            return new CfProveInfoVo();
        }
        CfSendProve cfProve = cfProveDao.getlastOne(fundingInfo.getId());
        if (Objects.isNull(cfProve)) {
            return new CfProveInfoVo();
        }
        List<CfSendProveTemplate> cfProveTemplateVos = cfProveTemplateDao.findByCaseIdAndProveId(fundingInfo.getId(),
                cfProve.getId(), List.of(CrowdfundingInfoStatusEnum.UN_SUBMITTED.getCode(), CrowdfundingInfoStatusEnum.PASSED.getCode(),
                        CrowdfundingInfoStatusEnum.REJECTED.getCode()));
        //4=补充证明代录入
        var auditStatus = credibleInfoDao.getAuditStatus(cfProve.getId(), 4);

        List<CfReportAddTrustDisposeVo> cfReportAddTrustDisposeVos = this.getCfReportAddTrustDisposeVos(infoUuid);

        CfProveInfoVo cfProveInfoVo = new CfProveInfoVo();
        List<String> pictureList = Lists.newArrayList();
        if (StringUtils.isNotBlank(cfProve.getPictureUrl())) {
            pictureList = Stream.of(cfProve.getPictureUrl().split(",")).collect(Collectors.toList());
        }

        if (CollectionUtils.isNotEmpty(cfProveTemplateVos)) {
            cfProveTemplateVos.forEach(cfProveTemplateVo -> {
                cfProveTemplateVo.setCaseId(0);
            });
        }

        cfProveInfoVo.setPictureList(pictureList);
        cfProveInfoVo.setPictureAuditStatus(cfProve.getPictureAuditStatus());
        cfProveInfoVo.setCfProveTemplateVos(cfProveTemplateVos);
        cfProveInfoVo.setDisposeAction(cfReportAddTrustDisposeVos);
        cfProveInfoVo.setStatus(auditStatus);
        return cfProveInfoVo;
    }

    private List<CfReportAddTrustDisposeVo> getCfReportAddTrustDisposeVos(String infoUuid) {
        String content = cfReportDisposeActionBiz.getDisposeAction(infoUuid);
        List<CfReportAddTrustDisposeVo> cfReportAddTrustDisposeVos = Lists.newArrayList();
        if (StringUtils.isNotBlank(content)) {
            try {
                cfReportAddTrustDisposeVos = JSONObject.parseObject(content, new TypeReference<List<CfReportAddTrustDisposeVo>>() {
                });
            } catch (Exception e) {
                log.error("getCfReportAddTrustDisposeVos parse error", e);
            }
        }
        return cfReportAddTrustDisposeVos;
    }

    public Response submitProveInfo(List<CfSendProveTemplate> cfProveInfoVos, String pictureUrl,
                                    int pictureAuditStatus, CrowdfundingInfo fundingInfo) {
        CfSendProve cfProve = cfProveDao.getlastOne(fundingInfo.getId());
        if (!CollectionUtils.isEmpty(cfProveInfoVos)){
            long proveId = cfProveInfoVos.get(0).getProveId();
            if (cfProve.getId() !=  proveId){
                return NewResponseUtil.makeError(CfErrorCode.USER_NO_REFRESH_PAGE);
            }
        }
        if (Objects.isNull(cfProve)) {
            return NewResponseUtil.makeSuccess(null);
        }
        var auditStatus = credibleInfoDao.getAuditStatus(cfProve.getId(), 4);
        int cancelStatus = 5;
        if (auditStatus == cancelStatus) {
            return NewResponseUtil.makeError(CfErrorCode.LINK_FAILURE);
        }
        //如果是驳回状态下提交，删除驳回是审核状态为撤回的模板,清空撤回原因
        if (auditStatus == CrowdfundingInfoStatusEnum.REJECTED.getCode()) {
            cfProveTemplateDao.delete(fundingInfo.getId(), cfProve.getId(), cancelStatus);
            cfProveDao.updateCancelReason(cfProve.getId(), "");
        }
        //提交图片
        if (pictureAuditStatus == CrowdfundingInfoStatusEnum.REJECTED.getCode()
                || pictureAuditStatus == CrowdfundingInfoStatusEnum.UN_SUBMITTED.getCode()) {
            cfProveDao.updateProveById(cfProve.getId(), pictureUrl, CrowdfundingInfoStatusEnum.SUBMITTED.getCode());
        }
        //提交模板
        for (CfSendProveTemplate cfSendProveTemplate : cfProveInfoVos) {
            if (cfSendProveTemplate.getAuditStatus() == CrowdfundingInfoStatusEnum.REJECTED.getCode()
                    || cfSendProveTemplate.getAuditStatus() == CrowdfundingInfoStatusEnum.UN_SUBMITTED.getCode()) {
                cfProveTemplateDao.updateContentById(cfSendProveTemplate.getId(), cfSendProveTemplate.getContent(),
                        CrowdfundingInfoStatusEnum.SUBMITTED.getCode());
            }
        }
        MessageResult messageResult = producer.send(new Message<>(MQTopicCons.CF, MQTagCons.CF_UPDATE_CREDIBLE_STATUS_V2,
                MQTagCons.CF_UPDATE_CREDIBLE_STATUS_V2 + "_" + fundingInfo.getId(), cfProve, DelayLevel.S5));
        log.info("发送提交消息:{}", JSON.toJSONString(messageResult));
        //记录快照
        this.addSnapshot(fundingInfo.getId(), fundingInfo.getInfoId(), CrowdfundingInfoStatusEnum.SUBMITTED.getCode());
        return NewResponseUtil.makeSuccess(null);
    }

    public List<UserReportVO> reportDetailInfo(CrowdfundingInfo crowdfundingInfo, UserInfoModel userInfoModel) {

        List<UserReportVO> userReportVOList = Lists.newArrayList();
        List<WorkOrderVO> workOrderVOS = getReportTypeWorkOrderByCaseId(crowdfundingInfo.getId());
        if (workOrderVOS == null) {
            return userReportVOList;
        }
        // 获取用户对该案例的举报
        List<CrowdfundingReport> userReportList = crowdfundingReportDao.getReportByConditions(userInfoModel.getUserId(), crowdfundingInfo.getId());
        for (WorkOrderVO workOrderVO : workOrderVOS) {
            if (repeatType(workOrderVO)) {
                continue;
            }
            List<WorkOrderExtVO> workOrderExtVOS = Lists.newArrayList();
            Response<List<WorkOrderExtVO>> resp = workOrderExtFeignClient.getListByName(workOrderVO.getWorkOrderId(), "reportId");
            if(NewResponseUtil.isOk(resp)){
                workOrderExtVOS = resp.getData().stream().sorted(Comparator.comparing(WorkOrderExtVO::getWorkOrderId).reversed()).collect(Collectors.toList());
            }

            List<ReportInsteadWorkOrderRelateDO> reportInsteadIds = reportInsteadWorkOrderRelateDAO.getByWorkOrderId(workOrderVO.getWorkOrderId());
            List<CheckResult> checkResults = Lists.newArrayList();
            getCheckResultList(crowdfundingInfo, reportInsteadIds, checkResults);
            // 用户自己举报
            haveUserReport(userInfoModel, userReportVOList, userReportList, workOrderExtVOS, workOrderVO);
            // 显示筹款进展
            fundingProgress(userReportVOList, workOrderVO, checkResults);
        }

        //获取发起人用户信息
        UserInfoModel fundRaiser = this.userInfoDelegate.getUserInfoByUserId(crowdfundingInfo.getUserId());
        //构造发起人用户信息
        CrowdfundingUserInfo crowdfundingUserInfo = crowdfundingInfoDetailService.buildCrowdfundingUserInfo(fundRaiser);
        if (crowdfundingUserInfo != null) {
            userReportVOList.forEach(vo -> {
                vo.setHeadImgUrl(crowdfundingUserInfo.getHeadImgUrl());
                vo.setNickName(crowdfundingUserInfo.getNickname());
            });
        }

        return userReportVOList;
    }

    private boolean repeatType(WorkOrderVO workOrderVO) {
        return workOrderVO.getOrderType() == WorkOrderType.up_grade_second.getType() ||
                workOrderVO.getOrderType() == WorkOrderType.lost_report.getType();
    }

    private boolean haveUserReport(UserInfoModel userInfoModel,
                                   List<UserReportVO> userReportVOList,
                                   List<CrowdfundingReport> userReportList,
                                   List<WorkOrderExtVO> workOrderExtVOS,
                                   WorkOrderVO workOrderVO) {
        String workOrderRelateReportIds = Joiner.on(",").join(workOrderExtVOS.stream().map(WorkOrderExtVO::getValue).distinct().collect(Collectors.toList()));
        int flag = 0;
        for(CrowdfundingReport userReport : userReportList){
            // 该条举报为友商举报时不显示
            if(isFriendReport(userReport.getId())){
                flag ++;
                break;
            }
            if(workOrderRelateReportIds.contains(String.valueOf(userReport.getId()))){
                UserReportVO userReportVO = new UserReportVO();
                userReportVO.setType(1);
                userReportVO.setTitle(userInfoModel.getRealName());
                userReportVO.setReportName(userReport.getName());
                userReportVO.setReportTime(userReport.getCreateTime());
                userReportVO.setReportType(getReportType(userReport.getId()));
                userReportVO.setReportDescribe(userReport.getContent());
                userReportVO.setReportProgress(getProcessStatus(workOrderVO).getReportProgress());
                userReportVOList.add(userReportVO);
                flag ++;
            }
        }
        return flag > 0;
    }

    // 是否友商举报
    private boolean isFriendReport(int reportId) {
        List<ReportHitStrategyRecord> reportHitStrategyRecords = cfReportClient.getStrategyRecord(Lists.newArrayList(reportId)).getData();
        if (CollectionUtils.isEmpty(reportHitStrategyRecords)) {
            return false;
        }
        int flag = 0;
        for (ReportHitStrategyRecord reportHitStrategyRecord : reportHitStrategyRecords) {
            if (reportHitStrategyRecord.getTypeCode() == LimitActionEnum.FLAG_FRIEND_REPORT.getId() ||
                    reportHitStrategyRecord.getTypeCode() == LimitActionEnum.FLAG_SUSPECT_FRIEND_REPORT.getId()) {
                flag = 1;
            }
        }
        return flag == 1;
    }

    private void fundingProgress (List < UserReportVO > userReportVOList, WorkOrderVO
            workOrderVO, List < CheckResult > checkResults){
        if (CollectionUtils.isNotEmpty(checkResults)) {
            ReportInsteadWorkOrderRelateDO reportInsteadWorkOrderRelateDO = reportInsteadWorkOrderRelateDAO.getOneByWorkOrderId(workOrderVO.getWorkOrderId());
            CfSendProve cfSendProve = cfProveDao.getById(reportInsteadWorkOrderRelateDO.getReportInsteadId());
            UserReportVO userReportVO = new UserReportVO();
            userReportVO.setType(2);
            userReportVO.setTitle("筹款最新进展");
            userReportVO.setReportTime(cfSendProve == null ? null : cfSendProve.getCreateTime());
            userReportVO.setCheckResults(checkResults);
            userReportVOList.add(userReportVO);
        }
    }

    // 代录入内容查询
    private void getCheckResultList (CrowdfundingInfo
                                             crowdfundingInfo, List < ReportInsteadWorkOrderRelateDO > reportInsteadIds, List < CheckResult > checkResults){
        if (CollectionUtils.isNotEmpty(reportInsteadIds)) {
            for (ReportInsteadWorkOrderRelateDO reportInsteadId : reportInsteadIds) {
                CheckResult checkResult = new CheckResult();
                CfSendProve cfSendProve = cfProveDao.getById(reportInsteadId.getReportInsteadId());
                if(cfSendProve == null){
                    return;
                }
                if (cfSendProve.getPictureUrl() != null) {
                    checkResult.setImages(cfSendProve.getPictureUrl());
                }
                CfSendProveTemplate cfSendProveTemplate = cfProveTemplateDao.getByAuditProve(crowdfundingInfo.getId(), reportInsteadId.getReportInsteadId());
                if(cfSendProveTemplate == null){
                    return;
                }
                if (cfSendProveTemplate.getTitle() != null) {
                    checkResult.setResult(cfSendProveTemplate.getContent());
                }
                checkResults.add(checkResult);
            }
        }
    }

    public ReportPageInfoVO getReportPageInfo(CrowdfundingInfo crowdfundingInfo, UserInfoModel userInfoModel){
        ReportPageInfoVO reportPageInfoVO = new ReportPageInfoVO();
        reportPageInfoVO.setIsShow(IS_NOT_SHOW);
        reportPageInfoVO.setIsReport(IS_NOT_SHOW);

        List<CrowdfundingReport> crowdfundingReportList = crowdfundingReportDao.getListByInfoId(crowdfundingInfo.getId());
        List<CrowdfundingReport> userReportList = crowdfundingReportDao.getReportByConditions(userInfoModel.getUserId(), crowdfundingInfo.getId());
        if(CollectionUtils.isEmpty(crowdfundingReportList)){
            return reportPageInfoVO;
        }
        List<Integer> allReportIds = crowdfundingReportList.stream().map(CrowdfundingReport::getId).distinct().collect(Collectors.toList());
        List<Integer> reportIds = crowdfundingReportList.stream()
                .collect(Collectors.collectingAndThen
                        (Collectors.toCollection(() ->
                                new TreeSet<>(Comparator.comparing(CrowdfundingReport::getUserId))), ArrayList::new))
                .stream().map(CrowdfundingReport::getId).distinct().collect(Collectors.toList());
        AdminCredibleInfoFeignClient.QueryParam queryParam = new AdminCredibleInfoFeignClient.QueryParam();
        queryParam.setCaseId(crowdfundingInfo.getId());
        queryParam.setStatus(Lists.newArrayList(AddTrustAuditStatusEnum.PASSED.getCode()));
        List<CfCredibleInfoVo> credibleInfo = Optional.ofNullable(credibleInfoFeignClient.queryRecordWithParticularStatus(queryParam)).map(OperationResult::getData).orElse(new ArrayList<>());
        // 案例被举报
        if (CollectionUtils.isNotEmpty(reportIds)) {
            reportPageInfoVO.setReportedCase(true);
        }
        // 获取该案例有「下发举报代录入」
        if (CollectionUtils.isNotEmpty(credibleInfo)) {
            reportPageInfoVO.setHasCredibleInfo(true);

            boolean success = redissonHandler.setNX("report_pop_up_" + crowdfundingInfo.getId() + "_" + userInfoModel.getUserId() + "_" + DateUtil.getCurFormatDate("yyyy-MM-dd"), crowdfundingInfo.getId(), RedissonHandler.ONE_DAY);
            if (!success) {
                reportPageInfoVO.setLimited(true);
            }
        }

        // 为友商举报只能看到官方公告
        if(!checkVerificationPassed(userInfoModel.getUserId())){
            return reportPageInfoVO;
        }
        // 有该用户举报 显示入口
        if(userReportList.size() > 0){
            reportPageInfoVO.setIsReport(IS_SHOW);
        }
        List<CrowdfundingReportLabel> crowdfundingReportLabels = crowdfundingReportDao.queryReportLabelByReportIds(allReportIds);
        if(CollectionUtils.isEmpty(crowdfundingReportLabels)){
            return reportPageInfoVO;
        }
        reportPageInfoVO.setReportTagVOList(getTagList(crowdfundingReportLabels, crowdfundingInfo));
        return reportPageInfoVO;
    }

    private void addSnapshot(int caseId, String infoUuid, int auditStatus) {
        CfSendProve cfSendProve = cfProveDao.getlastOne(caseId);
        List<CfSendProveTemplate> cfSendProveTemplates = cfProveTemplateDao.getAllProve(caseId, cfSendProve.getId());

        List<CfReportAddTrustDisposeVo> cfReportAddTrustDisposeVos = this.getCfReportAddTrustDisposeVos(infoUuid);

        CfSendProveSnapshotBO cfSendProveSnapshotBO = new CfSendProveSnapshotBO();
        cfSendProveSnapshotBO.setCfSendProve(cfSendProve);
        cfSendProveSnapshotBO.setCfReportAddTrustDisposeVos(cfReportAddTrustDisposeVos);
        cfSendProveSnapshotBO.setCfSendProveTemplates(cfSendProveTemplates);
        cfSendProveSnapshotBO.setAuditStatus(auditStatus);

        CfSendProveSnapshot cfSendProveSnapshot = new CfSendProveSnapshot();
        cfSendProveSnapshot.setCaseId(caseId);
        cfSendProveSnapshot.setProveId(cfSendProve.getId());
        cfSendProveSnapshot.setProveSnapshot(JSON.toJSONString(cfSendProveSnapshotBO));
        cfSendProveSnapshot.setAuditStatus(auditStatus);
        cfProveSnapshotDao.insertOne(cfSendProveSnapshot);
    }

    // 举报处理状态查询
    private UserReportVO getProcessStatus(WorkOrderVO workOrderVO){
        UserReportVO userReportVO = new UserReportVO();
        Set<Integer> labelCode = Sets.newHashSet(HandleResultEnum.end_deal_lost.getType(),
                HandleResultEnum.noneed_deal.getType(),
                HandleResultEnum.doing.getType(),
                HandleResultEnum.undoing.getType(),
                HandleResultEnum.later_doing.getType(),
                HandleResultEnum.end_deal_upgrade.getType(),
                HandleResultEnum.reach_agree.getType());
        if(labelCode.contains(workOrderVO.getHandleResult())) {
            userReportVO.setReportProgress("平台已收到您的举报，会根据实际情况，进行核实处理，感谢您的反馈");
        }
        if(workOrderVO.getHandleResult() == HandleResultEnum.end_deal.getType()) {
            userReportVO.setReportProgress("平台已和您进行联系，处理结果请见下方平台核实结果");
        }
        return userReportVO;
    }

    // C端和sea后台举报标签对应一级标签
    private List<ReportTagVO> getTagList(List<CrowdfundingReportLabel> crowdfundingReportLabels, CrowdfundingInfo crowdfundingInfo){
        List<ReportTagVO> reportTagVOList = Lists.newArrayList();
        Map<Integer, String> labelMap = Maps.newHashMap();
        labelMap.put(CfReportTypeEnum.economics.getCode(), CfReportTypeEnum.familyFinancialSituationRisk.getDesc());
        labelMap.put(CfReportTypeEnum.amount.getCode(),CfReportTypeEnum.CROWDFUNDING_AMOUNT_LIMIT.getDesc());
        labelMap.put(CfReportTypeEnum.accident.getCode(), CfReportTypeEnum.criminalAndAccident.getDesc());
        labelMap.put(CfReportTypeEnum.accident_error.getCode(), CfReportTypeEnum.criminalAndAccident.getDesc());
        labelMap.put(CfReportTypeEnum.transgression.getCode(), CfReportTypeEnum.criminalAndAccident.getDesc());
        labelMap.put(CfReportTypeEnum.fake.getCode(), CfReportTypeEnum.falseFundraising.getDesc());
        labelMap.put(CfReportTypeEnum.unconformity.getCode(), CfReportTypeEnum.PATIENTS.getDesc());
        labelMap.put(CfReportTypeEnum.more.getCode(), CfReportTypeEnum.MUCH_CROWDFUNDING.getDesc());
        labelMap.put(CfReportTypeEnum.paid.getCode(), CfReportTypeEnum.INSURANCE_MEDICAL_SUBSIDY_DONATION.getDesc());
        labelMap.put(CfReportTypeEnum.crowdfunding_no_patient.getCode(), CfReportTypeEnum.useOfFundsRisk.getDesc());
        labelMap.put(CfReportTypeEnum.dead.getCode(), CfReportTypeEnum.useOfFundsRisk.getDesc());
        for(CrowdfundingReportLabel crowdfundingReportLabel : crowdfundingReportLabels){
            ReportTagVO reportTagVO = new ReportTagVO();
            reportTagVO.setCTagId(crowdfundingReportLabel.getReportLabel());
            reportTagVO.setSeaTag(labelMap.get(crowdfundingReportLabel.getReportLabel()));
            reportTagVOList.add(reportTagVO);
        }

        AdminCfReportRiskTagLabel adminCfReportRiskTagLabel = adminCfReportRiskTagLabelDao.getLastByCaseId(crowdfundingInfo.getId());
        if(adminCfReportRiskTagLabel != null){
            List<String> seaReportLabel = Splitter.on(",").splitToList(adminCfReportRiskTagLabel.getRiskReportType());
            Set<Integer> showLabelCode = Sets.newHashSet(CfReportTypeEnum.useOfFundsRisk.getCode(),
                    CfReportTypeEnum.criminalAndAccident.getCode(),
                    CfReportTypeEnum.falseFundraising.getCode(),
                    CfReportTypeEnum.familyFinancialSituationRisk.getCode(),
                    CfReportTypeEnum.CROWDFUNDING_AMOUNT_LIMIT.getCode(),
                    CfReportTypeEnum.PATIENTS.getCode(),
                    CfReportTypeEnum.MUCH_CROWDFUNDING.getCode(),
                    CfReportTypeEnum.INSURANCE_MEDICAL_SUBSIDY_DONATION.getCode());
            for (String label : seaReportLabel) {
                if (StringUtils.isBlank(label)) {
                    continue;
                }
                ReportTagVO reportTagVO = new ReportTagVO();
                int labelId = CfReportTypeEnum.getByCode(Integer.parseInt(label)).getParentId();
                if(showLabelCode.contains(labelId)) {
                    reportTagVO.setSeaTag(CfReportTypeEnum.getByCode(labelId).getDesc());
                    reportTagVOList.add(reportTagVO);
                }
            }
        }
        return reportTagVOList.stream().distinct().collect(Collectors.toList());
    }

    // 获取我的举报举报类型
    private String getReportType(long reportId){
        List<CrowdfundingReportLabel> crowdfundingReportLabelList = crowdfundingReportDao.queryReportLabelByReportId((int) reportId);
        List<Integer> crowdfundingReportLabels = crowdfundingReportLabelList.stream().map(CrowdfundingReportLabel::getReportLabel).distinct().collect(Collectors.toList());
        List<String> labelName = Lists.newArrayList();
        for(int label : crowdfundingReportLabels){
            labelName.add(CfReportTypeEnum.getByCode(label).getDesc());
        }
        if(!CollectionUtils.isEmpty(labelName)){
            return StringUtils.join(labelName,",");
        }
        return null;
    }

    @Nullable
    private List<WorkOrderVO> getReportTypeWorkOrderByCaseId(int caseId) {
        Response<List<WorkOrderVO>> resp = cfWorkOrderClient.listByCaseIdsAndTypes(Lists.newArrayList(caseId), WorkOrderType.REPORT_TYPES);
        if (NewResponseUtil.isNotOk(resp)) {
            return null;
        }
        return resp.getData().stream().sorted(Comparator.comparing(WorkOrderVO::getWorkOrderId).reversed()).collect(Collectors.toList());
//        return resp.getData();
    }

    //用户是否被标记友商和疑似友商
    private boolean checkVerificationPassed(long userId) {
        log.debug("checkVerificationPassed in userId:{}", userId);
        // 查询实名信息
        List<UserRealInfo> userRealInfoList = this.userRealInfoBiz.getByVerifyStatus(userId,
                Lists.newArrayList(IdcardVerifyStatus.HANDLE_SUCCESS));
        // 没有实名信息
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(userRealInfoList)) {
            log.debug("checkVerificationPassed no real info userId:{}", userId);
            return true;
        }
        UserRealInfo userRealInfo = userRealInfoList.get(0);
        String cryptoIdCard = userRealInfo.getCryptoIdCard();
//        String idCard = shuidiCipher.decrypt(cryptoIdCard);

        UserInfoModel userInfo = userInfoDelegate.getUserInfoByUserId(userId);
        if (userInfo == null) {
            return true;
        }
        String mobile = userInfo.getCryptoMobile();

        List<BlacklistVerifyDto> paramList = Lists.newArrayList(
                createVerifyParam(BlacklistVerifyTypeEnum.USER_ID, String.valueOf(userId), null),
                createVerifyParam(BlacklistVerifyTypeEnum.MOBILE, null, mobile),
                createVerifyParam(BlacklistVerifyTypeEnum.ID_CARD, null, cryptoIdCard)
        );
        DarkListResult verifyResult = commonVerify(paramList, LimitActionEnum.FLAG_FRIEND_REPORT);
        DarkListResult likeVerifyResult = commonVerify(paramList, LimitActionEnum.FLAG_SUSPECT_FRIEND_REPORT);

        boolean passed = verifyResult.isPassed();
        boolean likePassed = likeVerifyResult.isPassed();
        if (passed && likePassed) {
            log.debug("verify raise passed");
            return true;
        }

        return false;
    }

    private DarkListResult commonVerify(List<BlacklistVerifyDto> paramList, LimitActionEnum targetCheckAction) {
        paramList = paramList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        DarkListResult darkListResult = new DarkListResult();
        darkListResult.setPassed(true);
        darkListResult.setHitList(Lists.newArrayList());
        long targetCheckActionId = targetCheckAction.getId();

        Response<List<BlacklistVerifyDto>> verifyResponse = blacklistVerifyClient.verifyV2(paramList);
        darkListResult.setResourceResponse(verifyResponse);
        log.debug("verify service {}", verifyResponse);
        if (verifyResponse == null || verifyResponse.notOk()) {
            log.debug("verify service fail {}", verifyResponse);
            return darkListResult;
        }
        List<BlacklistVerifyDto> resultList = verifyResponse.getData();
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(resultList)) {
            log.error("verify service error resultList {}", verifyResponse);
            return darkListResult;
        }
        List<BlacklistVerifyDto> hitList = resultList.stream()
                .filter(BlacklistVerifyDto::isHit)
                .filter(v -> v.getLimitActionIds().contains(targetCheckActionId))
                .collect(Collectors.toList());
        darkListResult.setHitList(hitList);

        boolean passed = org.apache.commons.collections4.CollectionUtils.isEmpty(hitList);
        log.debug("verify result passed:{}, verifyResponse:{}", passed, verifyResponse);
        if (passed) {
            darkListResult.setPassed(true);
            return darkListResult;
        }
        darkListResult.setPassed(false);

        return darkListResult;
    }

    @Nullable
    private BlacklistVerifyDto createVerifyParam(BlacklistVerifyTypeEnum verifyTypeEnum, String verifyData, String verifyEncryptData) {
        if (StringUtils.isAllBlank(verifyData, verifyEncryptData)) {
            return null;
        }
        BlacklistVerifyDto p = new BlacklistVerifyDto();
        p.setVerifyData(verifyData);
        p.setVerifyEncryptData(verifyEncryptData);
        p.setVerifyType(verifyTypeEnum.getCode());
        return p;
    }
}