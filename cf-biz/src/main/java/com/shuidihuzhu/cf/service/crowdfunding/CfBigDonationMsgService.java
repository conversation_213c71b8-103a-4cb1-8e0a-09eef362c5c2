package com.shuidihuzhu.cf.service.crowdfunding;

import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingOrderBiz;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfBigDonationMiddlePageVo;
import com.shuidihuzhu.common.web.util.MoneyUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CfBigDonationMsgService {
    @Autowired
    private UserInfoDelegate userInfoDelegate;
    @Autowired
    private CrowdfundingOrderBiz crowdfundingOrderBiz;
    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;

    public CfBigDonationMiddlePageVo getMiddlePageVo(String infoUuid, long userId, Long orderId) {
        if (StringUtils.isEmpty(infoUuid) || userId <= 0 || orderId == null || orderId <= 0) {
            return null;
        }

        UserInfoModel baseUserInfoModel = userInfoDelegate.getUserInfoByUserId(userId);
        if (null == baseUserInfoModel) {
            return null;
        }
        CfBigDonationMiddlePageVo middlePageVo = new CfBigDonationMiddlePageVo();
        String nickname = baseUserInfoModel.getNickname();
        middlePageVo.setNickname(StringUtils.isEmpty(nickname) ? "水滴筹爱心人士" : nickname);
        middlePageVo.setHeadImgUrl(baseUserInfoModel.getHeadImgUrl());

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (null == crowdfundingInfo || crowdfundingInfo.getId() < 0) {
            return null;
        }
        CrowdfundingOrder crowdfundingOrder = crowdfundingOrderBiz.getById(orderId);
        if (null == crowdfundingOrder) {
            return null;
        }
        if (crowdfundingOrder.isAnonymous()) {
            middlePageVo.setNickname("水滴筹爱心人士");
            middlePageVo.setHeadImgUrl("");
        }
        middlePageVo.setAmountYuan(MoneyUtil.buildBalance(crowdfundingOrder.getAmount()));
        middlePageVo.setComment(crowdfundingOrder.getComment());
        middlePageVo.setCreateTime(crowdfundingOrder.getCtime());
        return middlePageVo;
    }
}
