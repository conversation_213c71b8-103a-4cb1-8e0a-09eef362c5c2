package com.shuidihuzhu.cf.service.crowdfunding.listener.afteradd;

import com.google.common.collect.ImmutableList;
import com.shuidihuzhu.cf.service.crowdfunding.listener.base.AbstractCrowdFundingAddEventListener;
import org.apache.commons.lang3.StringUtils;
import com.shuidihuzhu.cf.constants.AdminUserIDConstants;
import com.shuidihuzhu.cf.enums.visitconfig.VisitConfigLogActionInfoEnum;
import com.shuidihuzhu.cf.enums.visitconfig.VisitConfigSourceEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoBaseVo;
import com.shuidihuzhu.cf.service.crowdfunding.event.AddListenerOrder;
import com.shuidihuzhu.cf.service.crowdfunding.event.CrowdFundingAddEvent;
import com.shuidihuzhu.cf.service.visitconfig.VisitConfigLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.event.SmartApplicationListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Slf4j
@Service
public class PinListener extends AbstractCrowdFundingAddEventListener implements SmartApplicationListener {


    @Resource
    private VisitConfigLogService visitConfigLogService;

    @Override
    public void onApplicationEvent(ApplicationEvent _event) {


        CrowdFundingAddEvent crowdFundingAddEvent = (CrowdFundingAddEvent) _event;
        CrowdfundingInfo cfCase = crowdFundingAddEvent.getCrowdfundingInfo();
        CrowdfundingInfoBaseVo infoBaseVo = crowdFundingAddEvent.getCrowdfundingInfoBaseVo();
        if (infoBaseVo != null) {
            if (!StringUtils.isEmpty(infoBaseVo.getSelfRealName()) ||
                    !StringUtils.isEmpty(infoBaseVo.getPatientRealName())) {
                try {
                    int caseId = cfCase.getId();
                    visitConfigLogService.pin(caseId,
                            VisitConfigSourceEnum.FIRST_APPROVE,
                            ImmutableList.of(
                                    VisitConfigLogActionInfoEnum.LOCK_SHARE,
                                    VisitConfigLogActionInfoEnum.LOCK_DONATION
                            ),
                            AdminUserIDConstants.SYSTEM);
                } catch (Exception e) {
                    log.error("PinListener error:", e);
                }
            }

        }

    }


    @Override
    public int getOrder() {
        return AddListenerOrder.Pin.getValue();
    }
}
