package com.shuidihuzhu.cf.service.huzhugrpc;

import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.model.HuzhuOrder;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.common.web.model.Response;

import com.shuidihuzhu.hz.client.hz.order.model.OrderDto;
import com.shuidihuzhu.hz.client.hz.order.service.OrderBiz;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class HzOrderService {


    @Autowired
    private OrderBiz orderBiz;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;

    public Boolean isHZMember(long userId, Boolean defaultValue){
        String userIdStr = String.valueOf(userId);
        String encryptUserId = oldShuidiCipher.aesEncrypt(userIdStr);
        Response response = orderBiz.isHzMemberInnerApi(encryptUserId);
        boolean fail = isFail(response);
        if (fail) {
            return defaultValue;
        }
        Integer data = (Integer) response.getData();
        if (data == null) {
            return defaultValue;
        }

        return data == 1;
    }

    private boolean isFail(Response response) {
        return null == response || 0 != response.getCode();
    }
}
