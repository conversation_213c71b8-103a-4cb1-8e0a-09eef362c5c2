package com.shuidihuzhu.cf.service.crowdfunding;

import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingProgress;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingComment;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdFundingVerificationVo;
import com.shuidihuzhu.cf.service.risk.dark.DarkListService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by Ahrievil on 2017/11/2
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class BlackListService {

    @Autowired
    private DarkListService darkListService;

    /**
     * 筛选逻辑，去除黑名单中的用户的评论
     *
     * @param commentList 需要处理的评论集合
     * @param userId      当前登录用户
     * @return 处理过的评论集合
     */
    public List<CrowdfundingComment> handleComment(List<CrowdfundingComment> commentList, long userId) {
        return filterUgcInBlacklist(commentList, userId, CrowdfundingComment::getUserId);
    }

    /**
     * 筛选逻辑，把黑名单中的用户订单备注改为加油
     *
     * @param orderList 需要处理的订单集合
     * @param userId    当前登录用户
     * @return 处理过的订单集合
     */
    public List<CrowdfundingOrder> handleOrder(List<CrowdfundingOrder> orderList, long userId) {
        emptyUgcInBlackList(orderList, CrowdfundingOrder::getUserId, (p) -> p.setComment("加油！"), userId);
        return orderList;
    }

    /**
     * 处理逻辑，去除黑名单中的用户的证实
     *
     * @param verificationList 需要处理的证实信息
     * @param userId           当前登录用户
     * @return 处理过的证实集合
     */
    public List<CrowdFundingVerification> handleVerification(List<CrowdFundingVerification> verificationList,
                                                             long userId) {
        emptyVerificationInBlackList(verificationList, CrowdFundingVerification::getVerifyUserId, (p) -> p.setDescription(""), userId);
        return verificationList;
    }

    public List<CrowdFundingVerificationVo> handleVerificationVo(List<CrowdFundingVerificationVo> verificationVoList, long currentUserId) {
        emptyVerificationInBlackList(verificationVoList, CrowdFundingVerificationVo::getVerifyUserId, (p) -> p.setDescription(""), currentUserId);
        return verificationVoList;
    }

    private <T> void emptyVerificationInBlackList(List<T> list, Function<T, Long> func, Consumer<T> consumer, long currentUserId) {
        try {
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            List<Long> userIds = list.stream()
                    .map(func)
                    .collect(Collectors.toList());
            Set<Long> hitSet = darkListService.checkVerificationHit(userIds);
            list.forEach(v -> {
                Long userId = func.apply(v);
                if (currentUserId == userId) {
                    return;
                }
                if (hitSet.contains(userId)) {
                    consumer.accept(v);
                }
            });
        } catch (Exception e) {
            log.error("error msg", e);
        }
    }

    private <T> void emptyUgcInBlackList(List<T> list, Function<T, Long> func, Consumer<T> consumer, long currentUserId) {
        try {
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            List<Long> userIds = list.stream()
                    .map(func)
                    .collect(Collectors.toList());
            Set<Long> hitSet = darkListService.checkUGCHit(userIds);
            list.forEach(v -> {
                Long userId = func.apply(v);
                if (currentUserId == userId) {
                    return;
                }
                if (hitSet.contains(userId)) {
                    consumer.accept(v);
                }
            });
        } catch (Exception e) {
            log.error("error msg", e);
        }
    }


    /**
     * 处理逻辑，去除黑名单中的用户的动态
     *
     * @param progressList 需要处理的动态信息
     * @param userId       当前登录用户
     * @return 处理过的动态集合
     */
    public List<CrowdFundingProgress> handleProgress(List<CrowdFundingProgress> progressList, long userId) {
        return filterUgcInBlacklist(progressList, userId, CrowdFundingProgress::getUserId);
    }

    private <T> List<T> filterUgcInBlacklist(List<T> objectList, long currentUserId, Function<T, Long> func) {
        try {
            if (CollectionUtils.isEmpty(objectList)) {
                return objectList;
            }
            List<Long> userIds = objectList.stream()
                    .map(func)
                    .collect(Collectors.toList());
            Set<Long> hitSet = darkListService.checkUGCHit(userIds);
            return objectList.stream().filter(v -> {
                Long userId = func.apply(v);
                if (currentUserId == userId) {
                    return true;
                }
                return !hitSet.contains(userId);
            }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("error msg", e);
        }
        return objectList;
    }

    public Set<Long> getVerificationHitUser(List<CrowdFundingVerification> list, long currentUserId) {
        if (CollectionUtils.isEmpty(list)) {
            return Sets.newHashSet();
        }
        try {
            List<Long> userIds = list.stream()
                    .map(CrowdFundingVerification::getVerifyUserId)
                    .collect(Collectors.toList());
            Set<Long> hitSet = darkListService.checkVerificationHit(userIds);
            hitSet.remove(currentUserId);
            return hitSet;
        } catch (Exception e) {
            log.error("error msg", e);
        }
        return Sets.newHashSet();
    }
}
