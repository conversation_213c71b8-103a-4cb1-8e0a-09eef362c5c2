package com.shuidihuzhu.cf.service.inventory.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.impl.CrowdfundingInfoBizImpl;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceDrawCashFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.model.finance.drawcash.CfDrawCashApplyV2;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.inventory.SecondInventoryDetail;
import com.shuidihuzhu.cf.service.inventory.InventoryRuleHandle;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @Description: 提现记录
 * @Author: panghairui
 * @Date: 2023/1/12 2:15 下午
 */
@Slf4j
@Service("cashDrawRuleHandle")
public class CashDrawRuleHandle implements InventoryRuleHandle {

    @Resource
    private CfFinanceDrawCashFeignClient cfFinanceDrawCashFeignClient;

    @Resource
    private CrowdfundingInfoBizImpl crowdfundingInfoBiz;

    @Override
    public void handle(SecondInventoryDetail secondInventoryDetail, long time) {

        // 取该userId最近的案例
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getLastByUserId(secondInventoryDetail.getUserId());
        if (Objects.isNull(crowdfundingInfo)) {
            return;
        }

        String url = "https://www.shuidichou.com/raise/withdraw/detail/" + crowdfundingInfo.getInfoId();
        String encodeUrl = URLEncoder.encode(url, StandardCharsets.UTF_8);
        secondInventoryDetail.setRouteUrl("sdchou://universal/page/web?url=" + encodeUrl);
        secondInventoryDetail.setContent("查看详情");

        FeignResponse<Integer> response = cfFinanceDrawCashFeignClient.totalDrawCashCount(crowdfundingInfo.getId());
        Integer drawCashCount = Optional.ofNullable(response)
                .filter(FeignResponse::ok)
                .map(FeignResponse::getData)
                .orElse(0);
        if (drawCashCount <= 0) {
            return;
        }

        secondInventoryDetail.setSituation("已收集" + drawCashCount + "条");


    }

}
