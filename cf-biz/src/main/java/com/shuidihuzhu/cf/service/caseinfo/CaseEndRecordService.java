package com.shuidihuzhu.cf.service.caseinfo;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.dao.caseinfo.CaseEndRecordDAO;
import com.shuidihuzhu.cf.domain.caseinfo.CaseEndRecordDO;
import com.shuidihuzhu.cf.model.CaseEndModel;
import com.shuidihuzhu.client.auth.saas.feign.UserFeignClient;
import com.shuidihuzhu.client.auth.saas.feign.UserGroupFeignClient;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthUserDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CaseEndRecordService {

    @Resource
    private UserGroupFeignClient userGroupFeignClient;

    @Autowired
    private UserFeignClient userFeignClient;

    @Resource
    private CaseEndRecordDAO caseEndRecordDAO;

    private static final int MAX_OPERATOR_NAME = 64;

    public void add(CaseEndModel caseEndModel) {
        CaseEndRecordDO v = new CaseEndRecordDO();
        v.setCaseId(caseEndModel.getCaseId());
        v.setDescription(caseEndModel.getDesc() == null ? "" : caseEndModel.getDesc());
        v.setFinishStatus(caseEndModel.getFinishStatus());
        int operatorId = caseEndModel.getOperatorId();
        v.setOperatorId(operatorId);
        v.setOperatorName(getOperatorName(operatorId));
        v.setReasonType(caseEndModel.getReasonType());
        caseEndRecordDAO.insert(v);
    }

    public CaseEndRecordDO getLastByCaseId(int caseId) {
        return caseEndRecordDAO.getLastByCaseId(caseId);
    }

    public String getOperatorName(int operatorId) {
        List<AuthUserDto> authUserDtos = userFeignClient.getAuthUserByIds(Lists.newArrayList((long) operatorId)).getData();
        if (CollectionUtils.isEmpty(authUserDtos)) {
            return "";
        }
        AuthUserDto model = authUserDtos.get(0);
        String organization = getOrganization(operatorId);
        organization = StringUtils.isNotEmpty(organization) ? organization + "-" : "";
        return organization + model.getUserName();
    }

    /**
     * 获取组织快照
     * @param operatorId
     * @return
     */
    private String getOrganization(Integer operatorId) {
        String org = "";
        if (operatorId == null || operatorId <= 0) {
            return org;
        }

        org = userGroupFeignClient.getGroupNameByUserId(operatorId.longValue()).getData();
        if (StringUtils.isEmpty(org)) {
            return "";
        }

        org = StringUtils.right(org, MAX_OPERATOR_NAME);
        return org;
    }
}
