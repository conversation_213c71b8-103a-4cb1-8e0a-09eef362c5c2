package com.shuidihuzhu.cf.service.crowdfunding;

import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.goods.GoodsOrderExtBiz;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingType;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.model.goods.GoodsOrderExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * @deprecated 这个类需要研究一下，是不是直接干掉。
 * 曾鋆
 */
@Service
@Deprecated
public class GoodsOrderExtService {

	@Autowired
	private GoodsOrderExtBiz goodsOrderExtBiz;
	@Autowired
	private CrowdfundingInfoBiz crowdfundingInfoBiz;


	@Async
	public void updateByCallback(CrowdfundingOrder order,CrowdfundingInfo cfInfo) {
		if (cfInfo != null && CrowdfundingType.fromValue(cfInfo.getType()) == CrowdfundingType.GOODS) {
			GoodsOrderExt goodsOrderExt = this.goodsOrderExtBiz.getByOrderId(order.getId());
			if (goodsOrderExt == null) {
				return;
			}
			long gearId = goodsOrderExt.getGearId();
			if(gearId > 0){
				this.goodsOrderExtBiz.updateNum(order.getId(), gearId, goodsOrderExt.getGoodsCount());
			}
			this.goodsOrderExtBiz.updateSuccessPayStatus(order.getId());
		}
		return;
	}
}
