package com.shuidihuzhu.cf.service.crowdfunding;

import com.shuidihuzhu.cf.dao.crowdfunding.msg.CfSendMsgStatisticDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfSendMsgStatistic;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2020/1/9
 */

@Slf4j
@Service
public class CfSendMsgStatisticService {

    @Resource
    private CfSendMsgStatisticDao cfSendMsgStatisticDao;

    public int selectSendCount(long userId, int thirdType) {
        Integer size = cfSendMsgStatisticDao.selectSendCount(userId, thirdType);
        return size == null ? 0 : size;
    }


    public int save(long userId, int thirdType, int sendCount) {
        CfSendMsgStatistic statistic = new CfSendMsgStatistic();
        statistic.setUserId(userId);
        statistic.setThirdType(thirdType);
        statistic.setSendCount(sendCount);

        return cfSendMsgStatisticDao.save(statistic);
    }
}
