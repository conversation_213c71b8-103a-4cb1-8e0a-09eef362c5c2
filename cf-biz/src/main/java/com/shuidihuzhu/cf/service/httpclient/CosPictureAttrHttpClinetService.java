package com.shuidihuzhu.cf.service.httpclient;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment;
import com.shuidihuzhu.cf.model.crowdfunding.OssPictureAttrDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.io.IOException;
import java.util.Map;

@Slf4j
@Service
public class CosPictureAttrHttpClinetService {
    public static String INFO_FORMAT = "format"; //图片格式
    public static String INFO_WIDTH = "width"; //图片的宽
    public static String INFO_HEIGHT = "height"; //图片的高
    public static String MAKE = "Make"; //相机的生产厂家
    public static String MODEL = "Model"; //相机型号
    public static String SHOOT_TIME = "DateTimeOriginal"; //拍摄时间
    public static String MODIFY_TIME = "DateTime"; //修改时间
    public static String LAT = "GPSLatitude"; //纬度
    public static String LAT_REF = "GPSLatitudeRef"; //纬度参考
    public static String LNG = "GPSLongitude"; //经度
    public static String LNG_REF = "GPSLongitudeRef"; //经度参考
    public static String ADOBE = "Software"; //通过Software解析图片是否被adobe处理过


    private static CloseableHttpClient httpClient = HttpClients.createMinimal() ;
    private static RequestConfig CONFIG = RequestConfig.custom()
            .setConnectTimeout(5000)   //设置连接超时时间
            .setSocketTimeout(5000) //设置客户端从服务器端读取数据的超时时间
            .build();

    public String queryPicAttrExecute(int id, String uri){
        String result = StringUtils.EMPTY;
        CloseableHttpResponse httpResponse = null;
        HttpGet httpGet = new HttpGet(uri);
        httpGet.setConfig(CONFIG);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        try {
            httpResponse = httpClient.execute(httpGet);
            if(null != httpResponse && null != httpResponse.getStatusLine() && 200 == httpResponse.getStatusLine().getStatusCode()){
                HttpEntity httpEntity = httpResponse.getEntity();
                if(null != httpEntity){
                    result = EntityUtils.toString(httpEntity);
                }
            } else {
                log.info("httpClient cos call FAIL,id:{}, result:{}", id, null != httpResponse && null != httpResponse.getStatusLine() ? httpResponse.getStatusLine() : -1);
            }
        } catch (Exception e){
            log.error("httpClient cos call EXCEPTION.id:{}", id, e);
        } finally {
            try {
                httpGet.releaseConnection();
                if(null != httpResponse){
                    httpResponse.close();
                }
                stopWatch.stop();
                log.info("httpClient cos cost time:{}", stopWatch.getTotalTimeMillis());
            } catch (Exception e){
                log.error("httpClient cos close EXCEPTION.", e);
            }
        }
        return result;
    }


    public Map<String, Object> parseExif(String json){
        if(StringUtils.isEmpty(json)){
            return Maps.newHashMap();
        }

        JSONObject jsonObject = null;
        try {
            jsonObject = JSON.parseObject(json);
        } catch (JSONException e) {
            log.error("cosPictureAttrHttpClinetService json parse error");
        }

        if(null == jsonObject){
            return Maps.newHashMap();
        }


        String maker = parseValue(jsonObject, MAKE);
        String model = parseValue(jsonObject, MODEL);
        String shootTime = parseValue(jsonObject, SHOOT_TIME);
        String modifyTime = parseValue(jsonObject, MODIFY_TIME);
        String lat = parseValue(jsonObject, LAT);
        String latRef = parseValue(jsonObject, LAT_REF);
        String lng = parseValue(jsonObject, LNG);
        String lngRef = parseValue(jsonObject, LNG_REF);
        String adobe = parseValue(jsonObject, ADOBE);

        Map<String, Object> resultMap = Maps.newHashMap();
        resultMap.put(MAKE, formatterStr(maker, 50));
        resultMap.put(MODEL, formatterStr(model, 50));
        resultMap.put(SHOOT_TIME, formatterStr(shootTime, 30));
        resultMap.put(MODIFY_TIME, formatterStr(modifyTime, 30));
        resultMap.put(LAT, strimQuotation(formatterStr(lat, 30)));
        resultMap.put(LAT_REF, formatterStr(latRef, 10));
        resultMap.put(LNG, strimQuotation(formatterStr(lng, 30)));
        resultMap.put(LNG_REF, formatterStr(lngRef, 10));
        resultMap.put(ADOBE, parseAdobe(adobe));
        //添加默认值，防止解析imageInfo信息失败 字段为null错误
        resultMap.put(INFO_FORMAT, "");
        resultMap.put(INFO_WIDTH, 0);
        resultMap.put(INFO_HEIGHT, 0);
        return resultMap;
    }

    public Map<String, Object> parseInfo(Map<String, Object> kvMap, String json){
        if(StringUtils.isEmpty(json) || MapUtils.isEmpty(kvMap)){
            return Maps.newHashMap();
        }

        JSONObject jsonObject = null;
        try {
            jsonObject = JSON.parseObject(json);
        } catch (JSONException e) {
            log.error("cosPictureAttrHttpClinetService json parse error");
        }

        if(null == jsonObject){
            return Maps.newHashMap();
        }
        String format = jsonObject.getString(INFO_FORMAT);
        String width = jsonObject.getString(INFO_WIDTH);
        String height = jsonObject.getString(INFO_HEIGHT);

        kvMap.put(INFO_FORMAT, formatterStr(format, 20));
        kvMap.put(INFO_WIDTH, parseInt(width));
        kvMap.put(INFO_HEIGHT, parseInt(height));
        return kvMap;
    }

    public OssPictureAttrDO valueOf(Map<String, Object> kvMap, CrowdfundingAttachment attachment){
        if(MapUtils.isEmpty(kvMap)){
            return null;
        }
        OssPictureAttrDO attrDO = new OssPictureAttrDO();
        attrDO.setAttaId(attachment.getId());
        attrDO.setCaseId(attachment.getParentId());
        if (attachment.getType() != null) {
            attrDO.setType(attachment.getType().value());
        }
        attrDO.setFormat((String) kvMap.get(INFO_FORMAT));
        attrDO.setMaker((String) kvMap.get(MAKE));
        attrDO.setModel((String) kvMap.get(MODEL));
        attrDO.setShootTime((String) kvMap.get(SHOOT_TIME));
        attrDO.setModifyTime((String) kvMap.get(MODIFY_TIME));
        attrDO.setWidth((Integer) kvMap.get(INFO_WIDTH));
        attrDO.setHeight((Integer) kvMap.get(INFO_HEIGHT));
        attrDO.setLat((String) kvMap.get(LAT));
        attrDO.setLatRef((String) kvMap.get(LAT_REF));
        attrDO.setLng((String) kvMap.get(LNG));
        attrDO.setLngRef((String) kvMap.get(LNG_REF));
        attrDO.setAdobe((Boolean) kvMap.get(ADOBE));
        attrDO.setXDimension(0);
        attrDO.setYDimension(0);
        return attrDO;
    }

    private boolean parseAdobe(String adobe){
        if(StringUtils.isEmpty(adobe)){
            return false;
        }

        adobe = adobe.toLowerCase();

        return adobe.contains("adobe");
    }

    private int parseInt(String key){
        if(!StringUtils.isNumeric(key) || StringUtils.isEmpty(key)){
            return 0;
        }

        int value = Integer.parseInt(key);

        if(value <= 0){
            return 0;
        }

        return value;
    }

    private String strimQuotation(String key){
        if(StringUtils.isEmpty(key) || !key.contains("\"")){
            return "";
        }

        int index = key.indexOf("\"");

        if(index > 0){
            return key.substring(0, index);
        }

        return key;
    }

    private String parseValue(JSONObject jsonObject, String key){
        String firstValue = jsonObject.getString(key);

        if(StringUtils.isEmpty(firstValue)){
            return "";
        }

        jsonObject = JSON.parseObject(firstValue);

        if(null == jsonObject){
            return "";
        }

        String result = jsonObject.getString("val");

        return StringUtils.isNotEmpty(result) ? result : "";
    }

    private String formatterStr(String original, int length){
        if(StringUtils.isEmpty(original)){
            return "";
        }

        if(original.length() > length){
            return original.substring(0, length);
        }

        return original;
    }

}
