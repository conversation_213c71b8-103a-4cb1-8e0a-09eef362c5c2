package com.shuidihuzhu.cf.service.crowdfunding;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.biz.crowdfunding.CfBaseInfoTemplatizeBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRedisKvBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingCommentBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingOrderBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.verification.CrowdFundingVerificationBiz;
import com.shuidihuzhu.cf.enums.crowdfunding.BaseInfoTemplateConst;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfBaseInfoTemplateVo;
import com.shuidihuzhu.cf.util.ListUtil;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Ahrievil
 */
@Slf4j
@Service
public class CfShareTemplateService {

    private static final String REDIS_KEY = "cf-share-templatize";
    private static final String REDIS_KEY2 = "cf-share-template-2";
    @Autowired
    private CfBaseInfoTemplatizeBiz cfBaseInfoTemplatizeBiz;
    @Autowired
    private CfContentPlaceHolderHandleService cfContentPlaceHolderHandleService;

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;
    @Autowired
    private CrowdFundingVerificationBiz crowdFundingVerificationBiz;
    @Autowired
    private CrowdfundingCommentBiz crowdfundingCommentBiz;
    @Autowired
    private CrowdfundingOrderBiz crowdfundingOrderBiz;
    @Autowired
    private CfRedisKvBiz redisKvBiz;
    private final static String VERIFY = "donor_text_page_verify";//证实
    private final static String COMMENT = "donor_text_page_message";//评论
    private static final String TIPS_MES = "tips_mes_forwardLan_";//小贴士业务
    private final static List<String> tipsTitle = Lists.newArrayList(
            "一场意外居然就这样发生了，现在他躺在病床上等待手术，医院还在催交费，我们已经走投无路了，希望大家帮我们一把，多多转发，每一次转发都至关重要，每一次转发都是莫大的帮助！转发出去让更多好心人看到，别让一个鲜活的生命就这样离开这个世界！谢谢大家，感激不尽！",
            "如果可以的话我们愿意独自承担，可是这场突如其来的不幸已经实在难以自己解决，时间一天一天过去，医院需要的治疗费简直就是天文数字。请大家帮帮我们，多一份转发就多一份希望，让更多的好心人看到，也许就能接力点亮生命的希望之光！谢谢大家了！",
            "病魔无情袭来，这场不幸已让我们精疲力尽，但我们绝不说放弃！要陪他一起同病魔抗争到底！相信人间有爱能创造奇迹！多一次转发就多一份希望，每一次转发都会增添我们坚持下去的信心与力量！多多转发让更多好心人关注，举手之劳做善事！谢谢大家，感激不尽！",
            "病情紧急！医生说他现在情况不太乐观，我们实在是走投无路，请大家帮帮我们，一点一滴都是情，每一分赠与都能增加他治愈的希望！爱心不分大小，大家的每一分钱都让我们万分感激！谢谢每一个关心我们的好心人！",
            "所有美好时光都被病魔无情击碎，他无助的躺在医院里，渴望能恢复健康。后续治疗还需要很多钱，我们想拼尽全力留住他。恳请大家帮帮忙，一分钱也能积少成多，一次顺手转发也能救人！增添能活下来的希望！谢谢大家的善心！感激不尽！",
            "医生说后续需要费用还很多，家里已倾尽所有，我实在不想放弃！这个世界这么美好我舍不得离开。恳求各位，帮帮我，大家点滴汇聚的爱心一定能帮我战胜病魔！爱心不分多少！每一分钱都能给我和我们全家增加信心和希望！谢谢大家，感激不尽！",
            "从没想过自己会得这样的病，疾病日日折磨着我，高额的费用像山一样压的我们全家喘不过气来。我们实在没有办法，只好求助大家，钱不在多少，每一份善心我们都无比感恩！一次转发也能助他战胜病魔！谢谢您伸出援手！在困难的时候能拉我们一把！");

    private List<CfBaseInfoTemplatize> getAllList() {
        return ListUtil.getList(3000, cfBaseInfoTemplatizeBiz::selectAllShareLimit);
    }

    private List<CfBaseInfoTemplatize> setCache() {
        log.info("CfBaseInfoTemplateService setCache");
        List<CfBaseInfoTemplatize> allList = this.getAllList();
        try {
            redissonHandler.addListNX(REDIS_KEY, allList, 60);
        } catch (Exception e) {
            log.error("", e);
        }
        return allList;
    }

    public Map<Integer, List<CfBaseInfoTemplatize>> getMap() {
        List<CfBaseInfoTemplatize> list = redissonHandler.getList(REDIS_KEY, CfBaseInfoTemplatize.class);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream().collect(Collectors.groupingBy(CfBaseInfoTemplatize::getRelationType));
        } else {
            log.info("CfBaseInfoTemplateService setCache");
            List<CfBaseInfoTemplatize> cfBaseInfoTemplatizeList = setCache();
            return cfBaseInfoTemplatizeList.stream().collect(Collectors.groupingBy(CfBaseInfoTemplatize::getRelationType));
        }
    }

    private List<CfBaseInfoTemplatize> setShareTemplateCache(Integer channelType, Integer current, Integer size) {
        log.info("CfBaseInfoTemplateService setShareTemplateCache");
        List<CfBaseInfoTemplatize> allList = cfBaseInfoTemplatizeBiz.selectAllShareTemplateByChannelTpye(channelType, current, size);
        try {
            redissonHandler.addListNX(REDIS_KEY2, allList, 60);
        } catch (Exception e) {
            log.error("", e);
        }
        return allList;
    }

    public List<CfBaseInfoTemplatize> getShareTemplateList(Integer channelType, Integer current, Integer size) {
        List<CfBaseInfoTemplatize> list = redissonHandler.getList(REDIS_KEY2, CfBaseInfoTemplatize.class);
        if (CollectionUtils.isNotEmpty(list)) {
            return list;
        } else {
            List<CfBaseInfoTemplatize> cfBaseInfoTemplatizeList = setShareTemplateCache(channelType, current, size);
            return cfBaseInfoTemplatizeList;
        }
    }

    /**
     * 根据关系类型获取分享语
     *
     * @param relationshipEnum      关系
     * @param cfTemplateBaseInfo    案例信息
     * @param includeDefaultContent 是否包含默认分享语
     * @return
     */
    public List<CfBaseInfoTemplateVo> getContents(BaseInfoTemplateConst.CfBaseInfoRelationshipEnum relationshipEnum,
                                                  CfTemplateBaseInfo cfTemplateBaseInfo, boolean includeDefaultContent) {
        Map<Integer, List<CfBaseInfoTemplatize>> shareTemplateMap = this.getMap();
        Set<Integer> relationList;
        if (relationshipEnum == BaseInfoTemplateConst.CfBaseInfoRelationshipEnum.NO) {
            relationList = Sets.newHashSet(relationshipEnum.getCode());
        } else {
            if (includeDefaultContent) {
                relationList = Sets.newHashSet(relationshipEnum.getCode(), BaseInfoTemplateConst.CfBaseInfoRelationshipEnum.OTHER.getCode());
            } else {
                relationList = Sets.newHashSet(relationshipEnum.getCode());
            }
        }
        List<CfBaseInfoTemplateVo> contents = shareTemplateMap.entrySet().stream()
                .filter(val -> relationList.contains(val.getKey()))
                .flatMap(val -> {
                    List<CfBaseInfoTemplatize> contents1;
                    if (cfTemplateBaseInfo != null) {
                        contents1 = cfContentPlaceHolderHandleService.getContents(val.getValue(), cfTemplateBaseInfo);
                    } else {
                        contents1 = val.getValue();
                    }
                    Collections.shuffle(contents1);
                    return contents1.stream();
                }).sorted(Comparator.comparing(CfBaseInfoTemplatize::getRelationType))
                .map(val -> new CfBaseInfoTemplateVo(val.getId(), val.getContent()))
                .collect(Collectors.toList());
        return contents;
    }

    public List<CfBaseInfoTemplateVo> getUserComment(long userId, int crowdFundingId, String infoUuid, String styleType,
                                                     int commentType, long id) {
        if (userId < 0 || crowdFundingId < 0 || StringUtils.isEmpty(styleType)) {
            return Lists.newArrayList();
        }
        log.info("评论与证实,userId:{}crowdFundingId:{}infoUuid:{},styleType:{},commentType:{},id:{}",
                userId, crowdFundingId, infoUuid, styleType, commentType, id);
        try {
            List<CfBaseInfoTemplateVo> contents = Lists.newArrayList();
            //证实的
            if (VERIFY.equalsIgnoreCase(styleType)) {
                List<Long> userList = Lists.newArrayList();
                userList.add(userId);
                List<CrowdFundingVerification> crowdFundingVerificationList = crowdFundingVerificationBiz.getByVerifyUserIds(userList, infoUuid);
                if (CollectionUtils.isEmpty(crowdFundingVerificationList)) {
                    return contents;
                }
                CrowdFundingVerification crowdFundingVerification = crowdFundingVerificationList.get(0);
                if (null == crowdFundingVerification) {
                    return contents;
                }
                CfBaseInfoTemplateVo cfBaseInfoTemplateVo = new CfBaseInfoTemplateVo(
                        crowdFundingVerification.getId(), crowdFundingVerification.getDescription());
                contents.add(cfBaseInfoTemplateVo);
                return contents;
            }
            //评论的
            if (COMMENT.equalsIgnoreCase(styleType) || id < 0 ) {
                CrowdfundingComment crowdfundingComment = null;
                //分类查 订单的还是评论的 0 为订单表的
                if (commentType == 0) {
                    CrowdfundingOrder crowdfundingOrder = crowdfundingOrderBiz.getById(id);
                    if (null == crowdfundingOrder) {
                        return contents;
                    }
                    CfBaseInfoTemplateVo cfBaseInfoTemplateVo = new CfBaseInfoTemplateVo(
                            crowdfundingOrder.getId(), crowdfundingOrder.getComment());
                    contents.add(cfBaseInfoTemplateVo);
                    return contents;
                } else {
                    crowdfundingComment = crowdfundingCommentBiz.getByIdNoCareDeleted(id);
                    if (null == crowdfundingComment) {
                        return contents;
                    }
                    CfBaseInfoTemplateVo cfBaseInfoTemplateVo = new CfBaseInfoTemplateVo(
                            crowdfundingComment.getId(), crowdfundingComment.getContent());
                    contents.add(cfBaseInfoTemplateVo);
                    return contents;
                }
            }
            //小贴士消息测试
            if (TIPS_MES.equalsIgnoreCase(styleType)) {
                if (commentType < 0){
                    List<String> resultList = Lists.newArrayList(tipsTitle);
                    Collections.shuffle(resultList);
                    resultList.remove(0);
                    //指定可以值
                    int key = 1;
                    for (String title : resultList){
                        CfBaseInfoTemplateVo cfBaseInfoTemplateVo = new CfBaseInfoTemplateVo(
                                key, title);
                        key ++;
                        contents.add(cfBaseInfoTemplateVo);
                    }
                    return contents;
                }

                if (commentType == 0) {
                    commentType = 1;
                }
                if (commentType > 30) {
                    commentType = 30;
                }
                String commentStr = redisKvBiz.queryByKey(TIPS_MES + commentType, true);
                CfBaseInfoTemplateVo cfBaseInfoTemplateVo = new CfBaseInfoTemplateVo(
                        commentType, commentStr);
                contents.add(cfBaseInfoTemplateVo);
                return contents;
            }
        } catch (Exception e) {
            log.error("getUserComment", e);
        }
        return Lists.newArrayList();
    }

}
