package com.shuidihuzhu.cf.service.promisehelpsignature;

import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.delegate.verifycode.IVerifyCodeDelegate;
import com.shuidihuzhu.cf.model.promisehelpsignature.PromiseHelpSignatureSelfVo;
import com.shuidihuzhu.cf.model.promisehelpsignature.PromiseHelpSignatureVo;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.growthtool.client.CfPromiseHelpSignatureFeignClient;
import com.shuidihuzhu.client.model.CfPromiseHelpSignatureSelfVo;
import com.shuidihuzhu.client.model.CfPromiseHelpSignatureVo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class PromiseHelpSignatureService {

    @Resource
    private IVerifyCodeDelegate verifyCodeDelegate;

    @Autowired
    private CfPromiseHelpSignatureFeignClient cfPromiseHelpSignatureFeignClient;

    @Autowired
    private UserInfoDelegate userInfoDelegate;

    @Autowired
    private ShuidiCipher shuidiCipher;

    public Response<PromiseHelpSignatureSelfVo> isSelf(long userId, String info) {
        PromiseHelpSignatureSelfVo result = new PromiseHelpSignatureSelfVo();
        String decrypt = getPhone(userId);
        Response<CfPromiseHelpSignatureSelfVo> response = cfPromiseHelpSignatureFeignClient.isSelf(info, decrypt);
        if (response == null) {
            return NewResponseUtil.makeFail("未获取到求助承诺链接");
        }
        if (response.notOk()) {
            return NewResponseUtil.makeFail(response.getMsg());
        }
        CfPromiseHelpSignatureSelfVo cfPromiseHelpSignatureSelfVo = response.getData();
        if (cfPromiseHelpSignatureSelfVo == null) {
            return NewResponseUtil.makeSuccess(result);
        }
        result.setSelf(cfPromiseHelpSignatureSelfVo.isSelf());
        result.setPhone(cfPromiseHelpSignatureSelfVo.getPhone());
        return NewResponseUtil.makeSuccess(result);
    }

    public Response<PromiseHelpSignatureVo> getPromiseHelp(long userId, String clientIp, String key, String verifyCode, String phone, String info) {
        if (StringUtils.isNotBlank(key) && StringUtils.isNotBlank(verifyCode)) {
            Response<Void> checkVerifyCodeResponse = verifyCodeDelegate.checkVerifyCode(key, phone, verifyCode, clientIp);
            if (checkVerifyCodeResponse == null || checkVerifyCodeResponse.notOk()) {
                return NewResponseUtil.makeFail("验证码校验失败");
            }
        } else {
            phone = getPhone(userId);
        }

        //查询用户是否有未签署的求助承诺
        Response<CfPromiseHelpSignatureVo> response = cfPromiseHelpSignatureFeignClient.getPromiseHelp(info, phone);
        if (response == null) {
            return NewResponseUtil.makeFail("未获取到求助承诺链接");
        }
        if (response.notOk()) {
            return NewResponseUtil.makeFail(response.getMsg());
        }
        CfPromiseHelpSignatureVo data = response.getData();

        PromiseHelpSignatureVo result = PromiseHelpSignatureVo.builder()
                .valid(data.isValid())
                .url(data.getSignatureUrl())
                .countdownComplete(data.isCountdownComplete())
                .build();
        return NewResponseUtil.makeSuccess(result);
    }

    public Response<Void> signature(long userId, String info, String signatureUrl) {
        UserInfoModel userInfoByUserId = userInfoDelegate.getUserInfoByUserId(userId);
        String signatureEncryptPhone = Optional.ofNullable(userInfoByUserId).map(UserInfoModel::getCryptoMobile).orElse("");
        return cfPromiseHelpSignatureFeignClient.signature(info, signatureEncryptPhone, signatureUrl);
    }

    public Response<Void> countdownComplete(String info) {
        return cfPromiseHelpSignatureFeignClient.countdownComplete(info);
    }

    private String getPhone(long userId) {
        UserInfoModel userInfoByUserId = userInfoDelegate.getUserInfoByUserId(userId);
        if (userInfoByUserId == null || StringUtils.isBlank(userInfoByUserId.getCryptoMobile())) {
            return "";
        }
        String cryptoMobile = userInfoByUserId.getCryptoMobile();
        return shuidiCipher.decrypt(cryptoMobile);
    }

}
