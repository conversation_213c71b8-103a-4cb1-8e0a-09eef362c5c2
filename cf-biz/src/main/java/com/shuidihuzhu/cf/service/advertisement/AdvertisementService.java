package com.shuidihuzhu.cf.service.advertisement;

import com.shuidihuzhu.cf.constants.crowdfunding.RedisKeyCons;
import com.shuidihuzhu.cf.vo.msg.MessageAdvertisementVO;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
@RefreshScope
public class AdvertisementService {

    @Resource(name = "redis-cf-api-new")
    private RedissonHandler redissonHandler;

    /**
     * 首屏消息点击次数阀值
     */
    @Value("${message.advertisement.threshold:2}")
    private int threshold;

    /**
     * 年月日格式化
     */
    private static final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");


    public MessageAdvertisementVO messageAdvertisement(long userId) {

        MessageAdvertisementVO vo = new MessageAdvertisementVO();

        LocalDateTime now = LocalDateTime.now();

        // 1 格式化时间 -> yyyyMMdd
        String localDateString = dateTimeFormatter.format(now);

        // 2 定义redisKey
        String redisKey = getRedisKey(userId, localDateString);

        // 3 计算key超时时间
        long expireMin = getExpire(now);

        // 4 自增
        long l = redissonHandler.incrAndSetTimeWhenNotExists(redisKey, TimeUnit.MINUTES.toMillis(expireMin));

        vo.setCount(l);

        if ( threshold == l ) {
            vo.setShow(true);
        }

        log.info("messageAdvertisement redisKey :{}, count: {}", redisKey, vo.getCount());

        return vo;
    }

    private long getExpire(LocalDateTime now) {

        // 今天结束时间
        LocalDateTime endTime = LocalDateTime.of(now.getYear(), now.getMonthValue(), now.getDayOfMonth(), 23, 59, 59);

        return ChronoUnit.MINUTES.between(now, endTime);
    }

    private String getRedisKey(long userId, String localDateString) {
        return new StringBuilder()
                .append(RedisKeyCons.CF_API_MESSAGE_ADVERTISEMENT)
                .append(localDateString)
                .append("#")
                .append(userId)
                .toString();
    }


}
