package com.shuidihuzhu.cf.service.crowdfunding;

import com.google.common.base.Preconditions;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.param.UserAccessParam;
import com.shuidihuzhu.client.dataservice.datautil.v1.DataUtilApiClient;
import com.shuidihuzhu.client.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @DATE 2020/3/31
 */
@Slf4j
@Service
@RefreshScope
public class IPService {

    public final static String country = "country";

    public final static String province = "province";

    public final static String city = "city";

    @Value("#{${newprovince.to.oldprovince.map:{}}}")
    private Map<String, String> newProvinceToOldProvinceMap;

    @Autowired
    private DataUtilApiClient dataUtilApiClient;

    /**
     * <p>兼容旧的解析 IP 接口</p>
     */
    public Map<String,String> getOldAddressFromIP(String ip) {
        Map<String, String> addressFromIP = getAddressFromIPByNewInterface(ip);
        String newProvince = addressFromIP.getOrDefault(province, "");
        String oldProvince = newProvinceToOldProvinceMap.getOrDefault(newProvince, newProvince);
        addressFromIP.put(province, oldProvince);
        return addressFromIP;
    }

    public Map<String,String> getAddressFromIPByNewInterface(String ip) {
        Response<Map<String, String>> response = dataUtilApiClient.queryAddressByIp(ip);
        Map<String, String> locationByIp = Optional.ofNullable(response)
                .filter(r->r.getCode() == 0)
                .map(Response::getData)
                .orElse(Maps.newHashMap());
        log.debug("ip:{} locationByIp:{}",ip, locationByIp);
        return locationByIp;
    }
    public String getProvinceFromIP(String ip){
        Map<String, String> locationByIp = getOldAddressFromIP(ip);
        return locationByIp.getOrDefault(province, "unkown");
    }

    public String getProvinceFromIPnull(String ip){
        Map<String, String> locationByIp = getOldAddressFromIP(ip);
        return locationByIp.getOrDefault(province, "");
    }
}
