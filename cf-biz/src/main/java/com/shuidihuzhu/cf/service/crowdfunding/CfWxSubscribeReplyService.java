package com.shuidihuzhu.cf.service.crowdfunding;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRedisKvBiz;
import com.shuidihuzhu.cf.constants.crowdfunding.CfRedisKvKey;
import com.shuidihuzhu.cf.dao.crowdfunding.CfWxSubscribeReplyDao;
import com.shuidihuzhu.cf.enums.crowdfunding.CfWxSubscribeReplyMethodEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfWxSubscribeReply;
import com.shuidihuzhu.common.web.util.cache.ICache;
import com.shuidihuzhu.wx.biz.WxMpGroupInfoBiz;
import com.shuidihuzhu.wx.biz.WxMpGroupService;
import com.shuidihuzhu.wx.model.WxAppInfo;
import com.shuidihuzhu.wx.model.WxMpGroup;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Created by wangsf on 17/3/10.
 * 微信关注自动回复表 本地缓存
 */
@Service
public class CfWxSubscribeReplyService extends AbstractCfCache<String, List<CfWxSubscribeReply> >
		implements ICache<String, List<CfWxSubscribeReply> >  {

	private static final Logger LOGGER = LoggerFactory.getLogger(CfWxSubscribeReplyService.class);

	@Autowired
	private WxMpGroupService wxMpGroupService;
	@Autowired
	private WxMpGroupInfoBiz wxMpGroupInfoBiz;
	@Autowired
	private CfWxSubscribeReplyDao cfWxSubscribeReplyDao;

	@Autowired
	private CfRedisKvBiz cfRedisKvBiz;
	public static final String ZHICHI_URL = "https://www.sobot.com/chat/h5/index.html?sysNum=cce1c6cf3d824ff0889f2774420c9f98";
	private static final String UDESK_IM_USER_KEY = "39aa8f667057379d770263b530f5f53d";

	@Override
	protected List<CfWxSubscribeReply> queryData(String key) {
		List<CfWxSubscribeReply> list = Lists.newArrayList();
		if(StringUtils.isBlank(key)) {
			return list;
		}

		LOGGER.info("CfWxSubscribeReplyService getFromDB key={}", key);

		if(key.startsWith("##regex##")) { //正则匹配
			List<CfWxSubscribeReply> cfWxSubscribeReplies = this.getByMethodFromDb(CfWxSubscribeReplyMethodEnum.REGEX);
			if(!CollectionUtils.isEmpty(cfWxSubscribeReplies)) {
				list.addAll(cfWxSubscribeReplies);
			}
		} else if (key.startsWith("##start_with##")) { //前缀匹配
			List<CfWxSubscribeReply> cfWxSubscribeReplies = this.getByMethodFromDb(CfWxSubscribeReplyMethodEnum.START_WITH);
			if(!CollectionUtils.isEmpty(cfWxSubscribeReplies)) {
				list.addAll(cfWxSubscribeReplies);
			}
		} else {
			List<String> items = Splitter.on("-").splitToList(key);
			String eventKey = items.get(0);
			int thirdType = Integer.parseInt(items.get(1));
			CfWxSubscribeReply cfWxSubscribeReply = this.getByEventKey(eventKey, thirdType);
			if (cfWxSubscribeReply != null) {
				list.add(cfWxSubscribeReply);
			}
		}
		return list;
	}

	@Override
	public List<CfWxSubscribeReply> get(String key) {
		if(StringUtils.isEmpty(key)) {
			return null;
		}

		try {
			LOGGER.debug("CfWxSubscribeReplyService getFromLocalCache key={}", key);
			return getValue(key);
		} catch (Exception e) {
			return null;
		}
	}

	public CfWxSubscribeReply getByEventKey(int thirdType, String key) {
		List<CfWxSubscribeReply> cfWxSubscribeReplies = this.get(key + "-" + thirdType);
		if(CollectionUtils.isEmpty(cfWxSubscribeReplies)) {
			return null;
		}
		CfWxSubscribeReply reply = cfWxSubscribeReplies.get(0);
		reply.setContent(kefuUrlReplace(reply.getContent()));
		return reply;
	}

	/**
	 * 替换欢迎语中在线咨询跳链
	 * @param content
	 * @param userId
	 * @param fromUserId
	 * @return
	 */
	public String kefuUrlReplace(String content) {
		if (StringUtils.isNotEmpty(content)) {
			content = content.replace(ZHICHI_URL, getOnlineChatRedirectUrl());
		}
        if (content.contains("##UDESK_PARAMS##")) {
            content.replace("##UDESK_PARAMS##", getUdeskParams());
        }
		return content;
	}

	public int getThirdTypeGroupId(int thirdType) {
		int groupId;
		if (thirdType > 0) {
			List<WxAppInfo> wxAppInfo = wxMpGroupService.selectListByThirdType(thirdType);
			List<Integer> groupIdList = wxAppInfo.stream().map(WxAppInfo::getGroupId).collect(Collectors.toList());
			List<WxMpGroup> wxMpGroups = wxMpGroupInfoBiz.selectByIds(groupIdList);
			Optional<WxMpGroup> first = wxMpGroups.stream().filter(val -> val.getType() == 1).max(Comparator.comparing(WxMpGroup::getId));
			// 默认是水滴筹大号的分组规则
			groupId = first.map(WxMpGroup::getId).orElse(6);
		} else {
			groupId = 2;
		}
		return groupId;
	}

	public String getUdeskParams() {
		// 防止漏改##UDESK_PARAMS##，统一返回空字符串
		return "";
	}

	public String getOnlineChatRedirectUrl() {
		return "https://www.shuidihuzhu.com/cs/client?channel=wx_welcome";
	}

	public List<CfWxSubscribeReply> getByMethod(CfWxSubscribeReplyMethodEnum method) {
		if(method == null) {
			return Lists.newArrayList();
		}

		if(CfWxSubscribeReplyMethodEnum.START_WITH.equals(method)) {
			return this.get("##start_with##");
		}

		if(CfWxSubscribeReplyMethodEnum.REGEX.equals(method)) {
			return this.get("##regex##");
		}

		return Lists.newArrayList();
	}

	@Override
	protected int getInitialCapacity() {
		int initialCapacity = this.cfRedisKvBiz.queryIntByKey(CfRedisKvKey.CF_SUBSCRIBE_GREETING_LOCAL_CACHE_CAPACITY, true);
		if(initialCapacity <= 0) {
			return super.getInitialCapacity();
		}

		return initialCapacity;
//		return super.getInitialCapacity();
	}

	@Override
	protected int getMaximumSize() {
//		return super.getMaximumSize();
		int maximumSize = this.cfRedisKvBiz.queryIntByKey(CfRedisKvKey.CF_SUBSCRIBE_GREETING_LOCAL_CACHE_MAX_SIZE, true);
		if(maximumSize <= 0) {
			return super.getMaximumSize();
		}
		return maximumSize;
	}

	@Override
	protected int getExpireTimeInSeconds() {
		int expire = this.cfRedisKvBiz.queryIntByKey(CfRedisKvKey.CF_SUBSCRIBE_GREETING_LOCAL_CACHE_EXPIRE, true);
		if(expire <= 0) {
			return 5;
		}
		return expire;
	}

	@Override
	protected int getRefreshTimeAfterWrite() {
		int refresh = this.cfRedisKvBiz.queryIntByKey(CfRedisKvKey.CF_SUBSCRIBE_GREETING_LOCAL_CACHE_REFRESH, true);
		if(refresh <= 0) {
			return 60;
		}
		return refresh;
	}
	private List<CfWxSubscribeReply> getByMethodFromDb(CfWxSubscribeReplyMethodEnum method) {
		return this.cfWxSubscribeReplyDao.getByMethod(method.getValue());
	}
	private CfWxSubscribeReply getByEventKey(String eventKey, int thirdType) {
		return this.cfWxSubscribeReplyDao.getByEventKey(eventKey, thirdType);
	}
}
