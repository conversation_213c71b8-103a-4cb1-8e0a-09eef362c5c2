package com.shuidihuzhu.cf.service.crowdfunding;

import com.alibaba.excel.util.StringUtils;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.client.apipure.model.crowdfunding.CaseDisplaySettingVo;
import com.shuidihuzhu.cf.dao.crowdfunding.CfCaseDisplaySettingDao;
import com.shuidihuzhu.cf.model.CaseDisplaySettingDo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CfCaseDisplaySettingService {
    @Autowired
    private CfCaseDisplaySettingDao cfCaseDisplaySettingDao;
    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;

    public boolean updateCaseDisplaySetting(CaseDisplaySettingDo caseDisplaySettingDo) {
        return cfCaseDisplaySettingDao.update(caseDisplaySettingDo) == 1;
    }

    public boolean addCaseDisplaySetting(CaseDisplaySettingDo caseDisplaySettingDo) {
        return cfCaseDisplaySettingDao.addOne(caseDisplaySettingDo) == 1;
    }

    public CaseDisplaySettingVo getByCaseIdOrInfoUuid(String infoUuid, int caseId) {
        if (StringUtils.isEmpty(infoUuid) && caseId <= 0) {
            return null;
        }
        if (caseId <= 0) {
            CrowdfundingInfo fundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
            if (fundingInfo == null) {
                return null;
            }
            caseId = fundingInfo.getId();
        }
        CaseDisplaySettingDo caseDisplaySettingDo = cfCaseDisplaySettingDao.select(caseId);

        if (caseDisplaySettingDo == null) {
            return null;
        }
        CaseDisplaySettingVo caseDisplaySettingVo = new CaseDisplaySettingVo();
        caseDisplaySettingVo.setColor(caseDisplaySettingDo.getColor());
        caseDisplaySettingVo.setLanguage(caseDisplaySettingDo.getLanguage());
        return caseDisplaySettingVo;
    }
}
