package com.shuidihuzhu.cf.service.crowdfunding;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.biz.crowdfunding.share.CfShareBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.verification.CrowdFundingVerificationBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.share.CfShareQrcode;
import com.shuidihuzhu.cf.service.IOssToCosTransService;
import com.shuidihuzhu.cf.util.OSSImageWaterMarkBuilder;
import com.shuidihuzhu.cf.util.aliyun.CfDataUploadUtils;
import com.shuidihuzhu.cf.util.crowdfunding.CfUrlUtil;
import com.shuidihuzhu.cf.vo.CfShareCaseUserVo;
import com.shuidihuzhu.cf.vo.ShareUserInfoVO;
import com.shuidihuzhu.cf.vo.WeiboShareContentVO;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.aliyun.enums.OSSBucketEnum;
import com.shuidihuzhu.common.web.util.image.MatrixToImageWriterUtil;
import com.shuidihuzhu.frame.client.api.platform.CounterClient;
import com.shuidihuzhu.frame.client.enums.CounterBizEnum;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * Created by wangsf on 18/3/14.
 */
@RefreshScope
@Slf4j
@Service
public class CfShareService {

    private static final int WEIBO_CASE_DESC_LENGTH = 35;

    private static final String REGEX_TITLE = "\\{" + "caseTitle}";
    private static final String REGEX_DESC = "\\{" + "caseContent}";

    private static final String KEY_USER_HEAD = "cf_u_head_";
    private static final long _1MINUTE = 60 * 1000L;

    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;

    @Autowired
    private CrowdfundingOrderBiz crowdfundingOrderBiz;

    @Autowired
    private CrowdFundingVerificationBiz crowdFundingVerificationBiz;

    @Autowired
    private CfShareBiz cfShareBiz;

    @Autowired
    private CrowdfundingTreatmentBiz crowdfundingTreatmentBiz;

    @Autowired
    private UserInfoDelegate userInfoDelegate;

    @Autowired
    private CfInfoStatBiz cfInfoStatBiz;

    @Autowired
    private CounterClient counterClient;

    @Value("${baseinfo.share.weibo.thumbnail:https://fundraising-img.shuidichou.com/case-share-img/template/weibo.png}")
    private String shareWeiboThumbnail;

    @Value("${baseinfo.share.weibo.img-scale:!cf_mtr_200}")
    private String shareImgScale;

    @Value("${baseinfo.share.img.cover.default:}")
    private String shareImgDefaultCover;

    @Value("${baseinfo.share.img.bg:http://cf-images.oss-cn-shanghai.aliyuncs.com/case-share-img/template/case_img_bg.png}")
    private String shareImgBg;

    @Value("${baseinfo.share.img.qrcode.width:280}")
    private int shareQrcodeWidth = 250;

    @Value("${baseinfo.share.img.qrcode.height:280}")
    private int shareQrcodeHeight = 250;

    @Value("${baseinfo.share.img.qrcode.left:23}")
    private int shareQrcodeLeft;

    @Value("${baseinfo.share.img.qrcode.top:726}")
    private int shareQrcodeTop;

    @Value("${baseinfo.share.img.cover.width:610}")
    private int shareImgCoverWidth;

    @Value("${baseinfo.share.img.cover.height:551}")
    private int shareImgCoverHeight;

    @Value("${baseinfo.share.img.cover.left:30}")
    private int shareImgCoverLeft;

    @Value("${baseinfo.share.img.cover.top:160}")
    private int shareImgCoverTop;

    @Value("${baseinfo.share.img.title.font-size:28}")
    private int shareImgTitleFontSize;

    @Value("${baseinfo.share.img.title.font-type:fangzhengheiti}")
    private String shareImgTitleFontType;
    //title中一个字的宽度
    @Value("${baseinfo.share.img.title.font-width:27}")
    private int shareImgTitleFontWidth;

    @Value("${baseinfo.share.img.title.font-height:38}")
    private int shareImgTitleFontHeight;

    @Value("${baseinfo.share.img.title.line-gap:2}")
    private int shareImgTitleLineGap;

    @Value("${baseinfo.share.img.title.line-letter-count:20}")
    private int shareImgTitleLineLetterCount;

    @Value("${baseinfo.share.img.title.left:60}")
    private int shareImgTitleLeft;

    @Value("${baseinfo.share.img.title.top:40}")
    private int shareImgTitleTop;

    @Value("${baseinfo.share.img.title.color:000000}")
    private String shareImgTitleColor;

    @Value("${baseinfo.share.img.disease.color:0092FF}")
    private String shareImgDiseaseColor;

    @Value("${baseinfo.share.img.title.prefix:求帮忙求扩散！}")
    private String shareImgTitlePrefix;

    @Value("${baseinfo.share.weibo.thumbnail-domain:https://fundraising-img.shuidichou.com}")
    private String weiboThumbnailDomain;

    @Value("${baseinfo.share.img.default.descs:}")
    private String shareImgDescs;

    @Value("${baseinfo.share.img.default.desc-line-max:21}")
    private int shareImgDescLineMax;

    @Value("${baseinfo.share.limit.duration.minute:10}")
    private long shareLimitDurationMinute;

    @Value("${baseinfo.share.limit.times:1250}")
    private int shareLimitTimes;

    @Autowired
    private CrowdfundingAuthorBiz cfAuthorBiz;

    @Autowired(required = false)
    private Producer producer;

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;

    private DateFormat dateFormat = new SimpleDateFormat("dd");

    @Autowired
    private IOssToCosTransService ossToCosTransService;
    @Resource
    private CrowdfundingInfoSimpleBiz crowdfundingInfoSimpleBiz;
    public ShareUserInfoVO getShareUserInfo(long userId, String infoUuid) {

        CfInfoSimpleModel cfInfoSimpleModel = crowdfundingInfoSimpleBiz.getFundingInfo(infoUuid);
        if (Objects.isNull(cfInfoSimpleModel)) {
            return null;
        }

        // 看转发人捐没捐过该案例
        long orderId = crowdfundingOrderBiz.getUserOrder(cfInfoSimpleModel.getId(), userId);
        if (orderId <= 0) {
            return null;
        }

        UserInfoModel userInfoModel = userInfoDelegate.getUserInfoByUserId(userId);
        if (Objects.isNull(userInfoModel)) {
            return null;
        }

        return ShareUserInfoVO.builder()
                .headImg(userInfoModel.getHeadImgUrl())
                .build();
    }

    public WeiboShareContentVO buildWeiboShareContent(long userId, String infoUuid) {
        if(StringUtils.isBlank(infoUuid)) {
            return null;
        }

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if(null == crowdfundingInfo) {
            return null;
        }

        long cfUserId = crowdfundingInfo.getUserId();

        String code = StringUtils.EMPTY;
        String text = StringUtils.EMPTY;
        if(userId == cfUserId) {
            code = "template1";
            text = "#水滴筹# 我们实在不知道该怎么办了，走投无路才向大家求助，希望各位可以帮帮我们！哪怕只是一次转发，对我们都是莫大的帮助！这是我发起的水滴筹“{caseTitle}”，恳请大家点击帮助：";
        } else {
            code = "template10";
            text = "#水滴筹# 我在水滴筹平台看到了这个筹款，但愿能通过转发帮患者多筹一些钱。希望大家也能伸出援助之手，一起来帮帮这个家！这是患者的水滴筹“{caseTitle}”，点击就可以帮助：";
        }

        WeiboShareContentVO result = new WeiboShareContentVO();
        result.setCode(code);

        String titleImg = crowdfundingInfo.getTitleImg();
        if(StringUtils.isNotEmpty(titleImg)) {
            String thumbnail = titleImg + this.shareImgScale;
            int index = thumbnail.indexOf(".com");
            thumbnail = this.weiboThumbnailDomain + thumbnail.substring(index + 4);
            result.setThumbnail(thumbnail);
        } else {
            result.setThumbnail(this.shareWeiboThumbnail);
        }

        String title = crowdfundingInfo.getTitle();
        title = (title == null ? "" : title.trim());
        String desc = crowdfundingInfo.getContent();
        desc = desc == null ? "" : desc.trim();
        desc = desc.length() > WEIBO_CASE_DESC_LENGTH ? desc.substring(0, WEIBO_CASE_DESC_LENGTH) + "......" : desc;

        text = text.replaceAll(REGEX_TITLE, title).replaceAll(REGEX_DESC, desc);

        result.setText(text);

        return result;
    }

    /**
     * 生成带二维码的案例图片用于分享
     *
     * @param userId
     * @param infoUuid
     * @param channel
     * @param selfTag
     * @return
     */
    public String getCaseImage(long userId, String infoUuid, String channel, String selfTag, int caseImgTemplate) {

        if(StringUtils.isBlank(infoUuid)) {
            return "";
        }

        CrowdfundingInfo crowdfundingInfo = this.crowdfundingInfoBiz.getNoReplaceFundingInfo(infoUuid);
        if(crowdfundingInfo == null) {
            return "";
        }

        CfShareQrcode shareQrcode = this.cfShareBiz.getShareQrcode(userId, infoUuid, channel);
        //如果没有对应的二维码，则生成一个新的
        String qrCode = "";
        if(shareQrcode == null || StringUtils.isBlank(shareQrcode.getUrl())) {
            qrCode = this.generateQrcode(crowdfundingInfo, channel + caseImgTemplate);
            if(StringUtils.isNotBlank(qrCode)) {
                //保存刚生成的二维码
                shareQrcode = new CfShareQrcode();
                shareQrcode.setInfoUuid(infoUuid);
                shareQrcode.setChannel(channel + caseImgTemplate);
                shareQrcode.setUrl(qrCode);
                this.cfShareBiz.insertShareQrcode(shareQrcode);
            }
        } else {
            qrCode = shareQrcode.getUrl();
        }

        //没有二维码直接返回
        if(StringUtils.isBlank(qrCode)) {
            return "";
        }

        CrowdfundingTreatment treatment = this.crowdfundingTreatmentBiz.get(crowdfundingInfo.getId());
        String disease = "";
        if(treatment != null && !StringUtils.isBlank(treatment.getDiseaseName())) {
            disease = treatment.getDiseaseName();
        }

        String title = crowdfundingInfo.getTitle();
        title = title == null ? "" : title.trim();

        String titleImg = crowdfundingInfo.getTitleImg();
        titleImg = titleImg == null ? shareImgDefaultCover : titleImg.trim();
        //如果是titleImg是腾讯云cdn域名，则用logo图片当背景
        if(StringUtils.isNotBlank(titleImg) && ossToCosTransService.isCosDomain(titleImg)) {
            titleImg = "";
        }
        if(titleImg == null) {
            titleImg = "";
        } else {
            int index = titleImg.indexOf(".com/");
            if(index != -1) {
                titleImg = titleImg.substring(index + 5);
            }
        }

        int index = qrCode.indexOf(".com/");
        if(index != -1) {
            qrCode = qrCode.substring(index + 5);
        }

        if(caseImgTemplate == 0) {
            return this.generateCaseImg(title, titleImg, disease, qrCode);
        } else {
            UserInfoModel userInfoModel = this.userInfoDelegate.getUserInfoByUserId(userId);
            CfInfoStat cfInfoStat = this.cfInfoStatBiz.getById(crowdfundingInfo.getId());
            int shareCount = cfInfoStat == null ? 0 : cfInfoStat.getShareCount();

            CrowdfundingAuthor patient = this.cfAuthorBiz.get(crowdfundingInfo.getId());
            String patientName = (patient == null ? "" : patient.getName());

            //caseImgTemplate - 1 决定了取哪个文案
            return this.generateCaseImg(patientName, titleImg, disease, qrCode, shareCount, userInfoModel, caseImgTemplate - 1);
        }
    }

    //渲染一张图出来，第一期需求
    private String generateCaseImg(String title, String titleImg, String disease, String qrcode) {

        log.info("generateCaseImgag:title:{};titleImg:{};disease:{};qrcode:{}", title, titleImg, disease, qrcode);

        OSSImageWaterMarkBuilder builder = new OSSImageWaterMarkBuilder(this.shareImgBg);

        //贴封面
        if(StringUtils.isNotBlank(titleImg)) {
            builder.buildImageWaterMark(titleImg, this.shareImgCoverWidth, this.shareImgCoverHeight, "fill", 100,
                    "center", 0, 0, this.shareImgCoverWidth, this.shareImgCoverHeight,  //裁剪参数
                    "nw", shareImgCoverLeft, shareImgCoverTop, 0, 0);
        }

        //贴二维码
        if(StringUtils.isNotBlank(qrcode)) {
            builder.buildImageWaterMark(qrcode, 0, 100, "nw", shareQrcodeLeft, shareQrcodeTop, 0, 0);
        }

        //绘制标题
        title = StringUtils.isBlank(title) ? "" : title.trim();
        String diseaseSection = "#" + disease + "#";
        String titleSection = this.shareImgTitlePrefix + title;
        if(StringUtils.isBlank(disease)) { //标题长度为0，只绘制标题

            String firstLine = titleSection;
            String secondLine = "";
            if(titleSection.length() > this.shareImgTitleLineLetterCount) {
                firstLine = titleSection.substring(0, this.shareImgTitleLineLetterCount);
                secondLine = titleSection.substring(this.shareImgTitleLineLetterCount);
            }
            if(!StringUtils.isBlank(firstLine)) {
                builder.buildTextWaterMark(firstLine, this.shareImgTitleFontType, "nw", this.shareImgTitleLeft, this.shareImgTitleTop,
                        this.shareImgTitleColor, this.shareImgTitleFontSize);
            }

            if(!StringUtils.isBlank(secondLine)) {
                builder.buildTextWaterMark(secondLine, this.shareImgTitleFontType, "nw",
                        this.shareImgTitleLeft, this.shareImgTitleTop + this.shareImgTitleFontHeight + this.shareImgTitleLineGap,
                        this.shareImgTitleColor, this.shareImgTitleFontSize);
            }

        } else if(disease.length() < this.shareImgTitleLineLetterCount - 2) { //加上两个"#"能在一行之内

            //先绘制疾病块
            builder.buildTextWaterMark(diseaseSection, this.shareImgTitleFontType, "nw", this.shareImgTitleLeft, this.shareImgTitleTop,
                    this.shareImgDiseaseColor, this.shareImgTitleFontSize);
            int x = this.shareImgTitleLeft + diseaseSection.length() * this.shareImgTitleFontWidth;
            String firstLine = titleSection;
            String secondLine = "";
            if(titleSection.length() > this.shareImgTitleLineLetterCount - diseaseSection.length()) {
                firstLine = titleSection.substring(0, this.shareImgTitleLineLetterCount - diseaseSection.length());
                secondLine = titleSection.substring(this.shareImgTitleLineLetterCount - diseaseSection.length());
                if(secondLine.length() > this.shareImgTitleLineLetterCount) {
                    secondLine = secondLine.substring(0, this.shareImgTitleLineLetterCount);
                }
            }

            if(!StringUtils.isBlank(firstLine)) {
                builder.buildTextWaterMark(firstLine, this.shareImgTitleFontType, "nw", x, this.shareImgTitleTop,
                        this.shareImgTitleColor, this.shareImgTitleFontSize);
            }

            if(!StringUtils.isBlank(secondLine)) {
                builder.buildTextWaterMark(secondLine, this.shareImgTitleFontType, "nw",
                        this.shareImgTitleLeft, this.shareImgTitleTop + this.shareImgTitleFontHeight + this.shareImgTitleLineGap,
                        this.shareImgTitleColor, this.shareImgTitleFontSize);
            }

        } else if(disease.length() == this.shareImgTitleLineLetterCount - 2) { //加上两个"#"刚好占满一行

            builder.buildTextWaterMark(diseaseSection, this.shareImgTitleFontType, "nw", this.shareImgTitleLeft, this.shareImgTitleTop,
                    this.shareImgDiseaseColor, this.shareImgTitleFontSize);
            titleSection = titleSection.length() > this.shareImgTitleLineLetterCount ? titleSection.substring(0, this.shareImgTitleLineLetterCount)
                    : titleSection;
            if(StringUtils.isBlank(titleSection)) {
                builder.buildTextWaterMark(titleSection, this.shareImgTitleFontType, "nw",
                        this.shareImgTitleLeft, this.shareImgTitleTop + this.shareImgTitleFontHeight + this.shareImgTitleLineGap,
                        this.shareImgTitleColor, this.shareImgTitleFontSize);
            }

        } else if(disease.length() < this.shareImgTitleLineLetterCount * 2 - 2) { //占满第一行，但是没占满第二行

            String firstLine = diseaseSection.substring(0, this.shareImgTitleLineLetterCount);
            String secondLine = diseaseSection.substring(this.shareImgTitleLineLetterCount);
            builder.buildTextWaterMark(firstLine, this.shareImgTitleFontType, "nw", this.shareImgTitleLeft, this.shareImgTitleTop,
                    this.shareImgDiseaseColor, this.shareImgTitleFontSize);
            builder.buildTextWaterMark(secondLine, this.shareImgTitleFontType, "nw",
                    this.shareImgTitleLeft, this.shareImgTitleTop + this.shareImgTitleFontHeight + this.shareImgTitleLineGap,
                    this.shareImgDiseaseColor, this.shareImgTitleFontSize);

            //如果加上前缀正好够两行
            int x = this.shareImgTitleLeft + secondLine.length() * this.shareImgTitleFontWidth;
            if(secondLine.length() + this.shareImgTitlePrefix.length() == this.shareImgTitleLineLetterCount) {
                builder.buildTextWaterMark(this.shareImgTitlePrefix, this.shareImgTitleFontType, "nw",
                        this.shareImgTitleLeft, this.shareImgTitleTop + this.shareImgTitleFontHeight + this.shareImgTitleLineGap,
                        this.shareImgTitleColor, this.shareImgTitleFontSize);
            } else if(secondLine.length() + this.shareImgTitlePrefix.length() < this.shareImgTitleLineLetterCount) {
                if(titleSection.length() > this.shareImgTitleLineLetterCount - secondLine.length()) {
                    titleSection = titleSection.substring(0, this.shareImgTitleLineLetterCount - secondLine.length());
                }
                builder.buildTextWaterMark(titleSection, this.shareImgTitleFontType, "nw",
                        x, this.shareImgTitleTop + this.shareImgTitleFontHeight + this.shareImgTitleLineGap,
                        this.shareImgTitleColor, this.shareImgTitleFontSize);
            } else {
                if(title.length() > this.shareImgTitleLineLetterCount - secondLine.length()) {
                    titleSection = title.substring(0, this.shareImgTitleLineLetterCount - secondLine.length());
                }
                builder.buildTextWaterMark(titleSection, this.shareImgTitleFontType, "nw",
                        x, this.shareImgTitleTop + this.shareImgTitleFontHeight + this.shareImgTitleLineGap,
                        this.shareImgTitleColor, this.shareImgTitleFontSize);
            }

        } else if(disease.length() == this.shareImgTitleLineLetterCount * 2 - 2) { //占满了第二行

            String firstLine = diseaseSection.substring(0, this.shareImgTitleLineLetterCount);
            String secondLine = diseaseSection.substring(this.shareImgTitleLineLetterCount);
            builder.buildTextWaterMark(firstLine, this.shareImgTitleFontType, "nw", this.shareImgTitleLeft, this.shareImgTitleTop,
                    this.shareImgDiseaseColor, this.shareImgTitleFontSize);
            builder.buildTextWaterMark(secondLine, this.shareImgTitleFontType, "nw",
                    this.shareImgTitleLeft, this.shareImgTitleTop + this.shareImgTitleFontHeight + this.shareImgTitleLineGap,
                    this.shareImgDiseaseColor, this.shareImgTitleFontSize);

        } else { //超出了第二行
            diseaseSection = "#" + disease.substring(0, this.shareImgTitleLineLetterCount * 2 - 2) + "#";
            String firstLine = diseaseSection.substring(0, this.shareImgTitleLineLetterCount);
            String secondLine = diseaseSection.substring(this.shareImgTitleLineLetterCount);
            builder.buildTextWaterMark(firstLine, this.shareImgTitleFontType, "nw", this.shareImgTitleLeft, this.shareImgTitleTop,
                    this.shareImgDiseaseColor, this.shareImgTitleFontSize);
            builder.buildTextWaterMark(secondLine, this.shareImgTitleFontType, "nw",
                    this.shareImgTitleLeft, this.shareImgTitleTop + this.shareImgTitleFontHeight + this.shareImgTitleLineGap,
                    this.shareImgDiseaseColor, this.shareImgTitleFontSize);
        }

        String imageUrl = builder.buildImageUrl();
        log.info("imageUrl:{}", imageUrl);
        return imageUrl;
    }

    //渲染分享图片，第二期:https://wiki.shuiditech.com/pages/viewpage.action?pageId=12140357
    private String generateCaseImg(String patientName, String titleImg, String disease, String qrcode, int shareCount, UserInfoModel userInfo,
                                   int descIndex) {
        log.info("generateCaseImage:patientName:{};titleImg:{};disease:{};qrcode:{};shareCount:{};userInfo:{}",
                patientName, titleImg, disease, qrcode, shareCount, userInfo);

        //贴封面图片
        OSSImageWaterMarkBuilder builder = new OSSImageWaterMarkBuilder("http://cf-images.oss-cn-shanghai.aliyuncs.com/case-share-img/template/case_img_bg_2.png");

        // 贴封面
        if(StringUtils.isNotBlank(titleImg)) {
            builder.buildImageWaterMark(titleImg, 670, 507, "fill", 100,
                    "center", 0, 0, 670, 507,  //裁剪参数
                    "nw", 0, 100, 0, 0);
        }

        //贴头像
        if(userInfo != null && !StringUtils.isBlank(userInfo.getHeadImgUrl()) && userInfo.getHeadImgUrl().startsWith("http")) {
            String userHeadImgOssKey = this.redissonHandler.get(KEY_USER_HEAD + userInfo.getUserId(), String.class);
            if(!StringUtils.isBlank(userHeadImgOssKey)) {
                builder.buildImageWaterMark(userHeadImgOssKey, 140, 140, "fill", 100,
                        "center", 0, 0, 140, 140,  //裁剪参数
                        "nw", 43, 632, 70, 0);
            } else {
                String userHeadImg = userInfo.getHeadImgUrl();
                //获取OSS上的头像
                userHeadImg = getUserHeadImg(userHeadImg);
                if(!StringUtils.isBlank(userHeadImg)) {
                    int index = userHeadImg.indexOf(".com/");
                    if(index != -1) {
                        userHeadImg = userHeadImg.substring(index + 5);
                    }
                    builder.buildImageWaterMark(userHeadImg, 140, 140, "fill", 100,
                            "center", 0, 0, 140, 140,  //裁剪参数
                            "nw", 43, 632, 70, 0);
                    this.redissonHandler.setEX(KEY_USER_HEAD + userInfo.getUserId(), userHeadImg, 5 * _1MINUTE);
                }
            }
        }

        //贴二维码
        if(StringUtils.isNotBlank(qrcode)) {
            builder.buildImageWaterMark(qrcode, 140, 140, "fill", 100,
                    "center", 0, 0, 146, 146,  //裁剪参数
                    "nw", 40, 828, 0, 0);
        }

        //写患者名称
        if(StringUtils.isNotBlank(patientName)) {
            builder.buildTextWaterMark(patientName, "fangzhengheiti", "nw", 40, 353, "FFFFFF", 38);
        }

        //写疾病名称
        if(StringUtils.isNotBlank(disease)) {
            if(disease.length() > this.shareImgDescLineMax) {
                disease = disease.substring(0, this.shareImgDescLineMax);
            }
            builder.buildTextWaterMark(disease, "fangzhengheiti", "nw", 40, 416, "FFFFFF", 26);
        }

        //TODO: 写PGC文案
        if(!StringUtils.isBlank(this.shareImgDescs)) {
            List<String> descs = Splitter.on(";;").splitToList(this.shareImgDescs);
            if(descs != null && descIndex >= 0 && descIndex < descs.size()) {
                String desc = descs.get(descIndex);
                if(!StringUtils.isBlank(desc)) {
                    //第一行
                    int lineCnt = 0;
                    int y = 474;
                    int maxLetterPerLine = shareImgDescLineMax;
                    while (desc.length() > maxLetterPerLine && lineCnt < 2) {
                        String firstLine = desc.substring(0, maxLetterPerLine);
                        desc = desc.substring(maxLetterPerLine);
                        builder.buildTextWaterMark(firstLine, "fangzhengheiti", "nw", 40, y, "FFFFFF", 24);
                        lineCnt++;
                        y += 33;
                    }

                    if(!StringUtils.isBlank(desc)) {
                        if(desc.length() > maxLetterPerLine) {
                            desc = desc.substring(0, maxLetterPerLine);
                        }
                        builder.buildTextWaterMark(desc, "fangzhengheiti", "nw", 40, y, "FFFFFF", 24);
                    }
                }
            }
        }

        //写捐款用户名字
        String nameLine = "我是水滴筹用户";
        if(userInfo != null && !StringUtils.isBlank(userInfo.getNickname())) {
            nameLine = "我是" + userInfo.getNickname();
        }
        if(nameLine.length() > 15) {
            nameLine = nameLine.substring(0, 15);
        }

        builder.buildTextWaterMark(nameLine, "fangzhengheiti", "nw", 209, 646, "000000", 32);

        //写捐款次数
        int x = 209;
        builder.buildTextWaterMark("我是第", "fangzhengheiti", "nw", 209, 698, "999999", 22);

        int count = shareCount > 0 ? shareCount : 1;
        int length = String.valueOf(count).length();

        x += 22 * 3; //写次数的起点
        builder.buildTextWaterMark(count + "", "fangzhengheiti", "nw", x, 700, "0092FF", 22);

        x += 11 * length + 2; //"位爱心接力人
        builder.buildTextWaterMark("位爱心接力人", "fangzhengheiti", "nw", x, 698, "999999", 22);

        builder.buildTextWaterMark("邀你一起 爱心传递", "fangzhengheiti", "nw", 209, 728, "999999", 22);

        return builder.buildImageUrl();
    }

    private String generateQrcode(CrowdfundingInfo crowdfundingInfo, String channel) {

        String url = CfUrlUtil.getContribute(crowdfundingInfo.getInfoId(), "?channel=" + channel, crowdfundingInfo.getType());
        BufferedImage image = MatrixToImageWriterUtil.createQrCode(url, shareQrcodeWidth, shareQrcodeHeight);
        if(image == null) {
            return "";
        }

        String qrCode = "";
        try (ByteArrayOutputStream os = new ByteArrayOutputStream()) {
            ImageIO.write(image, "jpg", os);
            qrCode = CfDataUploadUtils.uploadImageToOSS(new ByteArrayInputStream(os.toByteArray()), OSSBucketEnum.SHUIDI_CF_IMAGE, "case-share-img", "");
            log.info("infoUuid:{}; channel:{}; qrCode:{}", crowdfundingInfo.getInfoId(), channel, qrCode);
        } catch (IOException e) {
            log.error("生成案例详情地址二维码错误，订单ID:", e);
            return null;
        }

        return qrCode;
    }

    private String getUserHeadImg(String userHeadImg) {
        String url = downloadImageUploadToOSS(userHeadImg);
        return url;
    }

    private String downloadImageUploadToOSS(String imgUrl) {
        String ossUrl = null;
        if(org.apache.commons.lang3.StringUtils.isNotEmpty(imgUrl)) {
            try {
                URL avatarUrl = new URL(imgUrl);
                InputStream in = avatarUrl.openStream();
                ossUrl = CfDataUploadUtils.uploadImageToOSS(in, OSSBucketEnum.SHUIDI_CF_IMAGE, "case-share-img", "");
            } catch (IOException e) {
                log.warn("将微信头像下载上传到阿里云失败，地址：{}", imgUrl);
                return null;
            }
        }
        log.info("生成图片并上传至oss成功，图片地址：{}", ossUrl);
        return ossUrl;
    }

    public CfShareCaseUserVo getCfShareCaseUser(long sourceUserId, CrowdfundingInfo crowdfundingInfo) {
        List<CrowdfundingOrder> orderCases = crowdfundingOrderBiz.getByUserId(Sets.newHashSet(sourceUserId), crowdfundingInfo.getId());
        int totalAmount = 0;
        boolean isAnonymous = true;
        for (CrowdfundingOrder order : orderCases) {
            totalAmount = totalAmount + order.getAmount();
            if(!order.isAnonymous() && isAnonymous) {
                isAnonymous = false;
            }
        }
        CfShareCaseUserVo cfShareCaseUserVo = new CfShareCaseUserVo();
        cfShareCaseUserVo.setTotalAmount(totalAmount);
        cfShareCaseUserVo.setAnonymous(isAnonymous);
        UserInfoModel userInfoModel = userInfoDelegate.getUserInfoByUserId(sourceUserId);
        String nickName = "";
        if(null != userInfoModel && !Strings.isEmpty(userInfoModel.getNickname())) {
            nickName = userInfoModel.getNickname();
        }
        cfShareCaseUserVo.setNickName(nickName);
        String realName = "";
        List<CrowdFundingVerification> crowdFundingVerifications = crowdFundingVerificationBiz.getByVerifyUserIdAndInfoUuid(sourceUserId, crowdfundingInfo.getInfoId());
        if(!CollectionUtils.isEmpty(crowdFundingVerifications)) {
            cfShareCaseUserVo.setVerified(true);
            realName = crowdFundingVerifications.get(0).getUserName();
        }
        cfShareCaseUserVo.setRealName(realName);
        return cfShareCaseUserVo;
    }

    @Async
    public void sendShareMq(CfInfoShareRecord cfInfoShareRecord) {

        if(producer == null || cfInfoShareRecord == null) {
            return;
        }

        producer.send(new Message(MQTopicCons.CF, MQTagCons.CF_SHARE_SUCCESS_DELAY_MSG,
                MQTagCons.CF_SHARE_SUCCESS_DELAY_MSG + "_" + cfInfoShareRecord.getInfoId() + "_" + cfInfoShareRecord.getId(), cfInfoShareRecord, DelayLevel.S30));

        producer.send(new Message(MQTopicCons.CF, MQTagCons.CF_SHARE_SUCCESS_REAL_TIME_MSG,
                MQTagCons.CF_SHARE_SUCCESS_REAL_TIME_MSG + "_" + cfInfoShareRecord.getInfoId() + "_" + cfInfoShareRecord.getId(), cfInfoShareRecord));

    }

    /**
     * 标记分享次数
     */
    public void markShare() {
        long currentTime = DateUtils.truncate(new Date(), Calendar.MINUTE).getTime();
        String key = "share_" + currentTime;
        //过期时间 设置成统计时长2倍
        counterClient.incrForRedis(CounterBizEnum.CF.getBiz(), key, 2 * shareLimitDurationMinute * 60 * 1000);
    }

    public boolean isShare() {
        long currentTime = DateUtils.truncate(new Date(), Calendar.MINUTE).getTime();
        List<String> keys = Lists.newArrayList();
        keys.add("share_" + currentTime);
        for (int i = 1; i < shareLimitDurationMinute; i++) {
            keys.add("share_" + (currentTime - 60000 * i));
        }


        Response<Long> totalCountRsp = counterClient.findByKeys(CounterBizEnum.CF.getBiz(), keys);
        //面向成功兜底
        if(totalCountRsp == null || totalCountRsp.notOk()) {
            return true;
        }
        boolean result = totalCountRsp.getData() < shareLimitTimes;
        log.info("cfShareService case is share result:{}", result);
        return result;
    }

    public String getRedisKey(int caseId ,long userId){
        return "UserShareCaseToday-" + caseId + "-" + userId + "-" + dateFormat.format(new Date());
    }
}
