package com.shuidihuzhu.cf.service.crowdfunding.listener.afteradd;

import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingCaseChannelBiz;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoBaseVo;
import com.shuidihuzhu.cf.service.crowdfunding.event.AddListenerOrder;
import com.shuidihuzhu.cf.service.crowdfunding.event.CrowdFundingAddEvent;
import com.shuidihuzhu.cf.service.crowdfunding.listener.base.AbstractCrowdFundingAddEventListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.event.SmartApplicationListener;
import org.springframework.stereotype.Service;

/**
 * 异步推断来源
 */
@Slf4j
@Service
public class SaveOriginInfoForCaseListener extends AbstractCrowdFundingAddEventListener implements SmartApplicationListener {

    @Autowired
    private CrowdfundingCaseChannelBiz crowdfundingCaseChannelBiz;

    @Override
    public void onApplicationEvent(ApplicationEvent _event) {

        CrowdFundingAddEvent crowdFundingAddEvent = (CrowdFundingAddEvent) _event;
        CrowdfundingInfo cfCase = crowdFundingAddEvent.getCrowdfundingInfo();
        CrowdfundingInfoBaseVo infoBaseVo = crowdFundingAddEvent.getCrowdfundingInfoBaseVo();
        if (cfCase != null && infoBaseVo != null) {
            try {
                crowdfundingCaseChannelBiz.saveOriginInfoForCase(cfCase, false);
            } catch (Exception e) {
                log.error("SaveOriginInfoForCaseListener error:", e);
            }
        }

    }

    @Override
    public int getOrder() {
        return AddListenerOrder.SaveOriginInfoForCase.getValue();
    }
}