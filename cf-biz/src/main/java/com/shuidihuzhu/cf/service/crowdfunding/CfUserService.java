package com.shuidihuzhu.cf.service.crowdfunding;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.CommonFriendsView;
import com.shuidihuzhu.cf.model.user.UserContributionLevel;
import com.shuidihuzhu.cf.model.user.UserContributionLevelView;
import com.shuidihuzhu.cf.model.user.UserSimpleInfo;
import com.shuidihuzhu.client.dataservice.faceApi.v1.FaceApiClient;
import com.shuidihuzhu.client.model.ContributeDetailDO;
import com.shuidihuzhu.client.model.OneDegreeFriendDO;
import com.shuidihuzhu.client.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CfUserService {

	@Resource
	private UserInfoDelegate userInfoDelegate;

	@Resource
	private FaceApiClient faceApiClient;

	public List<UserSimpleInfo> getUserSimpleInfos(List<Long> userIds) {

		if(CollectionUtils.isEmpty(userIds)){
			return Lists.newArrayList();
		}

		List<UserSimpleInfo> userSimpleInfos = Lists.newArrayList();
		try {
			List<UserInfoModel> userInfoModels = userInfoDelegate.getUserInfoByUserIdBatch(userIds);
			if (CollectionUtils.isEmpty(userInfoModels)) {
				return userSimpleInfos;
			}

			Map<Long, UserSimpleInfo> map = Maps.newHashMap();

			userInfoModels.forEach(x -> {
				UserSimpleInfo userSimpleInfo = new UserSimpleInfo(x.getNickname(), x.getHeadImgUrl());
				map.put(x.getUserId(), userSimpleInfo);
			});
			for(Long userId: userIds) {
				userSimpleInfos.add(map.get(userId));
			}
		} catch (Exception e) {
			log.error("获取用户信息异常。 userIds:{}", userIds, e);
		}

		return userSimpleInfos;
	}


	public UserContributionLevelView getUserContributionLevelView(long sourceUserId, String caseId) {
		UserContributionLevelView userContributionLevelView = new UserContributionLevelView();
		Response<OneDegreeFriendDO> response = null;
		try {
			response = faceApiClient.oneDegreeDetail(sourceUserId, caseId);
		} catch (Exception e) {
			log.error("从数据组获取一度好友贡献排行信息异常。sourceUserId:{}, caseId:{}", sourceUserId, caseId, e);
		}
		
		if (response == null || response.getData() == null) {
			return userContributionLevelView;
		}

		OneDegreeFriendDO oneDegreeFriendDO = response.getData();
		userContributionLevelView.setTotalNumberOfForwarders(oneDegreeFriendDO.getShareAllUv());
		if (CollectionUtils.isEmpty(oneDegreeFriendDO.getContributeDetail())) {
			return userContributionLevelView;
		}
		List<UserContributionLevel> res = Lists.newArrayList();

		List<Long> userIds = Lists.newArrayList();
		for (ContributeDetailDO contributeDetailDO : oneDegreeFriendDO.getContributeDetail()) {
			userIds.add(contributeDetailDO.getUserId());
		}

		Map<Long, UserInfoModel> maps= Collections.EMPTY_MAP;
		try {
			List<UserInfoModel> userInfoGrpcClientList  = userInfoDelegate.getUserInfoByUserIdBatch(userIds);
			maps = Maps.uniqueIndex(userInfoGrpcClientList, UserInfoModel::getUserId);
		}catch (Exception e){
			log.error("获取userIds异常。userIds:{}",userIds,e);
		}

		for (ContributeDetailDO contributeDetailDO : oneDegreeFriendDO.getContributeDetail()) {
			UserContributionLevel userContributionLevel = new UserContributionLevel();
			userContributionLevel.setRanking(contributeDetailDO.getRanking());
			userContributionLevel.setShareCnt(contributeDetailDO.getShareCnt());
			userContributionLevel.setContributeDonateAmt(contributeDetailDO.getContributeDonateAmt());
			userContributionLevel.setContributeDonateNum(contributeDetailDO.getContributeDonateNum());

			UserInfoModel userInfoModel = maps.get(contributeDetailDO.getUserId());
			if (userInfoModel != null) {
				userContributionLevel.setNickname(StringUtils.isBlank(userInfoModel.getNickname()) ? StringUtils.EMPTY : userInfoModel.getNickname());
				userContributionLevel.setHeadImgUrl(StringUtils.isBlank(userInfoModel.getHeadImgUrl()) ? StringUtils.EMPTY : userInfoModel.getHeadImgUrl());
			} else {
				userContributionLevel.setNickname(StringUtils.EMPTY);
				userContributionLevel.setHeadImgUrl(StringUtils.EMPTY);
			}
			res.add(userContributionLevel);
		}
		userContributionLevelView.setUserContributionLevelList(res);

		return userContributionLevelView;
	}
}
