package com.shuidihuzhu.cf.service.crowdfunding;

import com.shuidihuzhu.cf.biz.crowdfunding.CfHospitalAuditMirrorBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfOperatingRecordBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfReportMirrorRecordBiz;
import com.shuidihuzhu.cf.enums.crowdfunding.CfInfoMirrorRecordEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfOperatingRecordEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfHospitalAuditMirror;
import com.shuidihuzhu.cf.model.crowdfunding.CfOperatingRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingReport;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by niejiangnan on 2017/10/18.
 */
@Service
public class CfReportMirrorService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CfReportMirrorService.class);

    @Autowired
    private CfReportMirrorRecordBiz cfReportMirrorRecordBiz;
    @Autowired
    @Lazy
    private CfOperatingRecordBiz cfOperatingRecordBiz;
    @Autowired
    private CfHospitalAuditMirrorBiz cfHospitalAuditMirrorBiz;

    public CfOperatingRecord before(String infoUuid,long userId, String userName,
                                    CfOperatingRecordEnum.Type type, CfOperatingRecordEnum.Role role, List<CrowdfundingReport> crowdfundingReports) {
        try {
            CfHospitalAuditMirror lastOne = null;
            switch (type) {
                case SUBMIT_HOSPITAL_AUDIT:
                    //下发医院核实
                case SEND_HOSPITAL_AUDIT:
                    //医院核实信息被驳回
                case REFUSE_HOSPITAL_AUDIT:
                    //医院核实信息通过
                case PASS_HOSPITAL_AUDIT:
                CfOperatingRecord cfOperatingRecord = this.cfOperatingRecordBiz.getLastOneByType(infoUuid, type);
                if (cfOperatingRecord != null) {
                   lastOne = cfHospitalAuditMirrorBiz.getLastOneByType(cfOperatingRecord.getId(), CfInfoMirrorRecordEnum.Type.AFTER);
                }
                break;
            }
            userName = StringUtils.trimToEmpty(userName);
            CfOperatingRecord cfOperatingRecord = this.cfOperatingRecordBiz.save(infoUuid, userId, userName, type,
                    role,"",0);
            switch (type) {
                //与增信相关
                case SUBMIT_ADD_TRUST:
                case SEND_ADD_TRUST:
                case REFUSE_ADD_TRUST:
                case PASS_ADD_TRUST:
                    cfReportMirrorRecordBiz.save(infoUuid, cfOperatingRecord.getId(), CfInfoMirrorRecordEnum.Type.BEFORE, role,
                            true, true, crowdfundingReports);
                    break;
                case SUBMIT_HOSPITAL_AUDIT:
                    //下发医院核实
                case SEND_HOSPITAL_AUDIT:
                    //医院核实信息被驳回
                case REFUSE_HOSPITAL_AUDIT:
                    //医院核实信息通过
                case PASS_HOSPITAL_AUDIT:
                        cfHospitalAuditMirrorBiz.save(infoUuid, cfOperatingRecord.getId(), CfInfoMirrorRecordEnum.Type.BEFORE,
                                role, lastOne);
                    break;

            }
            return cfOperatingRecord;
        } catch (Exception e) {
            LOGGER.error("CfReportMirrorService before error:", e);
            return null;
        }
    }

    public void after(CfOperatingRecord cfOperatingRecord, List<CrowdfundingReport> crowdfundingReports) {
        try {
            CfOperatingRecordEnum.Role role = CfOperatingRecordEnum.Role.getByCode(cfOperatingRecord.getRole());
            CfOperatingRecordEnum.Type type = CfOperatingRecordEnum.Type.getByCode(cfOperatingRecord.getType());
            switch (type) {
                case SUBMIT_ADD_TRUST:
                case SEND_ADD_TRUST:
                case REFUSE_ADD_TRUST:
                case PASS_ADD_TRUST:
                    cfReportMirrorRecordBiz.save(cfOperatingRecord.getInfoUuid(), cfOperatingRecord.getId(), CfInfoMirrorRecordEnum.Type.AFTER,
                            role, true, true, crowdfundingReports);
                case SUBMIT_HOSPITAL_AUDIT:
                    //下发医院核实
                case SEND_HOSPITAL_AUDIT:
                    //医院核实信息被驳回
                case REFUSE_HOSPITAL_AUDIT:
                    //医院核实信息通过
                case PASS_HOSPITAL_AUDIT:
                    cfHospitalAuditMirrorBiz.save(cfOperatingRecord.getInfoUuid(), cfOperatingRecord.getId(), CfInfoMirrorRecordEnum.Type.AFTER,
                            role, null);
                    break;
            }
        } catch (Exception e) {
            LOGGER.error("CfReportMirrorService after error:", e);
        }
    }
}
