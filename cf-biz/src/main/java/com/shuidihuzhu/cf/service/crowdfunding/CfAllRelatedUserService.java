package com.shuidihuzhu.cf.service.crowdfunding;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.biz.crowdfunding.CfInfoBlessingBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfInfoShareRecordBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingOrderBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.verification.CrowdFundingVerificationBiz;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 *
 * 封装水滴筹案例所有相关用户的服务
 *
 * Created by wangsf on 18/4/2.
 */
@Service
public class CfAllRelatedUserService {

	@Autowired
	private CrowdfundingInfoBiz crowdfundingInfoBiz;

	@Autowired
	private CrowdfundingOrderBiz crowdfundingOrderBiz;

	@Autowired
	private CrowdFundingVerificationBiz crowdFundingVerificationBiz;

	@Autowired
	private CfInfoShareRecordBiz cfInfoShareRecordBiz;

	@Autowired
	private CfInfoBlessingBiz cfInfoBlessingBiz;

	public List<Long> getAllRelatedUserIds(int cfId) {

		CrowdfundingInfo crowdfundingInfo = this.crowdfundingInfoBiz.getFundingInfoById(cfId);
		if(crowdfundingInfo == null) {
			return Collections.EMPTY_LIST;
		}

		List<Long> userIds = Lists.newLinkedList();

		userIds.addAll(getCfOrderUserIds(cfId));
		userIds.addAll(getVerificationUsers(crowdfundingInfo));
		userIds.addAll(getBlessingUserIds(crowdfundingInfo));
		userIds.addAll(getShareUserId(crowdfundingInfo));

		return userIds.stream().distinct().collect(Collectors.toList());
	}

	//获取所有捐款人
	public List<Long> getCfOrderUserIds(int cfId) {

		int offset = 0;
		int limit = 1000;
		List<CrowdfundingOrder> crowdfundingOrders = this.crowdfundingOrderBiz.getListByInfoId(cfId, offset, limit);
		Set<Long> userIds = Sets.newHashSet();
		while(!CollectionUtils.isEmpty(crowdfundingOrders)) {
			List<Long> uids = crowdfundingOrders.stream()
					.map(crowdfundingOrder -> crowdfundingOrder.getUserId())
					.collect(Collectors.toList());
			userIds.addAll(uids);

			offset += crowdfundingOrders.size();
			crowdfundingOrders = this.crowdfundingOrderBiz.getListByInfoId(cfId, offset, limit);
		}

		return Lists.newArrayList(userIds);
	}

	//获取所有证实用户
	public List<Long> getVerificationUsers(CrowdfundingInfo crowdfundingInfo) {

		Set<Long> userIds = Sets.newHashSet();

		String infoUuid = crowdfundingInfo.getInfoId();
		int anchorId = Integer.MAX_VALUE;
		int limit = 500;
		int size = 500;
		while(size == limit) {
			List<CrowdFundingVerification> verifications = this.crowdFundingVerificationBiz.getListByAnchorId(infoUuid, anchorId, limit);
			if(!CollectionUtils.isEmpty(verifications) && verifications.size() > 1) {
				anchorId = verifications.remove(verifications.size() - 1).getId();
				size = verifications.size();
			} else {
				if(CollectionUtils.isEmpty(verifications)) {
					size = 0;
				} else {
					size = verifications.size();
				}
			}
			List<Long> uids = verifications.stream()
					.map(CrowdFundingVerification::getVerifyUserId)
					.collect(Collectors.toList());
			userIds.addAll(uids);
		}

		return Lists.newArrayList(userIds);
	}

	//获取所有加油的用户
	public List<Long> getBlessingUserIds(CrowdfundingInfo crowdfundingInfo) {

		Set<Long> userIds = Sets.newHashSet();

		int anchorId = Integer.MAX_VALUE;
		int limit = 500;
		int size = 500;
		while(size == limit) {
			List<CfInfoBlessing> blessings = this.cfInfoBlessingBiz.getByInfoUUid(crowdfundingInfo.getInfoId(), anchorId, limit + 1);
			if(!org.springframework.util.CollectionUtils.isEmpty(blessings) && blessings.size() > 1) {
				anchorId = blessings.remove(blessings.size() - 1).getId();
				size = blessings.size();
			} else {
				if(org.springframework.util.CollectionUtils.isEmpty(blessings)) {
					size = 0;
				} else {
					size = blessings.size();
				}
			}

			List<Long> uids = blessings.stream()
					.map(CfInfoBlessing::getUserId)
					.collect(Collectors.toList());

			userIds.addAll(uids);
		}

		return Lists.newArrayList(userIds);
	}

	//获取所有分享的用户
	public List<Long> getShareUserId(CrowdfundingInfo crowdfundingInfo) {

		Set<Long> userIds = Sets.newHashSet();

		long anchorId = Long.MAX_VALUE;
		int limit = 500;
		int size = 500;
		while(size == limit) {
			List<CfInfoShareRecord> shareRecords = this.cfInfoShareRecordBiz.getListByInfoId(crowdfundingInfo.getId(), anchorId, limit + 1);
			if(!org.springframework.util.CollectionUtils.isEmpty(shareRecords) && shareRecords.size() > 1) {
				anchorId = shareRecords.remove(shareRecords.size() - 1).getId();
				size = shareRecords.size();
			} else {
				if(org.springframework.util.CollectionUtils.isEmpty(shareRecords)) {
					size = 0;
				} else {
					size = shareRecords.size();
				}
			}

			List<Long> uids = shareRecords.stream()
					.map(CfInfoShareRecord::getUserId)
					.collect(Collectors.toList());

			userIds.addAll(uids);
		}

		return Lists.newArrayList(userIds);
	}
}
