package com.shuidihuzhu.cf.service.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.*;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.biz.crowdfunding.material.IMaterialCenterService;
import com.shuidihuzhu.cf.client.material.model.CfMaterialObject;
import com.shuidihuzhu.cf.delegate.ICFFinanceFeignDelegate;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.finance.model.vo.DonationAmountInFenVo;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.service.crowdfunding.bigdata.DsUserMessageService;
import com.shuidihuzhu.client.cf.api.model.enums.VariableTypeEnum;
import com.shuidihuzhu.client.dataservice.ds.v1.DsApiClient;
import com.shuidihuzhu.client.dataservice.faceApi.v1.FaceApiClient;
import com.shuidihuzhu.client.model.CFDO;
import com.shuidihuzhu.client.model.RealtimeDO;
import com.shuidihuzhu.client.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.Days;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @time 2019/2/21 下午8:28
 * @desc
 */
@Slf4j
@Service
public class CfUserCaseStatService {
    @Autowired
    private DsApiClient dsApiClient;

    @Autowired
    private FaceApiClient faceApiClient;

    @Autowired
    private UserInfoDelegate userInfoDelegate;

    @Autowired
    private CfUserCaseBaseStatService cfUserCaseBaseStatService;

    @Autowired
    private CfUserBaseStatService cfUserBaseStatService;

    @Autowired
    private CfInfoStatBiz cfInfoStatBiz;

    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;

    @Autowired
    private DsUserMessageService dsUserMessageService;

    @Autowired
    private ICFFinanceFeignDelegate cfFinanceFeignDelegate;

    @Autowired
    private IMaterialCenterService materialCenterService;


    public CfUserBaseStatDO getUserBaseStatInfo(long userId) {
        // 累计捐款
        CfUserBaseStatDO userBaseStatDO = cfUserBaseStatService.selectByUser(userId);
        if(userBaseStatDO != null) {
            return userBaseStatDO;
        }
        userBaseStatDO = new CfUserBaseStatDO();

        Response<RealtimeDO> realtimeDOResponse = dsApiClient.get(userId);
        if(realtimeDOResponse != null &&
                realtimeDOResponse.getCode() == 0 &&
                realtimeDOResponse.getData() != null) {
            RealtimeDO realTimeDO = realtimeDOResponse.getData();
            Optional<CFDO> optionalCFDO = Optional.ofNullable(realTimeDO).map(RealtimeDO::getCf);
            userBaseStatDO.setShareCount(optionalCFDO.map(CFDO::getShare_cnt).filter(StringUtils::isNotBlank).map(Integer::valueOf).orElse(0));
            userBaseStatDO.setDonateCount(optionalCFDO.map(CFDO::getDonate_cnt).filter(StringUtils::isNotBlank).map(Integer::valueOf).orElse(0));

            Long donateAmount = optionalCFDO.map(CFDO::getDonate_amount)
                    .filter(StringUtils::isNotBlank)
                    .map(Double::valueOf)
                    .map(v -> v * 100)
                    .map(Double::longValue)
                    .orElse(0L);
            userBaseStatDO.setDonateAmount(donateAmount);

            // 累计捐款案例数
            userBaseStatDO.setDonateCaseCount(optionalCFDO.map(CFDO::getDonate_case_num).orElse(0));

            userBaseStatDO.setShareCaseCount(optionalCFDO.map(CFDO::getShare_case_num).orElse(0));
        }
        return userBaseStatDO;
    }

    public CfUserStatInfo createUserCaseStatInfo(long userId, long caseId, CfUserBaseStatDO userBaseStatDO, CfUserCaseBaseStatDO userCaseBaseStatDO) {
        int donateCaseCount = null != userBaseStatDO ? userBaseStatDO.getDonateCaseCount() : 0;
        int donateCount = null != userBaseStatDO ? userBaseStatDO.getDonateCount() : 0;
        long donateMoney = null != userBaseStatDO ? userBaseStatDO.getDonateAmount() : 0;
        int shareCaseCount = null != userBaseStatDO ? userBaseStatDO.getShareCaseCount() : 0;
        int shareCount = null != userBaseStatDO ? userBaseStatDO.getShareCount() : 0;
        int shareBroughtMoney = null != userCaseBaseStatDO ? userCaseBaseStatDO.getShareBroughtAmount() : 0;
        int shareBroughtCount = -1;
        int shareFirstMoney = null != userCaseBaseStatDO ? userCaseBaseStatDO.getShareFirstAmount() : 0;
        int currentDonateMoney = null != userCaseBaseStatDO ? userCaseBaseStatDO.getDonateAmount() : 0;
        int currentShareCount = null != userCaseBaseStatDO ? userCaseBaseStatDO.getShareCount() : 0;

        if((null == userBaseStatDO || null == userCaseBaseStatDO) && userId > 0) {
            Response<RealtimeDO> response = faceApiClient.getCaseBase(userId, caseId);
            CfUserBaseStatDO baseStatDO = new CfUserBaseStatDO();
            if(null != response && 0 == response.getCode() && null != response.getData()) {
                RealtimeDO realtimeDO = response.getData();
//                JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(response.getData()));
//                if (jsonObject.containsKey("cf")) {
                if(realtimeDO.getCf() != null) {
                    CFDO cfdo = realtimeDO.getCf();
//                        JSONObject cfObject = jsonObject.getJSONObject("cf");
//                    if (null != cfObject) {
//                        String donateCaseNum = cfObject.getString("donate_case_num");
//                        String donateCnt = cfObject.getString("donate_cnt");
//                        String donateAmount = cfObject.getString("donate_amount");
//                        String shareCaseNum = cfObject.getString("share_case_num");
//                        String shareNum = cfObject.getString("share_cnt");

                    donateCaseCount = cfdo.getDonate_case_num() != null ? cfdo.getDonate_case_num() : 0;
//                                StringUtils.isNotEmpty(donateCaseNum) ? Integer.parseInt(donateCaseNum) : 0;
                    donateCount = StringUtils.isNotEmpty(cfdo.getDonate_cnt()) ? Integer.parseInt(cfdo.getDonate_cnt()) : 0;
//                                StringUtil.isNotEmpty(donateCnt) ? Integer.parseInt(donateCnt) : 0;
                    donateMoney = StringUtils.isNotEmpty(cfdo.getDonate_amount()) && Double.valueOf(cfdo.getDonate_amount()) > 0
                            ? convertAmountLong(cfdo.getDonate_amount()) : 0;
//                        StringUtils.isNotEmpty(donateAmount) && Double.valueOf(donateAmount) > 0 ? convertAmount(donateAmount) : 0;

                    shareCaseCount = cfdo.getShare_case_num() != null ? cfdo.getShare_case_num() : 0;
//                                StringUtils.isNotEmpty(shareCaseNum) ? Integer.parseInt(shareCaseNum) : 0;
                    shareCount = StringUtils.isNotEmpty(cfdo.getShare_cnt()) ? Integer.parseInt(cfdo.getShare_cnt()) : 0;
//                                StringUtils.isNotEmpty(shareNum) ? Integer.parseInt(shareNum) : 0;

                    baseStatDO.setUserId(userId);
                    baseStatDO.setDonateCaseCount(donateCaseCount);
                    baseStatDO.setDonateCount(donateCount);
                    baseStatDO.setDonateAmount(donateMoney);
                    baseStatDO.setShareCaseCount(shareCaseCount);
                    baseStatDO.setShareCount(shareCount);
//                    }
                }
            }
            Response<RealtimeDO> extResponse = dsUserMessageService.dsApiClientGetCaseExt(userId, caseId);
            log.debug("faceApiClient.getCaseExt variable convergence bigdata userId:{}, caseId:{}, result:{}", userId, caseId, JSON.toJSONString(extResponse));
            CfUserCaseBaseStatDO userCaseBaseStat = new CfUserCaseBaseStatDO();
            if(null != extResponse && 0 == extResponse.getCode() && null != extResponse.getData()) {

                RealtimeDO extRealTimeDo = extResponse.getData();
//                if (jsonObject.containsKey("cf")) {
                if(extRealTimeDo.getCf() != null) {
//                    JSONObject cfObject = jsonObject.getJSONObject("cf");
//                    if (null != cfObject) {
//                        String shareBroughAmount = cfObject.getString("share_donate_amount");
//                        String share_donate_user_num = cfObject.getString("share_donate_user_num");
//                        String shareFirstAmount = cfObject.getString("case_first_donate_amount");
//                        String currentDonateAmount = cfObject.getString("case_donate_amount");
//                        String currentShareNum = cfObject.getString("case_share_cnt");

                    CFDO extCfDO = extRealTimeDo.getCf();
                    shareBroughtMoney = StringUtils.isNotEmpty(extCfDO.getShare_donate_amount())
                            && Double.valueOf(extCfDO.getShare_donate_amount()) > 0 ? convertAmount(extCfDO.getShare_donate_amount()) : 0;
//                                StringUtils.isNotEmpty(shareBroughAmount) && Double.valueOf(shareBroughAmount) > 0 ? convertAmount(shareBroughAmount) : 0;
                    shareFirstMoney = StringUtils.isNotEmpty(extCfDO.getCase_first_donate_amount())
                            && Double.valueOf(extCfDO.getCase_first_donate_amount()) > 0 ? convertAmount(extCfDO.getCase_first_donate_amount()) : 0;
//                                StringUtils.isNotEmpty(shareFirstAmount) && Double.valueOf(shareFirstAmount) > 0 ? convertAmount(shareFirstAmount) : 0;
                    currentDonateMoney = StringUtils.isNotEmpty(extCfDO.getCase_donate_amount())
                            && Double.valueOf(extCfDO.getCase_donate_amount()) > 0 ? convertAmount(extCfDO.getCase_donate_amount()) : 0;
//                                StringUtils.isNotEmpty(currentDonateAmount) && Double.valueOf(currentDonateAmount) > 0 ? convertAmount(currentDonateAmount) : 0;
                    currentShareCount = StringUtils.isNotEmpty(extCfDO.getCase_share_cnt()) ? Integer.parseInt(extCfDO.getCase_share_cnt()) : 0;
//                                StringUtils.isNotEmpty(currentShareNum)  ? Integer.parseInt(currentShareNum) : 0;
                    shareBroughtCount = extCfDO.getShare_donate_user_num() != null ? extCfDO.getShare_donate_user_num() : 0;
//                    StringUtils.isNotEmpty(share_donate_user_num)  ? Integer.parseInt(share_donate_user_num) : 0;

                    userCaseBaseStat.setUserId(userId);
                    userCaseBaseStat.setCaseId((int) caseId);
                    userCaseBaseStat.setShareBroughtAmount(shareBroughtMoney);
                    userCaseBaseStat.setShareFirstAmount(shareFirstMoney);
                    userCaseBaseStat.setDonateAmount(currentDonateMoney);
                    userCaseBaseStat.setShareCount(currentShareCount);
//                    }
                }
            }
            boolean validate = validate(baseStatDO, userCaseBaseStat, userId, caseId);

            if(!validate) {
                log.debug("bigdata result invalidate. userId:{}", userId);
            }

            //大数据如果返回的数据非法,不入库
            if(validate) {
                cfUserBaseStatService.insert(baseStatDO);
                cfUserCaseBaseStatService.insert(userCaseBaseStat);
            }
        }

        if(shareBroughtCount == -1) {
            Response<RealtimeDO> extResponse = dsUserMessageService.dsApiClientGetCaseExt(userId, caseId);
            log.debug("faceApiClient.getCaseExt userId:{}, caseId:{}, result:{}", userId, caseId, JSON.toJSONString(extResponse));
            if(extResponse != null && extResponse.getCode() == 0 &&
                    extResponse.getData() != null && Objects.nonNull(extResponse.getData().getCf())) {
                if(extResponse.getData().getCf().getShare_donate_user_num() != null) {
                    shareBroughtCount = extResponse.getData().getCf().getShare_donate_user_num();
                }
            }
        }

        CfUserStatInfo cfUserStatInfo = new CfUserStatInfo();
        cfUserStatInfo.setDonateCaseCount(Math.max(donateCaseCount, 0));
        cfUserStatInfo.setDonateCaseCountPlusOne(donateCaseCount + 1);
        cfUserStatInfo.setTotalDonateCount(Math.max(donateCount, 0));
        cfUserStatInfo.setDonateMoney(Math.max(donateMoney, 0L));
        cfUserStatInfo.setShareCaseCount(Math.max(shareCaseCount, 0));
        cfUserStatInfo.setShareCaseCountPlusOne(shareCaseCount + 1);
        cfUserStatInfo.setTotalShareCount(Math.max(shareCount, 0));
        cfUserStatInfo.setShareBroughtMoney(Math.max(shareBroughtMoney, 0));
        cfUserStatInfo.setShareFirstMoney(Math.max(shareFirstMoney, 0));
        cfUserStatInfo.setCurrentDonateMoney(Math.max(currentDonateMoney, 0));
        cfUserStatInfo.setCurrentShareCount(Math.max(currentShareCount, 0));
        cfUserStatInfo.setShareBroughtCount(shareBroughtCount != -1 ? shareBroughtCount : 0);
        return cfUserStatInfo;
    }

    /**
     * @param receiveUserId:接收消息的用户的userId 必传
     * @param infoUuid:案例uuid
     * @param userSourceId:转发过该案例的用户userId 没有可不传
     * @return
     */
    public Map<String, String> createUserCaseVariable(long receiveUserId, String infoUuid, long userSourceId) {

        CrowdfundingInfo cfInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);

        long cfUserId = null != cfInfo ? cfInfo.getUserId() : 0;
        int caseId = null != cfInfo ? cfInfo.getId() : 0;

        CfUserBaseInfo cfUserBaseInfo = buildNickName(cfUserId, receiveUserId, userSourceId);
        CfCaseBaseInfo caseInfo = buildCaseBaseInfo(cfInfo);

        CfUserBaseStatDO userBaseStatDO = cfUserBaseStatService.selectByUser(receiveUserId);
        CfUserCaseBaseStatDO userCaseBaseStatDO = cfUserCaseBaseStatService.selectByUserAndCase(receiveUserId, caseId);
        CfUserStatInfo cfUserStatInfo = createUserCaseStatInfo(receiveUserId, caseId, userBaseStatDO, userCaseBaseStatDO);

        Map<String, String> resultMap = Maps.newConcurrentMap();
        resultMap.put(VariableTypeEnum.NICKNAME.getName(), cfUserBaseInfo.getNickName());
        resultMap.put(VariableTypeEnum.CROWDFUNGING_NICKNAME.getName(), cfUserBaseInfo.getCrowdfundingNickName());
        resultMap.put(VariableTypeEnum.SHARE_NICKNAME.getName(), cfUserBaseInfo.getShareNickName());

        resultMap.put(VariableTypeEnum.TITLE.getName(), null != caseInfo ? caseInfo.getTitle() : "");
        resultMap.put(VariableTypeEnum.DONATE_COUNT.getName(), null != caseInfo ? String.valueOf(caseInfo.getDonateCount()) : String.valueOf(0));
        resultMap.put(VariableTypeEnum.SHARE_COUNT.getName(), null != caseInfo ? String.valueOf(caseInfo.getShareCount()) : String.valueOf(0));
        resultMap.put(VariableTypeEnum.AMOUNT.getName(), null != caseInfo ? String.valueOf(caseInfo.getAmount() / 100) : String.valueOf(0));
        resultMap.put(VariableTypeEnum.REMAINDER_DAY.getName(), null != caseInfo ? String.valueOf(caseInfo.getRemainderDay()) : String.valueOf(0));
        resultMap.put(VariableTypeEnum.REMAINDE_AMOUNT.getName(), null != caseInfo ? String.valueOf(caseInfo.getRemainderAmount() / 100) : String.valueOf(0));

        resultMap.put(VariableTypeEnum.DONATE_CASE_COUNT.getName(), String.valueOf(cfUserStatInfo.getDonateCaseCount()));
        resultMap.put(VariableTypeEnum.DONATE_CASE_COUNT_PLUS_ONE.getName(), String.valueOf(cfUserStatInfo.getDonateCaseCountPlusOne()));
        resultMap.put(VariableTypeEnum.TOTAL_DONATE_COUNT.getName(), String.valueOf(cfUserStatInfo.getTotalDonateCount()));
        resultMap.put(VariableTypeEnum.DONATE_MONEY.getName(), String.valueOf(cfUserStatInfo.getDonateMoney() / 100));
        resultMap.put(VariableTypeEnum.SHARE_CASE_COUNT.getName(), String.valueOf(cfUserStatInfo.getShareCaseCount()));
        resultMap.put(VariableTypeEnum.SHATE_CASE_COUNT_PLUS_ONE.getName(), String.valueOf(cfUserStatInfo.getShareCaseCountPlusOne()));
        resultMap.put(VariableTypeEnum.TOTAL_SHARE_COUNT.getName(), String.valueOf(cfUserStatInfo.getTotalShareCount()));
        resultMap.put(VariableTypeEnum.SHARE_BROUGHT_MONEY.getName(), String.valueOf(cfUserStatInfo.getShareBroughtMoney() / 100));
        resultMap.put(VariableTypeEnum.SHARE_FIRST_MONEY.getName(), String.valueOf(cfUserStatInfo.getShareFirstMoney() / 100));
        resultMap.put(VariableTypeEnum.CURRENT_DONATE_MONEY.getName(), String.valueOf(cfUserStatInfo.getCurrentDonateMoney() / 100));
        resultMap.put(VariableTypeEnum.CURRENT_SHARE_COUNT.getName(), String.valueOf(cfUserStatInfo.getCurrentShareCount()));

        return resultMap;
    }

    public CfUserBaseInfo buildNickName(long cfUserId, long userId, long sourceUserId) {

        List<Long> userIds = Lists.newArrayList();
        if(cfUserId > 0) {
            userIds.add(cfUserId);
        }
        if(userId > 0) {
            userIds.add(userId);
        }

        if(sourceUserId > 0) {
            userIds.add(sourceUserId);
        }

        List<UserInfoModel> userInfos = this.userInfoDelegate.getUserInfoByUserIdBatch(userIds);

        Map<Long, UserInfoModel> userInfoMap = Maps.newConcurrentMap();

        for (UserInfoModel userInfoModel : userInfos) {
            if(null != userInfoModel) {
                userInfoMap.put(userInfoModel.getUserId(), userInfoModel);
            }
        }
        UserInfoModel cfUserInfo = MapUtils.isNotEmpty(userInfoMap) && userInfoMap.containsKey(cfUserId) ? userInfoMap.get(cfUserId) : null;
        UserInfoModel userInfo = MapUtils.isNotEmpty(userInfoMap) && userInfoMap.containsKey(userId) ? userInfoMap.get(userId) : null;
        UserInfoModel shareUserInfo = MapUtils.isNotEmpty(userInfoMap) && userInfoMap.containsKey(sourceUserId) ? userInfoMap.get(sourceUserId) : null;

        CfUserBaseInfo cfUserBaseInfo = new CfUserBaseInfo();
        cfUserBaseInfo.setCrowdfundingNickName(Objects.nonNull(cfUserInfo) ? cfUserInfo.getNickname() : StringUtils.EMPTY);
        cfUserBaseInfo.setCrowdfundingImgUrl(Objects.nonNull(cfUserInfo) ? cfUserInfo.getHeadImgUrl() : StringUtils.EMPTY);
        cfUserBaseInfo.setNickName(Objects.nonNull(userInfo) ? userInfo.getNickname() : StringUtils.EMPTY);
        cfUserBaseInfo.setImgUrl(Objects.nonNull(userInfo) ? userInfo.getHeadImgUrl() : StringUtils.EMPTY);
        cfUserBaseInfo.setShareNickName(Objects.nonNull(shareUserInfo) ? shareUserInfo.getNickname() : StringUtils.EMPTY);
        cfUserBaseInfo.setShareImgUrl(Objects.nonNull(shareUserInfo) ? shareUserInfo.getHeadImgUrl() : StringUtils.EMPTY);
        return cfUserBaseInfo;
    }

    public CfCaseBaseInfo buildCaseBaseInfo(CrowdfundingInfo crowdfundingInfo) {
        if(null == crowdfundingInfo) {
            return null;
        }

        CfInfoStat cfInfoStat = cfInfoStatBiz.getById(crowdfundingInfo.getId());
        DonationAmountInFenVo donationAmountInFenVo = cfFinanceFeignDelegate.getDonationAmountInFen(crowdfundingInfo.getInfoId());
        int donationAmountInFen = 0;
        if(donationAmountInFenVo != null) {
            donationAmountInFen = donationAmountInFenVo.getDonationAmountInFen();
        }

        String title = crowdfundingInfo.getTitle();
        int donateCount = null != cfInfoStat ? cfInfoStat.getDonationCount() : crowdfundingInfo.getDonationCount();
        int shareCount = null != cfInfoStat ? cfInfoStat.getShareCount() : 0;
        int amount = donationAmountInFen;
        int remainderDay = Days.daysBetween(new LocalDate(System.currentTimeMillis()), new LocalDate(crowdfundingInfo.getEndTime().getTime())).getDays();
        int lastDays = Days.daysBetween(new LocalDate(crowdfundingInfo.getCreateTime().getTime()), new LocalDate(System.currentTimeMillis())).getDays();
        int remainderAmount = crowdfundingInfo.getTargetAmount() - amount;

        //获取筹款人名称或者发起人名称
        CfMaterialObject cfMaterialObject = materialCenterService.selectMaterialObjectByCaseId(crowdfundingInfo.getId());
        CfFirsApproveMaterial cfFirsApproveMaterial = cfMaterialObject.getFirstApproveMaterial();
        String realName = "";
        if(cfFirsApproveMaterial != null) {
            if(StringUtils.isNotBlank(cfFirsApproveMaterial.getPatientRealName())) {
                realName = cfFirsApproveMaterial.getPatientRealName();
            } else {
                realName = cfFirsApproveMaterial.getSelfRealName();
            }
        }

        CfCaseBaseInfo cfCaseBaseInfo = new CfCaseBaseInfo();
        cfCaseBaseInfo.setTitle(title);
        cfCaseBaseInfo.setDonateCount(donateCount);
        cfCaseBaseInfo.setShareCount(shareCount);
        cfCaseBaseInfo.setAmount(amount);
        cfCaseBaseInfo.setRemainderDay(Math.max(remainderDay, 0));
        cfCaseBaseInfo.setLastDays(Math.max(lastDays, 0));
        cfCaseBaseInfo.setRemainderAmount(Math.max(remainderAmount, 0));
        cfCaseBaseInfo.setRealName(realName);

        return cfCaseBaseInfo;
    }

    private boolean validate(CfUserBaseStatDO baseStatDO, CfUserCaseBaseStatDO userCaseBaseStat, long userId, long caseId) {
        if(null == baseStatDO || null == userCaseBaseStat) {
            return false;
        }

        if(Objects.isNull(baseStatDO.getUserId())) {
            log.debug("sharingjdbc baseStatDO key null. userId:{}", userId);
            return false;
        }

        if(Objects.isNull(userCaseBaseStat.getUserId())) {
            log.debug("sharingjdbc userCaseBaseStat key null. userId:{},caseId:{}", userId, caseId);
            return false;
        }

        if(baseStatDO.getDonateCaseCount() < 0) {
            return false;
        }

        if(baseStatDO.getDonateCount() < 0) {
            return false;
        }

        if(baseStatDO.getDonateAmount() < 0) {
            return false;
        }

        if(baseStatDO.getShareCaseCount() < 0) {
            return false;
        }

        if(baseStatDO.getShareCount() < 0) {
            return false;
        }

        if(userCaseBaseStat.getShareBroughtAmount() < 0) {
            return false;
        }

        if(userCaseBaseStat.getShareFirstAmount() < 0) {
            return false;
        }

        if(userCaseBaseStat.getDonateAmount() < 0) {
            return false;
        }

        if(userCaseBaseStat.getShareCount() < 0) {
            return false;
        }

        return true;
    }

    private int convertAmount(String amount) {
        Double amountDouble = 100 * Double.parseDouble(amount);
        return amountDouble.intValue();
    }

    private long convertAmountLong(String amount) {
        Double amountDouble = 100 * Double.parseDouble(amount);
        return amountDouble.longValue();
    }
}
