package com.shuidihuzhu.cf.service.crowdfunding;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.service.ISensitiveProcessService;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @time 2019/12/3 下午5:17
 * @desc
 */
@Service
public class SensitiveProcessorService implements InitializingBean, ApplicationContextAware {

    private List<ISensitiveProcessService> processServiceList = Lists.newArrayList();

    @Setter
    private ApplicationContext applicationContext;

    public String process(String source){
        if (StringUtils.isEmpty(source)) {
            return source;
        }

        for (ISensitiveProcessService processService : processServiceList){
            source = process(source, processService);
        }

        return source;
    }

    private String process(final String source, ISensitiveProcessService processService) {
        Pattern pattern = processService.buildPattern();
        int depth = processService.getDepth();
        if (pattern == null || depth < 1) {
            return source;
        }

        final Matcher matcher = pattern.matcher(source);
        final StringBuilder result = new StringBuilder();

        int length = source.length();
        int i = 0;
        int header = 0;
        while (matcher.find() && (i < depth)) {
            i++;
            int start = matcher.start();
            int end = matcher.end();
            if (start < 0 || end < 0) {
                break;
            }
            result.append(source.substring(header, start));
            header = end;
            String group = matcher.group();
            result.append(processService.encrypt(group));
        }
        if (header < length) {
            result.append(source.substring(header, length));
        }
        return result.toString();
    }

    /**
     * 保证校验规则是有序的，因为身份证号比手机号长，如果先校验手机号，当身份证号里满足手机号的规则，则会被按照手机号打码。
     * 1：先匹配校验身份证
     * 2：再匹配校验手机号
     * 3：...
     * 4：...
     */
    @Override
    public void afterPropertiesSet() throws Exception {
        Map<String,ISensitiveProcessService> beansMap = applicationContext.getBeansOfType(ISensitiveProcessService.class);
        List<String> beanNameList = Lists.newArrayList("identitySensitiveProcessServiceImpl", "mobileSensitiveProcessServiceImpl");

        for (String beanName : beanNameList){
            ISensitiveProcessService sensitiveProcessService = beansMap.get(beanName);
            processServiceList.add(sensitiveProcessService);
        }
    }
}
