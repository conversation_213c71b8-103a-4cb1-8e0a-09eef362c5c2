package com.shuidihuzhu.cf.service.dedicated.impl;

import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.CfInfoExtBiz;
import com.shuidihuzhu.cf.dao.dedicated.CfUserVolunteerRelationDao;
import com.shuidihuzhu.cf.dao.dedicated.CrowdfundingVolunteerInviteUserRecordDao;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.delegate.impl.CfGrowthtoolDelegate;
import com.shuidihuzhu.cf.domain.dedicated.CfUserVolunteerRelationDO;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.dedicated.ICfVolunteerService;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackApiClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewBaseInfoDO;
import com.shuidihuzhu.client.cf.growthtool.model.CfVolunteerMaterialDO;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author: wanghui
 * @create: 2019/1/14 8:08 PM
 */
@Service
@Slf4j
public class CfVolunteerServiceImpl implements ICfVolunteerService {

    @Autowired
    CrowdfundingVolunteerInviteUserRecordDao crowdfundingVolunteerInviteUserRecordDao;
    @Autowired
    CfUserVolunteerRelationDao cfUserVolunteerRelationDao;

    @Autowired
    private CfInfoExtBiz cfInfoExtBiz;

    @Resource
    private UserInfoDelegate userInfoDelegate;

    @Resource
    private CfClewtrackApiClient cfClewtrackApiClient;
    @Autowired
    private CfGrowthtoolDelegate cfGrowthtoolDelegate;
    @Resource
    private ShuidiCipher shuidiCipher;

    @Override
    public CrowdfundingVolunteer getByUniqueCode(String uniqueCode) {
        return cfGrowthtoolDelegate.getCfVolunteerDOByUniqueCode(uniqueCode);
    }


    @Override
    public CfUserVolunteerRelationDO getAccountUserAndVolunteerRelationByPhone(String phone) {
        return cfUserVolunteerRelationDao.getAccountUserAndVolunteerRelation(null,phone);
    }


    @Override
    public CfVolunteerMaterialDO getVolunteerMateri(String uniqueCode) {
        return cfGrowthtoolDelegate.getVolunteerMateriByUniqueCode(uniqueCode);
    }

    /**
     * 获取志愿者信息
     *
     * @param crowdfundingInfo
     * @return
     */
    @Override
    public CrowdfundingVolunteer getVolunteer(CrowdfundingInfo crowdfundingInfo) {
        CrowdfundingVolunteer cfVolunteer = null;
        if (crowdfundingInfo.getUserId() <= 0L) {
            return null;
        }
        CfInfoExt cfInfoExt = cfInfoExtBiz.getByInfoUuid(crowdfundingInfo.getInfoId());
        if (cfInfoExt != null){
            String volunteerUniqueCode = cfInfoExt.getVolunteerUniqueCode();
            if (StringUtils.isNotBlank(volunteerUniqueCode)){
                cfVolunteer = this.getByUniqueCode(volunteerUniqueCode);
            }
        } else{ //手机号确定志愿者
            UserInfoModel userInfoModel = userInfoDelegate.getUserInfoByUserId(crowdfundingInfo.getUserId());
            if (null != userInfoModel) {
                Response<CfClewBaseInfoDO> response = cfClewtrackApiClient.getLatestClewbaseByMobile(shuidiCipher.decrypt(userInfoModel.getCryptoMobile()));
                if (null != response && null != response.getData()) {
                    String uniqueCode = response.getData().getUniqueCode();
                    if (StringUtils.isNotBlank(uniqueCode)){
                        cfVolunteer = this.getByUniqueCode(uniqueCode);
                    }
                }
            }
        }
        log.info("LoveHome_cfVolunteer:{}", cfVolunteer);
        return cfVolunteer;
    }

}
