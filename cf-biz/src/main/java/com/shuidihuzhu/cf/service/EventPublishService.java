package com.shuidihuzhu.cf.service;

import com.shuidihuzhu.cf.event.OrderAddEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEvent;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class EventPublishService {

    @Autowired
    ApplicationContext applicationContext;

    public void publish(ApplicationEvent event){
        log.info("EventPublishService publish event {}", event);
        try {
            applicationContext.publishEvent(event);
        } catch (Exception e) {
            log.error("EventPublishService Error", e);
        }
    }

    public void sendOrderAddEvent(Object source, int caseId, long userId, Long orderId, String channel) {
        publish(new OrderAddEvent(source, caseId, userId, orderId, channel));
    }
}
