package com.shuidihuzhu.cf.service.crowdfunding;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Splitter;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Range;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.biz.crowdfunding.AdminCrowdfundingPageConfigBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfCaseVisitConfigBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfInfoExtBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.graytest.CfGrayTestBiz;
import com.shuidihuzhu.cf.dao.rule.INinetyConfigNodeDAO;
import com.shuidihuzhu.cf.enums.crowdfunding.AdminCfPageTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfAbTestCode;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfCaseVisitConfigVo;
import com.shuidihuzhu.cf.model.rule.NinetyDaysConfigNode;
import com.shuidihuzhu.cf.service.risk.limit.ICfRiskService;
import com.shuidihuzhu.client.auth.saas.feign.UserFeignClient;
import com.shuidihuzhu.client.auth.saas.model.dto.AuthUserDto;
import com.shuidihuzhu.client.cf.risk.model.enums.RiskOperateSourceEnum;
import com.shuidihuzhu.client.cf.risk.model.enums.UserOperationEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DurationFormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Created by wangsf on 18/4/17.
 */
@Service
@Slf4j
@RefreshScope
public class CfCaseVisitConfigService {

    /**
     * 是否展示定制的banner
     */
    @Value("${baseinfo.visit-config.show-custom-banner:false}")
    private boolean showBanner;

    @Value("${baseinfo.visit-config.global-custom-banner-img: http://alioss.shuidichou.com/imgs/banner/pf-banner.png}")
    private String bannerImgUrl;

    @Value("${baseinfo.visit-config.global-custom-banner-url:}")
    private String bannerUrl;

    @Value("${baseinfo.visit-config.default-custom-banner-img:https://alioss.shuidichou.com/imgs/banner/rumor-notice.png}")
    private String defaultBannerImgUrl;

    @Value("${baseinfo.visit-config.default-custom-banner-url:}")
    private String defaultBannerUrl;

    @Value("${baseinfo.visit-config.sharable:true}")
    private boolean sharable = true;

    @Value("${baseinfo.visit-config.show-popup:false}")
    private boolean showPopup;

    @Value("${baseinfo.visit-config.global-popup-text:}")
    private String popupText;

    @Value("${baseinfo.visit-config.global-popup-title:}")
    private String popupTitle;

    @Value("${baseinfo.visit-config.default-popup-text:}")
    private String defaultPopupText;

    @Value("${baseinfo.visit-config.default-popup-title:}")
    private String defaultPopupTitle;

    @Value("${baseinfo.visit-config.check-first-review-status:true}")
    private boolean checkFirstReviewStatus;

    @Value("${baseinfo.visit-config.can-show:true}")
    private boolean canShow;

    @Value("${baseinfo.visit-config.force-show-banner:true}")
    private boolean forceShowBanner;

    /**
     * 左滑页面的配置，格式为：
     * 10:url;120:url2;70:url3 ==> 即 随机比例:地址;比例2:地址
     */
    @Value("${baseinfo.visit-config.back-page-url:}")
    private String backPageUrlConfig;

    /**
     * 发起图文风险审核开关
     */
    @Value("${raise-risk-check:true}")
    private boolean checkRaiseRiskEnable;

    @Value("${raise-risk-check:true}")
    private boolean checkEnable;

    @Value("#{'${apollo.show.add.black.list:}'.split(',')}")
    private Set<Integer> blackList;

    private int SUB_CASE_ID = 1;

    @Autowired
    private CfCaseVisitConfigBiz caseVisitConfigBize;

    @Autowired
    private CfGrayTestBiz cfGrayTestBiz;

    @Autowired
    private AdminCrowdfundingPageConfigBiz configBiz;

    @Resource
    private CrowdfundingInfoBiz crowdfundingInfoBiz;

    @Resource
    private CfInfoExtBiz cfInfoExtBiz;

    @Autowired
    private ICfRiskService cfRiskService;

    @Autowired
    private UserFeignClient userFeignClient;

    @Resource
    private INinetyConfigNodeDAO configNodeDAO;

    @Autowired
    private CfCaseEndWhiteListService cfCaseEndWhiteListService;

    private LoadingCache<String, String> dataCache = CacheBuilder.newBuilder().maximumSize(50)
            // 每秒钟refresh一次，但是是读触发的；如果没有读，则不会操作
            .refreshAfterWrite(1, TimeUnit.MINUTES).build(new CacheLoader<String, String>() {
                @Override
                public String load(String key) throws Exception {
                    return getDate();
                }
            });

    private LoadingCache<Long, List<NinetyDaysConfigNode>> modelCaches = CacheBuilder.newBuilder()
            .maximumSize(10)
            .expireAfterWrite(300, TimeUnit.SECONDS)
            .build(new CacheLoader<Long, List<NinetyDaysConfigNode>>() {
                @Override
                public List<NinetyDaysConfigNode> load(Long key) throws Exception {
                    return getNinetyDaysConfigNodes();
                }
            });

    public boolean canDonate(int caseId) {
        if (!this.checkFirstReviewStatus) {
            return true;
        }

        return cfRiskService.operatorValid(0, caseId, UserOperationEnum.ORDER);
    }

    /**
     * 图文审核和初审均通过，设置案例可以转发与捐款
     *
     * 参考touchSetCanShareAndDonate
     */
    public void setRiskCanShareAndOrder(int caseId, int adminUserId, String reason){
        CrowdfundingInfo fundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        String infoUuid = fundingInfo.getInfoId();

        // 初审是否通过
        CfInfoExt ext = cfInfoExtBiz.getByInfoUuid(infoUuid);
        FirstApproveStatusEnum firstApproveStatus = FirstApproveStatusEnum.parse(ext.getFirstApproveStatus());
        if (FirstApproveStatusEnum.isNotPassed(firstApproveStatus)) {
            log.info("setRiskCanShareAndOrder caseId {}, reason {}", caseId, reason);
        }

        //TODO ###请勿改动、待删除####
        caseVisitConfigBize.saveShareAndDonateStatus(caseId, true, true);
        String operator = null;
        Integer operatorId = null;
        if(adminUserId > 0){
            AuthUserDto authUserDto = userFeignClient.getValidAuthUserById((long) adminUserId).getData();
            operator = Objects.nonNull(authUserDto) ? authUserDto.getUserName() : null;
            operatorId = adminUserId;
        }

        cfRiskService.addOperateLimit(caseId, true, true, RiskOperateSourceEnum.FIRST_APPROVE.getCode(), operator, operatorId);
        //TODO ###请勿改动、待删除####

        cfRiskService.writeRiskOperate(caseId, Lists.newArrayList(UserOperationEnum.SHARE, UserOperationEnum.ORDER), true, RiskOperateSourceEnum.FIRST_APPROVE, adminUserId, reason);
    }

    /**
     * 策略上，已全局配置为主，先看全局配置，如果全局配置为false，再看单个的配置
     */
    public CfCaseVisitConfigVo getByCaseId(int caseId, String selfTag) {

        CfCaseVisitConfigVo cfCaseVisitConfigVo = new CfCaseVisitConfigVo();

        CfCaseVisitConfig cfCaseVisitConfig = cfRiskService.riskAdapterVisitConfig(caseId);

        Map<Integer, Boolean> riskResultMap = cfRiskService.queryOperateMap(caseId);
        Boolean raiserFinish = riskResultMap.get(UserOperationEnum.RAISER_FINISH.getCode());
        Boolean shareCoordonate = riskResultMap.get(UserOperationEnum.SHARE_COORDONATE.getCode());
        Boolean notOnlyWechatShow = riskResultMap.get(UserOperationEnum.NOT_ONLY_WECHAT_SHOW.getCode());

        if (cfCaseVisitConfig == null) {
            log.info("visitconfig not exist caseId:{}", caseId);
            cfCaseVisitConfigVo = this.getGlobalConfig(selfTag);

            if(null != cfCaseVisitConfigVo){
                cfCaseVisitConfigVo.setRaiserFinish(Objects.nonNull(raiserFinish) && !raiserFinish ? false : true);
            }

            //所有案例自动打开
            if(forceShowBanner) {
                cfCaseVisitConfigVo.setShowBanner(true);
            }

            return cfCaseVisitConfigVo;
        }

        //先通过数据库的字段赋值
        BeanUtils.copyProperties(cfCaseVisitConfig, cfCaseVisitConfigVo);

        //所有案例自动打开
        if(forceShowBanner) {
            cfCaseVisitConfigVo.setShowBanner(true);
        }

        if (cfCaseVisitConfigVo.isShowBanner()) {
            if (StringUtils.isBlank(cfCaseVisitConfigVo.getBannerImgUrl())) {
                cfCaseVisitConfigVo.setBannerImgUrl(this.defaultBannerImgUrl);
            }

            if (StringUtils.isBlank(cfCaseVisitConfigVo.getBannerUrl())) {
                cfCaseVisitConfigVo.setBannerUrl(this.defaultBannerUrl);
            }
        }

//		if(cfCaseVisitConfigVo.isShowPopup()) {
        if (StringUtils.isBlank(cfCaseVisitConfigVo.getPopupText())) {
            cfCaseVisitConfigVo.setPopupText(this.defaultPopupText);
            cfCaseVisitConfigVo.setPopupTitle(this.defaultPopupTitle);
        }
//		}

        //设置左滑的url
        CfLeftPageUrlConfig config = this.getLeftPageUrl(selfTag);

        if (config != null) {
            cfCaseVisitConfigVo.setBackPageUrl(config.url);
            cfCaseVisitConfigVo.setBackPageUrlGroup(config.group);
            cfCaseVisitConfigVo.setBackPageShowGif(config.showGif);
        } else {
            cfCaseVisitConfigVo.setBackPageUrl("https://www.shuidihuzhu.com/pay/landing?channel=sdc_leftslide_default");
            cfCaseVisitConfigVo.setBackPageUrlGroup(-1);
            cfCaseVisitConfigVo.setBackPageShowGif(true);
        }

        //根据bannerText的startTime和endTime设置bannerText
        Date starTime = cfCaseVisitConfig.getStartTime();
        Date endTime = cfCaseVisitConfig.getEndTime();
        if (starTime != null && endTime != null) {
            Range<Long> range = Range.closed(starTime.getTime(), endTime.getTime());
            if (range.contains(System.currentTimeMillis())) {
                cfCaseVisitConfigVo.setBannerText(cfCaseVisitConfig.getBannerText());
            } else {
                cfCaseVisitConfigVo.setBannerText(null);
            }
        }

        if (checkRaiseRiskEnable) {
            cfCaseVisitConfigVo.setHidden(cfCaseVisitConfig.isHidden());
            cfCaseVisitConfigVo.setHiddenTitle(cfCaseVisitConfig.getHiddenTitle());
            cfCaseVisitConfigVo.setForceStop(cfCaseVisitConfig.isForceStop());
        } else {
            cfCaseVisitConfigVo.setHidden(false);
            cfCaseVisitConfigVo.setHiddenTitle("");
            cfCaseVisitConfigVo.setForceStop(false);
        }

        cfCaseVisitConfigVo.setAbnormalHidden(cfCaseVisitConfig.isAbnormalHidden());
        cfCaseVisitConfigVo.setAbnormalHiddenSelfTitle(cfCaseVisitConfig.getAbnormalHiddenSelfTitle());
        cfCaseVisitConfigVo.setAbnormalHiddenOtherTitle(cfCaseVisitConfig.getAbnormalHiddenOtherTitle());
        cfCaseVisitConfigVo.setRaiserFinish(Objects.nonNull(raiserFinish) && !raiserFinish ? false : true);
        cfCaseVisitConfigVo.setCanDonateCooperate(Objects.nonNull(shareCoordonate) && !shareCoordonate ? false : true);
        cfCaseVisitConfigVo.setNotOnlyWechatShow(Objects.nonNull(notOnlyWechatShow) && !notOnlyWechatShow ? false : true);

        // 由于canShow 和 abnormalHidden在前端为重复表现，所以若都命中只以abnormalHidden为准
        if (cfCaseVisitConfigVo.isAbnormalHidden() && !cfCaseVisitConfigVo.isCanShow()) {
            cfCaseVisitConfigVo.setCanShow(true);
        }

        return cfCaseVisitConfigVo;
    }

    private boolean riskDiff(CfCaseVisitConfig visitConfig, CfCaseVisitConfig riskConfig){
        if(Objects.isNull(visitConfig) && Objects.isNull(riskConfig)){
            return false;
        }

        if(Objects.isNull(visitConfig) || Objects.isNull(riskConfig)){
            return true;
        }

        if(visitConfig.isShowBanner() != riskConfig.isShowBanner()){
            return true;
        }

        String bannerText = riskConfig.getBannerText();
        if(StringUtils.isNotEmpty(visitConfig.getBannerText()) && !visitConfig.getBannerText().equals(bannerText)){
            return true;
        }

        if(visitConfig.isSharable() != riskConfig.isSharable()){
            return true;
        }

        if(visitConfig.isCanDonate() != riskConfig.isCanDonate()){
            return true;
        }

        if(visitConfig.isCanShow() != riskConfig.isCanShow()){
            return true;
        }

        if(visitConfig.isAbnormalHidden() != riskConfig.isAbnormalHidden()){
            return true;
        }

        if(StringUtils.isNotEmpty(visitConfig.getPopupText()) && !visitConfig.getPopupText().equals(riskConfig.getPopupText())){
            return true;
        }

        if(visitConfig.isHidden() != riskConfig.isHidden() || visitConfig.isForceStop() != riskConfig.isForceStop()){
            return true;
        }

        return false;
    }

    private CfCaseVisitConfigVo getGlobalConfig(String selfTag) {
        CfCaseVisitConfigVo cfCaseVisitConfigVo = new CfCaseVisitConfigVo();
        cfCaseVisitConfigVo.setShowBanner(this.showBanner);
        cfCaseVisitConfigVo.setBannerImgUrl(this.defaultBannerImgUrl);
        cfCaseVisitConfigVo.setBannerUrl(this.defaultBannerUrl);
        cfCaseVisitConfigVo.setSharable(this.sharable);
        cfCaseVisitConfigVo.setShowPopup(this.showPopup);
        cfCaseVisitConfigVo.setPopupText(this.defaultPopupText);
        cfCaseVisitConfigVo.setPopupTitle(this.defaultPopupTitle);
        cfCaseVisitConfigVo.setCanShow(this.canShow);

        // 异常案例隐藏案例详情页
        cfCaseVisitConfigVo.setAbnormalHidden(false);
        cfCaseVisitConfigVo.setAbnormalHiddenSelfTitle("");
        cfCaseVisitConfigVo.setAbnormalHiddenOtherTitle("");

        CfLeftPageUrlConfig config = this.getLeftPageUrl(selfTag);

        if (config != null) {
            cfCaseVisitConfigVo.setBackPageUrl(config.url);
            cfCaseVisitConfigVo.setBackPageUrlGroup(config.group);
            cfCaseVisitConfigVo.setBackPageShowGif(config.showGif);
        } else {
            cfCaseVisitConfigVo.setBackPageUrl("https://www.shuidihuzhu.com/pay/landing?channel=sdc_leftslide_default");
            cfCaseVisitConfigVo.setBackPageUrlGroup(-1);
            cfCaseVisitConfigVo.setBackPageShowGif(false);
        }

        return cfCaseVisitConfigVo;
    }

    private CfLeftPageUrlConfig getLeftPageUrl(String selfTag) {

        List<String> urls = null;
        String url = "";
        try {
            url = dataCache.get("back-page-url");
            urls = Splitter.on(";").splitToList(url);
        } catch (ExecutionException e) {
            log.info("getLeftPageUrl error url={}", url, e);
            return null;
        }
        if (CollectionUtils.isEmpty(urls)) {
            return null;
        }
        if (StringUtils.isBlank(selfTag)) {
            return null;
        }

        int index = this.cfGrayTestBiz.getGrayTestBySelfTagWithCode(CfAbTestCode.CF_BACK_PAGE_URL_TEST.getCode(), 0, selfTag);
        //下标越界
        if (index < 0 || index >= urls.size()) {
            return null;
        }

        String urlItem = urls.get(index);

        urlItem = StringUtils.isBlank(urlItem) ? "" : urlItem;

        List<String> items = Splitter.on(":").splitToList(urlItem);
        if (!CollectionUtils.isEmpty(items) && items.size() == 3) {
            try {
                return new CfLeftPageUrlConfig(items.get(1) + ":" + items.get(2), index, "1".equals(items.get(0)) ? true : false);
            } catch (Exception e) {
                log.warn("urlItem:{}", urlItem, e);
            }
        }

        return null;
    }

    private String getDate() {
        List<CfPageGlobalConfig> list = configBiz.getListByType(AdminCfPageTypeEnum.leftPage.getCode());

        if (CollectionUtils.isEmpty(list)) {
            return "";
        }
        StringBuffer result = new StringBuffer();

        list.stream().forEach(it -> {
            result.append(it.getEffectShow()).append(":").append(it.getJumperUrl()).append(";");
        });
        String data = result.toString().substring(0, result.length() - 1);

        return data;
    }

    public CfCaseVisitConfigVo getByCaseIdV2(int caseId, long contextUserId, String selfTag) {
        CfCaseVisitConfigVo view = getByCaseId(caseId, selfTag);

        // 查询用户维度禁止转发
        if (view.isSharable()) {
            boolean sharable = cfRiskService.operatorValid(contextUserId, caseId, UserOperationEnum.SHARE);
            view.setSharable(sharable);
        }
        return getFundraisingNews(view, caseId,contextUserId);
    }


    private CfCaseVisitConfigVo getFundraisingNews(CfCaseVisitConfigVo cfCaseVisitConfigVo, int caseId, long userId){
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoByIdFromSlave(caseId);
        List<CfCaseEndWhiteList> cfCaseEndWhiteList = cfCaseEndWhiteListService.getList();
        List<Long> caseIdList = cfCaseEndWhiteList.stream().map(CfCaseEndWhiteList::getCaseId).collect(Collectors.toList());

        if (blackList.contains(crowdfundingInfo.getId()) && userId != crowdfundingInfo.getUserId()) {
            Map<Long, Set<Long>> treeMap = Maps.newHashMap();
            Map<Long, NinetyDaysConfigNode> map = getAllNodes();
            for (NinetyDaysConfigNode node : map.values()) {
                if (!treeMap.containsKey(node.getSourceNodeId())) {
                    Set<Long> set = Sets.newHashSet(node.getId());
                    treeMap.put(node.getSourceNodeId(), set);
                } else {
                    treeMap.get(node.getSourceNodeId()).add(node.getId());
                }
            }

            JSONObject jsonObject = new JSONObject();
            long rootNodeId = 0;
            fillingTree(treeMap, map, jsonObject, rootNodeId);
            cfCaseVisitConfigVo.setNinetyDaysSwitch(jsonObject);
            return cfCaseVisitConfigVo;
        }

        if (caseIdList.contains((long)crowdfundingInfo.getId())) {
            cfCaseVisitConfigVo.setNinetyDaysSwitch(null);
        } else {
            Date now = new Date();
            String dayStr = "0";
            if (crowdfundingInfo.getEndTime().getTime() < now.getTime()) {
                //案例未结束会导致异常
                dayStr = DurationFormatUtils.formatPeriod(crowdfundingInfo.getEndTime().getTime(), now.getTime(), "d");
            }
            int days = Integer.parseInt(dayStr);
            if (days >= 90 && userId != crowdfundingInfo.getUserId()) {
                Map<Long, Set<Long>> treeMap = Maps.newHashMap();
                Map<Long, NinetyDaysConfigNode> map = getAllNodes();
                for (NinetyDaysConfigNode node : map.values()) {
                    if (!treeMap.containsKey(node.getSourceNodeId())) {
                        Set<Long> set = Sets.newHashSet(node.getId());
                        treeMap.put(node.getSourceNodeId(), set);
                    } else {
                        treeMap.get(node.getSourceNodeId()).add(node.getId());
                    }
                }

                JSONObject jsonObject = new JSONObject();
                long rootNodeId = 0;
                fillingTree(treeMap, map, jsonObject, rootNodeId);
                cfCaseVisitConfigVo.setNinetyDaysSwitch(jsonObject);
            } else {
                cfCaseVisitConfigVo.setNinetyDaysSwitch(null);
            }
        }
        return cfCaseVisitConfigVo;
    }

    private static class CfLeftPageUrlConfig {
        String url;
        int group;
        boolean showGif;

        public CfLeftPageUrlConfig(String url, int group, boolean showGif) {
            this.url = url;
            this.group = group;
            this.showGif = showGif;
        }
    }

    public void fillingTree(Map<Long, Set<Long>> treeMap, Map<Long, NinetyDaysConfigNode> map, JSONObject jsonObject, long nodeId) {

        Set<Long> nextIds = treeMap.get(nodeId);
        if(CollectionUtils.isEmpty(nextIds)){
            return;
        }

        for (long nextNodeId : nextIds) {
            NinetyDaysConfigNode node = map.get(nextNodeId);
            if (node.isLeafNode()) {
                jsonObject.put(node.getDatakey(), node.isDataValue());
            } else {
                jsonObject.put(node.getDatakey(), new JSONObject());
                fillingTree(treeMap, map, jsonObject.getJSONObject(node.getDatakey()), nextNodeId);
            }
        }
    }

    public Map<Long, NinetyDaysConfigNode> getAllNodes() {
        List<NinetyDaysConfigNode> nodes = null;
        try {
            nodes = modelCaches.get(0L);
        } catch (Exception e) {
            log.error("", e);
            return Maps.newHashMap();
        }

        if (CollectionUtils.isEmpty(nodes)) {
            return Maps.newHashMap();
        }

        return Maps.uniqueIndex(nodes, NinetyDaysConfigNode::getId);
    }

    private List<NinetyDaysConfigNode> getNinetyDaysConfigNodes() {
        return configNodeDAO.getAll();
    }

}
