package com.shuidihuzhu.cf.service.crowdfunding;

import com.shuidihuzhu.cf.dao.crowdfunding.OtherBrandClueMobileDao;
import com.shuidihuzhu.cf.util.SDEncryptUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Slf4j
@Service
public class OtherBrandClueMobileService {

    @Resource
    private OtherBrandClueMobileDao otherBrandClueMobileDao;

    public void saveMobile(String mobile){
        if(StringUtils.isNotEmpty(mobile)){
            otherBrandClueMobileDao.save(SDEncryptUtils.encrypt(mobile));
        }
    }

}
