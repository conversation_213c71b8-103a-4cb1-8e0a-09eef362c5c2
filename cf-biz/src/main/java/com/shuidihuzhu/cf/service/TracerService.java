package com.shuidihuzhu.cf.service;

import brave.Span;
import brave.Tracer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2019-04-15  17:38
 */
@Service
@Slf4j
public class TracerService {

    @Resource
    private Tracer tracer;

    @Nullable
    public String getSpanId(){
        Span currentSpan = tracer.currentSpan();
        String spanId = null;
        if (currentSpan != null && null != currentSpan.context()) {
            spanId = currentSpan.context().traceIdString();
        }
        return spanId;
    }

}
