package com.shuidihuzhu.cf.service.crowdfunding.casematerial;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import com.shuidihuzhu.cf.service.crowdfunding.CrowdfundingCityService;
import com.shuidihuzhu.cf.vo.v5.CfHospitalLocationVo;
import com.shuidihuzhu.cf.vo.v5.CfMaterialAuditListView;
import com.shuidihuzhu.cf.vo.v5.CfTreatmentMaterialView;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * @Author: lianghongchao
 * @Date: 2018/11/05 19:21
 */
@Slf4j
@Service
public class CfTreatmentMaterialService {
    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private CrowdfundingTreatmentBiz crowdfundingTreatmentBiz;
    @Autowired
    private CrowdfundingCityService cityService;
    @Autowired
    private CrowdfundingAttachmentBiz crowdfundingAttachmentBiz;
    @Autowired
    private CrowdfundingInfoStatusBiz crowdfundingInfoStatusBiz;
    @Autowired
    private CfMaterialStatusService cfMaterialStatusService;
    @Autowired
    private CfCaseMaterialListService cfCaseMaterialListService;

    /**
     * @param userId
     * @param infoUuid
     * @return
     */
    public Response<CfTreatmentMaterialView> getTreatmentMaterial(long userId, String infoUuid) {

        CrowdfundingInfo crowdfundingInfo = this.crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(CfErrorCode.CF_NOT_FOUND);
        }
        if (userId != crowdfundingInfo.getUserId()) {
            return NewResponseUtil.makeError(CfErrorCode.USER_ACCOUNT_TOKEN_ERROR);
        }


        CfTreatmentMaterialView treatmentMaterialView = new CfTreatmentMaterialView();
        treatmentMaterialView.buildParentParam(crowdfundingInfo);
        //审核状态
        CrowdfundingInfoStatus infoStatus = this.crowdfundingInfoStatusBiz.getByInfoUuidAndType(infoUuid,
                CrowdfundingInfoDataStatusTypeEnum.TREATMENT_INFO_SUBMIT.getCode());

        treatmentMaterialView.setDataStatus(CrowdfundingInfoStatusEnum.getByCode(
                infoStatus == null ? 0 : infoStatus.getStatus()).name());
        if (null == infoStatus) {
            return NewResponseUtil.makeSuccess(treatmentMaterialView);
        }
        //设置诊断证明的医院基本信息
        this.setTreatmentHospitalInfo(treatmentMaterialView, crowdfundingInfo.getId());

        /**
         * ATTACH_TREATMENT(2), // 医疗诊断证明
         * ATTACH_INSPECTION_REPORT(15), // 检查报告
         * ATTACH_TREATMENT_NOTE(14), // 医疗票据
         * ATTACH_PASS_HOSPITAL(13), // 出院或者入院证明
         * ATTACH_MEDICAL_RECORD_HOME(12), //病案首页
         * ATTACH_IN_HOSPITAL(35),   //住院证明
         * 	ATTACH_LEAVE_HOSPITAL(36), //出院证明
         */
        List<AttachmentTypeEnum> attachmentTypeEnumList = Lists.newArrayList(AttachmentTypeEnum.ATTACH_TREATMENT,
                AttachmentTypeEnum.ATTACH_INSPECTION_REPORT, AttachmentTypeEnum.ATTACH_TREATMENT_NOTE,
                AttachmentTypeEnum.ATTACH_PASS_HOSPITAL, AttachmentTypeEnum.ATTACH_MEDICAL_RECORD_HOME,
                AttachmentTypeEnum.ATTACH_IN_HOSPITAL,AttachmentTypeEnum.ATTACH_LEAVE_HOSPITAL);
        treatmentMaterialView.setAttachments(this.getAttachmentsMap(crowdfundingInfo.getId(), attachmentTypeEnumList));

        //驳回详情
        treatmentMaterialView.setTreatmentRejectDetail(cfMaterialStatusService.getRejectDetailsByType( infoUuid,CrowdfundingInfoDataStatusTypeEnum.TREATMENT_INFO_SUBMIT));
        Pair<Map<Integer, Set<String>>, List<CfMaterialAuditListView.ModifySuggest>> pair = cfCaseMaterialListService.getRejectDetails(infoUuid, CrowdfundingInfoDataStatusTypeEnum.TREATMENT_INFO_SUBMIT);
        if (pair != null) {
            treatmentMaterialView.setRejects(pair.getLeft());
            treatmentMaterialView.setSuggests(pair.getRight());
        }
        return NewResponseUtil.makeSuccess(treatmentMaterialView);
    }

    /**
     * 设置诊断证明医院信息(就诊医院与确诊医院)
     *
     * @param treatmentMaterialView
     * @param crowdFundingId
     */
    private void setTreatmentHospitalInfo(CfTreatmentMaterialView treatmentMaterialView, int crowdFundingId) {
        CrowdfundingTreatment treatment = this.crowdfundingTreatmentBiz.get(crowdFundingId);
        if (null == treatment) {
            return;
        }
        //疾病名称
        treatmentMaterialView.setDiseaseName(treatment.getDiseaseName());

        int diagnoseHospitalCityId = treatment.getDiagnoseHospitalCityId();
        int treatmentHospitalCityId = treatment.getHospitalCityId();
        //医院地理位置信息
        Map<Integer, CrowdfundingCity> mapByIds = cityService.getMapByIds(Lists.newArrayList(treatmentHospitalCityId, diagnoseHospitalCityId));
        if (mapByIds == null || mapByIds.isEmpty()) {
            return;
        }

        //就诊医院
        CfHospitalLocationVo treatmentHospital = this.getHospitalInfo(mapByIds.get(treatmentHospitalCityId));
        if (null == treatmentHospital) {
            treatmentHospital = new CfHospitalLocationVo();
        }
        treatmentMaterialView.setTreatmentHospital(treatmentHospital);
        treatmentHospital.setHospitalName(treatment.getHospitalName());
        treatmentHospital.setHospitalId(treatment.getHospitalId());

        //确诊医院
        CfHospitalLocationVo diagnoseHospital = this.getHospitalInfo(mapByIds.get(diagnoseHospitalCityId));
        if (diagnoseHospital != null) {
            treatmentMaterialView.setDiagnoseHospital(diagnoseHospital);
            diagnoseHospital.setHospitalId(treatment.getDiagnoseHospitalId());
            diagnoseHospital.setHospitalName(treatment.getDiagnoseHospitalName());
        }

        log.info("getTreatmentInfo getMapByIds: treatmentHospital:{};diagnoseHospital:{};", treatmentHospital, diagnoseHospital);
    }

    /**
     * 获取医院城市信息
     *
     * @param hospitalCity
     * @return
     */
    private CfHospitalLocationVo getHospitalInfo(CrowdfundingCity hospitalCity) {
        if (null == hospitalCity) {
            return null;
        }

        CfHospitalLocationVo hospitalLocationVo = new CfHospitalLocationVo();
        hospitalLocationVo.setCityId(hospitalCity.getId());
        hospitalLocationVo.setCityName(hospitalCity.getName());
        int provinceId = hospitalCity.getParentId();
        Map<Integer, CrowdfundingCity> cityMap = cityService.getMapByIds(Lists.newArrayList(provinceId));
        if (null == cityMap || cityMap.isEmpty()) {
            return hospitalLocationVo;
        }
        CrowdfundingCity province = cityMap.get(provinceId);
        hospitalLocationVo.setProvinceId(province.getId());
        hospitalLocationVo.setProvinceName(province.getName());

        return hospitalLocationVo;
    }

    /**
     * 获取图片的链接数组
     *
     * @param crowdFundingId
     * @param attachmentTypeEnumList
     * @return
     */
    private Map<AttachmentTypeEnum, List<String>> getAttachmentsMap(int crowdFundingId,
                                                                    List<AttachmentTypeEnum> attachmentTypeEnumList) {
        if (crowdFundingId <= 0 || CollectionUtils.isEmpty(attachmentTypeEnumList)) {
            return Maps.newHashMap();
        }

        Map<AttachmentTypeEnum, List<CrowdfundingAttachmentVo>> attachmentTypeEnumListMap = this.crowdfundingAttachmentBiz
                .getFundingAttachmentMap(crowdFundingId, attachmentTypeEnumList);

        if (null == attachmentTypeEnumListMap || attachmentTypeEnumListMap.isEmpty()) {
            return Maps.newHashMap();
        }
        Map<AttachmentTypeEnum, List<String>> resultUrlMap = Maps.newHashMap();

        for (Map.Entry<AttachmentTypeEnum, List<CrowdfundingAttachmentVo>> enumListEntry : attachmentTypeEnumListMap.entrySet()) {
            if (null == enumListEntry) {
                continue;
            }
            AttachmentTypeEnum attachmentTypeEnum = enumListEntry.getKey();
            List<CrowdfundingAttachmentVo> attachmentVoList = enumListEntry.getValue();
            if (CollectionUtils.isEmpty(attachmentVoList)) {
                continue;
            }
            List<String> urlList = attachmentVoList.stream().filter(item -> null != item)
                    .map(CrowdfundingAttachmentVo::getUrl)
                    .collect(Collectors.toList());

            resultUrlMap.put(attachmentTypeEnum, urlList);
        }

        return resultUrlMap;
    }
}
