package com.shuidihuzhu.cf.service.crowdfunding.toufang;

import com.shuidihuzhu.cf.biz.datautilapi.DataUtilApiBiz;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.stat.feign.stat.CfTouFangSignClient;
import com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingBaseInfoBackupDao;
import com.shuidihuzhu.cf.delegate.ICfGrowthtoolDelegate;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.delegate.WxUserEventStatusDelegate;

import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import org.apache.commons.lang3.StringUtils;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.CfUserOrderCountService;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.enums.crowdfunding.PrimaryChannelEnum;
import com.shuidihuzhu.cf.model.ToufangStatusModel;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.param.UserAccessParam;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.rule.IRuleCollectionDetailService;
import com.shuidihuzhu.cf.util.crowdfunding.UserAccessParamUtil;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackApiClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewBaseInfoDO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.wx.biz.ShuidiWxService;
import com.shuidihuzhu.wx.model.WxMpConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;
import java.util.Optional;


/**
 * 管理投放的渠道
 * <p>
 * 1. 通过各渠道的channel等字段推测 primary_channel https://wiki.shuiditech.com/pages/viewpage.action?pageId=22089476
 * <p>
 * Created by wangsf on 18/9/6.
 */
@Service
@RefreshScope
@Slf4j
public class CfPrimaryChannelService {

	private static final int RULE_COLLECTION_TOUFANG_SIGN = 2;
	private static final int RULE_COLLECTION_AD_REGISTER = 3;
	private static final int RULE_COLLECTION_ADVISER_SERVICE = 4;
	private static final int RULE_COLLECTION_CASE_CHANNEL = 5;
	@Value("${baseinfo.first-approve.activity_id:1}")
	private long activityId;
	@Autowired
	private IRuleCollectionDetailService ruleCollectionDetailService;
	@Autowired
	private ShuidiWxService shuidiWxService;
	@Autowired
	private WxUserEventStatusDelegate wxUserEventStatusDelegate;
	@Autowired
	private CrowdfundingInfoBiz crowdfundingInfoBiz;
	@Autowired
	private UserInfoDelegate userInfoDelegate;

	@Autowired
	private CfTouFangSignClient cfTouFangSignClient;

	@Autowired
	private CfUserOrderCountService cfUserOrderCountService;
	@Autowired
	private CfClewtrackApiClient cfClewtrackApiClient;

	@Autowired
	private DataUtilApiBiz dataUtilApiBiz;

	@Autowired
	private ICfGrowthtoolDelegate growthtoolDelegate;

    @Autowired
    private CrowdfundingBaseInfoBackupDao crowdfundingBaseInfoBackupDao;

	@Autowired
	private ShuidiCipher shuidiCipher;
	/**
	 * 推测cf_toufang_register_day的primaryChannel
	 *
	 * @param cfTouFangSign
	 * @return
	 */
	public String getPrimaryChannel(CfTouFangSign cfTouFangSign) {

		if (cfTouFangSign == null || StringUtils.isBlank(cfTouFangSign.getChannel())) {
			return PrimaryChannelEnum.OTHER.getValue();
		}

		return this.getPrimaryChannelByActivity(RULE_COLLECTION_TOUFANG_SIGN, cfTouFangSign);
	}

	/**
	 * 推测cf_ad_register_record的primaryChannel
	 *
	 * @param cfAdRegister
	 * @return
	 */
	public String getPrimaryChannel(CfAdRegister cfAdRegister) {

		if (cfAdRegister == null || StringUtils.isBlank(cfAdRegister.getChannel())) {
			return PrimaryChannelEnum.OTHER.getValue();
		}

		return this.getPrimaryChannelByActivity(RULE_COLLECTION_AD_REGISTER, cfAdRegister);
	}

	/**
	 * 推测cf_adviser_service的primaryChannel
	 *
	 * @param cfAdviserService
	 * @return
	 */
	public String getPrimaryChannel(CfAdviserService cfAdviserService) {

		if (cfAdviserService == null || StringUtils.isBlank(cfAdviserService.getChannel())) {
			return "";
		}

		return this.getPrimaryChannelByActivity(RULE_COLLECTION_ADVISER_SERVICE, cfAdviserService);
	}

	/**
	 * 推断案例的primary_channel
	 *
	 * @param crowdfundingInfo
	 * @return
	 */
	public String getPrimaryChannel(CrowdfundingInfo crowdfundingInfo) {

		if (crowdfundingInfo == null || StringUtils.isBlank(crowdfundingInfo.getChannel())) {
			return PrimaryChannelEnum.OTHER.getValue();
		}

		return this.getPrimaryChannelByActivity(RULE_COLLECTION_CASE_CHANNEL, crowdfundingInfo);
	}

	public String getPrimaryChannelByActivity(int activityId, Object data) {
		try {
			OpResult<String> result = this.ruleCollectionDetailService.filter(activityId, data);
			String primaryChannel = result.getData();
			if (StringUtils.isBlank(primaryChannel)) {
				return PrimaryChannelEnum.OTHER.getValue();
			}

			return primaryChannel;
		} catch (Exception e) {
			log.error("ruleCollectionDetailService error", e);
		}
		return PrimaryChannelEnum.OTHER.getValue();
	}

	/**
	 * 获得当前用户状态信息
	 *
	 * @param userId
	 * @param toufangStatusModel
	 * @param selfTag
	 * @param channel
	 * @param thirdTypes
	 * @return
	 */
	public ToufangStatusModel getToufangStatusByUserId(long userId, ToufangStatusModel toufangStatusModel, String selfTag, String channel, String thirdTypes) {
		String userThirdTypes[] = thirdTypes.split(",");
		if (userThirdTypes.length == 0) {
			return toufangStatusModel;
		}
		for (int i = 0; i < userThirdTypes.length; i++) {
			WxMpConfig wxMpConfig = shuidiWxService.getByThirdType(Integer.parseInt(userThirdTypes[i]));
			if (wxMpConfig == null) {
				log.info("无法获取公众号信息");
				return toufangStatusModel;
			}
			//判断是否关注
			boolean isSubscribe = wxUserEventStatusDelegate.checkSubscribeByUserId(userId, wxMpConfig.getThirdType());
			if (isSubscribe) {
				toufangStatusModel.setFollow(true);
				break;
			}
		}
		//判断是否发起案例
		if (crowdfundingInfoBiz.getLastByUserId(userId)!=null) {
			toufangStatusModel.setLaunch(true);
		}
		//判断30天是否有草稿，按照创建时间
        CrowdfundingBaseInfoBackup baseInfoBackup = crowdfundingBaseInfoBackupDao.selectRecentlyCfByUserId(userId,0);
        if (baseInfoBackup != null && DateUtils.addDays(baseInfoBackup.getCreateTime(),30).after(new Date())){
            toufangStatusModel.setHasDraftInThirtyDays(true);
        }
		//判断是否是线下BD
		boolean isVolunteerCreateCase = growthtoolDelegate.checkIsVolunteerCreateCase(userId);
		toufangStatusModel.setVolunteerFlag(isVolunteerCreateCase);
		//判断是否绑定手机和是否登记
		UserInfoModel userInfoModel = userInfoDelegate.getUserInfoByUserId(userId);
		if (userInfoModel != null && org.apache.commons.lang3.StringUtils.isNotBlank(userInfoModel.getCryptoMobile())) {
			toufangStatusModel.setLogin(true);
			RpcResult<com.shuidihuzhu.cf.client.stat.model.stat.CfTouFangSign> rpcResult =
					cfTouFangSignClient.selectByMoblie(userInfoModel.getCryptoMobile());
			com.shuidihuzhu.cf.client.stat.model.stat.CfTouFangSign cfTouFangSign = Optional.ofNullable(rpcResult).filter(RpcResult::isSuccess).map(RpcResult::getData).orElse(null);
			if (Objects.nonNull(cfTouFangSign)) {
				toufangStatusModel.setRegist(true);
			}
			Response<CfClewBaseInfoDO> response = cfClewtrackApiClient.getLatestClewbaseByMobile(shuidiCipher.decrypt(userInfoModel.getCryptoMobile()));
			setLatestRegist(toufangStatusModel, response);
		}
		//如果没有手机号，根据userId获取信息
		if(!toufangStatusModel.isLatestRegist() && userId>0){
			Response<CfClewBaseInfoDO> cfClewBaseInfoDOResponse = cfClewtrackApiClient.getLatestClewbaseByUserId(userId);
			log.info("getLatestClewbaseByUserId_userId:{},cfClewBaseInfoDOResponse:{}",userId,cfClewBaseInfoDOResponse);
			setLatestRegist(toufangStatusModel, cfClewBaseInfoDOResponse);
		}
		if (org.apache.commons.lang3.StringUtils.isNotBlank(channel) && org.apache.commons.lang3.StringUtils.isNotBlank(selfTag)) {
			//判断风险地区
			UserAccessParam userAccessParam = UserAccessParamUtil.build(userInfoModel, toufangStatusModel.getClientIp(), channel, selfTag);
			dataUtilApiBiz.setProvinceName(toufangStatusModel.getClientIp(),userAccessParam);

			OpResult opResult = ruleCollectionDetailService.filter(activityId, userAccessParam);
			if (opResult.isSuccess()) {
				toufangStatusModel.setRiskArea(true);
			}
		}
		//判断是否捐款
		if (cfUserOrderCountService.getOrderCount(userId) > 0) {
			toufangStatusModel.setDonate(true);
		}
		Integer userType = getUserType(userId,toufangStatusModel.isDonate());
		toufangStatusModel.setUserType(userType);
		return toufangStatusModel;
	}

	/**
	 * 设置最近24小时登记过
	 * @param toufangStatusModel
	 * @param response
	 */
	private void setLatestRegist(ToufangStatusModel toufangStatusModel, Response<CfClewBaseInfoDO> response) {
		if(response.ok() && response.getData()!=null){
			Date today = new Date();
			long diffMillions = today.getTime() - response.getData().getCreateTime().getTime();
			if (diffMillions < (24 * 60 * 60 * 1000)) {
				toufangStatusModel.setLatestRegist(true);
			}
			//最近三十天是否登记过
			if (diffMillions < (30L * 24 * 60 * 60 * 1000)) {
				toufangStatusModel.setRegistInThirtyDays(true);
			}
		}
	}

	private Integer getUserType(Long userId,boolean isDonate){
		if(userId==null || userId<=0){
			return 0;
		}
		CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getLastCrowdfundedByUserId(userId);
		if(crowdfundingInfo!=null){
			return 1;
		}
		if(isDonate){
			return 2;
		}
		return 0;
	}

}
