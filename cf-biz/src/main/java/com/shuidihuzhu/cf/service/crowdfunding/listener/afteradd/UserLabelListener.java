package com.shuidihuzhu.cf.service.crowdfunding.listener.afteradd;

import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.delegate.IUserProfileDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoBaseVo;
import com.shuidihuzhu.cf.service.crowdfunding.event.AddListenerOrder;
import com.shuidihuzhu.cf.service.crowdfunding.event.CrowdFundingAddEvent;
import com.shuidihuzhu.cf.service.crowdfunding.listener.base.AbstractCrowdFundingAddEventListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEvent;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Created by sven on 2019/9/15.
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserLabelListener extends AbstractCrowdFundingAddEventListener {

    /** https://wiki.shuiditech.com/pages/viewpage.action?pageId=********* */
    private final static String KEY = "cf_self_case_near_status";

    @Resource
    private IUserProfileDelegate userProfileDelegate;

    @Override
    public void onApplicationEvent(ApplicationEvent event) {
        CrowdFundingAddEvent crowdFundingAddEvent = (CrowdFundingAddEvent) event;
        CrowdfundingInfo cfCase = crowdFundingAddEvent.getCrowdfundingInfo();
        CrowdfundingInfoBaseVo crowdfundingInfoBaseVo = crowdFundingAddEvent.getCrowdfundingInfoBaseVo();
        UserRelTypeEnum relType = crowdfundingInfoBaseVo.getRelType();

        log.info("CrowdfundingInfoBaseVo {}", crowdfundingInfoBaseVo);

        if(relType != null && relType == UserRelTypeEnum.SELF) {
            userProfileDelegate.setProfile(cfCase.getUserId(), KEY, 1);
        }
    }

    @Override
    public int getOrder() {
        return AddListenerOrder.UserLabel.getValue();
    }
}
