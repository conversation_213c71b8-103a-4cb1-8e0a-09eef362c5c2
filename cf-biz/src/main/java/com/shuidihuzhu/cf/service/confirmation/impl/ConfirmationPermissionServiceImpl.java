package com.shuidihuzhu.cf.service.confirmation.impl;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.client.material.model.MaterialPlanVersion;
import com.shuidihuzhu.cf.client.ugc.feign.MaterialBundleFeignClient;
import com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingBaseInfoBackupDao;
import com.shuidihuzhu.cf.model.FamilyVersionSwitch;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingBaseInfoBackup;
import com.shuidihuzhu.cf.service.confirmation.ConfirmationPermissionService;
import com.shuidihuzhu.client.cf.clewtrack.model.UserConfirmationCheckModel;
import com.shuidihuzhu.client.cf.clewtrack.model.UserScanCodeCheckModel;
import com.shuidihuzhu.client.cf.growthtool.client.CfVolunteerScanCodeFeignClient;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/8/3 7:33 PM
 */
@Slf4j
@Service
@RefreshScope
public class ConfirmationPermissionServiceImpl implements ConfirmationPermissionService {

    private FamilyVersionSwitch familyVersionSwitch;

    @Value("${apollo.case.family.switch:}")
    public void setAuditPublicInfo(String caseFamilySwitchStr) {
        try {
            familyVersionSwitch = JSON.parseObject(caseFamilySwitchStr, FamilyVersionSwitch.class);
        } catch (Exception e) {
            log.error("apollo.case.family.switch 解析失败", e);
        }
    }

    @Resource
    private MaterialBundleFeignClient materialBundleFeignClient;
    @Resource
    private CfVolunteerScanCodeFeignClient cfVolunteerScanCodeFeignClient;
    @Resource
    private CrowdfundingBaseInfoBackupDao crowdfundingBaseInfoBackupDao;

    @Override
    public UserConfirmationCheckModel judgeSkipConfirmation(Long userId) {

        CrowdfundingBaseInfoBackup crowdfundingBaseInfoBackup = crowdfundingBaseInfoBackupDao.selectRecentlyCfByUserId(userId, 0);
        if (Objects.isNull(crowdfundingBaseInfoBackup)) {
            return buildConfirmationSkipModel();
        }

        // 判断是否已完成确权
        Response<UserConfirmationCheckModel> response = cfVolunteerScanCodeFeignClient.checkUserNeedConfirmation(userId, (long) crowdfundingBaseInfoBackup.getId());
        UserConfirmationCheckModel userConfirmationCheckModel = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null);
        if (Objects.isNull(userConfirmationCheckModel)) {
            return buildConfirmationSkipModel();
        }

        return userConfirmationCheckModel;
    }

    @Override
    public UserScanCodeCheckModel judgeEnterAuthenticity(Long userId) {
        Response<UserScanCodeCheckModel> response = cfVolunteerScanCodeFeignClient.checkUserScanCode(userId);
        UserScanCodeCheckModel userScanCodeCheckModel = Optional.ofNullable(response)
                .filter(Response::ok)
                .map(Response::getData)
                .orElse(null);
        if (Objects.isNull(userScanCodeCheckModel) || Objects.isNull(familyVersionSwitch)) {
            return buildScanCodeSkipModel();
        }

        if (familyVersionSwitch.getWhiteListUsers().contains(userId)) {
            userScanCodeCheckModel.setMaterialPlanId(MaterialPlanVersion.PLAN_160.getCode());
            return userScanCodeCheckModel;
        }
        // 扫码用户走扫码自发起开关、自发起用户走自发起开关
        if (userScanCodeCheckModel.isUserScanTag()) {
            userScanCodeCheckModel.setMaterialPlanId(familyVersionSwitch.isQRRaiseSwitch() ? MaterialPlanVersion.PLAN_160.getCode() : MaterialPlanVersion.PLAN_120.getCode());
        } else {
            userScanCodeCheckModel.setMaterialPlanId(familyVersionSwitch.isRaiseSwitch() ? MaterialPlanVersion.PLAN_160.getCode() : MaterialPlanVersion.PLAN_120.getCode());
        }

        return userScanCodeCheckModel;
    }

    /**
     * 极端情况，跳过确权
     */
    private UserConfirmationCheckModel buildConfirmationSkipModel() {
        UserConfirmationCheckModel model = new UserConfirmationCheckModel();
        model.setAllowRaise(true);
        return model;
    }

    /**
     * 极端情况，不走新增信
     */
    private UserScanCodeCheckModel buildScanCodeSkipModel() {
        UserScanCodeCheckModel model = new UserScanCodeCheckModel();
        model.setShowFourthPage(false);
        model.setMaterialPlanId(MaterialPlanVersion.PLAN_120.getCode());
        return model;
    }
}
