package com.shuidihuzhu.cf.service.crowdfunding.listener.base;

import com.shuidihuzhu.cf.service.crowdfunding.event.CrowdFundingAddEvent;
import com.shuidihuzhu.cf.service.crowdfunding.event.CrowdFundingAddPublisher;
import com.shuidihuzhu.cf.service.crowdfunding.event.CrowdFundingUpdateEvent;
import com.shuidihuzhu.cf.service.crowdfunding.event.CrowdFundingUpdatePublisher;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.event.SmartApplicationListener;

/**
 * <AUTHOR>
 * @date 2019-08-19
 */
public abstract class AbstractCrowdFundingAddOrUpdateEventListener implements SmartApplicationListener {

    @Override
    public boolean supportsSourceType(Class<?> sourceType) {
        if ((sourceType == CrowdFundingAddPublisher.class) || (sourceType == CrowdFundingUpdatePublisher.class)) {
            return true;
        }
        return false;
    }

    @Override
    public boolean supportsEventType(Class<? extends ApplicationEvent> eventType) {

        if (CrowdFundingAddEvent.class.isAssignableFrom(eventType) || CrowdFundingUpdateEvent.class.isAssignableFrom(eventType)) {
            return true;
        }
        return false;

    }
}
