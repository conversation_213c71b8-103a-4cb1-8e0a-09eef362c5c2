package com.shuidihuzhu.cf.service.calltree;

import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.fulllink.measurement.annotation.Operate;
import com.shuidihuzhu.cf.model.calltree.MethodCallChain;
import com.shuidihuzhu.cf.model.calltree.MethodCallTree;
import com.shuidihuzhu.common.datasource.annotation.DataSource;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.eb.grafana.configuration.plugin.mybatis.typecheck.MappedStatementUtils;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import com.shuidihuzhu.eb.grafana.configuration.plugin.mybatis.typecheck.ExceptionUtils;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Node;
import org.dom4j.tree.DefaultElement;
import org.dom4j.tree.DefaultText;
import org.jdom.input.SAXBuilder;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Profile;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.util.ReflectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.io.IOException;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: cuikexiang
 */
@Service
@Profile({"bedin", "debug", "canary"})
@Slf4j
public class MethodCallChainServiceImpl {

    public static final String DAO = "dao";
    public static final String FEIGN = "feign";

    @Autowired
    SqlSessionTemplate sqlSessionTemplate;

    @Autowired
    private ApplicationContext appContext;
    @Autowired
    private MethodCallChainFeignClient methodCallChainFeignClient;

    @Value("${spring.application.name:}")
    private String applicationName;

    //    private final String HYSTRIX_TIMEOUT_KEY_FORMAT = "hystrix.command.'CfGrowthtoolVolunteerFeignClient#addApplyBaseInfo(CfVolunteerBaseInfoParam)'.execution.isolation.thread.timeoutInMilliseconds";
    private final String HYSTRIX_TIMEOUT_KEY_FORMAT = "hystrix.command.'%s'.execution.isolation.thread.timeoutInMilliseconds";
    private final String HYSTRIX_TIMEOUT_KEY = "hystrix.command.default.execution.isolation.thread.timeoutInMilliseconds";

    private Map<String, String> daoMethodName2dataSourceName = new HashMap<>(300);
    private Map<String, String> daoMethodName2operateType = new HashMap<>(300);


    private Map<String, Set<String>> daoMethodName2TabelNames = new HashMap<>(300);
    private Map<String, String> daoMethodName2Sql = new HashMap<>(300);

    private Map<Class/** class**/, Map<String/**methodName**/, Method>> methodCache = new HashMap<>();

    private String HTML = "<!DOCTYPE html>\n" +
            "<html lang=\"en\">\n" +
            "<head>\n" +
            "    <meta charset=\"UTF-8\">\n" +
            "    <style>\n" +
            "        table, th, td {\n" +
            "            border: 1px solid black;\n" +
            "        }\n" +
            "\n" +
            "        th, td {\n" +
            "            padding: 10px;\n" +
            "        }\n" +
            "    </style>\n" +
            "</head>\n" +
            "<body>\n" +
            "\n" +
            "<br>\n" +
            "数据源：%s\n" +
            "<br>\n" +
            "<br>\n" +
            "<br>\n" +
            "%s\n" +
            "</body>\n" +
            "</html>";

    public Response<List<MethodCallTree>> get(String uri) {
        List<MethodCallTree> methodCallTrees = new ArrayList<>(100);
        Response<MethodCallChain> methodCallChain = methodCallChainFeignClient.findMethodCallChain(applicationName, uri);
        if (methodCallChain.notOk() || Objects.isNull(methodCallChain.getData())) {
            log.error("findMethodCallChain error {}", methodCallChain);
            return NewResponseUtil.makeFail(methodCallChain.getCode(), methodCallChain.getMsg(), null);
        } else {
            MethodCallChain data = methodCallChain.getData();
            if (Objects.nonNull(data) && Objects.nonNull(data.getMethodCallTree())) {
                data.setDataSources(new HashSet<>());
                try {
                    fillInfo(data.getMethodCallTree(), data.getDataSources());
                } catch (Throwable e) {
                    log.error("findMethodCallChain", e);
                }
                updateDescription(data.getMethodCallTree());
                getAllMethodCallTree(data.getMethodCallTree(), methodCallTrees);
            }
        }

        methodCallTrees = methodCallTrees.stream().filter(t -> StringUtils.isNotBlank(t.getType())).collect(Collectors.toList());
        return NewResponseUtil.makeSuccess(methodCallTrees);
    }

    private void getAllMethodCallTree(MethodCallTree methodCallTree, List<MethodCallTree> methodCallTrees) {
        if (Objects.isNull(methodCallTree)) {
            return;
        }
        methodCallTrees.add(methodCallTree);

        if (Objects.isNull(methodCallTree.getChildren())) {
            return;
        }

        for (MethodCallTree child : methodCallTree.getChildren()) {
            getAllMethodCallTree(child, methodCallTrees);
        }
    }

    public String getByUri(String uri) {
        String result = HTML;
        Response<MethodCallChain> methodCallChain = methodCallChainFeignClient.findMethodCallChain(applicationName, uri);
        if (methodCallChain.notOk() || Objects.isNull(methodCallChain.getData())) {
            log.error("findMethodCallChain error {}", methodCallChain);
        } else {
            MethodCallChain data = methodCallChain.getData();
            if (Objects.nonNull(data) && Objects.nonNull(data.getMethodCallTree())) {
                data.setDataSources(new HashSet<>());
                try {
                    fillInfo(data.getMethodCallTree(), data.getDataSources());
                } catch (Throwable e) {
                    log.error("findMethodCallChain", e);
                }

//                initTree(data.getMethodCallTree());

                StringBuilder table = new StringBuilder(255);
                table.append("<table style=\"width:100%\">").append("\r\n");
                table.append("<tr>").append("\r\n");
                table.append(" <th>方法</th>").append("\r\n");
                table.append(" <th>类型</th>").append("\r\n");
                table.append(" <th>故障名</th>").append("\r\n");
                table.append(" <th> 备注</th>").append("\r\n");
                table.append(" <th>表</th>").append("\r\n");
                table.append(" <th> sql</th>").append("\r\n");
                table.append("</tr>").append("\r\n");


                updateDescription(data.getMethodCallTree(), table);
                String dataSources = data.getDataSources().stream().collect(Collectors.joining(","));
                result = String.format(HTML, dataSources, table.toString());
            }
        }


        return result;
    }

    private void fillInfo(MethodCallTree methodCallTree, Set<String> dataSources) {
        if (Objects.isNull(methodCallTree)) {
            return;
        }

        try {
            //com.shuidihuzhu.cf.controller.api.v4.CrowdfundingV4InfoGetController.getFundingInfoV2(String, String)
            String methodSignature = methodCallTree.getMethodSignature();

            int index = methodSignature.indexOf("(");
            String className = methodSignature;
            if (index > 0) {
                className = methodSignature.substring(0, index);
            }
            className = className.substring(0, className.lastIndexOf("."));
            Class<?> classZ = Class.forName(className);


            //dao
            String daoMethodName = methodSignature.substring(0, methodSignature.indexOf("("));
            String dataSourceName = daoMethodName2dataSourceName.get(daoMethodName);
            if (daoMethodName2dataSourceName.containsKey(daoMethodName)) {
                dataSources.add(dataSourceName);
                methodCallTree.setType(DAO);
                methodCallTree.setDaoOperateType(daoMethodName2operateType.get(daoMethodName));
                String tableName = "";
                String sql = "";

                Set<String> tableNames = daoMethodName2TabelNames.get(daoMethodName);
                if (Objects.nonNull(tableNames) && !tableNames.isEmpty()) {
                    tableName = tableNames.stream().collect(Collectors.joining(","));
                }

                sql = daoMethodName2Sql.get(daoMethodName);
                if (Objects.isNull(sql)) {
                    sql = "";
                }

                methodCallTree.setTabelNames(tableName);
                methodCallTree.setSql(sql);
            }

            //feign
            FeignClient feignClient = classZ.getAnnotation(FeignClient.class);
            if (Objects.nonNull(feignClient)) {
                methodCallTree.setType(FEIGN);
                String simpleName = className.substring(className.lastIndexOf(".") + 1);

                //getFinanceCapitalStatusV2(java.lang.String, int)
                String methoName = methodSignature.replace(className + ".", "");
                String params = methoName.substring(methoName.indexOf("(") + 1, methoName.lastIndexOf(")"));
                methoName = methoName.substring(0, methoName.indexOf("("));

                List<String> paramList = Splitter.on(",").trimResults().omitEmptyStrings().splitToList(params);
                String paramTemp = paramList.stream().map(t -> {
                    if (t.contains(".")) {
                        t = t.substring(t.lastIndexOf("."));
                    }
                    return t;
                }).collect(Collectors.joining(","));
                String hystrixTimeoutKey = String.format(HYSTRIX_TIMEOUT_KEY_FORMAT, simpleName + "#" + methoName + "(" + paramTemp + ")");
                String value = appContext.getEnvironment().getProperty(hystrixTimeoutKey);

                if (Objects.isNull(value)) {
                    value = appContext.getEnvironment().getProperty(HYSTRIX_TIMEOUT_KEY);
                }

                methodCallTree.setFeignHystrixTimeoutInMilliseconds(value);

                String path = getPathPrefix(feignClient);
                Method method = findByMethodName(classZ, methoName);
                if (Objects.nonNull(method)) {
                    path = path + extractUri(method);
                }

                methodCallTree.setFeignUri(path);
            }


            if (classZ.isAssignableFrom(Producer.class)) {
                methodCallTree.setType("rocketmq-Producer");
            }

            if (classZ.isAssignableFrom(org.apache.kafka.clients.producer.Producer.class)) {
                methodCallTree.setType("kafka-Producer");
            }

            if (classZ.isAssignableFrom(RedissonHandler.class)) {
                methodCallTree.setType("redis");
            }
        } catch (Throwable e) {

        }

        if (Objects.isNull(methodCallTree.getChildren())) {
            return;
        }

        for (MethodCallTree child : methodCallTree.getChildren()) {
            fillInfo(child, dataSources);
        }
    }

    @EventListener
    public void init(ApplicationReadyEvent event) {
        new Thread(this::init).start();
    }


    private void init() {
        initDaoConfig();
        processMappedStatement();
    }


    private void initDaoConfig() {
        Collection<Class<?>> mappers = sqlSessionTemplate.getConfiguration().getMapperRegistry().getMappers();
        for (Class<?> classZ : mappers) {
            String classLevelDataSourceName = "";

            DataSource annotation = classZ.getAnnotation(DataSource.class);
            if (Objects.nonNull(annotation)) {
                classLevelDataSourceName = annotation.value();
            }

            Method[] methods = ReflectionUtils.getAllDeclaredMethods(classZ);
            for (Method method : methods) {
                String methodLevelDataSourceName = classLevelDataSourceName;
                DataSource dataSource = method.getAnnotation(DataSource.class);
                if (Objects.nonNull(dataSource)) {
                    methodLevelDataSourceName = dataSource.value();
                }
                daoMethodName2dataSourceName.put(classZ.getName() + "." + method.getName(), methodLevelDataSourceName);

                Operate operate = method.getAnnotation(Operate.class);
                if (Objects.nonNull(operate)) {
                    daoMethodName2operateType.put(classZ.getName(), operate.value());
                }

            }
        }
    }

    private String getClientName(FeignClient client) {
        if (client == null) {
            return null;
        }
        String value = client.contextId();
        if (!StringUtils.isNotEmpty(value)) {
            value = client.value();
        }
        if (!StringUtils.isNotEmpty(value)) {
            value = client.name();
        }
        if (StringUtils.isNotEmpty(value)) {
            return value;
        }
        return null;
    }


    private String getPathPrefix(FeignClient client) {
        String path = client.path();
        if (path.endsWith("/")) {
            path = path.substring(0, path.lastIndexOf("/"));
        }

        return path;
    }

    private void initTree(MethodCallTree methodCallTree) {
        if (Objects.isNull(methodCallTree)) {
            return;
        }

        if (Objects.isNull(methodCallTree.getChildren()) || methodCallTree.getChildren().isEmpty()) {
            return;
        }

        for (MethodCallTree child : methodCallTree.getChildren()) {
            child.setParent(methodCallTree);
            initTree(child);
        }
    }


    private void updateDescription(MethodCallTree methodCallTree, StringBuilder table) {

        if (Objects.isNull(methodCallTree)) {
            return;
        }

        if (StringUtils.isNotBlank(methodCallTree.getType())) {
            //com.shuidihuzhu.cf.dao.crowdfunding.material.credit.ICrowdfundingAuthorPropertyDAO.selectByCaseId(int)
            String methodSignature = methodCallTree.getMethodSignature();

            //com.shuidihuzhu.cf.dao.crowdfunding.material.credit.ICrowdfundingAuthorPropertyDAO.selectByCaseId
            String name = methodSignature.substring(0, methodSignature.indexOf("("));
            int methodStart = name.substring(0, name.lastIndexOf(".")).lastIndexOf(".");
            name = name.substring(methodStart + 1);

            String faultName = "";
            String remark = "";
            String tableName = "";
            String sql = "";

            if (DAO.equalsIgnoreCase(methodCallTree.getType())) {
                faultName = name;
                remark = "DaoOperateType:" + methodCallTree.getDaoOperateType();
                tableName = methodCallTree.getTabelNames();
                sql = methodCallTree.getSql();
            }


            if (FEIGN.equalsIgnoreCase(methodCallTree.getType())) {
                faultName = methodCallTree.getFeignUri();
                remark = "FeignHystrixTimeoutInMilliseconds: " + methodCallTree.getFeignHystrixTimeoutInMilliseconds();
            }


            table.append("<tr>").append("\r\n");
            table.append("<td> ").append(name).append("</td>").append("\r\n");
            table.append("<td> ").append(methodCallTree.getType()).append("</td>").append("\r\n");
            table.append("<td> ").append(faultName).append("</td>").append("\r\n");
            table.append("<td> ").append(remark).append("</td>").append("\r\n");
            table.append("<td> ").append(tableName).append("</td>").append("\r\n");
            table.append("<td> ").append(sql).append("</td>").append("\r\n");
            table.append(" </tr>").append("\r\n");

        }

        List<MethodCallTree> children = methodCallTree.getChildren();
        if (Objects.nonNull(children)) {
            for (MethodCallTree child : children) {
                updateDescription(child, table);
            }
        }

    }

    private void updateDescription(MethodCallTree methodCallTree) {

        if (Objects.isNull(methodCallTree)) {
            return;
        }

        if (StringUtils.isNotBlank(methodCallTree.getType())) {
            //com.shuidihuzhu.cf.dao.crowdfunding.material.credit.ICrowdfundingAuthorPropertyDAO.selectByCaseId(int)
            String methodSignature = methodCallTree.getMethodSignature();

            //com.shuidihuzhu.cf.dao.crowdfunding.material.credit.ICrowdfundingAuthorPropertyDAO.selectByCaseId
            String name = methodSignature.substring(0, methodSignature.indexOf("("));
            int methodStart = name.substring(0, name.lastIndexOf(".")).lastIndexOf(".");
            name = name.substring(methodStart + 1);

            String faultName = "";
            String remark = "";

            if (DAO.equalsIgnoreCase(methodCallTree.getType())) {
                faultName = name;
                remark = "DaoOperateType:" + methodCallTree.getDaoOperateType();
            }

            if (FEIGN.equalsIgnoreCase(methodCallTree.getType())) {
                faultName = methodCallTree.getFeignUri();
                remark = "FeignHystrixTimeoutInMilliseconds: " + methodCallTree.getFeignHystrixTimeoutInMilliseconds();
            }

            methodCallTree.setFaultName(faultName);
            methodCallTree.setRemark(remark);
        }

        List<MethodCallTree> children = methodCallTree.getChildren();
        if (Objects.nonNull(children)) {
            for (MethodCallTree child : children) {
                updateDescription(child);
            }
        }

    }

    private void getAllLeafNode(MethodCallTree methodCallTree, Set<MethodCallTree> leafNodes) {
        if (Objects.isNull(methodCallTree.getChildren()) || methodCallTree.getChildren().isEmpty()) {
            leafNodes.add(methodCallTree);
        }

        for (MethodCallTree child : methodCallTree.getChildren()) {
            getAllLeafNode(child, leafNodes);
        }

    }


    private String extractUri(Method method) {
        StringBuilder sb = new StringBuilder();
        RequestMapping requestMapping = method.getDeclaringClass().getAnnotation(RequestMapping.class);
        if (Objects.nonNull(requestMapping)) {
            if (requestMapping.path().length >= 1) {
                sb.append(requestMapping.path()[0]);
            }
            if (requestMapping.value().length >= 1) {
                sb.append(requestMapping.value()[0]);
            }
        }

        boolean exitSeparator = false;
        if (sb.length() > 0) {
            if (sb.toString().endsWith("/")) {
                exitSeparator = true;
            }
        }

        requestMapping = method.getAnnotation(RequestMapping.class);
        if (Objects.nonNull(requestMapping)) {
            String uri = "";
            if (requestMapping.path().length >= 1) {
                uri = requestMapping.path()[0];
            }

            if (requestMapping.value().length >= 1) {
                uri = requestMapping.value()[0];
            }

            appenUri(sb, exitSeparator, uri);
            return sb.toString();
        }

        PostMapping annotation = method.getAnnotation(PostMapping.class);
        if (Objects.nonNull(annotation)) {
            String uri = "";
            if (annotation.path().length >= 1) {
                uri = annotation.path()[0];
            }

            if (annotation.value().length >= 1) {
                uri = annotation.value()[0];
            }

            appenUri(sb, exitSeparator, uri);
            return sb.toString();
        }

        GetMapping getMapping = method.getAnnotation(GetMapping.class);
        if (Objects.nonNull(getMapping)) {
            String uri = "";
            if (getMapping.path().length >= 1) {
                uri = getMapping.path()[0];
            }

            if (getMapping.value().length >= 1) {
                uri = getMapping.value()[0];
            }
            appenUri(sb, exitSeparator, uri);
            return sb.toString();
        }

        return null;
    }

    private void appenUri(StringBuilder sb, boolean exitSeparator, String uri) {
        if (exitSeparator && uri.startsWith("/")) {
            uri = uri.substring(1);
        }

        if (!exitSeparator && !uri.startsWith("/")) {
            sb.append("/");
        }

        sb.append(uri);
    }


    /**
     * 参数判断情况多种，不考虑了，方法名相同的不处理了，麻烦
     *
     * @param classZ
     * @param methodName
     * @return
     */
    private Method findByMethodName(Class classZ, String methodName) {
        Map<String, Method> methodName2Method = methodCache.get(classZ);
        if (Objects.isNull(methodName2Method)) {
            Method[] methods = ReflectionUtils.getAllDeclaredMethods(classZ);
            methodName2Method = Maps.newHashMapWithExpectedSize(methods.length);

            Map<String, List<Method>> methodNameGroup = Stream.of(methods).collect(Collectors.groupingBy(Method::getName));
            for (Map.Entry<String, List<Method>> entry : methodNameGroup.entrySet()) {
                if (entry.getValue().size() == 1) {
                    methodName2Method.put(entry.getKey(), entry.getValue().get(0));
                }
            }

            methodCache.put(classZ, methodName2Method);
        }

        return methodName2Method.get(methodName);
    }


    private void processMappedStatement() {
        Iterator<MappedStatement> iterator = sqlSessionTemplate.getConfiguration().getMappedStatements().iterator();
        while (iterator.hasNext()) {
            MappedStatement statement = null;
            try {
                statement = iterator.next();
                processMappedStatement(statement);
            } catch (Throwable e) {
                String message = "";
                try {
                    message = ExceptionUtils.getMessage(e);
                } catch (IOException ex) {
                    log.warn("error", ex);
                }

                boolean contains = message.contains("java.lang.ClassCastException: org.apache.ibatis.session.Configuration$StrictMap$Ambiguity cannot be cast to org.apache.ibatis.mapping.MappedStatement");
                if (!contains) {
                    String id = null;
                    if (Objects.nonNull(statement)) {
                        id = statement.getId();
                    }
                    log.warn("error statement.Id{}", id, e);
                }
            }
        }
    }


    private void processMappedStatement(MappedStatement statement) throws
            DocumentException, IOException, ReflectiveOperationException {
        //statement.getId() = com.shuidihuzhu.cf.dao.crowdfunding.CfFirstApproveIdCardWhiteListDao.add
        if (statement.getId().contains("!selectKey")) {
            return;
        }

        String methodName = MappedStatementUtils.getMethodName(statement);
        String daoClassName = MappedStatementUtils.getDaoClassName(statement);
        SqlCommandType sqlCommandType = statement.getSqlCommandType();

        String fileName = MappedStatementUtils.getFileName(statement);
        if (org.apache.commons.lang3.StringUtils.isBlank(fileName)) {
            log.warn("不支持 {}", statement.getId());
            return;
        }
        Document document = MappedStatementUtils.of(fileName);

        String sql = MappedStatementUtils.getSql(methodName, sqlCommandType, document);


        List<Node> sqlNode = document.selectNodes("//sql");

        for (Node node : sqlNode) {
            DefaultElement element = (DefaultElement) node;
            element.getName();
            String id = element.attribute("id").getValue();
            String value = element.getText();
            value = value.replaceAll("\\$\\{.*\\}", "");
            sql = sql.replaceAll(String.format("<include.*%s.*/>", id), value);
        }

        sql = sql.substring(sql.indexOf(">") + 1);
        sql = sql.substring(0, sql.lastIndexOf("<")).trim();

        log.info("========= {}", sql);

        daoMethodName2Sql.put(daoClassName + "." + methodName, sql);

        Set<String> allTableNames = getAllTableName(methodName, sqlCommandType, sql, fileName);

        if (allTableNames.isEmpty()) {
            log.warn("allTableNames is empty");
        }

        daoMethodName2TabelNames.put(daoClassName + "." + methodName, allTableNames);

    }


    public static Set<String> getAllTableName(String methodName, SqlCommandType type, String sql, String fileName) {
        //title[@lang='eng']
        Set<String> tableNames = new HashSet<>(6);


        String soureSql = sql;


        if (type == SqlCommandType.UNKNOWN || type == SqlCommandType.FLUSH) {
            return tableNames;
        }

        if (type == SqlCommandType.UPDATE) {

            int index = org.apache.commons.lang3.StringUtils.indexOfAny(sql, "update ", "UPDATE ", "update\n", "UPDATE\n");
            if (index < 0) {
                log.warn("取不到表名: {}; {}", fileName, methodName);
                return tableNames;
            }

            sql = sql.substring(index + "update".length() + 1).trim();

        }

        if (type == SqlCommandType.DELETE) {

            int index = org.apache.commons.lang3.StringUtils.indexOfAny(sql, "from ", "FROM ", "from\n", "FROM\n");
            if (index < 0) {
                log.warn("取不到表名: {}; {}", fileName, methodName);
                return tableNames;
            }

            sql = sql.substring(index + "from".length() + 1).trim();
        }


        if (type == SqlCommandType.SELECT) {
            int index = org.apache.commons.lang3.StringUtils.indexOfAny(sql, "from ", "FROM ", "from\n", "FROM\n");
            if (index < 0) {
                log.warn("取不到表名: {}; {}", fileName, methodName);
                return tableNames;
            }
            sql = sql.substring(index + 5).trim();
            //子查询
            if (sql.startsWith("(")) {
                log.warn("子查询不支持先: {}; {}", fileName, methodName);
                return tableNames;
            }
        }

        if (type == SqlCommandType.INSERT) {
            int index = org.apache.commons.lang3.StringUtils.indexOfAny(sql, "into ", "INTO ", "into\n", "INTO\n");
            if (index < 0) {
                log.warn("取不到表名: {}; {}", fileName, methodName);
                return tableNames;
            }
            sql = sql.substring(index + 5).trim();
            sql = sql.substring(0, sql.indexOf("("));
        }

        sql = getRealTableName(sql);

        int index = sql.indexOf(".");
        if (index > 0) {
            //处理	<sql id="mainTable">`shuidi_dev`.`mobile_client`</sql>
            sql = sql.substring(sql.indexOf(".") + 1, sql.length());
        }

        if (sql.startsWith("`")) {
            sql = sql.substring(1, sql.length() - 1);
        }

        if (sql.contains("\n")) {
            sql = sql.substring(0, sql.indexOf("\n"));
        }

        sql = sql.trim();

        if (org.apache.commons.lang3.StringUtils.isBlank(sql)) {
            log.warn("取不到表名: {}; {}", fileName, methodName);
        }

        tableNames.add(sql);
        if (org.apache.commons.lang3.StringUtils.indexOfAny(soureSql, "join ", "JOIN ", "join\n", "JOIN\n") < 0) {

            return tableNames;
        }


        index = org.apache.commons.lang3.StringUtils.indexOfAny(soureSql, "from ", "FROM ", "from\n", "FROM\n");
        soureSql = soureSql.substring(index + "from".length() + 1);
        Set<String> tableFromJoinOrSubQuery = getTableFromJoinOrSubQuery(soureSql);
        if (tableFromJoinOrSubQuery.isEmpty()) {
            log.warn("关联查询{}; {}", fileName, methodName);
        }
        Set<String> set = tableFromJoinOrSubQuery.stream().map(t -> {
            if (t.startsWith("`")) {
                return t;
            }

            return '`' + t + '`';
        }).collect(Collectors.toSet());

        tableNames.addAll(set);
        return tableNames;
    }


    private static String getRealTableName(String sql) {
        int end = sql.indexOf(" ");
        if (end > 0) {
            sql = sql.substring(0, end).trim();
        }

        return sql;
    }

    private static Set<String> getTableFromJoinOrSubQuery(final String sql) {
        Set<String> tableNames = new HashSet<>(6);

        String from = sql;
        int index = org.apache.commons.lang3.StringUtils.indexOfAny(from, "from ", "FROM ", "from\n", "FROM\n");
        while (index >= 0) {
            from = from.substring(index + "from".length() + 1).trim();
            String realTableName = getRealTableName(from);
            tableNames.add(realTableName);

            from = from.substring(realTableName.length() + 1);
            index = org.apache.commons.lang3.StringUtils.indexOfAny(from, "from ", "FROM ", "from\n", "FROM\n");
        }

        String join = sql;
        index = org.apache.commons.lang3.StringUtils.indexOfAny(join, "join ", "JOIN ", "join\n", "join\n");
        while (index >= 0) {
            join = join.substring(index + "join".length() + 1).trim();
            String realTableName = getRealTableName(join);
            tableNames.add(realTableName);

            join = join.substring(realTableName.length() + 1);
            index = org.apache.commons.lang3.StringUtils.indexOfAny(join, "join ", "JOIN ", "join\n", "join\n");
        }

        return tableNames;
    }

}
