package com.shuidihuzhu.cf.service.crowdfunding;

import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.crowdfunding.CfInfoExtBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Service
public class CfInfoExtService {
    private static final Logger LOGGER = LoggerFactory.getLogger(CfInfoExtService.class);

    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private CfInfoExtBiz cfInfoExtBiz;

    public Map<String, String> getInfoUuidByProductName(List<String> productNameList) {
        if (CollectionUtils.isEmpty(productNameList)) {
            return Collections.emptyMap();
        }
        Map<String, CfInfoExt> cfInfoExtMap = this.cfInfoExtBiz.getMapByProductNames(productNameList);
        Map<String, String> map = Maps.newHashMap();
        for (String productName : productNameList) {
            try {
                String infoUuid = "";
                CfInfoExt cfInfoExt = cfInfoExtMap.get(productName);
                if (cfInfoExt == null) {
                    List<String> stringList = Splitter.on("_").splitToList(productName);
                    if (stringList.size() == 3) {
                        List<CrowdfundingInfo> crowdfundingInfos = this.crowdfundingInfoBiz.getByInfoUuidPrefix
                                (stringList.get(1));
                        if (!CollectionUtils.isEmpty(crowdfundingInfos)) {
                            if (crowdfundingInfos.size() == 1) {
                                infoUuid = crowdfundingInfos.get(0).getInfoId();
                            } else {
                                for (CrowdfundingInfo crowdfundingInfo : crowdfundingInfos) {
                                    if (crowdfundingInfo.getCreateTime().getTime() == Long.parseLong(stringList.get(2))) {
                                        infoUuid = crowdfundingInfo.getInfoId();
                                    }
                                }
                            }
                        }
                    } else if (stringList.size() == 2) {
                        CrowdfundingInfo crowdfundingInfo = this.crowdfundingInfoBiz.getFundingInfoById(
                                Integer.parseInt(stringList.get(1)));
                        if (crowdfundingInfo != null) {
                            infoUuid = crowdfundingInfo.getInfoId();
                        }
                    }
                } else {
                    infoUuid = cfInfoExt.getInfoUuid();
                }
                if (StringUtils.isNotBlank(infoUuid)) {
                    map.put(productName, infoUuid);
                }
            } catch (Exception e) {
                LOGGER.error("", e);
            }

        }
        return map;
    }

}
