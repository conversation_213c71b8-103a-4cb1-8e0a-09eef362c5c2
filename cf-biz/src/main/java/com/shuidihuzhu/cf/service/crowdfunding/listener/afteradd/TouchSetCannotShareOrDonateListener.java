package com.shuidihuzhu.cf.service.crowdfunding.listener.afteradd;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.service.crowdfunding.listener.base.AbstractCrowdFundingAddEventListener;
import com.shuidihuzhu.client.cf.risk.model.enums.UserOperationEnum;
import org.apache.commons.lang3.StringUtils;
import com.shuidihuzhu.cf.biz.crowdfunding.CfCaseVisitConfigBiz;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoBaseVo;
import com.shuidihuzhu.cf.model.timeline.TimeLineModel;
import com.shuidihuzhu.cf.service.crowdfunding.event.AddListenerOrder;
import com.shuidihuzhu.cf.service.crowdfunding.event.CrowdFundingAddEvent;
import com.shuidihuzhu.cf.service.risk.limit.ICfRiskService;
import com.shuidihuzhu.client.cf.risk.model.enums.RiskOperateSourceEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.event.SmartApplicationListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 禁止案例转发
 */
@Slf4j
@Service
public class TouchSetCannotShareOrDonateListener extends AbstractCrowdFundingAddEventListener implements SmartApplicationListener {

    @Autowired
    private CfCaseVisitConfigBiz caseVisitConfigBize;


    @Autowired
    private ICfRiskService cfRiskService;

    /**
     * 设置案例不能转发与捐款
     *
     * @param caseId
     */
    public void touchSetCannotShareOrDonate(int caseId) {
        log.info("touchSetCannotShareOrDonate:{}", caseId);
        caseVisitConfigBize.saveShareAndDonateStatus(caseId, false, false);

        TimeLineModel.create(caseId).description("touchSetCannotShareOrDonate").build();

    }

    @Override
    public void onApplicationEvent(ApplicationEvent _event) {

        CrowdFundingAddEvent crowdFundingAddEvent = (CrowdFundingAddEvent) _event;
        CrowdfundingInfoBaseVo infoBaseVo = crowdFundingAddEvent.getCrowdfundingInfoBaseVo();
        CrowdfundingInfo cfCase = crowdFundingAddEvent.getCrowdfundingInfo();
        if (infoBaseVo != null && cfCase != null) {
            try {
                if (!StringUtils.isEmpty(infoBaseVo.getSelfRealName()) ||
                        !StringUtils.isEmpty(infoBaseVo.getPatientRealName())) {

                    //TODO ###请勿改动、待删除####
                    this.touchSetCannotShareOrDonate(cfCase.getId());
                    cfRiskService.addOperateLimit(cfCase.getId(), false, false, RiskOperateSourceEnum.FIRST_APPROVE.getCode(), null, null);
                    //TODO ###请勿改动、待删除####

                    cfRiskService.writeRiskOperate(cfCase.getId(), Lists.newArrayList(UserOperationEnum.SHARE, UserOperationEnum.ORDER), false, RiskOperateSourceEnum.FIRST_APPROVE,0,"发起案例");
                }
            } catch (Exception e) {
                log.error("touchSetCannotShareOrDonate error:", e);
            }
        }
    }

    @Override
    public int getOrder() {
        return AddListenerOrder.TouchSetCannotShareOrDonate.getValue();
    }
}
