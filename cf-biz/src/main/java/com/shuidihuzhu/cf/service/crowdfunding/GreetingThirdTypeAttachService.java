package com.shuidihuzhu.cf.service.crowdfunding;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.delegate.service.WxGreetingServiceBiz;
import com.shuidihuzhu.common.web.util.cache.ICache;
import com.shuidihuzhu.wx.grpc.client.GetGreetingResponse;
import com.shuidihuzhu.wx.grpc.client.common.GreetingMatchRule;
import com.shuidihuzhu.wx.grpc.client.common.GreetingThirdTypeAttach;
import com.shuidihuzhu.wx.grpc.enums.GreetingMatchRuleEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: WangYing on 2018/7/4
 */
@Service
public class GreetingThirdTypeAttachService extends AbstractCfCache<String, List<GreetingThirdTypeAttach> >
        implements ICache<String, List<GreetingThirdTypeAttach> > {

    @Resource
    private WxGreetingServiceBiz wxGreetingServiceBiz;

    @Override
    protected List<GreetingThirdTypeAttach> queryData(String key) {
        List<GreetingThirdTypeAttach> greetingThirdTypeAttaches = Lists.newArrayList();
        if (StringUtils.isBlank(key)) {
            return greetingThirdTypeAttaches;
        }
        List<String> items = Splitter.on("#").splitToList(key);
        int thirdType = Integer.parseInt(items.get(0));
        String eventKey = items.get(1);
        GreetingMatchRule greetingMatchRule = GreetingMatchRule.forNumber(Integer.parseInt(items.get(2)));
        List<com.shuidihuzhu.wx.grpc.model.GreetingThirdTypeAttach> greetings = wxGreetingServiceBiz.getGreeting(thirdType, eventKey, greetingMatchRule);
        if (CollectionUtils.isEmpty(greetings)) {
            return greetingThirdTypeAttaches;
        }
        return greetings.stream().map(attach -> {
            return GreetingThirdTypeAttach.newBuilder()
                            .setGreetingId(attach.getGreetingId())
                            .setContent(attach.getContent())
                            .setDate(attach.getDate())
                            .setSubBizType(attach.getSubBizType())
                            .setThirdType(attach.getThirdType())
                            .setKey(attach.getKey())
                            .setMatchRule(GreetingMatchRule.forNumber(attach.getMatchRule().getValue()))
                            .setTitle(attach.getTitle())
                            .setWxGroupAttachId(attach.getWxGroupAttachId())
                            .build();
        }).collect(Collectors.toList());
    }

    @Override
    public List<GreetingThirdTypeAttach> get(String key) {
        if(StringUtils.isEmpty(key)) {
            return Lists.newArrayList();
        }

        try {
            return getValue(key);
        } catch (Exception e) {
            return Lists.newArrayList();
        }
    }

    public List<GreetingThirdTypeAttach> get(int thirdType, String eventKey, GreetingMatchRule greetingMatchRule) {
        String cacheKey = thirdType + "#" + eventKey + "#" + greetingMatchRule.getNumber();
        if(thirdType <= 0 || StringUtils.isEmpty(eventKey)) {
            return Lists.newArrayList();
        }
        try {
            return getValue(cacheKey);
        } catch (Exception e) {
            return Lists.newArrayList();
        }
    }

}
