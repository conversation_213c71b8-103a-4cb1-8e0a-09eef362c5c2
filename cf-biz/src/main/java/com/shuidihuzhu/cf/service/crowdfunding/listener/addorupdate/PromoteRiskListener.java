package com.shuidihuzhu.cf.service.crowdfunding.listener.addorupdate;

import com.shuidihuzhu.cf.domain.CaseRaiseRiskDO;
import com.shuidihuzhu.cf.facade.raiserisk.CaseRaiseRiskFacade;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.crowdfunding.event.*;
import com.shuidihuzhu.cf.service.crowdfunding.listener.base.AbstractCrowdFundingAddOrUpdateEventListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.event.SmartApplicationListener;
import org.springframework.stereotype.Service;

/**
 * AI 风险
 */
@Slf4j
@Service
public class PromoteRiskListener extends AbstractCrowdFundingAddOrUpdateEventListener implements SmartApplicationListener {

    @Autowired
    private CaseRaiseRiskFacade caseRaiseRiskFacade;

    @Override
    public void onApplicationEvent(ApplicationEvent _event) {


        String event = "";
        CrowdfundingInfo cfCase;
        if (_event.getSource() instanceof CrowdFundingAddPublisher) {
            event = "CrowdFundingAddEvent";
            CrowdFundingAddEvent crowdFundingAddEvent = (CrowdFundingAddEvent) _event;
            cfCase = crowdFundingAddEvent.getCrowdfundingInfo();
        } else {
            event = "CrowdFundingUpdateEvent";
            CrowdFundingUpdateEvent crowdFundingUpdateEvent = (CrowdFundingUpdateEvent) _event;
            cfCase = crowdFundingUpdateEvent.getCrowdfundingInfo();
        }
        if (cfCase != null) {
            try {
                caseRaiseRiskFacade.promoteRisk(cfCase.getInfoId());
            } catch (Exception e) {
                log.error("PromoteRiskListener {} error:", event, e);
            }
        }


    }


    @Override
    public int getOrder() {
        return AddListenerOrder.PromoteRisk.getValue();
    }

}

