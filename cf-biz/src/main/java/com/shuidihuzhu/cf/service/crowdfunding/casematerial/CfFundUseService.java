package com.shuidihuzhu.cf.service.crowdfunding.casematerial;


import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoSimpleBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoStatusBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingRefuseReasonItemBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.material.IMaterialCenterService;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.material.model.CfRaiseFundUseModel;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoDataStatusTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSimpleModel;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoStatus;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingRefuseReasonItem;
import com.shuidihuzhu.cf.model.crowdfunding.casematerial.CfFundUseAuditInfo;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.InitialAuditItem;
import com.shuidihuzhu.cf.vo.v5.CfMaterialAuditListView;
import com.shuidihuzhu.cf.vo.v5.CfTreatmentMaterialView;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
@Service
public class CfFundUseService {

    @Autowired
    private IMaterialCenterService materialCenterService;
    @Autowired
    private CrowdfundingInfoSimpleBiz infoSimpleBiz;
    @Autowired
    private CrowdfundingInfoStatusBiz infoStatusBiz;
    @Autowired
    private CfMaterialStatusService materialStatusService;
    @Autowired
    private CrowdfundingRefuseReasonItemBiz reasonItemBiz;
    @Autowired
    private CfCaseMaterialListService cfCaseMaterialListService;

    public final static Map<String, CfFundUseAuditInfo.MaterialRejectPosition> EDIT_MATERIAL_MAP = Maps.newHashMap();

    static {
        EDIT_MATERIAL_MAP.put("治疗花费情况", CfFundUseAuditInfo.MaterialRejectPosition.ZHI_LIAO_HUA_FEI);
        EDIT_MATERIAL_MAP.put("政府医疗救助", CfFundUseAuditInfo.MaterialRejectPosition.ZHENG_FU_YI_LIAO_JIU_ZHU);
        EDIT_MATERIAL_MAP.put("款项预期用途", CfFundUseAuditInfo.MaterialRejectPosition.CHOU_KUAN_WEI_LAI_YONG_TU);
    }

    public CfErrorCode addOrUpdateFundUse(String infoUuid, long userId, CfRaiseFundUseModel useModel) {

        if (StringUtils.isBlank(infoUuid) || useModel == null) {
            return CfErrorCode.SYSTEM_PARAM_ERROR;
        }

        CfInfoSimpleModel info = infoSimpleBiz.getFundingInfo(infoUuid);
        if (info == null || info.getUserId() != userId) {
            return CfErrorCode.USER_NOT_THIS_CASE_OWNER_OR_NULL;
        }

        CrowdfundingInfoStatus materialStatus =
                infoStatusBiz.getByInfoUuidAndType(infoUuid, CrowdfundingInfoDataStatusTypeEnum.FUND_USE_SUBMIT.getCode());

        if (materialStatus != null && (materialStatus.getStatus() == CrowdfundingInfoStatusEnum.PASSED.getCode()
            || materialStatus.getStatus() == CrowdfundingInfoStatusEnum.SUBMITTED.getCode())) {

            log.info("案例的资金用途材料不可修改.caseId:{}", info.getId());
            return CfErrorCode.FUND_USE_MATERIAL_CAN_NOT_SUBMIT;
        }

        RpcResult<String> result = materialCenterService.addOrUpdateFundUse(info.getId(), useModel);
        if (result == null || !result.isSuccess()) {
            return CfErrorCode.FUND_USE_MATERIAL_FAIL_SUBMIT;
        }

        if (materialStatus == null) {
            CrowdfundingInfoStatus crowdfundingInfoStatus = new CrowdfundingInfoStatus();
            crowdfundingInfoStatus.setInfoUuid(infoUuid);
            crowdfundingInfoStatus.setType(CrowdfundingInfoDataStatusTypeEnum.FUND_USE_SUBMIT.getCode());
            crowdfundingInfoStatus.setStatus(CrowdfundingInfoStatusEnum.UN_SUBMITTED.getCode());
            crowdfundingInfoStatus.setCaseId(info.getId());

            this.infoStatusBiz.add(crowdfundingInfoStatus);
        } else {
            infoStatusBiz.updateByInfoId(infoUuid,
                    CrowdfundingInfoDataStatusTypeEnum.FUND_USE_SUBMIT.getCode(),
                    CrowdfundingInfoStatusEnum.UN_SUBMITTED);
        }

        return CfErrorCode.SUCCESS;
    }


    public CfFundUseAuditInfo selectFundUse(String infoUuid) {

        CfFundUseAuditInfo auditInfo = new CfFundUseAuditInfo();
        CfInfoSimpleModel info = infoSimpleBiz.getFundingInfo(infoUuid);
        if (info == null ) {
            return null;
        }

        // 需要查找状态
        CrowdfundingInfoStatus infoStatus = infoStatusBiz.getByInfoUuidAndType(infoUuid, CrowdfundingInfoDataStatusTypeEnum.FUND_USE_SUBMIT.getCode());
        if (infoStatus == null) {
            auditInfo.setAuditStatus(CrowdfundingInfoStatusEnum.UN_SAVE);
            return auditInfo;
        }
        auditInfo.setFundUseModel(materialCenterService.selectFundUse(info.getId()));
        auditInfo.setAuditStatus(CrowdfundingInfoStatusEnum.getByCode(infoStatus.getStatus()));

        // 驳回理由
        if (infoStatus.getStatus() == CrowdfundingInfoStatusEnum.REJECTED.getCode()) {
            CfTreatmentMaterialView.TreatmentRejectDetail rejectDetail = materialStatusService.getRejectDetailsByType(infoUuid,
                    CrowdfundingInfoDataStatusTypeEnum.FUND_USE_SUBMIT);

            if (rejectDetail != null) {
                auditInfo.setRejectDetail(convertMsg(rejectDetail.getRejectDetailsV1()));
            }
        }
        Pair<Map<Integer, Set<String>>, List<CfMaterialAuditListView.ModifySuggest>> pair = cfCaseMaterialListService.getRejectDetails(infoUuid, CrowdfundingInfoDataStatusTypeEnum.FUND_USE_SUBMIT);
        if (pair != null){
            auditInfo.setRejects(pair.getLeft());
            auditInfo.setSuggests(pair.getRight());
        }
        return auditInfo;
    }


    private  Map<Integer, Set<String>> convertMsg(Map<Integer, Set<String>> rejectDetails) {

        if (MapUtils.isEmpty(rejectDetails)) {
            return Maps.newHashMap();
        }

        Map<Integer, CrowdfundingRefuseReasonItem> itemIdMap = reasonItemBiz.getMapByIds(new ArrayList<Integer>(rejectDetails.keySet()));
        Map<Integer, Set<String>> newRejectMsgs = Maps.newHashMap();
        for (Map.Entry<Integer, Set<String>> entry : rejectDetails.entrySet()) {
            CrowdfundingRefuseReasonItem reasonItem = itemIdMap.get(entry.getKey());
            if (reasonItem == null || EDIT_MATERIAL_MAP.get(reasonItem.getContent()) == null) {
                log.warn("不能找到驳回位置item id:{}", entry.getKey());
                continue;
            }

            newRejectMsgs.put( EDIT_MATERIAL_MAP.get(reasonItem.getContent()).getCode(),
                    entry.getValue());
        }

        return newRejectMsgs;
    }


}
