package com.shuidihuzhu.cf.service.crowdfunding.event;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019/12/16
 */

@Service
public class SaveDraftEventPublisher {

    @Autowired
    ApplicationContext applicationContext;

    public void publishSaveDraftEvent(long userId, String pageFlag, int cfVersion) {
        if (userId <= 0 || StringUtils.isBlank(pageFlag)) {
            return;
        }

        SaveDraftEvent event = new SaveDraftEvent(this, userId, pageFlag, cfVersion);
        applicationContext.publishEvent(event);
    }
}
