package com.shuidihuzhu.cf.service.crowdfunding;

import com.shuidihuzhu.cf.biz.crowdfunding.CfOperatingRecordBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingOperationBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enums.crowdfunding.CfOperatingRecordEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfOperatingRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOperation;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoBaseVo;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Slf4j
@Service
public class CfInfoMirrorService {

	@Autowired
	@Lazy
	private CfOperatingRecordBiz cfOperatingRecordBiz;
	@Autowired
	private CrowdfundingOperationBiz crowdfundingOperationBiz;

	@Autowired(required = false)
	private Producer producer;

	public void addBaseAfter(CrowdfundingInfoBaseVo crowdfundingInfoBaseVo, String infoUuid) {
		try {
			doAddBaseAfter(crowdfundingInfoBaseVo, infoUuid);
		} catch (Exception e) {
			log.error("addBaseAfter Error!", e);
		}
	}

	private void doAddBaseAfter(CrowdfundingInfoBaseVo crowdfundingInfoBaseVo, String infoUuid) {
		if (StringUtils.isBlank(infoUuid)) {
			return;
		}
		CfOperatingRecord cfOperatingRecord = this.cfOperatingRecordBiz.save(infoUuid,
		                                                                     crowdfundingInfoBaseVo.getUserId(), StringUtils.trimToEmpty(crowdfundingInfoBaseVo.getPayeeName()),
		                                                                           CfOperatingRecordEnum.Type.SUBMIT_BASE, CfOperatingRecordEnum.Role.USER);
		if(cfOperatingRecord.getId() > 0 && producer != null) {
			cfOperatingRecord.setReportLinkInfo(crowdfundingInfoBaseVo.getReportLinkInfo());
			MessageResult messageResult = producer.send(new Message(MQTopicCons.CF, MQTagCons.CF_OPERATION_MSG,MQTagCons.CF_OPERATION_MSG + "_" + cfOperatingRecord.getId(), cfOperatingRecord));
			log.info("doAddBaseAfter send {},{},{}", MQTagCons.CF_OPERATION_MSG, cfOperatingRecord, messageResult);
			messageResult = producer.send(new Message(MQTopicCons.CF, MQTagCons.CF_OPERATION_DELAY_10M_MSG,MQTagCons.CF_OPERATION_DELAY_10M_MSG+ "_" + cfOperatingRecord.getId(), cfOperatingRecord, DelayLevel.M10));
			log.info("doAddBaseAfter send {},{},{}", MQTagCons.CF_OPERATION_DELAY_10M_MSG, cfOperatingRecord, messageResult);
		}
	}

	public CfOperatingRecord before(CrowdfundingInfo crowdfundingInfo,long userId, String userName,
	                                CfOperatingRecordEnum.Type type, CfOperatingRecordEnum.Role role) {
		try {
			return doBefore(crowdfundingInfo, userId, userName, type, role, "");
		} catch (Exception e) {
			log.error("before Error!", e);
			return null;
		}
	}

	public CfOperatingRecord before(CrowdfundingInfo crowdfundingInfo,long userId, String userName,
			CfOperatingRecordEnum.Type type, CfOperatingRecordEnum.Role role, String comment) {
		try {
			return doBefore(crowdfundingInfo, userId, userName, type, role, comment);
		} catch (Exception e) {
			log.error("before Error!", e);
			return null;
		}
	}

	private CfOperatingRecord doBefore(CrowdfundingInfo crowdfundingInfo,long userId, String userName,
			CfOperatingRecordEnum.Type type, CfOperatingRecordEnum.Role role, String comment) {
		userName = StringUtils.trimToEmpty(userName);
		String infoUuid = crowdfundingInfo.getInfoId();
		CrowdfundingOperation crowdfundingOperation = null;
        if (Objects.equals(type, CfOperatingRecordEnum.Type.AUDIT_FAILED)) {
			//获取案例当前的审核次数
            crowdfundingOperation = crowdfundingOperationBiz.getByInfoId(crowdfundingInfo.getInfoId());
        }
		return this.cfOperatingRecordBiz.save(infoUuid, userId, userName, type, role,
				comment, crowdfundingOperation == null ? 0 : crowdfundingOperation.getRefuseCount());
	}

	public void after(CfOperatingRecord cfOperatingRecord) {
		try {
			doAfter(cfOperatingRecord);
		} catch (Exception e) {
			log.error("after error!", e);
		}
	}

	private void doAfter(CfOperatingRecord cfOperatingRecord) {
		if(cfOperatingRecord==null){
			return;
		}
		//更新资金操作记录并存储
		cfOperatingRecordBiz.updateCfOperatingResion(cfOperatingRecord);
	}


	/**
	 * 如果在事物中调用，就不能捕获异常了
	 * @param crowdfundingInfo
	 * @param userId
	 * @param userName
	 * @param type
	 * @param role
	 * @return
	 */
	public CfOperatingRecord beforeNew(CrowdfundingInfo crowdfundingInfo,long userId, String userName,
									CfOperatingRecordEnum.Type type, CfOperatingRecordEnum.Role role) {

		return doBefore(crowdfundingInfo, userId, userName, type, role, "");

	}

	/**
	 * 如果在事物中调用，就不能捕获异常了,或者或者捕获异常，记录日志，继续上抛
	 * @param cfOperatingRecord
	 */
	public void afterNew(CfOperatingRecord cfOperatingRecord) {

			doAfter(cfOperatingRecord);

	}

}
