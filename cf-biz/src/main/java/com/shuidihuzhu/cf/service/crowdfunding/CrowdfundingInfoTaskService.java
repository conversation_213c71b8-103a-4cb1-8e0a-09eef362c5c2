package com.shuidihuzhu.cf.service.crowdfunding;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.CfAdviserServiceBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfFirstApproveBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfInfoExtBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.delegate.finance.IFinanceDrawCashFeignDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.finance.model.vo.CfDrawCashApplyVo;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfInfoTaskVo;
import com.shuidihuzhu.cf.util.crowdfunding.CfUrlUtil;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.growthtool.client.CfGrowthtoolFeginClient;
import com.shuidihuzhu.client.cf.growthtool.model.CfAdRegisterDO;
import com.shuidihuzhu.client.cf.growthtool.model.CfTouFangSignDO;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by wangsf on 18/2/1.
 */
@Service
@RefreshScope
@Slf4j
public class CrowdfundingInfoTaskService {

	@Autowired
	private CrowdfundingInfoBiz crowdfundingInfoBiz;

	@Autowired
	private CfAdviserServiceBiz cfAdviserServiceBiz;

	@Autowired
	private UserInfoDelegate userInfoDelegate;

	@Autowired
	private CfInfoExtBiz cfInfoExtBiz;

	@Autowired
	private CfFirstApproveBiz firstApproveBiz;

	@Autowired
	private IFinanceDrawCashFeignDelegate financeDrawCashFeignDelegate;
	@Autowired
	private ShuidiCipher shuidiCipher;

	@Value("${baseinfo.task.guide.title:1对1免费筹款指导}")
	private String cfInfoTaskGuideTitle;
	@Value("${baseinfo.task.guide.description:筹款志愿者电话帮你发起筹款，教你如何快速筹钱。}")
	private String cfInfoTaskGuideDescription;
	@Value("${baseinfo.task.guide.btn-txt:立即登记}")
	private String cfInfoTaskGuideBtnTxt;
	@Value("${baseinfo.task.guide.url:https://www.shuidichou.com/cf/activity/raise-adviser}")
	private String cfInfoTaskGuideUrl;

	@Value("${baseinfo.task.raise.title:不会写筹款文章？}")
	private String cfInfoTaskRaiseTitle;
	@Value("${baseinfo.task.raise.description:2分钟填信息，立即开始筹款！}")
	private String cfInfoTaskRaiseDescription;
	@Value("${baseinfo.task.raise.btn-txt:立即筹款}")
	private String cfInfoTaskRaiseBtnTxt;

	@Value("${baseinfo.task.share-morning.title:筹款助手提醒你}")
	private String cfInfoTaskShareMorningTitle;
	@Value("${baseinfo.task.share-morning.description:据了解，爱心人士最爱在早上帮忙转发}")
	private String cfInfoTaskShareMorningDescription;
	@Value("${baseinfo.task.share-morning.btn-txt:立即转发}")
	private String cfInfoTaskShareMorningBtnTxt;

	@Value("${baseinfo.task.share-noon.title:筹款助手提醒你}")
	private String cfInfoTaskShareNoonTitle;
	@Value("${baseinfo.task.share-noon.description:一人之力远远不够，亲朋好友一起转发更有效}")
	private String cfInfoTaskShareNoonDescription;
	@Value("${baseinfo.task.share-noon.btn-txt:立即转发}")
	private String cfInfoTaskShareNoonBtnTxt;

	@Value("${baseinfo.task.share-night.title:筹款助手提醒你}")
	private String cfInfoTaskShareNightTitle;
	@Value("${baseinfo.task.share-night.description:别错过晚上的黄金时间，发动亲友一起转发}")
	private String cfInfoTaskShareNightDescription;
	@Value("${baseinfo.task.share-night.btn-txt:立即转发}")
	private String cfInfoTaskShareNightBtnTxt;

	@Value("${baseinfo.task.finish.title:你的筹款已结束}")
	private String cfInfoTaskFinishTitle;
	@Value("${baseinfo.task.finish.description:你的筹款已于##END_TIME##结束，共筹到##AMOUNT##元，请申请提现。}")
	private String cfInfoTaskFinishDescription;
	@Value("${baseinfo.task.finish.btn-txt:立即提现}")
	private String cfInfoTaskFinishBtnTxt;

	@Value("${baseinfo.task.first-approve-inprogress.title:筹款还不能转发？}")
	private String cfInfoTaskFirstApproveInProgressTitle;
	@Value("${baseinfo.task.first-approve-inprogress.description:医疗材料未公示，还得再耐心等一下。}")
	private String cfInfoTaskFirstApproveInProgressDescription;

	@Value("${baseinfo.task.first-approve-inprogress.morethanfifty.description:医疗材料和筹款金额说明未公示，还得再耐心等一下。}")
	private String cfInfoTaskFirstApproveInProgressMoreThanFiftyDescription;

	@Value("${baseinfo.task.first-approve-inprogress.btn-txt:等待公示}")
	private String cfInfoTaskFirstApproveInProgressBtnText;

	@Value("${baseinfo.task.first-approve-reject.title:审核材料不合格？}")
	private String cfInfoTaskFirstApproveRejectTitle;
	@Value("${baseinfo.task.first-approve-reject.place-holder.description:%s待修改 }")
	private String cfInfoTaskFirstApproveRejectPlaceDescription;

	@Value("${baseinfo.task.first-approve-reject.btn-txt:修改基本信息}")
	private String cfInfoTaskFirstApproveRejectBtnTxt;

	@Autowired
	CfGrowthtoolFeginClient cfGrowthtoolFeginClient;

	public CfInfoTaskVo getInfoTask(long userId) {
		if(userId <= 0) {
			return this.createRaiseTask();
		}

		List<CrowdfundingInfoView> crowdfundingInfos = this.crowdfundingInfoBiz.getByUserId(userId);

		if(CollectionUtils.isEmpty(crowdfundingInfos)) {
			//无在筹案例，且无未提现案例，需要查看是否已经登记
			return getCfInfoTaskVoWithNoInfo(userId);
		}

        //没有正在提现案例，判断一下是否有在筹案例
        Collections.sort(crowdfundingInfos, new CfInfoEndTimeComparator());

        //找到最近一个案例
        List<CrowdfundingInfoView> illnessCases = Lists.newLinkedList();
        for (CrowdfundingInfoView crowdfundingInfoView : crowdfundingInfos) {
            if (crowdfundingInfoView.getType() == CrowdfundingType.SERIOUS_ILLNESS.value()) {
                illnessCases.add(crowdfundingInfoView);
                break;
            }
        }

        //没有找到在筹的大病案例
        if (CollectionUtils.isEmpty(illnessCases)) {
            return this.getCfInfoTaskVoWithNoInfo(userId);
        }

        List<Integer> caseIdList = illnessCases.stream()
                .map(crowdfundingInfoView -> crowdfundingInfoView.getId())
                .distinct()
                .collect(Collectors.toList());

		Response<Map<Integer, CfDrawCashApplyVo>> response = financeDrawCashFeignDelegate.getApplyInfoMap(caseIdList);
		Map<Integer, CfDrawCashApplyVo> drawCashApplyVoMap = response.getData();

        //先判断是否有正在提现案例
        if (MapUtils.isNotEmpty(drawCashApplyVoMap)) {
            //判断是否有在提现进行中的案例(已提现未到账）(已提现未到账）(已提现未到账）(已提现未到账）
            for (CfDrawCashApplyVo drawCashApplyVo : drawCashApplyVoMap.values()) {

                //判断是否已提交申请（申请中、申请成功）
                if (drawCashApplyVo.getApplyStatus() == CfDrawCashConstant.ApplyStatus.SUBMIT_APPROVE_PENDING.getCode()
                        || drawCashApplyVo.getApplyStatus() == CfDrawCashConstant.ApplyStatus.APPROVE_SUCCESS.getCode()) {

                    //判断提现是否处于"未生成"、"未处理"、"正在处理"、"部分处理"、"失败" 状态
                    if (drawCashApplyVo.getDrawStatus() == CfDrawCashConstant.DrawStatus.UNBUILD.getCode() ||
                            drawCashApplyVo.getDrawStatus() == CfDrawCashConstant.DrawStatus.UNHANDLE.getCode() ||
                            drawCashApplyVo.getDrawStatus() == CfDrawCashConstant.DrawStatus.HANDLING.getCode() ||
                            drawCashApplyVo.getDrawStatus() == CfDrawCashConstant.DrawStatus.HANDLE_PARTIAL_SUCCESS.getCode() ||
                            drawCashApplyVo.getDrawStatus() == CfDrawCashConstant.DrawStatus.HANDLE_FAILED.getCode()) {

                        return createInfoTask(
                                CfInfoTaskEnum.RE_RAISE.getCode(),
                                this.cfInfoTaskRaiseTitle,
                                this.cfInfoTaskRaiseDescription,
                                this.cfInfoTaskRaiseBtnTxt,
                                "", "");
                    }
                }
            }

        } else {
            // 处理初审的任务
            CfInfoTaskVo taskVoForFirstApprove = createFirstApproveTask(
            		illnessCases.stream().map(crowdfundingInfoView -> crowdfundingInfoView.getInfoId())
					.distinct().collect(Collectors.toList()));
            if (taskVoForFirstApprove != null) {
                return taskVoForFirstApprove;
            }

            //判断是否有未结束案例
            Timestamp now = DateUtil.nowTime();
            CrowdfundingInfoView latest = null;
            for (CrowdfundingInfoView crowdfundingInfoView : illnessCases) {
                //找到一个未结束案例即跳出
                if (crowdfundingInfoView.getEndTime().after(now)) {
                    latest = crowdfundingInfoView;
                    break;
                }
            }

            if (latest != null) {
                //最后一个还未结束
                String caseUrl = CfUrlUtil.getContribute(latest.getInfoId(), "", CrowdfundingType.SERIOUS_ILLNESS);
                if (latest.getEndTime().after(now)) {
                    int hour = LocalDateTime.now().getHour();
                    if (hour < 11) {
                        return this.createInfoTask(
                                CfInfoTaskEnum.SHARE_MORNING.getCode(),
                                this.cfInfoTaskShareMorningTitle,
                                this.cfInfoTaskShareMorningDescription,
                                this.cfInfoTaskShareMorningBtnTxt,
                                caseUrl, latest.getInfoId());
                    } else if (hour >= 11 && hour < 18) {
                        return this.createInfoTask(
                                CfInfoTaskEnum.SHARE_NOON.getCode(),
                                this.cfInfoTaskShareNoonTitle,
                                this.cfInfoTaskShareNoonDescription,
                                this.cfInfoTaskShareNoonBtnTxt,
                                caseUrl, latest.getInfoId());
                    } else {
                        return this.createInfoTask(
                                CfInfoTaskEnum.SHARE_NIGHT.getCode(),
                                this.cfInfoTaskShareNightTitle,
                                this.cfInfoTaskShareNightDescription,
                                this.cfInfoTaskShareNightBtnTxt,
                                caseUrl, latest.getInfoId());
                    }
                }
            }
        }


        //判断用户是否有正常结束且未申请提现案例
        Timestamp now = DateUtil.nowTime();
        Set<Integer> drawCashCaseIdSet = drawCashApplyVoMap == null ? Sets.newHashSet() : drawCashApplyVoMap.keySet();

        for (CrowdfundingInfoView crowdfundingInfoView : crowdfundingInfos) {
            if (crowdfundingInfoView.getAmount() > 0 && crowdfundingInfoView.getEndTime().before(now)) { //有钱但已结束
                //如果提现记录中没有记录
                if (!drawCashCaseIdSet.contains(crowdfundingInfoView.getId())) {
                    String caseUrl = CfUrlUtil.getContribute(crowdfundingInfoView.getInfoId(), "", CrowdfundingType.SERIOUS_ILLNESS);
                    int amount = (int) crowdfundingInfoView.getAmount();
                    String date = DateFormatUtils.format(crowdfundingInfoView.getEndTime(), "MM月dd日hh时");
                    String description = this.cfInfoTaskFinishDescription.replaceAll("##END_TIME##", date)
                            .replaceAll("##AMOUNT##", amount + "");
                    return this.createInfoTask(CfInfoTaskEnum.FINSIH.getCode(),
                            this.cfInfoTaskFinishTitle,
                            description,
                            this.cfInfoTaskFinishBtnTxt,
                            caseUrl, crowdfundingInfoView.getInfoId());
                }
            }
        }

        //有历史案例
        return createInfoTask(
                CfInfoTaskEnum.RE_RAISE.getCode(),
                this.cfInfoTaskRaiseTitle,
                this.cfInfoTaskRaiseDescription,
                this.cfInfoTaskRaiseBtnTxt,
                "", "");
	}

	private CfInfoTaskVo createFirstApproveTask(List<String> infoUuids) {
		// 处理未通过初审的案例
		Map<String, CfInfoExt> infoExtMap = cfInfoExtBiz.getMapByInfoUuids(infoUuids);

		CfInfoTaskVo task = createTask(infoExtMap, FirstApproveStatusEnum.APPLYING,
		                               (infoUuid) -> this.createFirstApproveInProgressTask(infoUuid));
		if (task != null) {
			return task;
		}
		task = createTask(infoExtMap, FirstApproveStatusEnum.APPLY_FAIL,
		                  (infoUuid) -> this.createFirstApproveRejectTask(infoUuid));
		return task;
	}

	private CfInfoTaskVo createTask(Map<String, CfInfoExt> infoExtMap,
	                                FirstApproveStatusEnum statusEnum,
	                                Function<String, CfInfoTaskVo> function) {
		if (CollectionUtils.isEmpty(infoExtMap)) {
			return null;
		}
		List<String> infoUuids = infoExtMap.values()
		                                   .stream()
		                                   .filter(item -> item.getFirstApproveStatus() ==
		                                                   statusEnum.getCode())
		                                   .map(CfInfoExt::getInfoUuid)
		                                   .collect(Collectors.toList());
		if (!CollectionUtils.isEmpty(infoUuids)) {
			return function.apply(infoUuids.get(0));
		} else {
			return null;
		}
	}


	//返回没有案例的任务
	private CfInfoTaskVo getCfInfoTaskVoWithNoInfo(long userId) {
		UserInfoModel userInfoModel = this.userInfoDelegate.getUserInfoByUserId(userId);
		if(userInfoModel == null) {
			return this.createRaiseTask();
		}

		String cryptoMobile = userInfoModel.getCryptoMobile();
		if(StringUtils.isEmpty(cryptoMobile)) {
			return this.createRaiseTask();
		}

		String mobile = "";
		try {
			mobile = shuidiCipher.decrypt(cryptoMobile);
		} catch (IllegalArgumentException e) {

		}

		Response<CfTouFangSignDO> resSign = cfGrowthtoolFeginClient.findByMobile(mobile);
		if(resSign != null && resSign.ok()){
			return this.createRaiseTask();
		}
		Response<CfAdRegisterDO> resRegister = cfGrowthtoolFeginClient.findByRegisterMobile(mobile);
		if(resRegister != null && resRegister.ok()){
			return this.createRaiseTask();
		}

		//用户未登记、无在筹、无正在提现案例，返回登记页
		return this.createRaiseTask();
	}

	private CfInfoTaskVo createInfoTask(int status, String title, String description, String btnTxt, String url, String infoUuid) {
		CfInfoTaskVo cfInfoTaskVo = new CfInfoTaskVo();
		cfInfoTaskVo.setTitle(title);
		cfInfoTaskVo.setDescription(description);
		cfInfoTaskVo.setBtnText(btnTxt);
		cfInfoTaskVo.setStatus(status);
		cfInfoTaskVo.setUrl(url);
		cfInfoTaskVo.setInfoUuid(infoUuid);
		return cfInfoTaskVo;
	}

	private CfInfoTaskVo createGuideTask() {
		return createInfoTask(
				CfInfoTaskEnum.GUIDE.getCode(),
				this.cfInfoTaskGuideTitle,
				this.cfInfoTaskGuideDescription,
				this.cfInfoTaskGuideBtnTxt,
				this.cfInfoTaskGuideUrl, "");
	}

	private CfInfoTaskVo createFirstApproveInProgressTask(String infoUuid) {
		String firstApproveUrl = CfUrlUtil.getFirstApproveUrl(infoUuid, "");
		CrowdfundingInfo info = crowdfundingInfoBiz.getFundingInfo(infoUuid);

		return createInfoTask(
				CfInfoTaskEnum.FIRST_APPROVE_IN_PROGRESS.getCode(),
				this.cfInfoTaskFirstApproveInProgressTitle,
				info.getTargetAmount() > CfFirstApproveBiz.TARGET_AMOUT_NEED_DESC * 100 ?
						cfInfoTaskFirstApproveInProgressMoreThanFiftyDescription :
						this.cfInfoTaskFirstApproveInProgressDescription,
				this.cfInfoTaskFirstApproveInProgressBtnText,
				firstApproveUrl, infoUuid);
	}

	private CfInfoTaskVo createFirstApproveRejectTask(String infoUuid) {
		String caseUrl = CfUrlUtil.getContribute(infoUuid, "", CrowdfundingType.SERIOUS_ILLNESS);
		CfFirsApproveMaterial firstMaterial = firstApproveBiz.getByInfoUuid(infoUuid);


		String comment = "医疗材料";

		if (firstMaterial != null) {

			if (firstMaterial.getRejectReasonType() == EditMaterialType.SUGGEST_END_CASE.getCode()) {
				return 	createInfoTask(CfInfoTaskEnum.FIRST_APPROVE_REJECT.getCode(),
						"已暂停筹款", "您的筹款不在服务范围内，已暂停筹款", "查看筹款基本信息", caseUrl,
						infoUuid);
			}

			CrowdfundingInfo info = crowdfundingInfoBiz.getFundingInfo(infoUuid);
			comment = firstApproveBiz.getCanEditMsg(firstMaterial.getRejectReasonType(),
					info.getTargetAmount() > CfFirstApproveBiz.TARGET_AMOUT_NEED_DESC * 100);
		}

		return createInfoTask(
				CfInfoTaskEnum.FIRST_APPROVE_REJECT.getCode(),
				this.cfInfoTaskFirstApproveRejectTitle,
				String.format(this.cfInfoTaskFirstApproveRejectPlaceDescription, comment),
				this.cfInfoTaskFirstApproveRejectBtnTxt,
				caseUrl, infoUuid);
	}

	private CfInfoTaskVo createRaiseTask() {
		return createInfoTask(
				CfInfoTaskEnum.RAISE.getCode(),
				this.cfInfoTaskRaiseTitle,
				this.cfInfoTaskRaiseDescription,
				this.cfInfoTaskRaiseBtnTxt,
				"", "");
	}

	private static class CfInfoEndTimeComparator implements Comparator<CrowdfundingInfoView> {

		@Override
		public int compare(CrowdfundingInfoView o1, CrowdfundingInfoView o2) {

			if(o1 == null || o2 == null) {
				return -1;
			}

			if (o1.getEndTime().after(o2.getEndTime())) {
                return -1;
            }

			return 0;
		}
	}

}
