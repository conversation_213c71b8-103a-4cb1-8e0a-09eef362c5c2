package com.shuidihuzhu.cf.service.crowdfunding.app;

import com.shuidihuzhu.cf.dao.crowdfunding.app.AppDeviceBindDao;
import com.shuidihuzhu.cf.dao.crowdfunding.app.AppEventDao;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.model.crowdfunding.app.vo.AppDeviceBindVo;
import com.shuidihuzhu.cf.model.crowdfunding.app.vo.AppEventVo;
import com.shuidihuzhu.cf.response.OpResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019-06-24
 */
@Slf4j
@RefreshScope
@Service
public class AppPushService {

    @Autowired
    private AppDeviceBindDao appDeviceBindDao;
    @Autowired
    private AppEventDao appEventDao;

    /**
     * 保存设备绑定关系
     * @param appUnqiueId
     * @param clientId
     * @param userId
     */
    public void saveAppDeviceBind(String appUnqiueId, String clientId, Long userId) {
        AppDeviceBindVo appDeviceBindVo = appDeviceBindDao.getAppDeviceBindVoByAppUnqiueId(appUnqiueId);
        if (appDeviceBindVo != null){
            appDeviceBindVo.setClientId(clientId);
            appDeviceBindVo.setUserId(userId);
            appDeviceBindDao.update(appDeviceBindVo);
        }else{
            appDeviceBindVo =  new AppDeviceBindVo();
            appDeviceBindVo.setAppUniqId(appUnqiueId);
            appDeviceBindVo.setClientId(clientId);
            appDeviceBindVo.setUserId(userId);
            appDeviceBindDao.insert(appDeviceBindVo);
        }
    }

    public OpResult saveAppEvent(String appUnqiueId, int eventId, Long userId) {
        AppEventVo appEventVo = appEventDao.getAppEventDo(appUnqiueId,eventId);
        if (appEventVo == null){
            appEventVo = new AppEventVo();
            appEventVo.setAppUniqId(appUnqiueId);
            appEventVo.setEventId(eventId);
            appEventVo.setUserId(userId);
            appEventDao.insert(appEventVo);
            return OpResult.createSucResult();
        }else{
            //表中已经有数
            return OpResult.createFailResult(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
    }
}
