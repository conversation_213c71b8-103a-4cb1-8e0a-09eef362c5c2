package com.shuidihuzhu.cf.service.crowdfunding;

import com.google.common.collect.Lists;

import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.UserRealTimeDataModel;

import com.shuidihuzhu.client.model.DonateAndShareDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;

import lombok.extern.slf4j.Slf4j;

/**
 * @Author: WangYing on 2018/8/23
 */
@Service
@Slf4j
public class UserRealTimeDataModelService {
    @Autowired
    CrowdfundingInfoBiz crowdfundingInfoBiz;

    /**
     * 获取未捐款已转发的案例id列表.
     *
     * @return 案例id列表
     */
    public List<Integer> getNotDonatedButSharedCaseIds(UserRealTimeDataModel userRealTimeDataModel) {
        List<Integer> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(userRealTimeDataModel.getCfVisitedCaseIds()) || CollectionUtils.isEmpty(userRealTimeDataModel.getCfSharedCaseIds())) {
            return result;
        }
        // 已转发，未捐款
        if (CollectionUtils.isEmpty(userRealTimeDataModel.getCfDonatedCaseIds())) {
            return getNotFinishedCaseIds(userRealTimeDataModel.getCfSharedCaseIds());
        }
        for (Integer cfSharingCaseId : userRealTimeDataModel.getCfSharedCaseIds()) {
            if (!userRealTimeDataModel.getCfDonatedCaseIds().contains(cfSharingCaseId)) {
                result.add(cfSharingCaseId);
            }
        }
        return result;
    }

    /**
     * 获取未捐款未转发的案例id列表.
     *
     * @return 案例id列表
     */
    public List<Integer> getNotDonatedAndNotSharedCaseIds(UserRealTimeDataModel userRealTimeDataModel) {
        List<Integer> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(userRealTimeDataModel.getCfVisitedCaseIds())) {
            return result;
        }
        for (Integer cfVisistedCaseId : userRealTimeDataModel.getCfVisitedCaseIds()) {
            if ((CollectionUtils.isEmpty(userRealTimeDataModel.getCfSharedCaseIds()) || !userRealTimeDataModel.getCfSharedCaseIds().contains(cfVisistedCaseId))
                    && (CollectionUtils.isEmpty(userRealTimeDataModel.getCfDonatedCaseIds()) || !userRealTimeDataModel.getCfDonatedCaseIds().contains(cfVisistedCaseId))) {
                result.add(cfVisistedCaseId);
            }
        }
        return result;
    }

    /**
     * 获取已捐款未转发的案例id列表.
     *
     * @return 案例id列表
     */
    public List<Integer> getDonatedButNotSharedCaseIds(UserRealTimeDataModel userRealTimeDataModel) {
        if (CollectionUtils.isEmpty(userRealTimeDataModel.getCfVisitedCaseIds()) || CollectionUtils.isEmpty(userRealTimeDataModel.getCfDonatedCaseIds())) {
            return Lists.newArrayList();
        }
        List<Integer> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(userRealTimeDataModel.getCfSharedCaseIds())) {
            return getNotFinishedCaseIds(userRealTimeDataModel.getCfDonatedCaseIds());
        }
        for (Integer cfDonateCaseId : userRealTimeDataModel.getCfDonatedCaseIds()) {
            if (!userRealTimeDataModel.getCfSharedCaseIds().contains(cfDonateCaseId)) {
                result.add(cfDonateCaseId);
            }
        }
        return result;
    }

    /**
     * 获取已捐款已转发的案例id列表.
     *
     * @return 案例id列表
     */
    public List<Integer> getDonatedAndSharedCaseIds(UserRealTimeDataModel userRealTimeDataModel) {
        List<Integer> result = Lists.newArrayList();
        if (null != userRealTimeDataModel && CollectionUtils.isNotEmpty(userRealTimeDataModel.getCfSharedCaseIds()) && CollectionUtils.isNotEmpty(userRealTimeDataModel.getCfDonatedCaseIds())) {
            for (Integer cfSharedCaseId : userRealTimeDataModel.getCfSharedCaseIds()) {
                if (userRealTimeDataModel.getCfDonatedCaseIds().contains(cfSharedCaseId) && crowdfundingInfoBiz.getFundingInfoById(cfSharedCaseId).getEndTime().after(new Date())) {
                    result.add(cfSharedCaseId);
                }
            }
        }
        return result;
    }

    public Integer getCanDonateCaseId(UserRealTimeDataModel userRealTimeDataModel, Integer caseId) {
        List<Integer> result;
        if (userRealTimeDataModel == null) {
            return null;
        }
        if (null != userRealTimeDataModel &&
                !CollectionUtils.isEmpty(userRealTimeDataModel.getCfDonatedCaseIds())) {
            userRealTimeDataModel.getCfDonatedCaseIds().remove(caseId);
        }
        if (null != userRealTimeDataModel &&
                !CollectionUtils.isEmpty(userRealTimeDataModel.getCfSharedCaseIds())) {
            userRealTimeDataModel.getCfSharedCaseIds().remove(caseId);
        }
        if (null != userRealTimeDataModel &&
                !CollectionUtils.isEmpty(userRealTimeDataModel.getCfVisitedCaseIds())) {
            userRealTimeDataModel.getCfVisitedCaseIds().remove(caseId);
        }

        userRealTimeDataModel.setCfDonatedCaseIds(excludeRaiserDonateCases(userRealTimeDataModel.getCfDonatedCaseIds(), userRealTimeDataModel.getUserId()));
        userRealTimeDataModel.setCfVisitedCaseIds(excludeRaiserDonateCases(userRealTimeDataModel.getCfVisitedCaseIds(), userRealTimeDataModel.getUserId()));
        userRealTimeDataModel.setCfSharedCaseIds(excludeRaiserDonateCases(userRealTimeDataModel.getCfSharedCaseIds(), userRealTimeDataModel.getUserId()));

        if (null != userRealTimeDataModel && CollectionUtils.isNotEmpty(result = getNotDonatedButSharedCaseIds(userRealTimeDataModel)) || CollectionUtils.isNotEmpty(result = getNotDonatedAndNotSharedCaseIds(userRealTimeDataModel))
                || CollectionUtils.isNotEmpty(result = getDonatedButNotSharedCaseIds(userRealTimeDataModel)) || CollectionUtils.isNotEmpty(result = getDonatedAndSharedCaseIds(userRealTimeDataModel))) {
            Collections.reverse(result);
            return result.get(0);
        }
        return null;
    }

    private List<Integer> excludeRaiserDonateCases(List<Integer> donatedCaseIds, long userId) {
        List<Integer> donatedCaseExclueMe = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(donatedCaseIds)) {
            donatedCaseIds.forEach(donatedCaseId -> {
                if (userId != crowdfundingInfoBiz.getFundingInfoById(donatedCaseId).getUserId()) {
                    donatedCaseExclueMe.add(donatedCaseId);
                }
            });
        }
        return donatedCaseExclueMe;
    }

    public Integer getCanShareCaseId(UserRealTimeDataModel userRealTimeDataModel, Integer caseId) {
        List<Integer> result;
        if (null != userRealTimeDataModel &&
                !CollectionUtils.isEmpty(userRealTimeDataModel.getCfDonatedCaseIds())) {
            userRealTimeDataModel.getCfDonatedCaseIds().remove(caseId);
        }
        if (null != userRealTimeDataModel &&
                !CollectionUtils.isEmpty(userRealTimeDataModel.getCfSharedCaseIds())) {
            userRealTimeDataModel.getCfSharedCaseIds().remove(caseId);
        }
        if (null != userRealTimeDataModel &&
                !CollectionUtils.isEmpty(userRealTimeDataModel.getCfVisitedCaseIds())) {
            userRealTimeDataModel.getCfVisitedCaseIds().remove(caseId);
        }
        if (CollectionUtils.isNotEmpty(result = getDonatedButNotSharedCaseIds(userRealTimeDataModel)) || CollectionUtils.isNotEmpty(result = getNotDonatedAndNotSharedCaseIds(userRealTimeDataModel))
                || CollectionUtils.isNotEmpty(result = getNotDonatedButSharedCaseIds(userRealTimeDataModel)) || CollectionUtils.isNotEmpty(result = getDonatedAndSharedCaseIds(userRealTimeDataModel))) {
            Collections.reverse(result);
            return result.get(0);
        }
        return null;
    }

    public UserRealTimeDataModel convert(DonateAndShareDO userData, long wxUserId) {
        if (null == userData) {
            return null;
        }
        UserRealTimeDataModel userRealTimeDataModel = new UserRealTimeDataModel();
        userRealTimeDataModel.setUserId(wxUserId);

        String donate = userData.getDonate_case_ids();
        if (null != donate && !donate.equals(StringUtils.EMPTY)) {
            List<String> cfDonateCaseIdStrs = Arrays.asList(donate.split(","));
            List<Integer> cfDonateCaseIds = Lists.newArrayList();
            for (String id : cfDonateCaseIdStrs) {
                if (StringUtils.isNotBlank(id)) {
                    cfDonateCaseIds.add(Integer.valueOf(id));
                }
            }
            userRealTimeDataModel.setCfDonatedCaseIds(getNotFinishedCaseIds(cfDonateCaseIds));
        }

        String share = userData.getShare_case_ids();
        if (null != share && !share.equals(StringUtils.EMPTY)) {
            List<String> cfShareCaseIdStrs = Arrays.asList(share.split(","));
            List<Integer> cfShareCaseIds = Lists.newArrayList();
            CollectionUtils.collect(cfShareCaseIdStrs, caseId -> Integer.valueOf(caseId), cfShareCaseIds);
            userRealTimeDataModel.setCfSharedCaseIds(getNotFinishedCaseIds(cfShareCaseIds));
        }

        String visit = userData.getVisit_case_ids();
        if (null != visit && !visit.equals(StringUtils.EMPTY)) {
            List<String> cfVisitCaseIdStrs = Arrays.asList(visit.split(","));
            List<Integer> cfVisitCaseIds = Lists.newArrayList();
            CollectionUtils.collect(cfVisitCaseIdStrs, caseId -> Integer.valueOf(caseId), cfVisitCaseIds);
            userRealTimeDataModel.setCfVisitedCaseIds(getNotFinishedCaseIds(cfVisitCaseIds));
        }
        return userRealTimeDataModel;
    }

    public List<Integer> getNotFinishedCaseIds(List<Integer> cfCaseIds) {
        List<Integer> caseIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(cfCaseIds)) {
            cfCaseIds.forEach(caseId -> {
                CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
                if (null != crowdfundingInfo && crowdfundingInfo.getEndTime().after(new Date())) {
                    caseIds.add(crowdfundingInfo.getId());
                }
            });
        }
        return caseIds;
    }
}
