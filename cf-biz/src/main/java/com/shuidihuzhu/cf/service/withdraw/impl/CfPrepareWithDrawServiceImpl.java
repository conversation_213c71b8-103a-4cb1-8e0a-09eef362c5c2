package com.shuidihuzhu.cf.service.withdraw.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoSimpleBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoStatusBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.verification.CrowdFundingVerificationBiz;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoDataStatusTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoStatusEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.MaterialPrepareEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.WithDrawPrepareVO;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceBcpWhitelistFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.enums.BcpWhiteTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSimpleModel;
import com.shuidihuzhu.cf.service.withdraw.CfPrepareWithDrawService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/12/19 4:47 PM
 */
@Slf4j
@Service
@RefreshScope
public class CfPrepareWithDrawServiceImpl implements CfPrepareWithDrawService {

    @Resource
    private CrowdfundingInfoStatusBiz crowdfundingInfoStatusBiz;
    @Resource
    private CrowdFundingVerificationBiz crowdFundingVerificationBiz;
    @Resource
    private CfFinanceBcpWhitelistFeignClient cfFinanceBcpWhitelistFeignClient;
    @Resource
    private CrowdfundingInfoSimpleBiz crowdfundingInfoSimpleBiz;


    @Override
    public WithDrawPrepareVO getPrepareWithDrawStatus(String infoUuid) {

        CfInfoSimpleModel simpleModel = crowdfundingInfoSimpleBiz.getFundingInfo(infoUuid);

        // 判断是否证实白名单
        boolean isAlreadySkipFriendsVerification = Optional.ofNullable(
                cfFinanceBcpWhitelistFeignClient
                        .isExistInWhitelist(simpleModel.getId(), BcpWhiteTypeEnum.SKIP_FRIEND_VERIFICATION)
        ).filter(FeignResponse::ok)
                .map(FeignResponse::getData)
                .orElse(false);

        // 获取案例证实个数
        Integer verificationCount = crowdFundingVerificationBiz.countCrowdFundingVerificationByInfoUuid(infoUuid);

        // 获取补充材料的状态
        Map<Integer, Integer> dataTypeStatus = crowdfundingInfoStatusBiz.getMapByInfoUuid(infoUuid);
        if (MapUtils.isEmpty(dataTypeStatus) || Objects.isNull(verificationCount)) {
            return WithDrawPrepareVO.builder()
                    .withDrawPrepareSwitch(0)
                    .build();
        }

        // 获取补充材料的状态
        Integer materialPrepareStatus = getMaterialStatus(dataTypeStatus);
        return WithDrawPrepareVO.builder()
                // TODO 后续下掉switch开关字段
                .withDrawPrepareSwitch(1)
                .verificationCount(verificationCount)
                .materialPrepareStatus(materialPrepareStatus)
                .alreadySkipFriendsVerification(isAlreadySkipFriendsVerification)
                .build();

    }

    private Integer getMaterialStatus(Map<Integer, Integer> dataTypeStatus) {

        if (MapUtils.isEmpty(dataTypeStatus)) {
            return MaterialPrepareEnum.TO_FINISH.getCode();
        }

        List<Integer> statusList = Lists.newArrayList(dataTypeStatus.values());
        if (statusList.size() <= 3) {
            return MaterialPrepareEnum.TO_FINISH.getCode();
        }
        if (statusList.contains(CrowdfundingInfoStatusEnum.REJECTED.getCode())) {
            return MaterialPrepareEnum.TO_CHANGE.getCode();
        }
        if (statusList.contains(CrowdfundingInfoStatusEnum.UN_SAVE.getCode()) || statusList.contains(CrowdfundingInfoStatusEnum.UN_SUBMITTED.getCode())) {
            return MaterialPrepareEnum.TO_FINISH.getCode();
        }
        if (statusList.contains(CrowdfundingInfoStatusEnum.SUBMITTED.getCode())) {
            return MaterialPrepareEnum.TO_REVIEW.getCode();
        }

        return MaterialPrepareEnum.COMPLETED.getCode(); // 所有材料都是PASSED状态
    }

}
