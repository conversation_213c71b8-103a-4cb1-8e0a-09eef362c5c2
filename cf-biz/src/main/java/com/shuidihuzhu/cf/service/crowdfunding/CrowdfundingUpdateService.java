package com.shuidihuzhu.cf.service.crowdfunding;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.biz.crowdfunding.material.IMaterialCenterService;
import com.shuidihuzhu.cf.biz.crowdfunding.material.common.MaterialCenterConvert;
import com.shuidihuzhu.cf.client.material.model.materialField.CfBaseInfoField;
import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.dao.mask.CfImageMaskDao;
import com.shuidihuzhu.cf.domain.CfImageAiMaskDO;
import com.shuidihuzhu.cf.domain.caseinfo.CaseInfoApproveStageDO;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoBaseVo;
import com.shuidihuzhu.cf.service.caseinfo.CaseInfoApproveStageService;

import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.client.model.enums.ImageMaskBizEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;


@Slf4j
@RefreshScope
@Service
public class CrowdfundingUpdateService {

    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private CrowdfundingAttachmentBiz crowdfundingAttachmentBiz;
    @Autowired
    private CrowdfundingInfoStatusBiz crowdfundingInfoStatusBiz;
    @Autowired
    private IMaterialCenterService materialCenterService;

    @Autowired
    private CaseInfoApproveStageService caseInfoApproveStageService;

    @Autowired
    private SensitiveProcessorService sensitiveProcessorService;
    @Resource
    private CfImageMaskDao cfImageMaskDao;

    @Autowired
    private OldShuidiCipher oldShuidiCipher;
    @Transactional(rollbackFor = Exception.class,transactionManager = CfDataSource.CROWDFUNDIN_DATASOURCE_TRANSACTION_MANAGER)
    public void updateBaseInfo(CrowdfundingInfoBaseVo crowdfundingInfoBaseVo, CrowdfundingInfo crowdfundingInfo) {

        String infoUuid = crowdfundingInfoBaseVo.getInfoUuid();
        long userId = crowdfundingInfoBaseVo.getUserId();

        String channel = crowdfundingInfo.getChannel(); // 不能更新原有渠道
        String channelTypeName = crowdfundingInfoBaseVo.getChannelTypeName();
        List<String> attachments = crowdfundingInfoBaseVo.getAttachments();
        crowdfundingInfo.setUserId(userId);
        if (StringUtils.isNotBlank(channelTypeName)) {
            crowdfundingInfo.setChannelType(CrowdfundingChannelTypeEnum.fromTypeName(channelTypeName));
        } else { // V3 版本缺省为 individual
            crowdfundingInfo.setChannelType(CrowdfundingChannelTypeEnum.individual);
        }
        crowdfundingInfo.setChannel(channel);
        crowdfundingInfo.setBeginTime(new Date());
        crowdfundingInfo.setEndTime(DateUtils.addDays(new Date(), 30));
        crowdfundingInfo.setTargetAmount(Objects.requireNonNullElse(crowdfundingInfoBaseVo.getTargetAmount(), 0) * 100);


        // 图文内容存储到暂存区 审核后写入原表
        String content = crowdfundingInfoBaseVo.getContent();
        String newContent = StringUtils.isEmpty(content) ? crowdfundingInfo.getContent() : content.replaceAll("\r", "<br />");
        caseInfoApproveStageService.update(crowdfundingInfo.getId(), crowdfundingInfoBaseVo.getTitle(), newContent, attachments);

        //2,更新筹款基本信息
        crowdfundingInfoBiz.updateBaseInfoV2(crowdfundingInfo);
        if (CrowdfundingStatus.APPROVE_DENIED.equals(crowdfundingInfo.getStatus())) {
            //3,更新筹款信息状态
            crowdfundingInfoStatusBiz.updateByInfoId(infoUuid,
                    CrowdfundingInfoDataStatusTypeEnum.BASE_INFO_SUBMIT.getCode(), CrowdfundingInfoStatusEnum.SUBMITTED);
        }
        syncMaterialCenter(crowdfundingInfo.getId(), crowdfundingInfoBaseVo);
    }

    /**
     * 审核成功 从暂存区写入原表
     * @param caseId
     */
    public void commitFromStage(int caseId){
        CaseInfoApproveStageDO v = caseInfoApproveStageService.getByCaseId(caseId);
        log.info("commitFromStage caseId:{}, info:{}", caseId, v);
        if (v == null) {
            return;
        }
        CrowdfundingInfo fundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        fundingInfo.setTitle(v.getTitle());
        fundingInfo.setContent(sensitiveProcessorService.process(v.getContent()));
        fundingInfo.setEncryptContent(oldShuidiCipher.aesEncrypt(v.getContent()));

        List<String> attachments = Arrays.asList(StringUtils.split(v.getImages(), (",")));
        updateAttachment(caseId, AttachmentTypeEnum.ATTACH_CF, attachments);

        if (CollectionUtils.isNotEmpty(attachments)) {
            List<CrowdfundingAttachmentVo> crowdfundingAttachmentVos = crowdfundingAttachmentBiz.getAttachmentsByType(caseId, AttachmentTypeEnum.ATTACH_CF);
            if (CollectionUtils.isEmpty(crowdfundingAttachmentVos)) {
                fundingInfo.setTitleImg(attachments.get(0));
            } else {
                fundingInfo.setTitleImg(maskImage(attachments.get(0), crowdfundingAttachmentVos));
            }
        }else {
            fundingInfo.setTitleImg("");
        }
        crowdfundingInfoBiz.updateFromStageInfo(fundingInfo);
    }

    private String maskImage(String attachment, List<CrowdfundingAttachmentVo> crowdfundingAttachmentVos) {
        CrowdfundingAttachmentVo crowdfundingAttachmentVo = crowdfundingAttachmentVos.get(0);
        List<CfImageAiMaskDO> cfImageAiMaskDOS = cfImageMaskDao.selectByBizIdAndType((long) crowdfundingAttachmentVo.getId(), ImageMaskBizEnum.CF_DETAIL_IMAGE.getCode());
        if (CollectionUtils.isEmpty(cfImageAiMaskDOS)) {
            return attachment;
        }

        CfImageAiMaskDO cfImageAiMaskDO = cfImageAiMaskDOS.get(0);
        if (StringUtils.isBlank(cfImageAiMaskDO.getImageUrlAi())
                || cfImageAiMaskDO.getImageHandleStatus() != 2) {
            return attachment;
        }

        return cfImageAiMaskDO.getImageUrlAi();
    }


    //更新附件
    private void updateAttachment(int infoId, AttachmentTypeEnum attachmentTypeEnum, List<String> attachments) {
        this.crowdfundingAttachmentBiz.deleteByParentIdAndType(infoId, Lists.newArrayList(attachmentTypeEnum));
        if (attachments != null && attachments.size() > 0) {
            List<CrowdfundingAttachment> attachmentList = Lists.newArrayList();
            for (int i = 0; i < attachments.size(); i++) {
                String url = attachments.get(i);
                attachmentList.add(new CrowdfundingAttachment(infoId, attachmentTypeEnum, url, i));
            }
            this.crowdfundingAttachmentBiz.add(attachmentList);
        }
    }

    private void syncMaterialCenter(int caseId, CrowdfundingInfoBaseVo baseVo) {

        materialCenterService.addOrUpdateRaiseContentInfo(caseId,
                Sets.newHashSet(
                        CfBaseInfoField.title,
                        CfBaseInfoField.content,
                        CfBaseInfoField.raise_images,
                        CfBaseInfoField.base_info_last_update_time),
                MaterialCenterConvert.convertFromInfoBaseVo(baseVo));
    }

}
