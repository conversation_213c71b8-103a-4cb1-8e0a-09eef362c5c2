package com.shuidihuzhu.cf.service.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.account.utils.ClearUserIdUtil;
import com.shuidihuzhu.cf.biz.credit.AuthenticityIndicatorBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.biz.crowdfunding.impl.CfAiMaskImageServiceImpl;
import com.shuidihuzhu.cf.biz.crowdfunding.impl.CrowdFundingProgressBizImpl;
import com.shuidihuzhu.cf.biz.crowdfunding.material.CfPropertyInsuranceAuditService;
import com.shuidihuzhu.cf.biz.crowdfunding.material.IMaterialCenterService;
import com.shuidihuzhu.cf.biz.crowdfunding.material.IMaterialReadService;
import com.shuidihuzhu.cf.biz.crowdfunding.verification.CrowdFundingVerificationBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.verification.service.CrowdFundingVerificationQueryService;
import com.shuidihuzhu.cf.biz.datautilapi.DataUtilApiBiz;
import com.shuidihuzhu.cf.biz.graytest.CfGrayTestBiz;
import com.shuidihuzhu.cf.client.adminpure.enums.CfCasePublicInfoTypeEnum;
import com.shuidihuzhu.cf.client.adminpure.feign.workOrder.ImagePublicWorkOrderFeignClient;
import com.shuidihuzhu.cf.client.adminpure.model.caseinfo.CfCasePublicInfo;
import com.shuidihuzhu.cf.client.apipure.model.crowdfunding.CaseDisplaySettingVo;
import com.shuidihuzhu.cf.client.apipure.model.crowdfunding.CrowdfundingMoneyVO;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.client.material.model.*;
import com.shuidihuzhu.cf.client.material.model.materialField.MaterialExtKeyConst;
import com.shuidihuzhu.cf.constants.AsyncPoolConstants;
import com.shuidihuzhu.cf.constants.crowdfunding.CfRedisKvKey;
import com.shuidihuzhu.cf.constants.crowdfunding.RedisKeyCons;
import com.shuidihuzhu.cf.dao.crowdfunding.AdminCaseDetailsMsgDao;
import com.shuidihuzhu.cf.dao.mask.CfImageMaskDao;
import com.shuidihuzhu.cf.delegate.ICFFinanceFeignDelegate;
import com.shuidihuzhu.cf.delegate.hystrix.CaseBaseDataFeignDelegate;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.domain.CfImageAiMaskDO;
import com.shuidihuzhu.cf.domain.caseinfo.CaseEndRecordDO;
import com.shuidihuzhu.cf.domain.caseinfo.CaseInfoApproveStageDO;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.facade.loverank.Impl.LoveRankCacheService;
import com.shuidihuzhu.cf.facade.raiserisk.CaseRaiseRiskFacade;
import com.shuidihuzhu.cf.facade.risk.IAntiCrawlerFacade;
import com.shuidihuzhu.cf.facade.risk.verify.RiskUgcVerifyFacade;
import com.shuidihuzhu.cf.finance.model.vo.CfFinanceCapitalStatusVo;
import com.shuidihuzhu.cf.mapper.CfCasePageAnnouncementManageMapper;
import com.shuidihuzhu.cf.mapper.CrowdfundingUserInfoMapper;
import com.shuidihuzhu.cf.model.CaseLabelsManagement;
import com.shuidihuzhu.cf.model.CfContentStyle;
import com.shuidihuzhu.cf.model.CfPopUpWindowVo;
import com.shuidihuzhu.cf.model.UserAccountView;
import com.shuidihuzhu.cf.model.ban.BanInfoVo;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.card.CardMsg;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.InitialAuditItem;
import com.shuidihuzhu.cf.model.crowdfunding.material.credit.CreditSupplementModel;
import com.shuidihuzhu.cf.model.crowdfunding.vo.*;
import com.shuidihuzhu.cf.model.getfundinginfo.*;
import com.shuidihuzhu.cf.model.river.RiverReviewDO;
import com.shuidihuzhu.cf.model.river.RiverStatusEnum;
import com.shuidihuzhu.cf.model.river.RiverUsageTypeEnum;
import com.shuidihuzhu.cf.param.UserAccessParam;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.IOssToCosTransService;
import com.shuidihuzhu.cf.service.RefundCountByBigdataHystrixService;
import com.shuidihuzhu.cf.service.ban.CommercialForwarderControlService;
import com.shuidihuzhu.cf.service.caseinfo.CaseEndRecordService;
import com.shuidihuzhu.cf.service.caseinfo.CaseInfoApproveStageService;
import com.shuidihuzhu.cf.service.crowdfunding.bigdata.DSFriendsService;
import com.shuidihuzhu.cf.service.crowdfunding.comment.CfCommentHandlerService;
import com.shuidihuzhu.cf.service.crowdfunding.content.CfContentStyleService;
import com.shuidihuzhu.cf.service.encrypt.IFakeIdService;
import com.shuidihuzhu.cf.service.label.CaseLabelsJinYunCountyService;
import com.shuidihuzhu.cf.service.label.CaseLabelsJinYunCountyVerificationService;
import com.shuidihuzhu.cf.service.label.CaseLabelsManagementService;
import com.shuidihuzhu.cf.service.label.CfLabelService;
import com.shuidihuzhu.cf.service.risk.simpleblack.SimpleBlacklistService;
import com.shuidihuzhu.cf.service.river.RiverReviewService;
import com.shuidihuzhu.cf.service.stat.uv.CaseVisitorService;
import com.shuidihuzhu.cf.store.enums.CfDomainEnum;
import com.shuidihuzhu.cf.store.model.AnalysisUrl;
import com.shuidihuzhu.cf.util.ConfusionUtils;
import com.shuidihuzhu.cf.util.DataDesensitizations;
import com.shuidihuzhu.cf.util.ProvinceUtil;
import com.shuidihuzhu.cf.util.crowdfunding.UserAccessParamUtil;
import com.shuidihuzhu.cf.vo.AnchorPageV2VO;
import com.shuidihuzhu.cf.vo.CrowdFundingVerificationListVo;
import com.shuidihuzhu.cf.vo.GoodsOrderExtVo;
import com.shuidihuzhu.cf.vo.crowdfunding.CfEndReasonVO;
import com.shuidihuzhu.cf.vo.crowdfunding.CfSimpleVoWithFirstStatus;
import com.shuidihuzhu.cf.vo.crowdfunding.CrowdfundingBilingualVo;
import com.shuidihuzhu.cf.vo.initialaudit.CfInsuranceLivingAuditVo;
import com.shuidihuzhu.cf.vo.initialaudit.CfPropertyInsuranceVO;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.admin.model.AdminCaseDetailsMsg;
import com.shuidihuzhu.client.cf.olap.model.CfCaseBaseData;
import com.shuidihuzhu.client.feign.CfUserAttentionClient;
import com.shuidihuzhu.client.model.enums.ImageMaskBizEnum;
import com.shuidihuzhu.client.param.GetUserAttentionParam;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.constants.CrowdfundingCons;
import com.shuidihuzhu.common.web.model.IdcardInfoExtractor;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.MoneyUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.shuidihuzhu.cf.constants.AsyncPoolConstants.GET_VISITCONFIG_POOL;
import static com.shuidihuzhu.cf.constants.AsyncPoolConstants.GET_BAN_INFO_VO_POOL;
import static com.shuidihuzhu.cf.enums.crowdfunding.CfOrderListTypeEnum.OTHER;
import static com.shuidihuzhu.cf.enums.crowdfunding.CfOrderListTypeEnum.OTHER_FILTER_FRIENDS;
import static com.shuidihuzhu.cf.enums.crowdfunding.CfOrderListTypeEnum.SELF;


@Slf4j
@Service
@RefreshScope
public class CrowdfundingInfoDetailService {
    private static final Logger LOGGER = LoggerFactory.getLogger(CrowdfundingInfoDetailService.class);

    public static final long MAX_FAKE_ANCHOR_ID = 100000000000L;
    public static final int REDIS_PAGE_NUM = 30;

    private static final int MAX_FRIENDS_COUNT = 30;

    // 过滤好友的订单的最小金额
    public static final int FILTER_FRIENDS_ORDER_AMOUNT = 1000;
    @Autowired
    private CrowdfundingInfoDetailService crowdfundingInfoDetailService;
    @Autowired
    private DSFriendsService dsFriendsService;
    @Autowired
    private CrowdfundingCommentBiz crowdfundingCommentBiz;
    @Autowired
    private CrowdfundingUserBiz crowdfundingUserBiz;
    @Autowired
    private CfRedisKvBiz cfRedisKvBiz;
    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private CfGrayTestBiz cfGrayTestBiz;
    @Autowired
    private CfUserStatBiz cfUserStatBiz;
    @Resource(name = "cfRedissonHandler")
    private RedissonHandler cfRedissonHandler;
    @Autowired
    private CrowdfundingInfoDetailRedisService crowdfundingInfoDetailRedisService;

    @Resource
    private RiskUgcVerifyFacade riskUgcVerifyFacade;
    @Resource
    private CaseRaiseRiskFacade caseRaiseRiskFacade;
    @Autowired
    private CfReportAddTrustBiz cfReportAddTrustBiz;
    @Autowired
    private CfHospitalAuditBiz cfHospitalAuditBiz;
    @Resource
    private IMaterialReadService materialReadService;
    @Autowired
    private UserInfoDelegate userInfoDelegate;
    @Autowired
    private CrowdfundingOrderBiz crowdfundingOrderBiz;
    @Autowired
    private CfRefuseStatusBiz cfRefuseStatusBiz;
    @Autowired
    private CfInfoExtBiz cfInfoExtBiz;
    @Resource
    private CfContentStyleService cfContentStyleService;
    @Autowired
    private CrowdfundingInfoHospitalPayeeBiz crowdfundingInfoHospitalPayeeBiz;
    @Autowired
    private CfSeekHelpInfoFragmentBiz cfSeekHelpInfoFragmentBiz;
    @Autowired
    private CrowdFundingVerificationBiz crowdFundingVerificationBiz;
    @Autowired
    private CfInfoStatBiz cfInfoStatBiz;
    @Autowired
    private CrowdfundingAuthorBiz crowdfundingAuthorBiz;
    @Autowired
    private CrowdfundingTreatmentBiz crowdfundingTreatmentBiz;
    @Resource
    private CfCharityPayeeBiz cfCharityPayeeBiz;
    @Autowired
    private CrowdfundingInfoStatusBiz crowdfundingInfoStatusBiz;
    @Autowired
    private CrowdfundingAttachmentBiz crowdfundingAttachmentBiz;
    @Autowired
    private CrowdfundingInfoService crowdfundingInfoService;
    @Autowired
    private CfCommentHandlerService cfCommentHandlerService;
    @Autowired
    private CaseInfoApproveStageService caseInfoApproveStageService;
    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private RiverReviewService riverReviewService;
    @Autowired
    private CfPropertyInsuranceAuditService insuranceAuditService;
    @Autowired
    private IMaterialCenterService materialCenterService;
    @Autowired
    private CrowdfundingFundUseAuditProgressBiz auditProgressBiz;
    @Autowired
    private CrowdfundingInfoSimpleBiz crowdfundingInfoSimpleBiz;
    @Autowired
    private IFakeIdService fakeIdService;

    @Autowired
    private DataUtilApiBiz dataUtilApiBiz;

    @Autowired
    private ICFFinanceFeignDelegate cfFinanceFeignDelegate;

    @Autowired
    private CfCustomRelationService customRelationService;

    @Autowired
    private CfUserAttentionClient cfUserAttentionClient;

    @Autowired
    private SimpleBlacklistService simpleBlackListService;

    @Autowired
    private AdminBlessingCardBiz adminBlessingCardBiz;

    @Autowired
    private CaseLabelsJinYunCountyService caseLabelsJinYunCountyService;

    @Autowired
    private ImagePublicWorkOrderFeignClient imagePublicWorkOrderFeignClient;

    @Resource
    private CfCasePageAnnouncementManageService cfCasePageAnnouncementManageService;

    @Autowired
    private CrowdFundingProgressBizImpl crowdFundingProgressBiz;

    @Autowired
    private CaseLabelsManagementService caseLabelsManagementService;
    @Resource
    private CfImageMaskDao cfImageMaskDao;

    @Autowired
    private AdminCaseDetailsMsgDao adminCaseDetailsMsgDao;

    @Autowired
    private CfUserCaseBaseStatService cfUserCaseBaseStatService;

    @Autowired
    private RefundCountByBigdataHystrixService refundCountByBigdataHystrixService;

    @Autowired
    private LoveRankCacheService loveRankCacheService;

    @Autowired
    private CrowdfundingUserInfoMapper crowdfundingUserInfoMapper;

    @Autowired
    private CfCasePageAnnouncementManageMapper cfCasePageAnnouncementManageMapper;

    @Resource
    private CaseVisitorService caseVisitorService;

    @Autowired
    private CaseLabelsJinYunCountyVerificationService caseLabelsJinYunCountyVerificationService;

    @Resource(name = "cfShareViewRedissonHandler")
    private RedissonHandler shareRedissonHandler;

    @Autowired
    private CfShareService cfShareService;

    @Resource(name = GET_VISITCONFIG_POOL)
    private Executor getVisitConfigExecutor;

    @Resource(name = GET_BAN_INFO_VO_POOL)
    private Executor getBanInfoVoExecutor;

    @Resource(name = AsyncPoolConstants.OLAP_THREAD_POOL)
    private Executor olapExecutor;

    @Resource
    private AuthenticityIndicatorBiz authenticityIndicatorBiz;

    @Resource
    private CaseEndRecordService caseEndRecordService;

    @Autowired
    private CfLabelService cfLabelService;

    @Autowired
    private IAntiCrawlerFacade antiCrawlerFacade;

    @Autowired
    private CfBlackListBiz cfBlackListBiz;

    @Autowired
    private CfCaseDisplaySettingService cfCaseDisplaySettingService;

    @Value("${apollo.create.time.old-and-new:0}")
    private long oldAndNewCreateTime;

    @Value("${apollo.cf.new-help-record-switch:false}")
    private boolean newHelpRecordSwitch;
    @Value("${apollo.cf.order.fake-order-id-scale:1.1}")
    private double fakeOrderIdScale;
    private static final List<AttachmentTypeEnum> medicalAttachmentType = Arrays.asList(AttachmentTypeEnum.ATTACH_TREATMENT, AttachmentTypeEnum.ATTACH_TREATMENT_VERIFY,
            AttachmentTypeEnum.ATTACH_TREATMENT_NOTE, AttachmentTypeEnum.ATTACH_IN_HOSPITAL,
            AttachmentTypeEnum.ATTACH_LEAVE_HOSPITAL);


    Map<String, CfEndReasonVO> cfEndReasonVOMap = new HashMap<>();

    /**
     * 提升案例美誉度-医院核实信息展示时间阀值
     */
    @Value("${funding.show.hospital.audit.time:2021-08-08 00:00:00}")
    private String showHospitalAuditTime;

    /**
     * 掩码图片是否展示开关
     */
    @Value("${apollo.cf.mask.image.switch:false}")
    private boolean MASK_IMAGE_SWITCH;

    /**
     * 通过大数据获取帮助次数开关
     */
    @Value("${apollo.bigdata.real.donate.count.enable:false}")
    private boolean realDonateCountByDigDataEnable;
    /**
     * 开始使用cf_info_stat表，不使用大数据接口获取退款次数的caseId，后续下掉
     */
    @Value("${apollo.new.count.remove.refund.case:100000000}")
    private long useSqlRemoveRefundCaseId;
    /**
     * 此案例id后开始使用新逻辑的帮助人数
     */
    @Value("${apollo.new.donator.count.case:100000000}")
    private long useNewDonatorCount;

    @Value("${apollo.get.funding.info.base.info.switch:false}")
    private boolean baseInfoSwitch;

    @Value("${cf.poor.label.new.text.time:2021-03-20 00:00:00}")
    private String poorLabelNewTextTime;

    /**
     * 动态列表改版-新老案例时间分割；
     */
    @Value("${case.progress.version.time:1672416000000}")
    private long caseProgressVersionTime;

    @Value("${case.end.reason.config:[]}")
    public void setCaseEndPlatformConfig(String caseEndPlatformConfig) {
        if (StringUtils.isBlank(caseEndPlatformConfig)) {
            return;
        }
        cfEndReasonVOMap = JSONObject.parseArray(caseEndPlatformConfig, CfEndReasonVO.class)
                .stream()
                .collect(Collectors.toMap(CfEndReasonVO::getEndConfig, Function.identity(), (x, y) -> x));
    }

    @Resource
    private CfAiMaskImageServiceImpl cfAiMaskImageService;
    @Autowired
    private CaseBaseDataFeignDelegate caseBaseDataFeignDelegate;

    @Autowired
    private CfCaseVisitConfigService caseVisitConfigService;

    @Resource
    private CommercialForwarderControlService commercialForwarderControlService;

    @Value("${apollo.cf.order.open-fake-anchorid-switch:false}")
    private boolean fakeAnchorIdSwitch;

    @Value("${apollo.cf.order.open-fake-order-switch:false}")
    private boolean fakeOrderIdSwitch;
    public Response<AnchorPageV2VO<CrowdfundingUserView>> fillUpDataAndSetRedis(List<CrowdfundingOrder> orderList, int size, int crowdfundingId,
                                                                                long contextUserId, int pageNum,
                                                                                CfOrderListTypeEnum orderListType) {
        return fillUpDataAndSetRedis(orderList, size, crowdfundingId, contextUserId,
                false, pageNum, orderListType, Collections.EMPTY_SET);
    }

    public Response<AnchorPageV2VO<CrowdfundingUserView>> fillUpDataAndSetRedis(List<CrowdfundingOrder> orderList, int size, int crowdfundingId,
                                                                                long contextUserId, boolean isYesterday, int pageNum,
                                                                                CfOrderListTypeEnum orderListType, Set<Long> friendsUserIds) {
        if (CollectionUtils.isEmpty(orderList)) {
            return NewResponseUtil.makeSuccess(new AnchorPageV2VO<>());
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Response<AnchorPageV2VO<CrowdfundingUserView>> response = doFillUpData(orderList, size,
                crowdfundingId, contextUserId, isYesterday, pageNum, orderListType, friendsUserIds);
        stopWatch.stop();
        LOGGER.debug("getFromDB isSelf, getDetail-total-cost:{}\tlistType:{}", stopWatch.getTime(), orderListType);
        return response;
    }

    public CrowdfundingUserView fillUpData(CrowdfundingOrder crowdfundingOrder, int crowdfundingId, long contextUserId) {
        if (crowdfundingOrder == null) {
            return null;
        }

        List<CrowdfundingUserView> crowdfundingUserViews = fillUserInfoAndComment(Lists.newArrayList(crowdfundingOrder), 1, contextUserId, crowdfundingId);

        crowdfundingUserViews = riskUgcVerifyFacade.handleDetail(crowdfundingId, crowdfundingUserViews, contextUserId);

        //增加点赞
        hasFollow(contextUserId, crowdfundingUserViews);
        ClearUserIdUtil.clearUserId(crowdfundingUserViews);
        //过滤敏感词
        cfCommentHandlerService.replaceUserView(crowdfundingUserViews);

        return CollectionUtils.isNotEmpty(crowdfundingUserViews) ? crowdfundingUserViews.get(0) : null;
    }

    private Response<AnchorPageV2VO<CrowdfundingUserView>> doFillUpData(List<CrowdfundingOrder> orderList, int size, int crowdfundingId,
                                                                        long contextUserId, boolean isYesterday, int pageNum, CfOrderListTypeEnum orderListType,
                                                                        Set<Long> friendsUserIds) {
        Long retAnchorId = orderList.get(orderList.size() - 1).getId();
        boolean hasNext = false;
        if (orderList.size() >= size + 1) {
            hasNext = true;
            retAnchorId = orderList.get(size - 1).getId();
        }

        List<CrowdfundingUserView> crowdfundingUserViews = fillUserInfoAndComment(orderList, size, contextUserId, crowdfundingId);
        Set<Long> userIds = Sets.newHashSet();
        orderList.forEach(item -> userIds.add(item.getUserId()));

        if (pageNum >= 1 && pageNum <= REDIS_PAGE_NUM) {
            switch (orderListType) {
                case SELF:
                    crowdfundingInfoDetailRedisService.updateSelfOrderToRedis(crowdfundingUserViews, pageNum,
                            crowdfundingId, contextUserId,
                            size, hasNext, retAnchorId);
                    break;
                case OTHER:
                case OTHER_FILTER_FRIENDS:
                    crowdfundingInfoDetailRedisService.updateOtherToRedis(crowdfundingUserViews, pageNum,
                            crowdfundingId, size, hasNext,
                            retAnchorId, isYesterday);
                    break;
                case FRIENDS:
                    // 过滤掉匿名的
                    crowdfundingUserViews = filterAnonmous(crowdfundingUserViews);
                    crowdfundingInfoDetailRedisService.updateFriendsToRedis(crowdfundingUserViews, pageNum,
                            crowdfundingId, contextUserId,
                            size, hasNext, retAnchorId);
                    break;
            }

        }
        if (orderListType != SELF) {
            crowdfundingUserViews = filterCurrentUser(crowdfundingUserViews, contextUserId);
        }
        if (orderListType == OTHER_FILTER_FRIENDS) {
            crowdfundingUserViews =
                    filterFriendsOrder(crowdfundingUserViews, friendsUserIds, FILTER_FRIENDS_ORDER_AMOUNT);
        }
//        crowdfundingUserViews = promoteSubsidy(crowdfundingId, crowdfundingUserViews, orderList);
        crowdfundingUserViews = riskUgcVerifyFacade.handleDetail(crowdfundingId, crowdfundingUserViews, contextUserId);
        //增加点赞
        hasFollow(contextUserId, crowdfundingUserViews);
        ClearUserIdUtil.clearUserId(crowdfundingUserViews);
        //过滤敏感词
        cfCommentHandlerService.replaceUserView(crowdfundingUserViews);

        AnchorPageV2VO<CrowdfundingUserView> result = new AnchorPageV2VO<>(Long.valueOf(retAnchorId), size, hasNext, crowdfundingUserViews);
        return NewResponseUtil.makeSuccess(result);
    }

    private List<CrowdfundingUserView> fillUserInfoAndComment(List<CrowdfundingOrder> orderList,
                                                              int size, long curUserId, int crowdfundingId) {
        if (orderList.size() >= size + 1) {
            orderList = orderList.subList(0, size);
        }

        List<CrowdfundingUser> userList = this.convert2CrowdfundingUserFromUserThird(orderList);

        // 1.根据UserThird填充用户信息
        this.crowdfundingUserBiz.getUserInfo(userList);

        // 2.填充评论列表
        // 2.1获取匿名信息默认名字和头像
        String anonymousName = this.cfRedisKvBiz.queryByKeyWithLocalCache(CfRedisKvKey.KEY_ANONYMOUS_DEFAULT_NAME);
        anonymousName = anonymousName == null ? "" : anonymousName;
        String anonymousHead = this.cfRedisKvBiz.queryByKeyWithLocalCache(CfRedisKvKey.KEY_ANONYMOUS_DEFAULT_HEAD);
        anonymousHead = anonymousHead == null ? "" : anonymousHead;

        List<Long> parentIdList = Lists.newArrayList();
        for (CrowdfundingUser cu : userList) {
            if (cu.isAnonymous()) {
                cu.setNickname(anonymousName);
                cu.setHeadImgUrl(anonymousHead);
            }
            parentIdList.add(cu.getOrderId());
        }


        Map<Long, List<CrowdfundingCommentView>> commentViewMap = this.crowdfundingCommentBiz
                .getCommentsWithBlackList(parentIdList, 0, size * 10, curUserId);
//        if (crowdfundingId != 4179158) {
//
//        }
        List<CrowdfundingUserView> crowdfundingUserViews = Lists.newArrayList();

        List<Long> userIds =
                userList.stream().map(CrowdfundingUser::getUserId).filter(integer -> integer != null && integer > 0).collect(
                        Collectors.toList());

        /**
         * 取爱心值的
         */
        List<CfUserStat> cfUserStatList = cfUserStatBiz.selectCfUserStatsByUserIds(
                userIds);

        Map<Long, Integer> cfUserStatMap = Maps.newHashMap();
        for (CfUserStat cfUserStat : cfUserStatList) {
            cfUserStatMap.put(cfUserStat.getUserId(), cfUserStat.getScore());
        }

        CfInfoSimpleModel cfInfoSimpleModel = crowdfundingInfoBiz.getSimpleInfoByIdFromSlave(crowdfundingId);
        for (CrowdfundingUser cu : userList) {
            CrowdfundingUserView cv = new CrowdfundingUserView(cu);
            cv.setIsShare(false);
            //设置一个加密的id
            cv.setTargetId(fakeIdService.encryptId(cv.getId()));
            List<CrowdfundingCommentView> comments = commentViewMap.get(cu.getOrderId());
            if (comments == null) {
                comments = Lists.newArrayList();
            }
            cv.setAnonymous(cu.isAnonymous());
            // 设置匿名

            for (CrowdfundingCommentView ccv : comments) {
                ccv.setCrowdfundingId(0);
                if (cu.isAnonymous()) {
                    UserAccountView userAccountView = ccv.getUser();
                    if (userAccountView != null && userAccountView.getUserId() > 0 && cu.getUserId() > 0
                            && userAccountView.getUserId() == cu.getUserId()) {
                        userAccountView.setNickname(anonymousName);
                        userAccountView.setHeadImgUrl(anonymousHead);
                        if (cfInfoSimpleModel != null && userAccountView.getUserId() == cfInfoSimpleModel.getUserId()) {
                            userAccountView.setPrFlag(true);
                        }
                    }
                    UserAccountView commentedUser = ccv.getCommentedUser();
                    if (commentedUser != null && commentedUser.getUserId() > 0 && cu.getUserId() > 0
                            && commentedUser.getUserId() == cu.getUserId()) {
                        commentedUser.setNickname(anonymousName);
                        commentedUser.setHeadImgUrl(anonymousHead);
                        if (cfInfoSimpleModel != null && commentedUser.getUserId() == cfInfoSimpleModel.getUserId()) {
                            commentedUser.setPrFlag(true);
                        }
                    }
                }
            }
            cv.setComments(comments);
            if (cfUserStatMap.containsKey(cu.getUserId())) {
                cv.setScore(cfUserStatMap.get(cu.getUserId()));
            } else {
                cv.setScore(0);
            }
            cv.setBlessingCardText(cu.getBlessingCardText());
            cv.setBlessingCardImage(cu.getBlessingCardImage());
//			cv.setId(-1L);
            crowdfundingUserViews.add(cv);

        }

        return crowdfundingUserViews;
    }

    // 是否显示或过滤分享过的订单和证实列表。是已经分享功能的开关。
    @Deprecated
    public boolean showSharedWithCfUserId(String selfTag, long userId, int degree, long cfUserId) {
        // 发起人自己，就不过滤
        if (cfUserId == userId) {
            return false;
        }
        int mode = cfGrayTestBiz.getGrayTestBySelfTag("cf_detail_show_shared_order", userId > 0 ? userId : 0, selfTag);
        // degree=0 表示1度好友，1表示二度好友
        if (mode == 1 && (degree == 1 || degree == 0)) {
            return true;
        }
        return false;
    }

    @Deprecated
    public boolean showSharedWithCfId(String selfTag, long userId, int degree, int cfId) {
        CrowdfundingInfo cfInfo = crowdfundingInfoBiz.getFundingInfoById(cfId);
        return showSharedWithCfUserId(selfTag, userId, degree, cfInfo.getUserId());
    }


    // 过滤当前用户
    public List<CrowdfundingUserView> filterCurrentUser(List<CrowdfundingUserView> viewList, long currentUserId) {
        if (currentUserId <= 0 || CollectionUtils.isEmpty(viewList)) {
            return viewList;
        }
        return viewList.stream()
                .filter(item -> item.getUserId() != currentUserId)
                .collect(Collectors.toList());
    }

    // 过滤掉匿名的
    public List<CrowdfundingUserView> filterAnonmous(List<CrowdfundingUserView> viewList) {
        if (CollectionUtils.isEmpty(viewList)) {
            return viewList;
        }
        return viewList.stream()
                .filter(item -> !item.isAnonymous())
                .collect(Collectors.toList());
    }

    /**
     * 过滤好友的，金额大于指定参数的订单
     *
     * @param viewList
     * @param friendsUserids
     * @param amountInFen
     * @return
     */
    public List<CrowdfundingUserView> filterFriendsOrder(List<CrowdfundingUserView> viewList, Set<Long> friendsUserids,
                                                         int amountInFen) {
        if (CollectionUtils.isEmpty(viewList) || CollectionUtils.isEmpty(friendsUserids) || amountInFen < 0) {
            return viewList;
        }
        return viewList.stream()
                .filter(item -> {
                    if (friendsUserids.contains(item.getUserId()) &&
                            item.getAmt() * 100 >= amountInFen &&
                            !item.isAnonymous()) {
                        return false;
                    }
                    return true;
                })
                .collect(Collectors.toList());
    }

    public List<CrowdfundingUser> convert2CrowdfundingUserFromUserThird(List<CrowdfundingOrder> orderList) {
        List<AdminBlessingCardVo> adminBlessingCardVos = adminBlessingCardBiz.getList();
        Map<Integer, AdminBlessingCardVo> integerStringMap = adminBlessingCardVos.stream().collect(Collectors.toMap(AdminBlessingCardVo::getId, Function.identity()));
        Map<Long, Integer> orderMap = adminBlessingCardBiz.getMap(orderList.stream().map(CrowdfundingOrder::getId).collect(Collectors.toList()));
        List<CrowdfundingUser> userList = Lists.newArrayList();
        for (CrowdfundingOrder order : orderList) {
            CrowdfundingUser user = new CrowdfundingUser();
            user.setUserThirdId(order.getUserThirdId());
            user.setUserId(order.getUserId());
            user.setAmt(order.getAmountInYuanDouble());
            user.setComment(order.getComment());
            user.setTime((int) (order.getCtime().getTime() / 1000));
            user.setOrderId(order.getId());// 添加order id
            user.setAnonymous(order.isAnonymous());
            Integer blessingCardId = MapUtils.isEmpty(orderMap) ? null : orderMap.get(order.getId());
            user.setBlessingCardText(Objects.isNull(integerStringMap.get(blessingCardId)) ? StringUtils.EMPTY : integerStringMap.get(blessingCardId).getBlessingCardText());
            user.setBlessingCardImage(Objects.isNull(integerStringMap.get(blessingCardId)) ? StringUtils.EMPTY : integerStringMap.get(blessingCardId).getBlessingCardImage());
            userList.add(user);
        }
        return userList;
    }

    /**
     * 获取帮助记录
     * @return
     */
    public Response<AnchorPageV2VO<CrowdfundingUserView>> getDetailData(String clientIp,
                                                    long contextUserId,
                                                    Boolean filterFriends,
                                                    Long anchorId,
                                                    String infoUuid,
                                                    int size,
                                                    Integer isYesterday,
                                                    int pageNum) {

        if(antiCrawlerFacade.inBlackList(clientIp,"/api/cf/v5/detail/get", contextUserId)){
            return Response.OK;
        }

        // 0.参数验证
        CfErrorCode resultCode = checkParam(infoUuid, size);
        if(!CfErrorCode.SUCCESS.equals(resultCode)) {
            return NewResponseUtil.makeError(resultCode);
        }
        CfInfoSimpleModel simpleModel = crowdfundingInfoSimpleBiz.getFundingInfo(infoUuid);

        // 1.参数数据库验证
        resultCode = checkDBParam(simpleModel, contextUserId);
        if(!CfErrorCode.SUCCESS.equals(resultCode)) {

            if(contextUserId == 0 && resultCode == CfErrorCode.CF_NOT_FOUND && !StringUtils.equals(infoUuid, "undefined")){
                antiCrawlerFacade.addRequestRecord(clientIp,"/api/cf/v5/detail/get", contextUserId);
            }

            return NewResponseUtil.makeError(resultCode);
        }

        // 用AB测试关闭 filterFriends
        long userIdvalue = contextUserId > 0 ? contextUserId : 0;
        int switchValue = cfGrayTestBiz.getGrayTestBySelfTag("friend_order_switch_backend", userIdvalue, "");

        // 筹款人也过滤掉
        if(switchValue == 0 ||
                contextUserId == simpleModel.getUserId()) {
            filterFriends = false;
        }

        int crowdfundingId = simpleModel.getId();
        // 处理锚点
        long realAnchorId = ConfusionUtils.ORDER_MAX;
        if(anchorId != null) {
            realAnchorId = decodeFakeAnchorId(anchorId);
        }

        String startTime = null;
        String endTime = null;
        boolean isYesterdayBool = false;
        if(isYesterday != null && isYesterday > 0) {
            try {
                startTime = com.shuidihuzhu.common.util.DateUtil.getNextDayStartTime(com.shuidihuzhu.common.util.DateUtil.getCurrentDateStr(), -1);
                endTime = com.shuidihuzhu.common.util.DateUtil.getNextDayStartTime(com.shuidihuzhu.common.util.DateUtil.getCurrentDateStr(), 0);
                isYesterdayBool = true;
            } catch (ParseException e) {
                LOGGER.error("", e);
            }
        }

        // 2.获取数据
        Set<Long> friendUserIds = Collections.EMPTY_SET;
        CfOrderListTypeEnum orderListType = OTHER;
        if(filterFriends && contextUserId > 0) {
            friendUserIds = dsFriendsService.getFriendsUserId(contextUserId, MAX_FRIENDS_COUNT,
                    FriendsBizTypeEnum.FRIEND_ORDER_LIST);
            orderListType = OTHER_FILTER_FRIENDS;
        }

        // 走缓存
        if(pageNum >= 1 && pageNum <= CrowdfundingInfoDetailService.REDIS_PAGE_NUM) {
            AnchorPageV2VO<CrowdfundingUserView> anchorPageVo =
                    this.crowdfundingInfoDetailRedisService.getOthersOrderFromRedis(
                            pageNum, crowdfundingId, isYesterdayBool);
            if(anchorPageVo != null && CollectionUtils.isNotEmpty(anchorPageVo.getList())) {
                List<CrowdfundingUserView> list = anchorPageVo.getList();
                cfCommentHandlerService.replaceCrowdFundingUserViews(list);
                // 过滤掉自己的订单
                list = crowdfundingInfoDetailService.filterCurrentUser(list, contextUserId);
                // 过滤好友订单
                if(filterFriends) {
                    list = crowdfundingInfoDetailService.filterFriendsOrder(
                            list,
                            friendUserIds,
                            CrowdfundingInfoDetailService.FILTER_FRIENDS_ORDER_AMOUNT
                    );
                }
                list = riskUgcVerifyFacade.handleDetail(crowdfundingId, list, contextUserId);
                anchorPageVo.setList(list);
                //增加点赞
                crowdfundingInfoDetailService.hasFollow(contextUserId,list);
                ClearUserIdUtil.clearUserId(list);
                AnchorPageV2VO<CrowdfundingUserView> anchorPageV2VO = makeFakeAnchorId(anchorPageVo);
                resetIdOfPageVo(anchorPageV2VO);

                //数据脱敏处理
                DataDesensitizations.dataDesensitization(anchorPageV2VO);

                return NewResponseUtil.makeSuccess(anchorPageV2VO);
            }
        }

        List<CrowdfundingOrder> allOrderList;
        if (newHelpRecordSwitch && crowdfundingId > useSqlRemoveRefundCaseId) {
            allOrderList = this.crowdfundingOrderBiz.getByAnchorIdAndSingleRefundFlagWithBlackList(crowdfundingId,
                    realAnchorId, size + 1, contextUserId, startTime, endTime);
        } else {
            allOrderList = this.crowdfundingOrderBiz.getByAnchorIdWithBlackList(crowdfundingId,
                    realAnchorId, size + 1, contextUserId, startTime, endTime);
        }


        // 3.填充并返回
        Response<AnchorPageV2VO<CrowdfundingUserView>> anchorPageV2VOResponse = this.crowdfundingInfoDetailService.fillUpDataAndSetRedis(
                allOrderList,
                size,
                crowdfundingId,
                contextUserId,
                isYesterdayBool,
                pageNum,
                orderListType,
                friendUserIds
        );
        Optional.ofNullable(anchorPageV2VOResponse)
                .map(Response::getData)
                .map(AnchorPageV2VO::getList)
                .map(item -> item.stream().peek(v -> Optional.ofNullable(v)
                        .map(CrowdfundingUserView::getGoodsOrderExtVo)
                        .map(GoodsOrderExtVo::getNewUserAddressVo)
                        .map(newUserAddressVo -> {
                            newUserAddressVo.setMobile("");
                            return newUserAddressVo;
                        })).collect(Collectors.toList()));

        if(anchorPageV2VOResponse == null) {
            return anchorPageV2VOResponse;
        }

        anchorPageV2VOResponse.setData(makeFakeAnchorId(anchorPageV2VOResponse.getData()));
        //消除id
        resetIdOfResponse(anchorPageV2VOResponse);

        //数据脱敏处理
        DataDesensitizations.dataDesensitization(anchorPageV2VOResponse);
        return anchorPageV2VOResponse;
    }

    public FundingInfoVo getCrowdfundingDetailV2(long contextUserId, CrowdfundingInfo crowdfundingInfo, String clientIp, String userSourceId, String selfTag) {
        FundingInfoVo result = new FundingInfoVo();

        int infoId = crowdfundingInfo.getId();
        String infoUuid = crowdfundingInfo.getInfoId();

        //异步 获取支付成功后给用户返回的信息
        CompletableFuture<CfOrderPaySuccessPageVO> caseBaseDataCompletableFuture = getCaseBaseDataCompletableFuture(infoId);

        //异步 商业转发人管控
        CompletableFuture<BanInfoVo> banInfoVoCompletableFuture = getBanInfoVoCompletableFuture(contextUserId, infoId);

        //异步 控制案例展示详情的model
        CompletableFuture<CfCaseVisitConfigVo> cfCaseVisitConfigVoFuture = getCfCaseVisitConfigVoCompletableFuture(infoId, contextUserId, selfTag);

        //前端使用了里面的字段，new一个返回给前端使用 默认传老版详情页
        result.setNewDetailVo(new NewDetailVo("0"));

        //案例info的title与内容的处理
        cfCommentHandlerService.replaceCrowdFunding(crowdfundingInfo);

        //访问了案例详情页
        caseVisitorService.visitCaseDetailsPage(contextUserId, crowdfundingInfo);

        //访问人是否是发起人
        boolean isSelf = crowdfundingInfo.isApplicant(contextUserId);
        result.setSelf(isSelf);

        result.setDataStatus(crowdfundingInfo.getDataStatus());
        result.setMaterialStatus(crowdfundingInfo.getStatus());

        //获取该用户对该案例当天转发次数
        Integer userShareCountToday = getUserShareCountToday(infoId, contextUserId);
        result.setUserShareCountToday(userShareCountToday);

        boolean after = crowdfundingInfo.getCreateTime().after(com.shuidihuzhu.common.util.DateUtil.getStr2LDate(poorLabelNewTextTime));
        result.setPoorHouseholdsNewTextSwitch(after);

        //获取发起人用户信息
        UserInfoModel userInfoModel = this.userInfoDelegate.getUserInfoByUserId(crowdfundingInfo.getUserId());

        //构造发起人用户信息
        CrowdfundingUserInfo crowdfundingUserInfo = buildCrowdfundingUserInfo(userInfoModel);
        result.setCrowdfundingUserInfo(crowdfundingUserInfo);

        //获取患者信息
        CrowdfundingAuthor author = crowdfundingAuthorBiz.get(infoId);

        //获取材料信息
        CfMaterialObject materialObject = materialCenterService.selectMaterialObjectByCaseId(infoId);
        CfFirsApproveMaterial material = materialObject.getFirstApproveMaterial();

        result.setRelationTypeForC(material != null ? material.getUserRelationTypeForC() : 0);
        // 红梅确认 直接发返回用户的姓名没有泄密风险
        result.setRaiserName(getRaiserName(material));
        result.setPatientName(getPatientName(author, material));

        //获取疾病情况信息
        CrowdfundingTreatment treatment = crowdfundingTreatmentBiz.get(infoId);

        //案例疾病开关
        CaseLabelsManagement caseLabelsManagement = cfLabelService.getLabelsManagement(infoUuid);
        boolean showDiseaseExtraInfo = caseLabelsManagement.getDisease() != -1;
        result.setShowDiseaseExtraInfo(showDiseaseExtraInfo);

        //获取案例图片信息
        Map<AttachmentTypeEnum, List<CrowdfundingAttachmentVo>> attachmentMap = crowdfundingAttachmentBiz.getFundingAttachmentMap(infoId);

        //帮助次数根据Apollo开关取大数据侧数据
        CfInfoStat cfInfoStat = handleCfInfoStat(infoId, infoUuid);
        updateCrowdfundingInfoByCfInfoStat(crowdfundingInfo, cfInfoStat);
        CfInfoStatParam cfInfoStatParam = buildCfInfoStatParam(cfInfoStat);

        result.setShareCount(cfInfoStatParam.getShareCount());

        //获取资金信息
        CfFinanceCapitalStatusVo cfFinanceCapitalStatusVo = cfFinanceFeignDelegate.getFinanceCapitalStatus(infoUuid, infoId);

        //判断是否登录
        boolean isLogin = contextUserId > 0;

        //因为FundingInfoVo使用的是Boolean 初始化成false（兼容老逻辑）
        result.dataInit();
        if (isLogin) {
            LoginSceneInfoParam loginSceneInfoParam = LoginSceneInfoParam.builder()
                    .contextUserId(contextUserId)
                    .infoUuid(infoUuid)
                    .infoId(infoId)
                    .build();
            buildLoginSceneInfo(result, loginSceneInfoParam);
        }

        Map<String, List<String>> materialValueMap = materialCenterService.selectValueByFields(crowdfundingInfo.getId(), Lists.newArrayList(MaterialExtKeyConst.first_approve_special_report));

        //诊断信息
        CrowdfundingTreatmentInfoParam crowdfundingTreatmentInfoParam = CrowdfundingTreatmentInfoParam.builder()
                .attachmentMap(attachmentMap)
                .treatment(treatment)
                .crowdfundingInfo(crowdfundingInfo)
                .cfFirsApproveMaterial(material)
                .userId(contextUserId)
                .materialValueMap(materialValueMap)
                .build();
        CrowdfundingTreatmentInfo crowdfundingTreatmentInfo = buildCrowdfundingTreatmentInfo(crowdfundingTreatmentInfoParam);
        result.setTreatmentInfo(crowdfundingTreatmentInfo);

        CfInfoExt cfInfoExt = cfInfoExtBiz.getByInfoUuidBySlaveAndMaster(infoUuid);
        buildCfInfoExt(result, cfInfoExt);

        CfContentStyle cfContentStyle = cfContentStyleService.getStyleByCaseId(infoId);

        CrowdfundingBaseInfoParam param = CrowdfundingBaseInfoParam.builder()
                .isSelf(isSelf)
                .author(author)
                .material(material)
                .cfInfoExt(cfInfoExt)
                .donatorCount(cfInfoStatParam.getDonatorCount())
                .attachmentMap(attachmentMap)
                .crowdfundingInfo(crowdfundingInfo)
                .cfFinanceCapitalStatusVo(cfFinanceCapitalStatusVo)
                .cfContentStyle(cfContentStyle)
                .build();
        CrowdfundingBaseInfo baseInfo = buildCrowdfundingBaseInfo(param);
        result.setBaseInfo(baseInfo);

        result.setCaseProgressVersion(baseInfo.getCreateTime().getTime() > caseProgressVersionTime);

        FundingInfoVo.CfUserCaseBaseStatVo cfUserCaseBaseStatVo = getCfUserCaseBaseStatVo(contextUserId, infoId);
        result.setCfUserCaseBaseStatVo(cfUserCaseBaseStatVo);

        //获取收款人信息
        CrowdfundingPayeeBaseInfoVo crowdfundingPayeeBaseInfoVo = getPayeeInfo(crowdfundingInfo);
        result.setPayeeInfo(crowdfundingPayeeBaseInfoVo);

        //获取增信信息
        buildCreditEnhancement(result, cfInfoExt, materialObject.getInsuranceInfoModel(), crowdfundingInfo, author);

        //缙云医保局“困难申报人员”已核验 true：展示；false：不展示
        boolean isJinYunLabel = isJinYunLabel(result, crowdfundingInfo, infoUuid);
        result.setJinYunLabel(isJinYunLabel);

        //案例的增信是在那个阶段提交
        int caseRaiseMaterialType = insuranceAuditService.selectPropertyCaseType(cfInfoExt);
        result.setCaseRaiseMaterialType(caseRaiseMaterialType);

        //举报增信核实消息
        CfReportAddTrust cfReportAddTrust = cfReportAddTrustBiz.getByInfoUuid(infoUuid);
        result.setHaveAddTrust(cfReportAddTrust != null && cfReportAddTrust.getAuditStatus() != CrowdfundingInfoStatusEnum.PASSED.getCode());

        //筹款信息状态表 获取info_status里面的type-status数据
        Map<Integer, Integer> dataMap = crowdfundingInfoStatusBiz.getMapByInfoUuid(infoUuid);
        result.setInfoDataStatus(dataMap);

        fillDrawCashState(result, cfFinanceCapitalStatusVo);

        //获取拒绝原因
        int rejectType = getRejectType(crowdfundingInfo.getInfoId(), crowdfundingInfo.getId());
        result.setRejectType(rejectType);

        //案例结束状态
        buildCaseEndStatus(result, crowdfundingInfo, cfInfoExt);

        //当前用户访问参数
        UserAccessParam userAccessParam = UserAccessParamUtil.build(null, clientIp);
        dataUtilApiBiz.setProvinceName(clientIp, userAccessParam);
        result.setUserAccessParam(userAccessParam);

        //发起AI检测禁止发起案例
        boolean isPassed = caseRaiseRiskFacade.isPassed(infoUuid);
        result.setHighRiskVerifying(!isPassed);

        // 案例被运营结束 则跳转到用户最新发起的案例
        String redirectUuid = crowdfundingInfoBiz.getNewlyInfoIdAtFinishBySystem(crowdfundingInfo, cfInfoExt, contextUserId);
        result.setRedirectUuid(redirectUuid);

        //是否展示贫困说明
        int poverty = crowdfundingInfoBiz.displayPovertyInCaseDetail(crowdfundingInfo.getId());
        result.setPoverty(poverty);

        // 资金用途的审核
        List<FundingInfoVo.FundUseDetail> fundUseDetailList = auditProgressBiz.selectAuditSuccessFundUses(infoId);
        result.setFundUseDetails(fundUseDetailList);
        fillDetailStage(result);

        // 公约2.0 新增
        fillLivingGuard(infoId, result, materialObject.getLiveGuardModel());
        fillFundUse(result, materialObject.getRaiseFundUseModel());

        //自定义关系展示
        String customRelationDesc = getCustomRelationDesc(userInfoModel, crowdfundingInfo.getId(), cfInfoExt, author, material);
        result.setCustomRelationDesc(customRelationDesc);

        //案例页辟谣公告栏
        List<FundingInfoVo.CfCasePageAnnouncementManageVo> cfCasePageAnnouncementManageVoList = getCfCasePageAnnouncementManageList();
        result.setCfCasePageAnnouncementManageVo(cfCasePageAnnouncementManageVoList);

        //是否在分享内容测试黑名单中
        boolean inShareContentTestBlackList = simpleBlackListService.inShareContentTestBlackList(infoId);
        result.setInShareContentTestBlackList(inShareContentTestBlackList);

        //缙云县困难申报人员弹窗
        boolean jinYunDifficultyPersonPop = caseLabelsJinYunCountyService.pop(crowdfundingInfo, cfInfoExt, isSelf);
        result.setJinYunDifficultyPersonPop(jinYunDifficultyPersonPop);

        //基金会案例标签信息
        FoundationLabelInfoVO foundationLabelInfoVO = getFoundationLabelInfo(infoUuid);
        result.setFoundationLabelInfo(foundationLabelInfoVO);

        //医院相关信息
        buildHospitalInfo(result, cfInfoExt, treatment);

        //支付详情页里需要展示的信息
        CfOrderPaySuccessPageVO cfOrderPaySuccessPageVO = getCfOrderPaySuccessPageVO(caseBaseDataCompletableFuture);
        result.setPaySuccessPageVO(cfOrderPaySuccessPageVO);

        //商业转发人管控
        BanInfoVo banInfoVo = getBanInfoVo(banInfoVoCompletableFuture);
        result.setBanInfoVo(banInfoVo);

        //控制案例展示详情的model
        CfCaseVisitConfigVo cfCaseVisitConfigVo = getCfCaseVisitConfigVo(cfCaseVisitConfigVoFuture);
        result.setVisitConfig(cfCaseVisitConfigVo);
        //控制案例展示设置
        result.setCrowdfundingBilingual(getCrowdfundingBilingual(crowdfundingInfo.getInfoId()));
        fillFundingInfoVo(result, cfCaseVisitConfigVo);

        return result;
    }

    private CfInfoStat handleCfInfoStat(int infoId, String infoUuid) {
        CfInfoStat cfInfoStat = cfInfoStatBiz.getById(infoId);
        if (cfInfoStat == null) {
            return null;
        }
        LOGGER.debug("cfInfoStat,{}", cfInfoStat);
        if (infoId > useNewDonatorCount) {
            int setDonatorCount = Math.max(cfInfoStat.getDonatorCount(), 0);
            cfInfoStat.setDonatorCount(setDonatorCount);
            LOGGER.debug("cfInfoStat.setDonatorCount:{}", setDonatorCount);
        } else {
            cfInfoStat.setDonatorCount(0);
        }
        int donationCount = cfInfoStat.getDonationCount();
        int refundCount = getRefundCount(infoId, infoUuid, cfInfoStat);

        cfInfoStat.setDonationCount(Math.max((donationCount - refundCount), 0));

        return cfInfoStat;
    }

    private int getRefundCount(int infoId, String infoUuid, CfInfoStat cfInfoStat) {

        // 新案例帮助次数取cf_info_stat表
        if (infoId > useSqlRemoveRefundCaseId) {
            return cfInfoStat.getRefundCount();
        }
        if (!realDonateCountByDigDataEnable) {
            return 0;
        }
        // 老案例帮助次数走大数据
        try {
            StopWatch watch = StopWatch.createStarted();
            final Integer refundCount = refundCountByBigdataHystrixService.getRefundCount(infoId, infoUuid);
            watch.stop();
            LOGGER.debug("退款次数查询耗时: {}", watch.getTime());
            return refundCount == null ? 0 : refundCount;
        } catch (Exception e) {
            LOGGER.info("refundCountByBigData获取error");
        }
        return 0;
    }

    private void medicalMaterialMask(List<String> attachments, Map<AttachmentTypeEnum, List<CrowdfundingAttachmentVo>> attachmentMap, Integer caseId) {

        List<CrowdfundingAttachmentVo> attachmentVos = Lists.newArrayList();
        for (AttachmentTypeEnum typeEnum : medicalAttachmentType) {
            if (attachmentMap.containsKey(typeEnum)) {
                attachmentVos.addAll(attachmentMap.get(typeEnum));
            }
        }
        if (CollectionUtils.isEmpty(attachmentVos)) {
            return;
        }

        List<CfImageAiMaskDO> cfImageAiMaskDOS = cfImageMaskDao.selectByCaseIdAndType(caseId, Lists.newArrayList(ImageMaskBizEnum.CF_MEDICAL_IMAGE.getCode()))
                .stream()
                .filter(f -> StringUtils.isNotBlank(f.getImageUrlAi()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(cfImageAiMaskDOS)) {
            cfImageAiMaskDOS = Lists.newArrayList();
        }

        Map<Long, CfImageAiMaskDO> cfImageAiMaskDOMap = cfImageAiMaskDOS.stream()
                .collect(Collectors.toMap(CfImageAiMaskDO::getBizId, Function.identity(), (o1, o2) -> o2));
        buildMaskImageList(attachments, attachmentVos, cfImageAiMaskDOMap);

    }

    @Resource
    private IOssToCosTransService iOssToCosTransService;

    private void buildMaskImageList(List<String> imageUrls, List<CrowdfundingAttachmentVo> attachmentVos, Map<Long, CfImageAiMaskDO> cfImageAiMaskMap) {
        if (CollectionUtils.isEmpty(attachmentVos)) {
            return;
        }

        for (CrowdfundingAttachmentVo attachmentVo : attachmentVos) {
            Long bizId = (long) attachmentVo.getId();
            if (cfImageAiMaskMap.containsKey(bizId)) {
                CfImageAiMaskDO cfImageAiMaskDO = cfImageAiMaskMap.get(bizId);
                String maskUrl = iOssToCosTransService.convertToCosUrl(cfImageAiMaskDO.getImageUrlAi());
                imageUrls.add(maskUrl);
            } else {
                imageUrls.add(attachmentVo.getUrl());
            }
        }
    }

    public CrowdfundingMoneyVO getCrowdfundingMoneyVO(String infoUuid) {
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoFromSlave(infoUuid);
        if (crowdfundingInfo == null) {
            return null;
        }
        CfFinanceCapitalStatusVo cfFinanceCapitalStatusVo = cfFinanceFeignDelegate.getFinanceCapitalStatus(infoUuid, crowdfundingInfo.getId());
        int donationAmountInFen = 0;
        if (cfFinanceCapitalStatusVo != null) {
            donationAmountInFen = cfFinanceCapitalStatusVo.getDonationAmountInFen();
        }

        int targetAmount = crowdfundingInfo.getTargetAmount() / 100;
        double amountInDouble = MoneyUtil.divide(String.valueOf(donationAmountInFen), "100", 2, BigDecimal.ROUND_HALF_UP).doubleValue();
        int donationCount = crowdfundingInfo.getDonationCount();

        CrowdfundingMoneyVO moneyVO = new CrowdfundingMoneyVO();
        moneyVO.setInfoId(infoUuid);
        moneyVO.setTargetAmount(targetAmount);
        moneyVO.setAmountInDouble(amountInDouble);
        moneyVO.setDonationCount(donationCount);
        return moneyVO;
    }

    private String getBankCard(String bankCard) {
        if (StringUtils.isEmpty(bankCard)) {
            return null;
        }
        bankCard = shuidiCipher.decrypt(bankCard);
        if(StringUtils.isBlank(bankCard)){
            return null;
        }
        return replaceBetween(bankCard, 4, bankCard.length() - 4);
    }

    private static String replaceBetween(String sourceStr, int begin, int end) {
        if (sourceStr == null) {
            return "";
        }

        int replaceLength = end - begin;
        if (StringUtils.isNotBlank(sourceStr) && replaceLength > 0) {
            replaceLength = replaceLength > 4 ? 4 : replaceLength;
            StringBuilder sb = new StringBuilder(sourceStr);
            sb.replace(begin, end, StringUtils.repeat("*", replaceLength));
            return sb.toString();
        } else {
            return sourceStr;
        }
    }

    private FundingInfoVo.HospitalAuditInfo buildHospitalAuditInfo(CfHospitalAuditInfo cfHospitalAuditInfo) {

        FundingInfoVo.HospitalAuditInfo hospitalAuditInfo = new FundingInfoVo.HospitalAuditInfo();

        hospitalAuditInfo.setHospitalName(cfHospitalAuditInfo.getHospitalName());
        hospitalAuditInfo.setDepartmentName(cfHospitalAuditInfo.getDepartment());
        hospitalAuditInfo.setDoctorName(cfHospitalAuditInfo.getDoctorName());
        // 掩码第四位后面的数字
        String hospitalizationNumber = cfHospitalAuditInfo.getHospitalizationNumber();
        if (StringUtils.isNotEmpty(hospitalizationNumber)) {
            String code = "*";
            StringBuilder sb = new StringBuilder();
            int length = hospitalizationNumber.length();
            if (length > 4) {
                String beginString = hospitalizationNumber.substring(0, 4);
                int index = length - 4;
                for (int i = 0; i < index; i++) {
                    sb.append(code);
                }
                String endString = sb.toString();
                hospitalizationNumber = beginString + endString;
                hospitalAuditInfo.setHospitalizationNumber(hospitalizationNumber);
            }
        }
        String auditTime = StringUtils.EMPTY;
        if (Objects.nonNull(cfHospitalAuditInfo.getAuditTime())) {
            try {
                auditTime = DateUtil.dateToString(cfHospitalAuditInfo.getAuditTime(), "yyyy-MM-dd HH:mm:ss");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        hospitalAuditInfo.setAuditTime(auditTime);

        return hospitalAuditInfo;
    }

    private void fillLivingGuard(int caseId, FundingInfoVo result, CfBasicLivingGuardModel guardModel) {

        if (guardModel == null) {
            return;
        }

        CfInsuranceLivingAuditVo.CfBasicLivingGuardVo guardVo = new CfInsuranceLivingAuditVo.CfBasicLivingGuardVo(guardModel);
        RiverReviewDO livingDo = riverReviewService.getByCaseIdAndUsageType(caseId, RiverUsageTypeEnum.DI_BAO.getValue());
        if (livingDo == null || livingDo.getInfoStatus() != RiverStatusEnum.PASS.getValue()) {
            guardVo.setLivingAllowance(null);
            guardVo.setAllowanceImg(null);
        }

        RiverReviewDO povertyDo = riverReviewService.getByCaseIdAndUsageType(caseId, RiverUsageTypeEnum.PIN_KUN.getValue());
        if (povertyDo == null || povertyDo.getInfoStatus() != RiverStatusEnum.PASS.getValue()) {
            guardVo.setHasPoverty(null);
            guardVo.setPovertyImg(null);
        }
        result.setLivingGuardVo(guardVo);
    }

    private void fillFundUse(FundingInfoVo result, CfRaiseFundUseModel fundUseModel) {

        if (result.getMaterialStatus() != CrowdfundingStatus.CROWDFUNDING_STATED
                || (MapUtils.isNotEmpty(result.getInfoDataStatus())
                && !result.getInfoDataStatus().containsKey(CrowdfundingInfoDataStatusTypeEnum.FUND_USE_SUBMIT.getCode()))) {
            return;
        }

        result.setFundUseModel(fundUseModel);
    }


    private void fillDrawCashState(FundingInfoVo result, CfFinanceCapitalStatusVo cfFinanceCapitalStatusVo) {
        if (cfFinanceCapitalStatusVo == null) {
            result.setApplyDrawStatus(CfDrawCashConstant.ApplyStatus.UNSUBMIT.getCode());
            result.setApplyRefundStatus(NewCfRefundConstant.ApplyStatus.UNSUBMIT.getCode());
            return;
        }

        Timestamp finishTime = getFinishTime(cfFinanceCapitalStatusVo);
        result.setFinishTime(finishTime);
        result.setMissionVerify(cfFinanceCapitalStatusVo.isVerify());
        result.setApplyDrawStatus(cfFinanceCapitalStatusVo.getDrawApplyStatus());
        result.setApplyRefundStatus(cfFinanceCapitalStatusVo.getRefundApplyStatus());

        if (cfFinanceCapitalStatusVo.getDrawStartTime() != null) {
            result.setStartPaying(1);
            result.setStartPayingTime(cfFinanceCapitalStatusVo.getDrawStartTime().getTime());
        } else {
            result.setStartPaying(0);
        }

        if (cfFinanceCapitalStatusVo.getPublicityStartTime() != null) {
            result.setDrawPublicStartTime(cfFinanceCapitalStatusVo.getPublicityStartTime().getTime());
        }

        if (StringUtils.isNotBlank(cfFinanceCapitalStatusVo.getStopCfReason())
                || StringUtils.isNotBlank(cfFinanceCapitalStatusVo.getUseOfFunds())
                || StringUtils.isNotBlank(cfFinanceCapitalStatusVo.getPatientConditionNow())
                || CollectionUtils.isNotEmpty(cfFinanceCapitalStatusVo.getAttachments())) {
            result.setApplyDrawReason(new FundingInfoVo.ApplyDrawReason(
                    cfFinanceCapitalStatusVo.getStopCfReason(), cfFinanceCapitalStatusVo.getUseOfFunds(),
                    cfFinanceCapitalStatusVo.getPatientConditionNow(),
                    cfFinanceCapitalStatusVo.getAttachments()));
        }

    }

    private void fillDetailStage(FundingInfoVo result) {

        if (CollectionUtils.isNotEmpty(result.getFundUseDetails())) {
            result.setDetailStage(FundingInfoVo.CaseDetailStage.FUND_USE_SUBMIT);
            return;
        }

        // 开始打款
        if (result.getStartPaying() != null && result.getStartPaying() == 1) {
//			result.setDetailStage(FundingInfoVo.CaseDetailStage.BEGIN_DRAW);
            result.setDetailStage(FundingInfoVo.CaseDetailStage.DRAW_PUBLIC);
            return;
        }

        // 开始提现公示
        if (result.getDrawPublicStartTime() != null && result.getDrawPublicStartTime() > 0) {
            result.setDetailStage(FundingInfoVo.CaseDetailStage.DRAW_PUBLIC);
            return;
        }

        // 公约2.0 初审通过
        if (result.getCaseRaiseMaterialType() == PropertyInsuranceCaseType.INITIAL_WITH_INSURANCE.getCode()) {
            if (FirstApproveStatusEnum.isPassed(result.getFirstApproveStatus())) {
                result.setDetailStage(FundingInfoVo.CaseDetailStage.MATERIAL_AUDIT);
                return;
            }
        }

        if (result.getCaseRaiseMaterialType() == PropertyInsuranceCaseType.MATERIAL_SUBMIT_INSURANCE.getCode()) {
            if (result.getInsuranceStatus() == RiverStatusEnum.PASS.getValue()) {
                result.setDetailStage(FundingInfoVo.CaseDetailStage.MATERIAL_AUDIT);
                return;
            }
        }

        if (result.getCaseRaiseMaterialType() == PropertyInsuranceCaseType.DEFAULT.getCode()) {
            if (result.getMaterialStatus() == CrowdfundingStatus.CROWDFUNDING_STATED) {
                result.setDetailStage(FundingInfoVo.CaseDetailStage.MATERIAL_AUDIT);
                return;
            }
        }

        // 案例发起后就是初审
        result.setDetailStage(FundingInfoVo.CaseDetailStage.BASE_AUDIT);

        return;
    }


    /**
     * 从前置或者患者信息的身份证中获取省份信息
     *
     * @param idCard
     * @return
     */
    public String getAuthorProvince(String idCard) {
        if (StringUtils.isBlank(idCard) || StringUtils.length(idCard) <= 3) {
            return StringUtils.EMPTY;
        }
        String code = idCard.substring(0, 2);
        if (StringUtils.isEmpty(code) || !StringUtils.isNumeric(code)) {
            return StringUtils.EMPTY;
        }

        return ProvinceUtil.getProvinceBycode(Integer.valueOf(code));
    }

    /**
     * 从前置或者患者信息的身份证中获取身份证信息
     *
     * @param author
     * @param material
     * @return
     */
    public String getIdCard(CrowdfundingAuthor author, CfFirsApproveMaterial material) {
        String cryptoIdCard = null;
        if (material != null) {
            cryptoIdCard = material.getPatientCryptoIdcard();
        } else if (author != null) {
            cryptoIdCard = author.getCryptoIdCard();
        } else {
            return StringUtils.EMPTY;
        }
        return shuidiCipher.decrypt(cryptoIdCard);
    }
    public CardMsg getCardMsg(String idCard) {
        CardMsg cardMsg = new CardMsg();
        if (StringUtils.isBlank(idCard)) {
            return cardMsg;
        }

        if (idCard.length() == 15) {
            int age = calculateAge(idCard);
            cardMsg.setAge(age);
            String sexStr = parseGender(idCard);
            int sex = "男".equals(sexStr) ? 1 : "女".equals(sexStr) ? 2 : 0;
            cardMsg.setSex(sex);
            cardMsg.setIdCardFlag(true);
           return cardMsg;
        }
        IdcardInfoExtractor idcardInfoExtractor = new IdcardInfoExtractor(idCard);
        if (Objects.isNull(idcardInfoExtractor)) {
            return cardMsg;
        }
        LocalDate localDate = LocalDate.now();
        int age = localDate.getYear() - idcardInfoExtractor.getYear();
        cardMsg.setAge(age);

        int sex = "男".equals(idcardInfoExtractor.getGender()) ? 1 : "女".equals(idcardInfoExtractor.getGender()) ? 2 : 0;
        cardMsg.setSex(sex);

        cardMsg.setIdCardFlag(true);

        return cardMsg;
    }

    public static void main(String[] args) {
        IdcardInfoExtractor idcardInfoExtractor = new IdcardInfoExtractor("***************");
        if (Objects.isNull(idcardInfoExtractor)) {

        }
        LocalDate localDate = LocalDate.now();
        int age = localDate.getYear() - idcardInfoExtractor.getYear();
        System.out.println(1);

        String idCard = "***************";
        if (idCard.length() == 15) {
            int age1 = calculateAge(idCard);
            String sexStr = parseGender(idCard);
            int sex = "男".equals(sexStr) ? 1 : "女".equals(sexStr) ? 2 : 0;
            System.out.println(1);
        }
    }

    // 解析性别
    public static String parseGender(String idCardNumber) {
        String genderCode = idCardNumber.substring(14, 15);
        int genderValue = Integer.parseInt(genderCode);
        return genderValue % 2 == 0 ? "女" : "男";
    }

    // 计算年龄
    public static int calculateAge(String idCardNumber) {
        String yearOfBirth = "19" + idCardNumber.substring(6, 8);
        int currentYear = LocalDate.now().getYear();
        return currentYear - Integer.parseInt(yearOfBirth);
    }

    public boolean isBaseInfoRejected(Map<Integer, Integer> infoStatusMap) {
        if (infoStatusMap == null || infoStatusMap.size() == 0) {
            return false;
        }

        return isDataRejected(infoStatusMap, CrowdfundingInfoDataStatusTypeEnum.BASE_INFO_SUBMIT.getCode());
    }

    public boolean isDreamBaseInfoRejected(Map<Integer, Integer> infoStatusMap) {
        if (infoStatusMap == null || infoStatusMap.size() == 0) {
            return false;
        }

        return isDataRejected(infoStatusMap, CrowdfundingInfoDataStatusTypeEnum.dreamBaseInfoSubmit.getCode());
    }

    public boolean isExtInfoRejected(Map<Integer, Integer> infoStatusMap) {
        if (infoStatusMap == null || infoStatusMap.size() == 0) {
            return false;
        }

        return isDataRejected(infoStatusMap, CrowdfundingInfoDataStatusTypeEnum.PATIENT_INFO_SUBMIT.getCode())
                || isDataRejected(infoStatusMap, CrowdfundingInfoDataStatusTypeEnum.PAYEE_INFO_SUBMIT.getCode())
                || isDataRejected(infoStatusMap, CrowdfundingInfoDataStatusTypeEnum.TREATMENT_INFO_SUBMIT.getCode())
                //此处不需要兼容新旧案例
                || isDataRejected(infoStatusMap, CrowdfundingInfoDataStatusTypeEnum.CREDIT_SUPPLEMENT_INFO_SUBMIT.getCode());
    }

    public boolean isDreamExtInfoRejected(Map<Integer, Integer> infoStatusMap) {
        if (infoStatusMap == null || infoStatusMap.size() == 0) {
            return false;
        }

        return isDataRejected(infoStatusMap, CrowdfundingInfoDataStatusTypeEnum.confirmSubmit.getCode())
                || isDataRejected(infoStatusMap, CrowdfundingInfoDataStatusTypeEnum.originatorSubmit.getCode());
    }

    private boolean isDataRejected(Map<Integer, Integer> infoStatusMap, int type) {
        if (!infoStatusMap.containsKey(type)) {
            return false;
        }

        Integer infoStatus = infoStatusMap.get(type);
        if (infoStatus == null) {
            return false;
        }

        return CrowdfundingInfoStatusEnum.REJECTED.getCode() == infoStatus;
    }

    public String getPatientName(CrowdfundingAuthor author, CfFirsApproveMaterial material) {

        if (author != null) {
            return author.getName();
        }

        if (material != null) {
            return material.getPatientRealName();
        }

        return Strings.EMPTY;
    }

    public String getRaiserName(CfFirsApproveMaterial material) {
        if (material == null) {
            return Strings.EMPTY;
        }

        return StringUtils.isNotBlank(material.getSelfRealName()) ? material.getSelfRealName() :
                material.getPatientRealName();
    }

    public CfSimpleVoWithFirstStatus getNoEndCaseByInfoUuid(String infoUuid, Date payTime) {

        CrowdfundingInfo caseInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);

        if (caseInfo == null || (caseInfo.getEndTime() != null && caseInfo.getEndTime().before(new Date()))) {
            return null;
        }
        CfInfoExt infoExt = cfInfoExtBiz.getByInfoUuid(infoUuid);
        if (infoExt == null) {
            return null;
        }

        return buildSimpleVo(caseInfo, infoExt, payTime);
    }

    public List<CfSimpleVoWithFirstStatus> getNoEndCaseByUserId(long userId, Date payTime) {
        List<CrowdfundingInfo> allCaseInfo = crowdfundingInfoBiz.getCrowdfundingInfoListByUserId(userId);

        if (CollectionUtils.isEmpty(allCaseInfo)) {
            return Lists.newArrayList();
        }

        // 过滤出没有结束的数据
        Date currDate = new Date();
        allCaseInfo = allCaseInfo.stream().
                filter(item -> item.getEndTime() != null && item.getEndTime().after(currDate)).collect(Collectors.toList());

        List<Integer> caseIds = allCaseInfo.stream().map(CrowdfundingInfo::getId).collect(Collectors.toList());
        Map<Integer, CfInfoExt> infoExtMap = cfInfoExtBiz.getCaseIdMapping(caseIds);
        if (MapUtils.isEmpty(infoExtMap)) {
            return Lists.newArrayList();
        }

        List<CfSimpleVoWithFirstStatus> simpleVoResult = Lists.newArrayList();
        for (CrowdfundingInfo info : allCaseInfo) {
            CfInfoExt currExt = infoExtMap.get(info.getId());
            if (currExt != null) {
                simpleVoResult.add(buildSimpleVo(info, currExt, payTime));
            }
        }

        return simpleVoResult;
    }

    private CfSimpleVoWithFirstStatus buildSimpleVo(CrowdfundingInfo caseInfo, CfInfoExt infoExt,
                                                    Date payTime) {
        CfSimpleVoWithFirstStatus simpleVo = new CfSimpleVoWithFirstStatus();
        simpleVo.setCaseId(caseInfo.getId());
        simpleVo.setInfoUuid(caseInfo.getInfoId());
        simpleVo.setTitle(caseInfo.getTitle());
        simpleVo.setTitleImg(caseInfo.getTitleImg());
        simpleVo.setCreateTime(caseInfo.getCreateTime());
        simpleVo.setInitialStatus(FirstApproveStatusEnum.parse(infoExt.getFirstApproveStatus()));
        simpleVo.setContent(caseInfo.getContent());
        simpleVo.setDonateUserCount(crowdfundingOrderBiz.donatorCountByCaseIdAndPayTime(caseInfo.getId(), payTime));


        return simpleVo;
    }


    public void hasFollow(long userId, List<CrowdfundingUserView> views) {

        if (CollectionUtils.isEmpty(views)) {
            return;
        }

        List<Long> userIds = views.stream().map(r -> Long.valueOf(r.getUserId())).collect(Collectors.toList());

        GetUserAttentionParam param = new GetUserAttentionParam();
        param.setUser(userId);
        param.setUserIds(userIds);

        Response<Set<Long>> response = cfUserAttentionClient.getAttentions(param);
        LOGGER.debug("order hasFollow param={},response{}", JSON.toJSONString(param), JSON.toJSONString(response));
        if (response != null && response.getCode() == 0 && response.getData() != null) {
            Set<Long> userSet = response.getData();
            views.stream().forEach(r -> {
                if (userSet.contains(Long.valueOf(r.getUserId()))) {
                    r.setFollow(true);
                }
            });
        }
    }

    public List<CrowdfundingUserView> fillUpListData(List<CrowdfundingOrder> orders) {

        if (CollectionUtils.isEmpty(orders)) {
            return Lists.newArrayList();
        }
        List<CrowdfundingUser> userList = this.convert2CrowdfundingUserFromUserThird(orders);
        // 1.根据UserThird填充用户信息
        this.crowdfundingUserBiz.getUserInfo(userList);
        List<CrowdfundingUserView> lists = Lists.newArrayList();
        for (CrowdfundingUser user : userList) {
            CrowdfundingUserView crowdfundingUserView = new CrowdfundingUserView();
            crowdfundingUserView.setNickname(user.getNickname());
            crowdfundingUserView.setHeadImgUrl(user.getHeadImgUrl());
            List<CrowdfundingOrder> filterOrders = orders.stream()
                    .filter(i -> Objects.equals(i.getId(), user.getOrderId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterOrders)) {
                crowdfundingUserView.setTotalAmount(filterOrders.get(0).getAmount() / 100);
                crowdfundingUserView.setPayTime(filterOrders.get(0).getPayTime());
                crowdfundingUserView.setUserId(filterOrders.get(0).getUserId());
            }

            lists.add(crowdfundingUserView);
        }
        return lists;
    }

    /**
     * 构建CrowdfundingBaseInfo
     *
     * @param param
     * @return
     */
    private CrowdfundingBaseInfo buildCrowdfundingBaseInfo(CrowdfundingBaseInfoParam param) {

        CfFirsApproveMaterial material = param.getMaterial();
        CrowdfundingAuthor author = param.getAuthor();
        String idCard = this.getIdCard(author, material);
        CardMsg cardMsg = this.getCardMsg(idCard);
        cardMsg.setProvinceName(this.getAuthorProvince(idCard));
        param.setCardMsg(cardMsg);


        CrowdfundingInfo crowdfundingInfo = param.getCrowdfundingInfo();
        if (crowdfundingInfo.getContentType() == CfContentTypeEnum.AS_FRAGMENT.getValue()) {
            CrowdfundingSeekHelpInfoFragment infoFragment = cfSeekHelpInfoFragmentBiz.get(crowdfundingInfo.getInfoId());
            if (Objects.nonNull(infoFragment)) {
                infoFragment.setUserId(0L);
            }
            param.setCrowdfundingSeekHelpInfoFragment(infoFragment);
        }


        CfInfoExt cfInfoExt = param.getCfInfoExt();
        if (cfInfoExt != null) {
            try {
                FirstApproveStatusEnum firstApproveStatusEnum = FirstApproveStatusEnum.APPLY_SUCCESS;
                firstApproveStatusEnum = FirstApproveStatusEnum.parse(cfInfoExt.getFirstApproveStatus());
                // 本人访问 并且初审未通过 则可以展示未审核的最新内容
                boolean isSelf = param.isSelf();
                if (isSelf && FirstApproveStatusEnum.isNotPassed(firstApproveStatusEnum)) {
                    CaseInfoApproveStageDO stageInfo = caseInfoApproveStageService.getByCaseId(crowdfundingInfo.getId());
                    param.setStageInfo(stageInfo);
                }
            } catch (Exception e) {
                LOGGER.error("案例首次审核状态的值不对, value:{}", cfInfoExt.getFirstApproveStatus(), e);
            }
        }


        // 筹款详情图片掩码处理
        if (MASK_IMAGE_SWITCH) {
            Map<AttachmentTypeEnum, List<CrowdfundingAttachmentVo>> attachmentMap = param.getAttachmentMap();
            List<CrowdfundingAttachmentVo> originalAttachments = attachmentMap.get(AttachmentTypeEnum.ATTACH_CF);
            sortAttachment(originalAttachments, crowdfundingInfo);
            List<String> attachments = cfAiMaskImageService.imageMaskProcess(originalAttachments, crowdfundingInfo);
            param.setAttachments(attachments);
        }

        return new CrowdfundingBaseInfo(param);
    }


    /**
     * 案例详情页图片排序
     */
    private void sortAttachment(List<CrowdfundingAttachmentVo> attachments, CrowdfundingInfo crowdfundingInfo) {
        moveHeadPictureAtFirst(attachments, crowdfundingInfo);
    }
    // 头图移到第一位
    private void moveHeadPictureAtFirst(List<CrowdfundingAttachmentVo> attachments, CrowdfundingInfo crowdfundingInfo) {
        AdminCaseDetailsMsg adminCaseDetailsMsg = adminCaseDetailsMsgDao.getByCaseId(crowdfundingInfo.getId());
        if (adminCaseDetailsMsg == null || CollectionUtils.isEmpty(attachments)) {
            return;
        }
        String headPictureUrl = adminCaseDetailsMsg.getHeadPictureUrl();
        if (StringUtils.isEmpty(headPictureUrl)) {
            return;
        }
        AnalysisUrl analysisUrl = AnalysisUrl.parse(headPictureUrl);
        analysisUrl.setHost(CfDomainEnum.COS_IMAGE.getDomain());
        String cosImageDomain = analysisUrl.toUrlString();
        analysisUrl.setHost(CfDomainEnum.COS_IMAGES.getDomain());
        String cosImagesDomain = analysisUrl.toUrlString();
        if (attachments.stream().noneMatch(obj -> obj.getUrl().equals(cosImageDomain) || obj.getUrl().equals(cosImagesDomain))) {
            return;
        }
        for (int i = 0;i < attachments.size(); i++) {
            CrowdfundingAttachmentVo attachment = attachments.get(i);
            if (StringUtils.equalsAny(attachment.getUrl(), cosImageDomain, cosImagesDomain)) {
                CrowdfundingAttachmentVo headPictureAttachment = attachments.remove(i);
                attachments.add(0, headPictureAttachment);
                return;
            }
        }
    }
    /**
     * 构建CrowdfundingTreatmentInfo
     *
     * @param param
     * @return
     */
    private CrowdfundingTreatmentInfo buildCrowdfundingTreatmentInfo(CrowdfundingTreatmentInfoParam param) {

        Map<String, List<String>> materialValueMap = param.getMaterialValueMap();
        List<String> firstApproveSpecialReportList = materialValueMap.get(MaterialExtKeyConst.first_approve_special_report);
        if (CollectionUtils.isNotEmpty(firstApproveSpecialReportList)) {
            boolean specialReport = firstApproveSpecialReportList.stream()
                    .map(Boolean::valueOf)
                    .findFirst()
                    .orElse(false);
            param.setSpecialReport(specialReport);
        }


        long contextUserId = param.getUserId();
        CrowdfundingInfo crowdfundingInfo = param.getCrowdfundingInfo();
        CfFirsApproveMaterial material = param.getCfFirsApproveMaterial();
        String medicalTreatmentPhoto = crowdFundingProgressBiz.getMedicalTreatmentPhoto(crowdfundingInfo, material, contextUserId, param.isSpecialReport());
        param.setMedicalTreatmentPhoto(medicalTreatmentPhoto);


        //获取掩码后的诊断材料
        List<String> attachmentList = Lists.newArrayList();
        if (CrowdfundingStatus.CROWDFUNDING_STATED == crowdfundingInfo.getStatus()) {
            // 筹款详情图片掩码处理
            if (MASK_IMAGE_SWITCH) {
                List<String> materialAttachments = Lists.newArrayList();
                Map<AttachmentTypeEnum, List<CrowdfundingAttachmentVo>> attachmentMap = param.getAttachmentMap();
                medicalMaterialMask(materialAttachments, attachmentMap, crowdfundingInfo.getId());
                attachmentList = materialAttachments;
            } else {
                OperationResult<List<CfCasePublicInfo>> operationResult = imagePublicWorkOrderFeignClient.getImagePublicInfo(crowdfundingInfo.getInfoId());
                List<CfCasePublicInfo> casePublicInfoList = Optional.ofNullable(operationResult)
                        .filter(OperationResult::isSuccess).map(OperationResult::getData).orElse(Lists.newArrayList());
                if (CollectionUtils.isNotEmpty(casePublicInfoList)) {
                    attachmentList = casePublicInfoList.stream().filter(info -> (0 == info.getSwitchInfo() &&
                                    CfCasePublicInfoTypeEnum.TREATMENT_IMAGE.getCode() == info.getType()))
                            .map(CfCasePublicInfo::getImageUrl).collect(Collectors.toList());
                }
            }
        }
        param.setAttachmentList(attachmentList);

        return new CrowdfundingTreatmentInfo(param);
    }

    /**
     * 构建CrowdfundingUserInfo
     *
     * @param userInfoModel
     * @return
     */
    public CrowdfundingUserInfo buildCrowdfundingUserInfo(UserInfoModel userInfoModel) {

        CrowdfundingUserInfo crowdfundingUserInfo = crowdfundingUserInfoMapper.toEntity(userInfoModel);

        if (Objects.nonNull(crowdfundingUserInfo)) {
            crowdfundingUserInfo.setUserId(0);
            crowdfundingUserInfo.setMobile(StringUtils.EMPTY);
            crowdfundingUserInfo.setCryptoMobile(StringUtils.EMPTY);
            crowdfundingUserInfo.setCryptoIdCard(StringUtils.EMPTY);
            crowdfundingUserInfo.setIdCard(StringUtils.EMPTY);
            //如果没有头像，则设置为空串
            if (StringUtils.isBlank(crowdfundingUserInfo.getHeadImgUrl())) {
                crowdfundingUserInfo.setHeadImgUrl("");
            }
        }

        return crowdfundingUserInfo;
    }

    private void updateCrowdfundingInfoByCfInfoStat(CrowdfundingInfo crowdfundingInfo, CfInfoStat cfInfoStat) {
        if (cfInfoStat == null) {
            return;
        }

        crowdfundingInfo.setAmount(Optional.ofNullable(cfInfoStat.getAmount()).orElse(0));
        crowdfundingInfo.setDonationCount(Optional.ofNullable(cfInfoStat.getDonationCount()).orElse(0));
    }

    private CfInfoStatParam buildCfInfoStatParam(CfInfoStat cfInfoStat) {

        CfInfoStatParam cfInfoStatParam = new CfInfoStatParam();

        if (cfInfoStat == null) {
            return cfInfoStatParam;
        }

        cfInfoStatParam.setShareCount(Optional.ofNullable(cfInfoStat.getShareCount()).orElse(0));

        if (Objects.nonNull(cfInfoStat.getDonationCount()) && cfInfoStat.getDonationCount() != 0) {
            cfInfoStatParam.setDonatorCount(Optional.ofNullable(cfInfoStat.getDonatorCount()).orElse(0));
        }

        return cfInfoStatParam;
    }

    /**
     * 获取拒绝原因
     *
     * @param infoUuid
     * @param caseId
     * @return
     */
    private int getRejectType(String infoUuid, int caseId) {
        CfRefuseStatus refuseStatus = this.cfRefuseStatusBiz.getCfRefuseStatusByInfoId(infoUuid, caseId);
        return (refuseStatus == null ? CFRefuseType1StatusEnum.OK.getCode() : refuseStatus.getType1());
    }

    /**
     * 获取打款完成时间
     *
     * @param cfFinanceCapitalStatusVo
     * @return
     */
    private Timestamp getFinishTime(CfFinanceCapitalStatusVo cfFinanceCapitalStatusVo) {
        Timestamp finishTime = null;
        if (cfFinanceCapitalStatusVo != null && cfFinanceCapitalStatusVo.getDrawFinishTime() != null && cfFinanceCapitalStatusVo.getDrawStatus() == CfDrawCashConstant.DrawStatus.HANDLE_SUCCESS.getCode()) {
            finishTime = new Timestamp(cfFinanceCapitalStatusVo.getDrawFinishTime().getTime());
        }
        return finishTime;
    }

    /**
     * 自定义关系展示
     *
     * @param userInfoModel
     * @param caseId
     * @param cfInfoExt
     * @param author
     * @param material
     * @return
     */
    private String getCustomRelationDesc(UserInfoModel userInfoModel, int caseId, CfInfoExt cfInfoExt, CrowdfundingAuthor author, CfFirsApproveMaterial material) {
        return customRelationService.getFundingShowDesc(caseId, cfInfoExt, author, material);
    }

    /**
     * 获取用户&案例纬度的统计数据
     *
     * @param contextUserId
     * @param caseId
     * @return
     */
    private FundingInfoVo.CfUserCaseBaseStatVo getCfUserCaseBaseStatVo(long contextUserId, int caseId) {

        FundingInfoVo.CfUserCaseBaseStatVo cfUserCaseBaseStatVo = new FundingInfoVo.CfUserCaseBaseStatVo();
        CfUserCaseBaseStatDO cfUserCaseBaseStatDO = cfUserCaseBaseStatService.selectByUserAndCase(contextUserId, caseId);
        if (Objects.nonNull(cfUserCaseBaseStatDO)) {
            cfUserCaseBaseStatVo.setDonateAmount(cfUserCaseBaseStatDO.getDonateAmount());
            cfUserCaseBaseStatVo.setShareCount(cfUserCaseBaseStatDO.getShareCount());
            cfUserCaseBaseStatVo.setShareBroughtAmount(cfUserCaseBaseStatDO.getShareBroughtAmount());
            cfUserCaseBaseStatVo.setShareFirstAmount(cfUserCaseBaseStatDO.getShareFirstAmount());
        }

        Integer shareVisitCount = loveRankCacheService.getShareVisitCount(caseId, contextUserId);
        cfUserCaseBaseStatVo.setShareVisitCount(shareVisitCount);

        return cfUserCaseBaseStatVo;
    }

    private void buildLoginSceneInfo(FundingInfoVo result, LoginSceneInfoParam param) {

        long contextUserId = param.getContextUserId();
        String infoUuid = param.getInfoUuid();
        int infoId = param.getInfoId();

        //案例是否有人证实
        boolean hasVerified = crowdFundingVerificationBiz.hasVerified(contextUserId, infoUuid);
        result.setHasVerified(hasVerified);

        //一念之善 访问案例次数
        String key = RedisKeyCons.CF_API_VISIT_COUNT + "_" + contextUserId + "_" + infoId + "_" + DateUtil.getCurrentDateStr();
        long visitCount = cfRedissonHandler.incr(key, RedisKeyCons.CF_INFO_KEY_USER_BANK_VERIFY_TIME);
        result.setTodayVisitCount(visitCount);


        //获取访问人对该案例的有效捐款信息
        CrowdfundingOrder crowdfundingOrder = this.crowdfundingOrderBiz.getUserOrderByUserId(infoId, contextUserId);
        boolean isHelp = crowdfundingOrder != null;
        //用户是否捐款
        result.setHelp(isHelp);
        //用户当天是否捐款
        result.setTodayHelp(isHelp && DateUtil.getFirstTimeOfDay(System.currentTimeMillis()).before(crowdfundingOrder.getCtime()));
    }

    /**
     * 获取收款人信息
     */
    private CrowdfundingPayeeBaseInfoVo getPayeeInfo(CrowdfundingInfo crowdfundingInfo) {

        if (crowdfundingInfo.getRelationType() == CrowdfundingRelationType.LOCATION_HOSPITAL_ACCOUNT) {
            CrowdfundingInfoHospitalPayee crowdfundingInfoHospitalPayee = crowdfundingInfoHospitalPayeeBiz.getByInfoUuid(crowdfundingInfo.getInfoId());
            if (crowdfundingInfoHospitalPayee != null) {
                return new CrowdfundingPayeeBaseInfoVo(crowdfundingInfoHospitalPayee.getHospitalAccountName(),
                        CrowdfundingRelationType.LOCATION_HOSPITAL_ACCOUNT, crowdfundingInfoHospitalPayee.getHospitalBankBranchName(), getBankCard(crowdfundingInfoHospitalPayee.getHospitalBankCard()));
            }
        } else if (crowdfundingInfo.getRelationType() == CrowdfundingRelationType.charitable_organization) {
            CfCharityPayee cfCharityPayee = cfCharityPayeeBiz.getCfCharityPayeeByUUid(crowdfundingInfo.getInfoId());
            if (cfCharityPayee != null) {
                return new CrowdfundingPayeeBaseInfoVo(cfCharityPayee.getOrgName(),
                        CrowdfundingRelationType.charitable_organization, cfCharityPayee.getOrgBankName(), getBankCard(cfCharityPayee.getOrgBankCard()));
            }
        } else {
            crowdfundingInfo.setPayeeBankCard(getBankCard(crowdfundingInfo.getPayeeBankCard()));
            return new CrowdfundingPayeeBaseInfoVo(crowdfundingInfo);
        }

        return null;
    }

    /**
     * 判断核实医院与就诊医院是否一致逻辑 ->审核状态已通过 + 名称包含匹配
     * 大部分数据 如有院区之分 核实表会在医院名称后追加院区 eg: 山东大学第二医院（中心院区） 就诊表 eg : 山东大学第二医院
     */
    private boolean isTreatmentHospitalIsSameAuditHospital(CfHospitalAuditInfo cfHospitalAuditInfo, CrowdfundingTreatment treatment) {
        int auditStatus = cfHospitalAuditInfo.getAuditStatus();
        String auditHospitalName = cfHospitalAuditInfo.getHospitalName();
        String treatmentHospitalName = treatment.getHospitalName();

        if (StringUtils.isEmpty(auditHospitalName) || StringUtils.isEmpty(treatmentHospitalName)) {
            return false;
        }

        return auditHospitalName.contains(treatmentHospitalName) && CrowdfundingInfoStatusEnum.PASSED.getCode() == auditStatus;
    }


    /**
     * 案例页辟谣公告栏
     *
     * @return
     */
    private List<FundingInfoVo.CfCasePageAnnouncementManageVo> getCfCasePageAnnouncementManageList() {
        List<CfCasePageAnnouncementManage> casePageAnnouncementManageList = cfCasePageAnnouncementManageService.getList();
        return cfCasePageAnnouncementManageMapper.toList(casePageAnnouncementManageList);
    }


    /**
     * @param result
     * @param cfInfoExt
     */
    private void buildCfInfoExt(FundingInfoVo result, CfInfoExt cfInfoExt) {
        if (cfInfoExt == null) {
            return;
        }
        result.setFinishReason(cfInfoExt.getFinishStatus());
        result.setCfVersion(cfInfoExt.getCfVersion());
        //返回运营填写的文案   如果没写  前端继续展示 默认文案  把影响消除到最小
        result.setFinishStr(cfInfoExt.getFinishStr());
        result.setNoHandlingFee(cfInfoExt.getNoHandlingFee());

        FirstApproveStatusEnum firstApproveStatusEnum = FirstApproveStatusEnum.APPLY_SUCCESS;
        try {
            firstApproveStatusEnum = FirstApproveStatusEnum.parse(cfInfoExt.getFirstApproveStatus());
        } catch (Exception e) {
            LOGGER.error("案例首次审核状态的值不对, value:{}", cfInfoExt.getFirstApproveStatus(), e);
        }
        result.setFirstApproveStatus(firstApproveStatusEnum);
    }

    private void buildCreditEnhancement(FundingInfoVo result, CfInfoExt cfInfoExt, CfPropertyInsuranceInfoModel materialObject, CrowdfundingInfo crowdfundingInfo, CrowdfundingAuthor crowdfundingAuthor) {
        if (cfInfoExt == null) {
            return;
        }
        // 添加首次审核的状态
        FirstApproveStatusEnum firstApproveStatusEnum = FirstApproveStatusEnum.APPLY_SUCCESS;
        try {
            firstApproveStatusEnum = FirstApproveStatusEnum.parse(cfInfoExt.getFirstApproveStatus());
        } catch (Exception e) {
            LOGGER.error("案例首次审核状态的值不对, value:{}", cfInfoExt.getFirstApproveStatus(), e);
        }

        // 初审就提交了增信
        if (CfVersion.isInitialProperty(cfInfoExt.getCfVersion())) {
            if (firstApproveStatusEnum == FirstApproveStatusEnum.APPLY_SUCCESS) {
                boolean isNewPlan = crowdfundingInfo.getCreateTime().getTime() > oldAndNewCreateTime;
                result.setInsuranceModelVo(insuranceAuditService.buildVOWithInitialAuditInfo(crowdfundingInfo.getId(), materialObject, crowdfundingInfo.getMaterialPlanId(), isNewPlan));
            }
        } else {
            // 旧案例提交的新增信信息
            if (CfVersion.isOldCaseSubmitNewInsurance(cfInfoExt.getCfVersion(), cfInfoExt.getNeedCaseList())) {
                RiverReviewDO reviewDO = riverReviewService.getByCaseIdAndUsageType(crowdfundingInfo.getId(),
                        RiverUsageTypeEnum.CREDIT_INFO.getValue());
                if (reviewDO != null && reviewDO.getInfoStatus() == RiverStatusEnum.PASS.getValue()) {
                    CfPropertyInsuranceVO insuranceModel = CfPropertyInsuranceVO.buildInsuranceModel(materialObject);
                    if (insuranceModel != null) {
                        insuranceModel.setMaterialStatus(InitialAuditItem.MaterialStatus.PASS);
                        insuranceModel.setRejectDetail(Maps.newHashMap());
                    }
                    result.setInsuranceModelVo(insuranceModel);
                }
                result.setInsuranceStatus(reviewDO == null ? 0 : reviewDO.getInfoStatus());

            } else {
                OpResult<CreditSupplementModel> opResult = OpResult.createFailResult(CfErrorCode.PARAM_INVALID);
                // 以前的逻辑
                if (cfInfoExt.getCfVersion() == CfVersion.definition_20181131.getCode()) {
                    opResult = materialReadService.getCreditSupplementByInfo(crowdfundingInfo);
                } else {
                    opResult = materialReadService.getCreditSupplementWithAuthor(crowdfundingInfo, crowdfundingAuthor);
                }

                if (opResult.isSuccess()) {
                    result.setCreditSupplementModel(opResult.getData());
                    result.setCreditSupplements(opResult.getData().getCreditSupplements());
                } else {
                    result.setCreditSupplementModel(null);
                }
            }
        }
    }

    /**
     * 案例结束状态
     */
    private void buildCaseEndStatus(FundingInfoVo result, CrowdfundingInfo crowdfundingInfo, CfInfoExt cfInfoExt) {
        Date endTime = crowdfundingInfo.getEndTime();
        int displayStatus = this.crowdfundingInfoService.getStatusAfterFinish(cfInfoExt, endTime);
        if (endTime != null && endTime.after(new Date())) {
            result.setHasFinished(false);
        } else {
            displayStatus = (displayStatus == 0 ? CfDisplayStatusEnum.FINISH_EXPIRE.getValue() : displayStatus);
            result.setHasFinished(true);
            //风险案例结束标识
            boolean checkCaseStatus = crowdfundingInfoBiz.checkCaseInitialAudit(crowdfundingInfo.getId());
            result.setCaseEndFlag(checkCaseStatus);
            try {
                buildPlatformCaseEndReason(result, cfInfoExt);
                if (displayStatus == CfDisplayStatusEnum.FINISH_BY_OPERATOR.getValue() || displayStatus == CfDisplayStatusEnum.REFUND_APPLIED.getValue() ||
                displayStatus == CfDisplayStatusEnum.REFUND_BY_OPERATOR.getValue() || displayStatus == CfDisplayStatusEnum.REFUND_PROCESSING.getValue() || displayStatus == CfDisplayStatusEnum.REFUND_FOR_REJECTED_CASE_PROCESSING.getValue()) {
                    completePlatformCaseEndReason(result, cfInfoExt);
                }
            } catch (Exception e) {
                LOGGER.info("get-funding-info buildCaseEndStatus {}", cfInfoExt.getCaseId(), e);
            }
        }
        result.setDisplayStatus(displayStatus);
    }

    private void buildPlatformCaseEndReason(FundingInfoVo result, CfInfoExt cfInfoExt) {
        if (MapUtils.isEmpty(cfEndReasonVOMap)) {
            return;
        }
        if (cfInfoExt.getFinishStatus() != CfFinishStatus.FINISH_BY_SHUIDI.getValue()) {
            return;
        }
        CaseEndRecordDO caseEndRecordDO = caseEndRecordService.getLastByCaseId(cfInfoExt.getCaseId());
        if (Objects.isNull(caseEndRecordDO) || caseEndRecordDO.getFinishStatus() != CfFinishStatus.FINISH_BY_SHUIDI.getValue()) {
            return;
        }
        String description = caseEndRecordDO.getDescription();
        if (StringUtils.isEmpty(description) || !description.contains(":")) {
            return;
        }
        String endTitle = description.contains("：") ? description.substring(description.indexOf(":") + 1, description.indexOf("：")) : description.substring(description.indexOf(":") + 1);
        CfEndReasonVO reasonConfig = cfEndReasonVOMap.get(endTitle);
        CfEndReasonVO endReasonVO = new CfEndReasonVO();
        if (Objects.isNull(reasonConfig)) {
            // 默认案例结束文案
            endReasonVO.setEndTitle("平台已停止该案例筹款");
            endReasonVO.setEndDesc("案例存在风险，为保证捐款人权益，平台已停止筹款，正在跟进中");
            if (cfInfoExt.getRefundStatus() == CfRefundStatus.REFUND_BY_PLATFORM_HANDLED.getValue() || cfInfoExt.getRefundStatus() == CfRefundStatus.REFUND_USER_APPLIED_HANDLED.getValue()) {
                //todo: 风控案例临时处理，后续上产品功能
                if (!StringUtils.equals("2cf1c5f3-04f0-491d-8d98-7348d3bc1517", cfInfoExt.getInfoUuid())) {
                    endReasonVO.setEndTitle(endReasonVO.getEndTitle() + "\n相关款项已退回");
                    endReasonVO.setEndDesc(endReasonVO.getEndDesc() + "，相关款项已退回");  
                }
            }
            return;
        }
        endReasonVO.setEndConfig(reasonConfig.getEndConfig());
        endReasonVO.setEndTitle(reasonConfig.getEndTitle());
        endReasonVO.setEndDesc(reasonConfig.getEndDesc());
        // 自定义案例详情页的C端展示逻辑
        if (StringUtils.equals("自定义案例详情页提示", reasonConfig.getEndConfig())) {
            endReasonVO.setEndDesc(description.substring(description.indexOf("：") + 1));
        }
        // 带退款的案例结束文案
        if (cfInfoExt.getRefundStatus() == CfRefundStatus.REFUND_BY_PLATFORM_HANDLED.getValue() || cfInfoExt.getRefundStatus() == CfRefundStatus.REFUND_USER_APPLIED_HANDLED.getValue()) {
            //todo: 风控案例临时处理，后续上产品功能
            if (!StringUtils.equals("2cf1c5f3-04f0-491d-8d98-7348d3bc1517", cfInfoExt.getInfoUuid())) {
                endReasonVO.setEndTitle(endReasonVO.getEndTitle() + "\n相关款项已退回");
                endReasonVO.setEndDesc(endReasonVO.getEndDesc() + "，相关款项已退回");  
            }
        }
        result.setPlatformFinishReason(endReasonVO);
    }

    private void completePlatformCaseEndReason(FundingInfoVo result, CfInfoExt cfInfoExt) {
        if (MapUtils.isEmpty(cfEndReasonVOMap)) {
            return;
        }
        if (cfInfoExt.getFinishStatus() != CfFinishStatus.FINISH_BY_SHUIDI.getValue()) {
            return;
        }
        CaseEndRecordDO caseEndRecordDO = caseEndRecordService.getLastByCaseId(cfInfoExt.getCaseId());
        if (Objects.isNull(caseEndRecordDO) || caseEndRecordDO.getFinishStatus() != CfFinishStatus.FINISH_BY_SHUIDI.getValue()) {
            return;
        }
        String description = caseEndRecordDO.getDescription();
        if (StringUtils.isEmpty(description) || !description.contains(":")) {
            return;
        }
        String endTitle = description.contains("：") ? description.substring(description.indexOf(":") + 1, description.indexOf("：")) : description.substring(description.indexOf(":") + 1);
        CfEndReasonVO reasonConfig = cfEndReasonVOMap.get(endTitle);
        CfEndReasonVO endReasonVO = result.getPlatformFinishReason();
        if (Objects.isNull(reasonConfig)) {
            endReasonVO.setTestTitle(endReasonVO.getEndTitle());
            endReasonVO.setTestEndDesc(endReasonVO.getEndDesc());
            if (cfInfoExt.getRefundStatus() == CfRefundStatus.REFUND_BY_PLATFORM_HANDLED.getValue() || cfInfoExt.getRefundStatus() == CfRefundStatus.REFUND_USER_APPLIED_HANDLED.getValue()) {
                endReasonVO.setTestEndDesc(endReasonVO.getEndDesc());
            }
            return;
        }
        endReasonVO.setTestTitle(reasonConfig.getEndTitle());
        endReasonVO.setTestEndDesc(reasonConfig.getEndDescV1());
        // 自定义案例详情页的C端展示逻辑
        if (StringUtils.equals("自定义案例详情页提示", reasonConfig.getEndConfig())) {
            endReasonVO.setTestEndDesc(endReasonVO.getEndDesc());
        }
        if (cfInfoExt.getRefundStatus() == CfRefundStatus.REFUND_BY_PLATFORM_HANDLED.getValue() || cfInfoExt.getRefundStatus() == CfRefundStatus.REFUND_USER_APPLIED_HANDLED.getValue()) {
            if (!StringUtils.equals("自定义案例详情页提示", reasonConfig.getEndConfig())) {
                endReasonVO.setTestEndDesc(reasonConfig.getEndDescV2());
            }
        }
        result.setPlatformFinishReason(endReasonVO);

    }

    /**
     * 医院相关信息
     *
     * @param result
     * @param cfInfoExt
     */
    private void buildHospitalInfo(FundingInfoVo result, CfInfoExt cfInfoExt, CrowdfundingTreatment treatment) {
        if (Objects.nonNull(cfInfoExt) && Objects.nonNull(cfInfoExt.getFirstApproveTime())) {

            // 增加医院核实信息展示条件
            // 标识位 医院核实时间控制开关
            try {
                LocalDateTime firstApproveLocalDateTime = cfInfoExt.getFirstApproveTime().toLocalDateTime();
                DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                LocalDateTime showHospitalAuditLocalDateTime = LocalDateTime.parse(showHospitalAuditTime, df);
                if (firstApproveLocalDateTime.isAfter(showHospitalAuditLocalDateTime)) {
                    result.setShowHospitalAuditTimeFlag(true);
                }
            } catch (Exception e) {
                LOGGER.info("showFlag format LocalDateTime error: {}", e.getMessage());
            }

            CfHospitalAuditInfo cfHospitalAuditInfo = cfHospitalAuditBiz.getByInfoUuid(cfInfoExt.getInfoUuid());
            if (Objects.nonNull(cfHospitalAuditInfo)) {

                boolean isHaveHospitalAudit = false;
                //医院核实消息
                if (cfHospitalAuditInfo.getAuditStatus() != CrowdfundingInfoStatusEnum.PASSED.getCode()) {
                    isHaveHospitalAudit = true;
                    result.setCfHospitalAuditInfoStatus(cfHospitalAuditInfo.getAuditStatus());
                }
                result.setHaveHospitalAudit(isHaveHospitalAudit);

                // 医院核实状态
                result.setAuditStatus(cfHospitalAuditInfo.getAuditStatus());
                // 医院核实信息
                FundingInfoVo.HospitalAuditInfo hospitalAuditInfo = buildHospitalAuditInfo(cfHospitalAuditInfo);
                result.setHospitalAuditInfo(hospitalAuditInfo);

                // 判断医院核实信息是否一致
                if (Objects.nonNull(treatment)) {
                    boolean isTreatmentHospitalIsSameAuditHospital = isTreatmentHospitalIsSameAuditHospital(cfHospitalAuditInfo, treatment);
                    result.setTreatMentHospitalIsSameAuditHospital(isTreatmentHospitalIsSameAuditHospital);
                }
            }
        }
    }

    /**
     * 缙云医保局“困难申报人员”已核验 true：展示；false：不展示
     */
    private boolean isJinYunLabel(FundingInfoVo result, CrowdfundingInfo crowdfundingInfo, String infoUuid) {
        if (authenticityIndicatorBiz.getMedicalInsurance(crowdfundingInfo.getMaterialPlanId(), result) == 1) {
            int res = caseLabelsJinYunCountyVerificationService.getCount(infoUuid);
            result.setJinYunLabel(res > 0);
            return res > 0;
        }
        return false;
    }

    /**
     * 获取该用户对该案例当天转发次数
     *
     * @return
     */
    private Integer getUserShareCountToday(int caseId, long contextUserId) {
        String redisKey = cfShareService.getRedisKey(caseId, contextUserId);
        int userShareCountToday = 0;
        Integer userShareCountTodayRedis = shareRedissonHandler.get(redisKey, Integer.class);
        if (userShareCountTodayRedis != null && contextUserId > 0) {
            userShareCountToday = userShareCountTodayRedis;
        }
        return userShareCountToday;
    }

    /**
     * 基金会案例标签
     *
     * @return
     */
    private FoundationLabelInfoVO getFoundationLabelInfo(String infoUuid) {
        return caseLabelsManagementService.getFoundationLabelInfo(infoUuid);
    }

    /**
     * 异步 获取支付成功后给用户返回的信息
     *
     * @param caseId
     * @return
     */
    private CompletableFuture<CfOrderPaySuccessPageVO> getCaseBaseDataCompletableFuture(int caseId) {

        return CompletableFuture.supplyAsync(() ->
        {

            CfCaseBaseData cfCaseBaseData = caseBaseDataFeignDelegate.getCaseBaseData(caseId);
            if (Objects.isNull(cfCaseBaseData)) {
                return null;
            }

            return CfOrderPaySuccessPageVO.builder()
                    .diseaseName(cfCaseBaseData.getDiseaseName())
                    .diseaseNorm(cfCaseBaseData.getDiseaseNorm())
                    .serviceTag(cfCaseBaseData.getServiceTag())
                    .build();

        }, olapExecutor);
    }

    /**
     * 异步 商业转发人管控
     *
     * @param contextUserId
     * @param caseId
     * @return
     */
    private CompletableFuture<BanInfoVo> getBanInfoVoCompletableFuture(long contextUserId, int caseId) {

        return CompletableFuture.supplyAsync(
                () -> commercialForwarderControlService.getBanInfoVo(contextUserId, caseId),
                getBanInfoVoExecutor
        );
    }

    /**
     * 控制案例展示详情的model
     *
     * @param caseId
     * @param contextUserId
     * @param selfTag
     * @return
     */
    private CompletableFuture<CfCaseVisitConfigVo> getCfCaseVisitConfigVoCompletableFuture(int caseId, long contextUserId, String selfTag) {

        return CompletableFuture.supplyAsync(
                () -> caseVisitConfigService.getByCaseIdV2(caseId, contextUserId, selfTag),
                getVisitConfigExecutor
        );
    }

    private void fillFundingInfoVo(FundingInfoVo fundingInfoVo, CfCaseVisitConfigVo cfCaseVisitConfigVo) {
        if (cfCaseVisitConfigVo == null || cfCaseVisitConfigVo.getNinetyDaysSwitch() == null) {
            return;
        }
        //求助人故事
        fundingInfoVo.getBaseInfo().setContent(null);
        //发起人姓名
//		fundingInfoVo.setRaiserName(null);
        //增信补充
        fundingInfoVo.setCreditSupplementModel(null);
        fundingInfoVo.setFundUseModel(null);
        fundingInfoVo.setLivingGuardVo(null);
    }

    /**
     * 获取支付成功后给用户返回的信息
     *
     * @return
     */
    private CfOrderPaySuccessPageVO getCfOrderPaySuccessPageVO(CompletableFuture<CfOrderPaySuccessPageVO> caseBaseDataCompletableFuture) {
        try {
            if (Objects.isNull(caseBaseDataCompletableFuture)) {
                return null;
            }

            CompletableFuture<CfOrderPaySuccessPageVO> completableFuture = caseBaseDataCompletableFuture.orTimeout(300, TimeUnit.MILLISECONDS);
            if (Objects.isNull(completableFuture)) {
                return null;
            }

            return completableFuture.get();
        } catch (Exception e) {
            LOGGER.error("获取支付成功后给用户返回的信息异常：{}", e.getMessage());
        }
        return null;
    }

    /**
     * 获取控制案例展示详情的model返回的信息
     *
     * @param cfCaseVisitConfigVoFuture
     * @return
     */
    private CfCaseVisitConfigVo getCfCaseVisitConfigVo(CompletableFuture<CfCaseVisitConfigVo> cfCaseVisitConfigVoFuture) {
        try {
            if (Objects.isNull(cfCaseVisitConfigVoFuture)) {
                return null;
            }

            CompletableFuture<CfCaseVisitConfigVo> completableFuture = cfCaseVisitConfigVoFuture.orTimeout(500, TimeUnit.MILLISECONDS);

            if (Objects.isNull(completableFuture)) {
                return null;
            }

            return completableFuture.get();
        } catch (Exception e) {
            LOGGER.error("getVisitConfig的信息异常：{}", e.getMessage());
        }
        return null;
    }

    /**
     * 获取商业转发人管控返回的信息
     *
     * @param banInfoVoCompletableFuture
     * @return
     */
    private BanInfoVo getBanInfoVo(CompletableFuture<BanInfoVo> banInfoVoCompletableFuture) {

        try {
            if (Objects.isNull(banInfoVoCompletableFuture)) {
                return null;
            }

            CompletableFuture<BanInfoVo> completableFuture = banInfoVoCompletableFuture.orTimeout(500, TimeUnit.MILLISECONDS);

            if (Objects.isNull(completableFuture)) {
                return null;
            }

            return completableFuture.get();
        } catch (Exception e) {
            LOGGER.error("getVisitConfig的信息异常：{}", e.getMessage());
        }
        return null;
    }

    private AnchorPageV2VO<CrowdfundingUserView> makeFakeAnchorId(AnchorPageV2VO<CrowdfundingUserView> pageV2VO) {

        if(pageV2VO == null || CollectionUtils.isEmpty(pageV2VO.getList())) {
            return pageV2VO;
        }

        //如果既不混淆orderId，也不混淆anchorid，则直接返回
        if(!fakeAnchorIdSwitch && !fakeOrderIdSwitch) {
            return pageV2VO;
        }

        long retAnchorId = pageV2VO.getAnchorId();

        //是否进行orderId投毒，如果投毒，则anchorId伪造
        Long returnAnchorId = Long.valueOf(retAnchorId);
        AnchorPageV2VO<CrowdfundingUserView> fakePageVo = pageV2VO;
        List<CrowdfundingUserView> crowdfundingUserViews = pageV2VO.getList();
        //TODO: 对orderId和anchorId进行编码
        if(fakeOrderIdSwitch) {
            if(!CollectionUtils.isEmpty(pageV2VO.getList())) {
                //对orderId进行投毒
                crowdfundingUserViews.forEach(crowdfundingUserView -> crowdfundingUserView.setId((long) (crowdfundingUserView.getId() * fakeOrderIdScale)));
            }
        }

        if(fakeAnchorIdSwitch) {
            fakePageVo.setAnchorId(MAX_FAKE_ANCHOR_ID + ConfusionUtils.confuseOrderId(returnAnchorId));
        }

        return pageV2VO;
    }

    private void resetIdOfPageVo(AnchorPageV2VO<CrowdfundingUserView> data){
        if (data == null){
            return;
        }
        List<CrowdfundingUserView> viewList = data.getList();
        if(CollectionUtils.isEmpty(viewList)) {
            return;
        }
        viewList.forEach(CrowdfundingUserView::resetId);
    }

    private long decodeFakeAnchorId(long fakeAnchorId) {
        //这里对比MAX_FAKE_ANCHOR_ID/2，为了防止confuse后的id有负数的情况
        if(fakeAnchorId > MAX_FAKE_ANCHOR_ID / 2) {
            return ConfusionUtils.recoverOrderId(fakeAnchorId - MAX_FAKE_ANCHOR_ID);
        }

        return fakeAnchorId;
    }

    public CfErrorCode checkParam(String infoUuid, Integer size) {
        if(StringUtils.isEmpty(infoUuid) || size == null || size < CrowdfundingCons.MIN_PAGE_SIZE
                || size > CrowdfundingCons.MAX_PAGE_SIZE) {
            return CfErrorCode.SYSTEM_PARAM_ERROR;
        }
        return CfErrorCode.SUCCESS;
    }
    /**
     * 检查数据库数据状态
     *
     * @param simpleModel
     * @param contextUserId
     * @return
     */
    public CfErrorCode checkDBParam(CfInfoSimpleModel simpleModel, long contextUserId) {

        if(simpleModel == null) {
            return CfErrorCode.CF_NOT_FOUND;
        }

        try {
            boolean inInfoIdBlack = cfBlackListBiz.isInInfoIdBlack(simpleModel.getId());
            // 黑名单对当前用户不生效
            if(contextUserId > 0 && simpleModel.getUserId() == contextUserId) {
                inInfoIdBlack = false;
            }
            if(inInfoIdBlack) {
                return CfErrorCode.CF_ERROR_IN_BLACK_LIST;
            }
        } catch (Exception e) {
            LOGGER.error("", e);
        }
        return CfErrorCode.SUCCESS;
    }

    //消除id
    public void resetIdOfResponse(Response<AnchorPageV2VO<CrowdfundingUserView>> response) {
        AnchorPageV2VO<CrowdfundingUserView> data = response.getData();
        if(data == null) {
            return;
        }
        resetIdOfPageVo(data);
    }

    private CrowdfundingBilingualVo getCrowdfundingBilingual(String infoUuid) {
        CaseDisplaySettingVo caseDisplaySettingVo = cfCaseDisplaySettingService.getByCaseIdOrInfoUuid(infoUuid, 0);
        CrowdfundingBilingualVo crowdfundingBilingualVo = null;
        if (caseDisplaySettingVo != null) {
            crowdfundingBilingualVo = new CrowdfundingBilingualVo();
            crowdfundingBilingualVo.setColor(caseDisplaySettingVo.getColor());
            crowdfundingBilingualVo.setDoubleLanguage(caseDisplaySettingVo.getLanguage());
        } else { // 根据身份证信息展示语言
            CfInfoSimpleModel fundingInfo = crowdfundingInfoSimpleBiz.getFundingInfo(infoUuid);
            if (Objects.isNull(fundingInfo)) {
                return null;
            }
            int caseId = fundingInfo.getId();
            crowdfundingBilingualVo = crowdfundingInfoService.xJJudgeResult(caseId);
        }
        return crowdfundingBilingualVo;
    }

    public CfPopUpWindowVo popUpWindow(String infoUuid) {
        CfPopUpWindowVo result = new CfPopUpWindowVo();

        if (StringUtils.isEmpty(infoUuid)) {
            return result;
        }
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfoFromSlave(infoUuid);
        if (crowdfundingInfo == null) {
            return result;
        }
        int infoId = crowdfundingInfo.getId();
        CfInfoExt cfInfoExt = cfInfoExtBiz.getByInfoUuidBySlaveAndMaster(infoUuid);

        //获取材料信息
        CfMaterialObject materialObject = materialCenterService.selectMaterialObjectByCaseId(infoId);
        CfFirsApproveMaterial material = materialObject.getFirstApproveMaterial();
        result.setRelationTypeForC(material != null ? material.getUserRelationTypeForC() : 0);

        //获取患者信息
        CrowdfundingAuthor author = crowdfundingAuthorBiz.get(infoId);
        //获取发起人用户信息
        UserInfoModel userInfoModel = this.userInfoDelegate.getUserInfoByUserId(crowdfundingInfo.getUserId());
        //构造发起人用户信息
        CrowdfundingUserInfo crowdfundingUserInfo = buildCrowdfundingUserInfo(userInfoModel);
        result.setCrowdfundingUserInfo(crowdfundingUserInfo);

        //自定义关系展示
        String customRelationDesc = getCustomRelationDesc(userInfoModel, crowdfundingInfo.getId(), cfInfoExt, author, material);
        result.setCustomRelationDesc(customRelationDesc);
        CrowdfundingBaseInfo baseInfo = new CrowdfundingBaseInfo();
        // 构造 baseInfo
        String idCard = this.getIdCard(author, material);
        CardMsg cardMsg = this.getCardMsg(idCard);
        baseInfo.setSex(cardMsg.getSex());
        baseInfo.setAttachments(getAttachmentsForPopUp(crowdfundingInfo));
        result.setBaseInfo(baseInfo);

        CfCaseBaseData cfCaseBaseData = caseBaseDataFeignDelegate.getCaseBaseData(infoId);
        if (Objects.nonNull(cfCaseBaseData)) {
            CfOrderPaySuccessPageVO paySuccessPageVO = CfOrderPaySuccessPageVO.builder()
                    .diseaseNorm(cfCaseBaseData.getDiseaseNorm())
                    .build();
            result.setPaySuccessPageVO(paySuccessPageVO);
        }
        return result;
    }
    private List<String> getAttachmentsForPopUp(CrowdfundingInfo crowdfundingInfo) {
        //获取案例图片信息
        Map<AttachmentTypeEnum, List<CrowdfundingAttachmentVo>> attachmentMap = crowdfundingAttachmentBiz.getFundingAttachmentMap(crowdfundingInfo.getId());
        List<String> attachments = new ArrayList<>();
        // 筹款详情图片掩码处理
        if (MASK_IMAGE_SWITCH) {
            attachments = cfAiMaskImageService.imageMaskProcess(attachmentMap.get(AttachmentTypeEnum.ATTACH_CF), crowdfundingInfo);
        }
        if (CollectionUtils.isNotEmpty(attachments)) {
            return attachments;
        }
        List<CrowdfundingAttachmentVo> attachmentVos = attachmentMap.get(AttachmentTypeEnum.ATTACH_CF);
        if (CollectionUtils.isNotEmpty(attachmentVos)) {
            for (CrowdfundingAttachmentVo attachmentVo : attachmentVos) {
                attachments.add(attachmentVo.getUrl());
            }
        }
        return attachments;
    }
}
