package com.shuidihuzhu.cf.service.inventory.impl;

import com.shuidihuzhu.cf.dao.inventory.CfInventoryDao;
import com.shuidihuzhu.cf.domain.UserDeviceInfo;
import com.shuidihuzhu.cf.model.inventory.SecondInventoryDetail;
import com.shuidihuzhu.cf.service.inventory.InventoryRuleHandle;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Description: 第三方账号
 * @Author: panghairui
 * @Date: 2023/1/12 5:30 下午
 */
@Service("thirdPartyRuleHandle")
public class ThirdPartyRuleHandle implements InventoryRuleHandle {

    @Resource
    private CfInventoryDao cfInventoryDao;

    @Override
    public void handle(SecondInventoryDetail secondInventoryDetail, long time) {

        secondInventoryDetail.setRouteUrl("sdchou://mine/page/setting");
        secondInventoryDetail.setContent("查看详情");

        UserDeviceInfo userDeviceInfo = cfInventoryDao.getOneDeviceInfoByUserId(secondInventoryDetail.getUserId(), secondInventoryDetail.getIdentification());
        if (Objects.isNull(userDeviceInfo)) {
            return;
        }

        if (userDeviceInfo.getCreateTime().getTime() < time) {
            return;
        }

        secondInventoryDetail.setSituation("已收集1条");
    }
}
