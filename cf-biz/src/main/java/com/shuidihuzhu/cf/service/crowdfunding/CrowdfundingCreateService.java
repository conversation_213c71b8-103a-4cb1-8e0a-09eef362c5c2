package com.shuidihuzhu.cf.service.crowdfunding;

import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.biz.crowdfunding.material.CfFirsApproveMaterialService;
import com.shuidihuzhu.cf.client.material.model.MaterialPlanVersion;
import com.shuidihuzhu.cf.constants.crowdfunding.CfDataSource;
import com.shuidihuzhu.cf.delegate.ICfFinanceFundStateDelegate;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.CfPayeeMirrorRemarkEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceCapitalAccountFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.model.CfCapitalAccountParam;
import com.shuidihuzhu.cf.finance.model.StateMachine;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoBaseVo;
import com.shuidihuzhu.cf.mq.producer.CommonMessageHelperService;
import com.shuidihuzhu.cf.response.crowdfunding.CreateInfoResultWrap;
import com.shuidihuzhu.cf.service.crowdfunding.casematerial.CfLivingGuardService;
import com.shuidihuzhu.cf.service.crowdfunding.toufang.CfPrimaryChannelService;
import com.shuidihuzhu.cf.util.AppVersionUitl;
import com.shuidihuzhu.cf.util.MobileUtil;
import com.shuidihuzhu.cf.util.crowdfunding.CrowdfundingUtil;
import com.shuidihuzhu.common.web.util.IpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;
import java.util.Objects;

@Slf4j
@RefreshScope
@Service
public class CrowdfundingCreateService {

    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private CrowdfundingOperationBiz crowdfundingOperationBiz;
    @Autowired
    private CrowdfundingAttachmentBiz crowdfundingAttachmentBiz;
    @Autowired
    private CrowdfundingInfoStatusBiz crowdfundingInfoStatusBiz;
    @Autowired
    private CfPrimaryChannelService primaryChannelService;
    @Autowired
    private CfInfoExtBiz cfInfoExtBiz;
    @Autowired
    private CfFirstApproveBiz cfFirstApproveBiz;
    @Autowired
    private ICfFinanceFundStateDelegate cfFinanceFundStateDelegate;
    @Autowired
    private CfFinanceCapitalAccountFeignClient cfFinanceCapitalAccountFeignClient;
    @Autowired
    private CfFirsApproveMaterialService cfFirsApproveMaterialService;
    @Resource
    private CommonMessageHelperService commonMessageHelperService;

    @Value("${need.submit.fund.use.material:1}")
    private int needSubmitFundUseMaterial = 1;

    @Transactional(rollbackFor = Exception.class,transactionManager = CfDataSource.CROWDFUNDIN_DATASOURCE_TRANSACTION_MANAGER)
    public CreateInfoResultWrap createInfo(CrowdfundingInfo crowdfundingInfo, CrowdfundingInfoBaseVo infoBaseVo, String clientIp) {
        CreateInfoResultWrap response = new CreateInfoResultWrap();

        int cfVersion = AppVersionUitl.getCaseVersion(infoBaseVo);
        crowdfundingInfo.setMaterialPlanId(getMaterialPlanId(cfVersion, infoBaseVo));

        //1.1,保存筹款基本信息
        crowdfundingInfoBiz.addNoSendMQ(crowdfundingInfo);
        log.info("crowdfundingInfo,id:{}", crowdfundingInfo.getId());
        if (crowdfundingInfo.getId() <= 0) {
            response.setErrorCode(CfErrorCode.CF_ADD_BASE_INFO_ERROR);
            response.setInfoUuid(crowdfundingInfo.getInfoId());
            return response;
        }
        //1.2,添加筹款运营操作状态
        CrowdfundingOperation crowdfundingOperation = new CrowdfundingOperation();
        crowdfundingOperation.setInfoId(crowdfundingInfo.getInfoId());
        crowdfundingOperation.setOperatorId(-1);
        crowdfundingOperation.setOperation(CrowdfundingOperationEnum.NONE_OPERATION.value());
        crowdfundingOperation.setReason("");
        crowdfundingOperation.setCaseId(crowdfundingInfo.getId());
        crowdfundingOperationBiz.add(crowdfundingOperation);
        //1.3,保存附件信息
        saveAttachmentAtRaise(crowdfundingInfo.getId(), infoBaseVo);
        //1.4,保存筹款信息状态
        CrowdfundingInfoStatus crowdfundingInfoStatus = new CrowdfundingInfoStatus();
        crowdfundingInfoStatus.setInfoUuid(crowdfundingInfo.getInfoId());
        CrowdfundingInfoDataStatusTypeEnum crowdfundingInfoDataStatusType = CrowdfundingInfoDataStatusTypeEnum.BASE_INFO_SUBMIT;
        crowdfundingInfoStatus.setType(crowdfundingInfoDataStatusType.getCode());
        crowdfundingInfoStatus.setStatus(CrowdfundingInfoStatusEnum.UN_SUBMITTED.getCode());
        crowdfundingInfoStatus.setCaseId(crowdfundingInfo.getId());
        this.crowdfundingInfoStatusBiz.add(crowdfundingInfoStatus);

        String registerMobile = "";
        //2.1添加资金账户
        try {
            FeignResponse<Integer>  response1 = cfFinanceCapitalAccountFeignClient
                    .saveCapitalAccountV2(CfCapitalAccountParam.builder()
                            .caseId(crowdfundingInfo.getId())
                            .infoUuid(crowdfundingInfo.getInfoId()).build());
            if (response1 == null || response1.notOk() || response1.getData() != 1) {
                log.error("资金账户添加失败infoId: {}", crowdfundingInfo.getInfoId());
            }
        } catch (Exception e) {
            log.error("finance服务异常,", e);
        }
        //2.2添加扩展表
        CfInfoExt cfInfoExt = new CfInfoExt();
        cfInfoExt.setInfoUuid(crowdfundingInfo.getInfoId());
        cfInfoExt.setSelfTag(StringUtils.trimToEmpty(infoBaseVo.getSelfTag()));
        cfInfoExt.setProductName(CrowdfundingUtil.getProductName(crowdfundingInfo));
        cfInfoExt.setUserThirdType(infoBaseVo.getUserThirdType() == null ? 0 : infoBaseVo.getUserThirdType());
        cfInfoExt.setClientIp(clientIp == null ? "" : clientIp);

        cfInfoExt.setCfVersion(cfVersion);
        cfInfoExt.setNeedCaseList(getRequiredMaterialIds());

        if (!MobileUtil.illegal(registerMobile)) {
            cfInfoExtBiz.setRegisterMobile(cfInfoExt, StringUtils.trimToEmpty(registerMobile));
        } else {
            cfInfoExtBiz.setRegisterMobile(cfInfoExt, "");
        }

        String primaryChannel = this.primaryChannelService.getPrimaryChannel(crowdfundingInfo);//一级渠道
        cfInfoExt.setPrimaryChannel(primaryChannel);
        cfInfoExt.setVolunteerUniqueCode(StringUtils.trimToEmpty(infoBaseVo.getVolunteerUniqueCode()));
        // 添加筹款基本信息id
        cfInfoExt.setCaseId(crowdfundingInfo.getId());

        //2.5添加案例状态、案例状态变迁历史
        cfFinanceFundStateDelegate.addFundState(crowdfundingInfo.getId(), crowdfundingInfo.getInfoId(), StateMachine.State.INITIAL.getCode(),
                StateMachine.StateStop.NONE.getCode(), CfPayeeMirrorRemarkEnum.CF_STATE_ADD, cfInfoExt.getProductName());

        //3，保存初次审核的材料
        if (!StringUtils.isEmpty(infoBaseVo.getSelfRealName()) ||
                !StringUtils.isEmpty(infoBaseVo.getPatientRealName())) {
            CfFirsApproveMaterial material = cfFirsApproveMaterialService.buildMaterialByBaseInfo(infoBaseVo,
                    IpUtil.ip2Long(clientIp),
                    crowdfundingInfo.getId(),
                    crowdfundingInfo.getInfoId());
            cfFirstApproveBiz.add(material, false);

            //2.4 案例状态, 后移  添加并更新FirstApproveStatusEnum
            cfInfoExt.setFirstApproveStatus(FirstApproveStatusEnum.APPLYING.getCode());

            //保存生成案例的草稿id
            cfInfoExt.setPreId(infoBaseVo.getPreId()==null?0:infoBaseVo.getPreId());

            this.cfInfoExtBiz.add(cfInfoExt, CrowdfundingType.fromValue(crowdfundingInfo.getType()),
                    crowdfundingInfo.getUserId());

            response.setCaseInfo(crowdfundingInfo);
            response.setInfoExt(cfInfoExt);
            response.setFirstApproveMaterial(material);

        } else {
            //cfInfoExtBiz.updateFirstApproveTime(crowdfundingInfo.getInfoId());
            //2.4 案例状态, 后移 添加并更新FirstApproveTime
            Timestamp timestamp = new Timestamp(System.currentTimeMillis());
            cfInfoExt.setFirstApproveTime(timestamp);
            this.cfInfoExtBiz.add(cfInfoExt, CrowdfundingType.fromValue(crowdfundingInfo.getType()),
                    crowdfundingInfo.getUserId());
        }

        response.setErrorCode(CfErrorCode.SUCCESS);
        response.setInfoUuid(crowdfundingInfo.getInfoId());
        return response;
    }

    private String getRequiredMaterialIds() {

        List<Integer> materialIds = Lists.newArrayList(
                CrowdfundingInfoDataStatusTypeEnum.BASE_INFO_SUBMIT.getCode(),
                CrowdfundingInfoDataStatusTypeEnum.PATIENT_INFO_SUBMIT.getCode(),
                CrowdfundingInfoDataStatusTypeEnum.PAYEE_INFO_SUBMIT.getCode(),
                CrowdfundingInfoDataStatusTypeEnum.TREATMENT_INFO_SUBMIT.getCode());

        if (needSubmitFundUse()) {
            materialIds.add(CrowdfundingInfoDataStatusTypeEnum.FUND_USE_SUBMIT.getCode());
        }

        return Joiner.on(",").join(materialIds);
    }


    public boolean needSubmitFundUse() {
        return needSubmitFundUseMaterial == 1;
    }


    private int getMaterialPlanId(int cfVersion, CrowdfundingInfoBaseVo infoBaseVo) {

        if (Objects.nonNull(infoBaseVo.getPlanId())) {
            return infoBaseVo.getPlanId();
        }

        // 初审提交了增信
        if (CfVersion.isInitialProperty(cfVersion)) {

            if (cfVersion == CfVersion.definition_3100.getCode()) {
                return calculateMaterialPlan(infoBaseVo);
            }
            // 临时代码 后端上线 但是前端还没有上线的时候 如果没有传是否在其它平台筹款 则是以前的老板公约2.0的数据。
            if (infoBaseVo.getPropertyInsuranceParam() != null
              && (infoBaseVo.getPropertyInsuranceParam().getOtherPlatform() == null
                    || infoBaseVo.getPropertyInsuranceParam().getOtherPlatform().getHasRaise() == null) ) {
                log.info("当前发起的是简版公约2.0的案例.param:{}", infoBaseVo);
                return MaterialPlanVersion.PLAN_2.getCode();
            }

            return needSubmitFundUse() ? MaterialPlanVersion.PLAN_3.getCode() : MaterialPlanVersion.PLAN_4.getCode();
        }

        // 2000的旧版本
        if (cfVersion == CfVersion.definition_20181131.getCode()) {

            return needSubmitFundUse() ? MaterialPlanVersion.PLAN_5.getCode() : MaterialPlanVersion.PLAN_1.getCode();
        }

        return MaterialPlanVersion.PLAN_0.getCode();
    }

    private int calculateMaterialPlan(CrowdfundingInfoBaseVo infoBaseVo) {
        if (Objects.isNull(infoBaseVo) || Objects.isNull(infoBaseVo.getPropertyInsuranceParam())) {
            return MaterialPlanVersion.PLAN_100.getCode();
        }

        if (Objects.nonNull(infoBaseVo.getPropertyInsuranceParam().getAuthenticityIndicator())) {
            return MaterialPlanVersion.PLAN_140.getCode();
        }

        if (Objects.nonNull(infoBaseVo.getPropertyInsuranceParam().getSelfBuiltHouse())) {
            return MaterialPlanVersion.PLAN_120.getCode();
        }

        return MaterialPlanVersion.PLAN_100.getCode();
    }

    private boolean hasSubmitSelfBuiltHouse(CrowdfundingInfoBaseVo infoBaseVo) {

        return infoBaseVo != null && infoBaseVo.getPropertyInsuranceParam() != null
                && infoBaseVo.getPropertyInsuranceParam().getSelfBuiltHouse() != null;
    }

    private void saveAttachmentAtRaise(int caseId, CrowdfundingInfoBaseVo infoBaseVo) {

        List<CrowdfundingAttachment> allAttachments = Lists.newArrayList();
        // 图文
        allAttachments.addAll(CrowdfundingAttachment.buildAttachmentByImageList(
                caseId, infoBaseVo.getAttachments(), AttachmentTypeEnum.ATTACH_CF));
        // 前置医疗
        if (StringUtils.isNotBlank(infoBaseVo.getPreAuditImageUrl())) {
            allAttachments.addAll(CrowdfundingAttachment.buildAttachmentByImageList(caseId,
                    Splitter.on(",").splitToList(infoBaseVo.getPreAuditImageUrl()),
                    AttachmentTypeEnum.ATTACH_FIRST_APPROVE_MEDICAL));
        }
        // 低保
        allAttachments.addAll(CfLivingGuardService.getBasicLivingGuardAttachment(caseId, infoBaseVo.getLivingGuardParam()));

        log.info("案例发起时保存图片.caseId:{} allAttachments:{}", caseId, allAttachments);
        crowdfundingAttachmentBiz.add(allAttachments);
    }

}
