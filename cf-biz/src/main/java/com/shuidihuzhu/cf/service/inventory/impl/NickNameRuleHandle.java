package com.shuidihuzhu.cf.service.inventory.impl;

import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.model.inventory.SecondInventoryDetail;
import com.shuidihuzhu.cf.service.inventory.InventoryRuleHandle;
import com.shuidihuzhu.cf.util.DesensitizationUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Description: 获取昵称个人清单信息
 * @Author: panghairui
 * @Date: 2023/1/11 7:36 下午
 */
@Service("nickNameRuleHandle")
public class NickNameRuleHandle implements InventoryRuleHandle {

    @Resource
    private UserInfoDelegate userInfoDelegate;

    @Override
    public void handle(SecondInventoryDetail secondInventoryDetail, long time) {

        UserInfoModel userInfoModel = userInfoDelegate.getUserInfoByUserId(secondInventoryDetail.getUserId());
        if (Objects.isNull(userInfoModel)) {
            return ;
        }

        if (StringUtils.isEmpty(userInfoModel.getNickname())) {
            return;
        }

        secondInventoryDetail.setContent(DesensitizationUtil.desensitizationNickName(userInfoModel.getNickname()));
        secondInventoryDetail.setSituation("已收集1条");

    }

}
