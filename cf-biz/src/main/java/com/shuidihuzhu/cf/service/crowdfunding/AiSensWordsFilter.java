package com.shuidihuzhu.cf.service.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.shuidihuzhu.cf.feign.crowdfunding.AiSensFilterClient;
import com.shuidihuzhu.common.web.model.Response;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
@Slf4j
public class AiSensWordsFilter {

    private static final int PATIENT_NAME_TYPE = 1;
    private static final int DISEASE_NAME = 2;
    public static final int SUCCESS = 0;

    @Autowired
    private AiSensFilterClient aiSensFilterClient;

    public AiFilterResponse patientAndDiseaseNameFilter(String authorName, String diseaseName) {

        List<FilterObject> filterObjectList = Arrays.asList( new FilterObject(PATIENT_NAME_TYPE, authorName),
                new FilterObject(DISEASE_NAME, diseaseName));

        long now = System.currentTimeMillis();
        log.info("ai敏感词过滤服务发起");
        AiSensWordsFilter.AiFilterResponse result = null;
        try {
            result = aiSensFilterClient.sensWordFilter(JSON.toJSONString(filterObjectList));
        } catch (Exception e) {
            log.error("ai敏感词过滤服务异常, authorName:{} , diseaseName:{} ", authorName, diseaseName, e);
        }

        log.info("ai敏感词过滤服务结束 authorName:{} , diseaseName:{} result：{}，cost：{} ms", authorName, diseaseName,
                result == null ? "null" : result, System.currentTimeMillis() - now);

        return result != null ? result : new AiFilterResponse(SUCCESS);
    }

    @Data
    @AllArgsConstructor
    private static class FilterObject {
        private int type;
        private String msg;
    }

    @Data
    @AllArgsConstructor
    public static class AiFilterResponse {
        int code;
        int type;
        String msg;

        public AiFilterResponse() {

        }
        public AiFilterResponse(int code) {
            this.code = code;
        }
    }
}
