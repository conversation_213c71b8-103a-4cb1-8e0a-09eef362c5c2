package com.shuidihuzhu.cf.service.crowdfunding;

import com.google.common.collect.Lists;
import com.google.common.collect.Ordering;
import com.shuidihuzhu.cf.dao.crowdfunding.CfCasePageAnnouncementManageDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfCasePageAnnouncementManage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CfCasePageAnnouncementManageService {
    @Resource
    private CfCasePageAnnouncementManageDao cfCasePageAnnouncementManageDao;

    public List<CfCasePageAnnouncementManage> getList(){
        List<CfCasePageAnnouncementManage> list = Lists.newArrayList();

        List<CfCasePageAnnouncementManage> cfCasePageAnnouncementManages =  cfCasePageAnnouncementManageDao.getList();
        if(CollectionUtils.isEmpty(cfCasePageAnnouncementManages)){
            return list;
        }

        Ordering<CfCasePageAnnouncementManage> ordering = Ordering.natural().reverse().onResultOf(CfCasePageAnnouncementManage::getCreateTime);
        List<CfCasePageAnnouncementManage> topList = cfCasePageAnnouncementManages.stream().filter(v -> v.getTop() == 1).sorted(ordering).collect(Collectors.toList());
        List<CfCasePageAnnouncementManage> nonTopList = cfCasePageAnnouncementManages.stream().filter(v -> v.getTop() == 0).sorted(ordering).collect(Collectors.toList());

        list.addAll(topList);
        list.addAll(nonTopList);

        return list;
    }
}
