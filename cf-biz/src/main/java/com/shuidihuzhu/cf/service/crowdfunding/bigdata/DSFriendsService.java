package com.shuidihuzhu.cf.service.crowdfunding.bigdata;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.account.model.service.MobileUserIdModel;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.delegate.SimpleUserAccountDelegate;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.FriendsBizTypeEnum;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.account.v1.accountservice.MobileUserIdResponse;
import com.shuidihuzhu.client.dataservice.faceApi.v1.FaceApiClient;
import com.shuidihuzhu.client.dataservice.faceApi.v1.dto.FriendIntimacyDO;
import com.shuidihuzhu.client.model.FriendDO;
import com.shuidihuzhu.client.model.RealtimeDO;
import com.shuidihuzhu.client.model.Response;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RefreshScope
public class DSFriendsService {
	@Value("${friend.redis-expire-time:3600000}")
	private long ONE_DAY_MILL_SEC;

	@Resource(name = "redis-cf-api-new")
	private RedissonHandler cfRedissonHandler;

	@Autowired
	private FaceApiClient faceApiClient;

	@Resource
	private CrowdfundingInfoBiz crowdfundingInfoBiz;

	@Autowired
	private SimpleUserAccountDelegate simpleUserAccountDelegate;

	@Autowired
	private UserInfoDelegate userInfoDelegate;
	@Resource
	private ShuidiCipher shuidiCipher;

	private static final int MAX_LOOP_COUNT = 100;

	private static final int STEP_LENGTH = 100;

	/**
	 * 获得好友数据
	 * @param userId
	 * @param maxCount
	 * @return
	 */
	public Set<Long> getFriendsUserId(long userId, int maxCount, FriendsBizTypeEnum typeEnum) {
		if (userId <= 0) {
			return Collections.EMPTY_SET;
		}
		Set<Long> friendSet = this.getFriendsFromRedis(userId, typeEnum);
		if (friendSet != null) {
			log.debug("缓存获得 loop friend set, userId:{}", userId);
			return friendSet;
		}
		friendSet = getFriendsFromDS(userId, maxCount);
		log.debug("好友数据, userId:{}\tfriends:{}", userId, friendSet);
		setFriendsToRedis(userId, typeEnum, friendSet);
		return friendSet;
	}

	/**
	 * v2支持分页,自动分页
	 */
	public Set<Long> getFriendsUserIdV2(long userId, int maxCount, FriendsBizTypeEnum typeEnum) {
		if (userId <= 0) {
			return Collections.EMPTY_SET;
		}
		Set<Long> friendSet = getFriendsFromDSV2(userId, maxCount);
		log.debug("好友数据v2, userId:{}\tfriends:{}", userId, friendSet);
		return friendSet;
	}

	private Set<Long> getFriendsFromDSV2(long userId, int targetCount) {
		return getFriendsFromDSV2(userId, targetCount, STEP_LENGTH, MAX_LOOP_COUNT);
	}

	/**
     *
	 * @param userId 用户id
	 * @param targetSize 目标数量
	 * @param stepLength 步长
	 * @param maxLoopCount 最大循环次数 保护逻辑防止循环过多
	 * @return
	 */
	@NotNull
	public Set<Long> getFriendsFromDSV2(long userId, int targetSize, int stepLength, int maxLoopCount) {
		Set<Long> resultSet = Sets.newHashSetWithExpectedSize(64);
		String nextId = "";
		// 最大循环次数
		int loopCount = 0;
		while (true) {
			int querySize;
			if (stepLength < targetSize) {
				querySize = stepLength;
			} else {
				querySize = targetSize;
			}

			targetSize = targetSize - stepLength;
			RealtimeDO realtimeDO = listFriendIdSetByPage(userId, querySize, nextId);

			if (realtimeDO == null) {
				return resultSet;
			}
			// 更新游标
			nextId = realtimeDO.getNextId();

			Set<Long> idSet = parseUserIdsByDO(realtimeDO);
			resultSet.addAll(idSet);

			// 没有更多好友了 直接返回
			if (StringUtils.isBlank(nextId)) {
				return resultSet;
			}

			// 保护逻辑 防止无限循环
			loopCount++;
			if (loopCount >= maxLoopCount) {
				log.error("获取好友循环次数过多 userId:{}", userId);
				return resultSet;
			}
		}
	}

	private RealtimeDO listFriendIdSetByPage(long userId, int maxCount, String nextId) {
		Response<RealtimeDO> friendResponse = faceApiClient.getFriend(userId, "friend", nextId, "friend_ids", maxCount);;
		log.debug("listFriendIdSetByPage userId:{}, nextId:{}, maxCount:{}, response:{}", userId, nextId, maxCount, JSON.toJSONString(friendResponse));
		if (friendResponse == null || friendResponse.getCode() != 0) {
			log.debug("获取好友失败 response:{}", JSON.toJSONString(friendResponse));
			return null;
		}
		return friendResponse.getData();
	}

	/**
	 * 从返回model中解析userIds
	 * @param data
	 * @return
	 */
	@NotNull
	private Set<Long> parseUserIdsByDO(RealtimeDO data) {
	    if (data == null) {
	    	return Collections.emptySet();
		}
		FriendDO friend = data.getFriend();
	    if (friend == null) {
			return Collections.emptySet();
		}
		String friendIds = friend.getFriend_ids();
		String[] split = StringUtils.split(friendIds, ",");
		if (split == null || split.length <= 0) {
			return Collections.emptySet();
		}
		HashSet<Long> idSet = Sets.newHashSetWithExpectedSize(split.length);
		for (String id : split) {
			idSet.add(Long.valueOf(id));
		}
		return idSet;
	}

	/**
	 *
	 * 捐款后通知一度好友(排除筹款人)
		DONATE_NOTIFY_FRIENDS,
	 * @param userId
	 * @return
	 */
	public Set<Long> getAllFriendsFromDS(long userId) {
	    return getFriendsFromDS(userId, 200).stream()
				.filter(crowdfundingInfoBiz::isNotFundraiser)
                .collect(Collectors.toSet());
	}

	/**
	 * 请求大数据,获得用户的好友信息
	 * @param userId
	 * @param maxCount
	 * @return
	 */
	public Set<Long> getFriendsFromDS(long userId, int maxCount) {
//		int connectionRequestTimeOut = 300; // 从连接池中获取连接的时间
//		int connectTimeOut = 400; // 建立链接的时间
//		int socketTimeOut = 500; //传输数据的时间
		Set<Long> friendSet = Sets.newHashSet();
		try {
			Response friendResponse = faceApiClient.getFriend(userId, "friend","", "friend_ids", maxCount);;

			if (null == friendResponse || friendResponse.getCode() != 0){
				log.debug("getFriend failure,userId:{}",userId);
				return friendSet;
			}
			Object dataObj  =friendResponse.getData();
			if (null == dataObj){
				log.debug("getFriend failure,Data is empty,userId:{}, ",userId);
			}
			//朋友圈,会话,其他
			JSONObject dataJson = JSONObject.parseObject(JSON.toJSONString(dataObj));
			friendSet = this.getFriendsUserIdSet(dataJson, maxCount);
		} catch (Exception e) {
			log.error("userId:{},请求大数据出错", userId, e);
		} finally {
			log.debug("userId:{},好友数量为: count{}", userId, friendSet.size());
		}
		return friendSet;
	}

	private void setFriendsToRedis(long userId, FriendsBizTypeEnum typeEnum, Set<Long> friendUserIdSet) {
		if (null == friendUserIdSet) {
			return;
		}
		String key = this.getRedissonKey(userId, typeEnum);
		cfRedissonHandler.setEX(key, friendUserIdSet, ONE_DAY_MILL_SEC);
	}

	private Set<Long> getFriendsFromRedis(long userId, FriendsBizTypeEnum typeEnum) {
		String key = this.getRedissonKey(userId, typeEnum);
		return cfRedissonHandler.get(key, Set.class);
	}

	private String getRedissonKey(long userId, FriendsBizTypeEnum typeEnum) {
		return "friends-cache-v3-" + userId + "-" + typeEnum;
	}

	private Set<Long> getFriendsUserIdSet(JSONObject jsonObject, int maxCount) {
		if (null == jsonObject) {
			return Sets.newHashSet();
		}

		//朋友圈,会话,其他
		Set<Long> friendsUserIdSet = Sets.newHashSet();

			try {
				JSONObject dataJsonObject = jsonObject.getJSONObject("friend");
				if (null == dataJsonObject){
					log.debug("Failed to resolve buddy data,friend is empty");
					return friendsUserIdSet;
				}
				String friendStrS = dataJsonObject.getString("friend_ids");
				if (StringUtils.isEmpty(friendStrS)) {
					log.debug("Failed to resolve buddy data,friend_ids is empty");
					return friendsUserIdSet;
				}
				List<String> userIdStrList = Arrays.asList(friendStrS.split(","));
				if (CollectionUtils.isEmpty(userIdStrList)) {
					log.debug("请求大数据的接口获得用户,好友关系为空");
					return friendsUserIdSet;
				}
				for (String userIdStr : userIdStrList) {
					if (friendsUserIdSet.size() >= maxCount) {
						break;
					}
					if (StringUtils.isEmpty(userIdStr)) {
					    continue;
                    }
					try {
						friendsUserIdSet.add(Long.parseLong(userIdStr.trim()));
					} catch (Exception e) {
						log.error("用户好友id异常,userId:{},好友的userIdString:{};",jsonObject.get("userId"),userIdStr,e);
					}
				}
			} catch (Exception e) {
				log.error("解析好友数据失败", e);
			}
		return friendsUserIdSet;
	}

	public Set<Long> getUserAllFriends(long userId) {
		Set<Long> friendsIds = Sets.newHashSet();
		int maxCount = 200;
		String nextId = "";
		try {
			while (true) {
				Response<RealtimeDO> friendResponse = faceApiClient.getFriend(userId, "friend", nextId, "friend_ids", maxCount);
				if (null == friendResponse || friendResponse.getCode() != 0) {
					log.debug("getFriend failure,userId:{}", userId);
					break;
				}
				RealtimeDO dataObj = friendResponse.getData();
				if (null == dataObj) {
					log.debug("getFriend failure,Data is empty,userId:{}, nextId={}", userId, nextId);
					break;
				}
				JSONObject dataJson = JSONObject.parseObject(JSON.toJSONString(dataObj));
				Set<Long> friendSet = this.getFriendsUserIdSet(dataJson, maxCount);
				if (CollectionUtils.isEmpty(friendSet)) {
					break;
				}
				friendsIds.addAll(friendSet);
				if (friendSet.size() < maxCount) {
					break;
				}
				nextId = dataObj.getNextId();
				if (StringUtils.isEmpty(nextId)) {
					break;
				}
			}
		} catch (Exception e) {
			log.error("userId:{}, 请求大数据出错", userId, e);
		}
		log.debug("userId:{},所有好友数量为: count={}", userId, friendsIds.size());
		return friendsIds;
	}

	public Map<Long, String> getAllFriendsMobilesByUserMobile(String mobile) {
		MobileUserIdModel mobileUser = simpleUserAccountDelegate.getUserIdByMobile(mobile);
		if (null == mobileUser || mobileUser.getUserId() <= 0) {
			return Maps.newHashMap();
		}
		Set<Long> friendsIds = getUserAllFriends(mobileUser.getUserId());
		Map<Long, String> friendsMobiles = Maps.newHashMap();
		friendsIds.forEach(friendId -> {
			UserInfoModel userAccount = userInfoDelegate.getUserInfoByUserId(friendId);
			String friendMobile = null == userAccount ? "" : shuidiCipher.decrypt(userAccount.getCryptoMobile());
			friendsMobiles.put(friendId, friendMobile);
		});
		return friendsMobiles;
	}

	/**
	 * 获取一度好友id以及亲密度
	 * @param userId
	 * @param maxCount
	 * @return
	 */
	public List<Map<String, Object>> getFriendIntimacy(long userId, int maxCount) {
		List<Map<String, Object>> friendsIds = Lists.newArrayList();
		String nextId = "";
		try {
			while (true) {
				Response<FriendIntimacyDO> friendResponse = faceApiClient.getFriendIntimacy(userId, nextId, maxCount);
				if (Objects.isNull(friendResponse) || friendResponse.getCode() != 0) {
					log.debug("getFriendIntimacy failure,userId:{}", userId);
					break;
				}
				FriendIntimacyDO friendResponseData = friendResponse.getData();
				if (Objects.isNull(friendResponseData)) {
					log.debug("getFriendIntimacy failure,Data is empty,userId:{}, nextId={}", userId, nextId);
					break;
				}

				friendsIds.addAll(friendResponseData.getFriends());
				if (friendsIds.size() < maxCount) {
					break;
				}
				nextId = friendResponseData.getNextId();
				if (StringUtils.isEmpty(nextId)) {
					break;
				}
			}
		} catch (Exception e) {
			log.error("userId:{}, 请求大数据出错", userId, e);
		}
		log.debug("userId:{},所有好友数量为: count={}", userId, friendsIds.size());
		return friendsIds;
	}

	/**
	 * 获取二度好友
	 * @param userId
	 * @return
	 */
	public Set<Long> getFriendsUserIdSecondDegree(long userId){
		Set<Long> friendSetSecond = Sets.newHashSet();
		// 获取一度好友集合
		Set<Long> friendUserIdSet = getUserAllFriends(userId);
		friendSetSecond.addAll(friendUserIdSet);
		for (Long id : friendUserIdSet) {
			Set<Long> friendsUserIdSecondSet = getUserAllFriends(id);
			friendSetSecond.addAll(friendsUserIdSecondSet);
		}
		return friendSetSecond;
	}
}
