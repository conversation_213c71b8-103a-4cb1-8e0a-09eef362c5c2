package com.shuidihuzhu.cf.service;

import com.shuidihuzhu.cf.domain.CaseRaiseRiskDO;
import com.shuidihuzhu.cf.enums.crowdfunding.CaseRaiseRiskLevelEnum;
import com.shuidihuzhu.cf.response.OpResult;

/**
 * <AUTHOR>
 * @date 2018-08-20  11:24
 */
public interface CaseRaiseRiskService {

    CaseRaiseRiskDO getByInfoUuid(String infoUuid);

    boolean isPassed(String infoUuid);

    OpResult<Integer> saveRaiseRisk(CaseRaiseRiskDO raiseRiskDO);

}
