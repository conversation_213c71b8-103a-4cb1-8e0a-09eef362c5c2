package com.shuidihuzhu.cf.service.crowdfunding.listener.afteradd;

import com.shuidihuzhu.cf.service.crowdfunding.listener.base.AbstractCrowdFundingAddEventListener;
import org.apache.commons.lang3.StringUtils;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoBaseVo;
import com.shuidihuzhu.cf.service.crowdfunding.CfCaseDataTraceService;
import com.shuidihuzhu.cf.service.crowdfunding.event.CrowdFundingAddEvent;
import com.shuidihuzhu.cf.service.crowdfunding.event.AddListenerOrder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.event.SmartApplicationListener;
import org.springframework.stereotype.Service;

/**
 * 开始筹款
 */
@Slf4j
@Service
public class CaseBeginFundingTraceListener extends AbstractCrowdFundingAddEventListener implements SmartApplicationListener {

    @Autowired
    private CfCaseDataTraceService cfCaseDataTraceService;

    @Override
    public void onApplicationEvent(ApplicationEvent _event) {

        CrowdFundingAddEvent crowdFundingAddEvent = (CrowdFundingAddEvent) _event;
        CrowdfundingInfo cfCase = crowdFundingAddEvent.getCrowdfundingInfo();
        CrowdfundingInfoBaseVo infoBaseVo = crowdFundingAddEvent.getCrowdfundingInfoBaseVo();
        if (cfCase != null&&infoBaseVo!=null) {
            if (!StringUtils.isEmpty(infoBaseVo.getSelfRealName()) ||
                    !StringUtils.isEmpty(infoBaseVo.getPatientRealName())) {
            } else {
                try {
                    cfCaseDataTraceService.cfCaseBeginFundingTrace(cfCase);
                } catch (Exception e) {
                    log.error("CaseBeginFundingTraceListener error:", e);
                }
            }
        }


    }


    @Override
    public int getOrder() {
        return AddListenerOrder.CaseBeginFundingTrace.getValue();
    }

}
