package com.shuidihuzhu.cf.service.crowdfunding.listener.afteradd;

import com.shuidihuzhu.cf.enums.crowdfunding.UserLabelAlterEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.mq.producer.impl.MQProdecer;
import com.shuidihuzhu.cf.service.crowdfunding.event.AddListenerOrder;
import com.shuidihuzhu.cf.service.crowdfunding.event.CrowdFundingAddEvent;
import com.shuidihuzhu.cf.service.crowdfunding.listener.base.AbstractCrowdFundingAddEventListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.event.SmartApplicationListener;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @time 2019/3/22 下午5:58
 * @desc 用户标签中有案例信息，用户新发起案例需要更新标签信息
 * https://wiki.shuiditech.com/pages/viewpage.action?pageId=164201711
 */
@Slf4j
@Service
public class UserLabelAlterListener extends AbstractCrowdFundingAddEventListener implements SmartApplicationListener {

    @Autowired
    private MQProdecer mqProdecer;

    @Override
    public void onApplicationEvent(ApplicationEvent event) {

        CrowdFundingAddEvent crowdFundingAddEvent = (CrowdFundingAddEvent) event;
        CrowdfundingInfo cfCase = crowdFundingAddEvent.getCrowdfundingInfo();

        if(null != cfCase && cfCase.getId() > 0){
            mqProdecer.sendUserLabelAlterMq(cfCase.getId(), cfCase.getUserId(), UserLabelAlterEnum.RAISE, DelayLevel.S5);
        }
    }


    @Override
    public int getOrder() {
        return AddListenerOrder.UserLableAlter.getValue();
    }
}
