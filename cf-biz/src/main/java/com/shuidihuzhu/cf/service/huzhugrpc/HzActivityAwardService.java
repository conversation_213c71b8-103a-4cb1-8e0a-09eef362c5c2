package com.shuidihuzhu.cf.service.huzhugrpc;

import com.google.common.collect.Lists;

import com.shuidihuzhu.cf.biz.crowdfunding.CfUserStatBiz;
import com.shuidihuzhu.cf.biz.duiba.DuibaIntegralService;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.model.ConsumeBonusPoint;
import com.shuidihuzhu.cf.model.ConsumePointsResult;
import com.shuidihuzhu.cf.model.awardactivity.ActivityAward;
import com.shuidihuzhu.cf.model.crowdfunding.UserPointsHistory;
import com.shuidihuzhu.cf.model.crowdfunding.converter.ActivityAwardVoConverter;
import com.shuidihuzhu.cf.vo.ActivityAwardVO;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.IdGenUtil;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import com.shuidihuzhu.hz.client.hz.award.model.ActivityAwardDto;
import com.shuidihuzhu.hz.client.hz.award.service.ActivityAwardService;
import com.shuidihuzhu.wx.grpc.enums.WxMpBizType;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RSet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * Created by chao on 16/9/12.
 */
@Service
@Slf4j
@RefreshScope
public class HzActivityAwardService {
    @Autowired
    private ActivityAwardService activityAwardService;
    @Autowired
    private HzActivityAwardHistoryService hzActivityAwardHistoryService;
    @Autowired
    private DuibaIntegralService duibaIntegralService;
    @Value("${award.lotteryUsersLimit:10000}")
    private Integer lotteryUsersLimit;
    @Value("${award.consumePoint:10}")
    private Integer consumePoint;

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;

    @Autowired
    private CfUserStatBiz cfUserStatBiz;
    ActivityAwardVoConverter activityAwardVoConverter = new ActivityAwardVoConverter();

    public void add(ActivityAward activityAward) {
        if (null == activityAward || activityAward.getActivityId() < 0) {
            log.warn("activityaward is illegal, activityaward:{}", activityAward);
            return;
        }
        log.info("add activityaward,{}", activityAward);
        activityAwardService.add(this.initActivityAwardDto(activityAward));
    }

    public void updateSendOutCount(ActivityAward activityAward) {
        if (null == activityAward || activityAward.getId() < 0) {
            log.warn("activityaward is illegal,activityaward:{}", activityAward);
            return;
        }

        log.info("updateSendOutCount,activityaward:{}", activityAward);
        activityAwardService.updateSendOutCount(activityAward.getId(), activityAward.getSendOutCount());
    }

    public List<ActivityAward> getAllAwardByActivityId(Integer activityId) {
        Response<List<ActivityAwardDto>> response = activityAwardService.getAllAwardByActivityId(activityId);

        if (null == response || 0 != response.getCode() || CollectionUtils.isEmpty(response.getData())) {
            log.info("getAllAwardByActivityId response is null");
            return Lists.newArrayList();
        }
        List<ActivityAward> resultList = Lists.newLinkedList();
        for (ActivityAwardDto activityAwardDto : response.getData()) {
            if (null == activityAwardDto) {
                continue;
            }
            resultList.add(this.initActivityAward(activityAwardDto));
        }
        return resultList;
    }

    public ActivityAward getById(Integer id) {
        Response<ActivityAwardDto> response = activityAwardService.getById(id);

        if (null == response || 0 != response.getCode() || null == response.getData()) {
            log.info("getById response is null");
            return null;
        }

        return this.initActivityAward(response.getData());
    }

    public ActivityAward lottery(long userId, int activityId) {
        return lottery(userId, activityId, activityAward -> true);
    }

    public Response<ActivityAwardVO> lotteryA3(long userId, int activityId) {
        String lotteryUsersKey = "ActivityAward:" + activityId + ":lotteryUsers";
        ActivityAward activityAward = null;
        RSet<Long> set = redissonHandler.getRedissonClient().getSet(lotteryUsersKey);
        ConsumeBonusPoint consumeBonusPoint = new ConsumeBonusPoint();
        String payUid = IdGenUtil.tradeNo();
        consumeBonusPoint.setUserId(userId);
        consumeBonusPoint.setAction(BonusPointAction.LOTTERY.getCode());
        consumeBonusPoint.setBizType(WxMpBizType.WXMPTYPE_CROWDFUNDING.getKey());
        consumeBonusPoint.setOutTradeNo(payUid);
        consumeBonusPoint.setPoints(consumePoint);
        consumeBonusPoint.setRemark(BonusPointAction.LOTTERY.getDesc());
        consumeBonusPoint.setUserThirdType(0);
        consumeBonusPoint.setChannel("");

        ConsumePointsResult result = cfUserStatBiz.consumePoints(consumeBonusPoint);
        if (result.getCode() != ConsumePointsResultCode.OK) {
            return NewResponseUtil.makeError(CfErrorCode.POINT_NOT_ENOUGH);
        }

//        ActivityAwardHistory activityAwardHistory = new ActivityAwardHistory();


        try {
            if (set.size() < lotteryUsersLimit) {
                set.add(userId);
                activityAward = lottery(userId, activityId, activityAward1 -> activityAward1.getType() != 1 && activityAward1.getType() != 2);
            } else {
                activityAward = lottery(userId, activityId);
                if (activityAward.getType() == 1) {

                    boolean luck = false;
                    RLock lock = redissonHandler.getRedissonClient().getLock("ActivityAward:" + activityId + ":lock");
                    if (lock.tryLock()) {
                        try {
                            if (set.size() >= lotteryUsersLimit) {
                                set.clear();
                                log.info("lotteryA3: get best award. luck:{} u:{} a:{}", luck, userId, activityAward.getId());
                                luck = true;
                            }
                        } finally {
                            lock.unlock();
                        }
                    }
                    if (!luck) {
                        log.info("lotteryA3: get best award. luck:{} u:{} a:{}", luck, userId, activityAward.getId());
                        activityAward = lottery(userId, activityId, activityAward1 -> activityAward1.getType() != 1 && activityAward1.getType() != 2);
                    }
                }
            }
//
//            activityAwardHistory.setAwardId(activityAward.getId());
//            activityAwardHistory.setUserId(userId);
//            activityAwardHistory.setActivityId(activityId);
//            activityAwardHistory.setAddTime(new Date());
//            activityAwardHistory.setContent(org.apache.commons.lang3.StringUtils.defaultString(activityAward.getExtraId()));
//            activityAwardHistory.setStatus(0);
//            activityAwardHistory.setTradeNo(payUid);
//            activityAwardHistory.setPayType(1);
//            hzActivityAwardHistoryService.add(activityAwardHistory);

            if (activityAward.getType()== 3 || activityAward.getType() == 4) {
                int point = Integer.parseInt(activityAward.getExtraId());
                System.out.println("point:" + point);
                cfUserStatBiz.bonusPoints(userId, point, BonusPointAction.LOTTERY.getCode(), IdGenUtil.tradeNo());
            }
        } catch (Exception e) {
            log.error("返还积分", e);
            try {
                UserPointsHistory userPointsHistory = duibaIntegralService.getUserPointsHistory(userId, payUid, UserPoinstHistoryType.CONSUME.getCode());
                if (userPointsHistory != null) {
                    //退还积分
                    duibaIntegralService.refundPoints(payUid + System.currentTimeMillis(), userPointsHistory.getPoints(), "抽奖异常:outTradeNo[" + payUid + "]:" + e.getMessage(), "hz-award", 0, BonusPointAction.BACK.getCode(), userId);
                    //更改原有订单状态
                    duibaIntegralService.UpdatePointsHistoryStatus(userId, payUid, UserPoinstHistoryType.CONSUME.getCode(), PointsStatus.REFUND.getCode(), "订单无效，积分返还");
                }
//            if (activityAwardHistory.getId() != null) {
//                log.info("delete activityAwardHistory " + activityAwardHistory.getId());
//                hzActivityAwardHistoryService.delete(activityAwardHistory.getId());
//            }
            } catch (Exception e1) {
                log.error("退换积分异常", e1);
            }
            return ResponseUtil.makeError(ErrorCode.SYSTEM_ERROR);
        }

        log.info("Success lotteryA3() returned: " + activityAward);

        return ResponseUtil.makeSuccess(activityAwardVoConverter.getActivityAwardVO(activityAward));
    }


    public ActivityAward lottery(long userId, int activityId, Predicate<? super ActivityAward> filter) {
        List<ActivityAward> activityAwardList =
                getAllAwardByActivityId(activityId).stream()
                        .filter(activityAward -> activityAward.getOdds() > 0.0f)
                        .filter(filter)
                        .sorted(Comparator.comparing(ActivityAward::getType))
                        .collect(Collectors.toList());

        sortList(activityAwardList);

        List<Double> originalRates = Lists.newArrayList();
        for (ActivityAward activityAward : activityAwardList) {
            double odds = activityAward.getOdds();
            if (odds < 0) {
                odds = 0;
            }
            originalRates.add(odds);
        }
        int originalIndex = lottery(originalRates);
        ActivityAward activityAward = activityAwardList.get(originalIndex);
        if (activityAward.getType() == 0) {
        } else if (activityAward.getTotalCount() == 0) {
        } else if (activityAward.getSendOutCount() >= activityAward.getTotalCount()) {
            Response<Integer> response = activityAwardService.decSendOutCount(activityAward.getId(),
                    activityAward.getSendOutCount());
            int r = 0;
            if (null != response && 0 == response.getCode() && null != response.getData()) {
                r = response.getData();
            }
            if (r == 0) {
                activityAward = activityAwardList.get(activityAwardList.size() - 1);
            }
        } else {
            activityAward = activityAwardList.get(activityAwardList.size() - 1);
        }


        return activityAward;
    }

    public void sortList(List<ActivityAward> activityAwardList) {
        Optional<ActivityAward> awardOptional = activityAwardList.stream().filter(new Predicate<ActivityAward>() {
            @Override
            public boolean test(ActivityAward activityAward) {
                return activityAward.getType() == 0;
            }
        }).findFirst();
        if (awardOptional.isPresent()) {
            activityAwardList.remove(awardOptional.get());
            activityAwardList.add(awardOptional.get());
        }
    }

    private static int lottery(List<Double> orignalRates) {
        if (orignalRates == null || orignalRates.isEmpty()) {
            return -1;
        }

        int size = orignalRates.size();

        // 计算总概率，这样可以保证不一定总概率是1
        double sumRate = 0d;
        for (double rate : orignalRates) {
            sumRate += rate;
        }

        // 计算每个物品在总概率的基础下的概率情况
        List<Double> sortOrignalRates = new ArrayList<Double>(size);
        Double tempSumRate = 0d;
        for (double rate : orignalRates) {
            tempSumRate += rate;
            sortOrignalRates.add(tempSumRate / sumRate);
        }

        // 根据区块值来获取抽取到的物品索引
        double nextDouble = Math.random();
        sortOrignalRates.add(nextDouble);
        Collections.sort(sortOrignalRates);

        return sortOrignalRates.indexOf(nextDouble);
    }

    private ActivityAwardDto initActivityAwardDto(ActivityAward activityAward) {
        ActivityAwardDto activityAwardDto = new ActivityAwardDto();
        activityAwardDto.setId(activityAward.getId());
        activityAwardDto.setType(activityAward.getType());
        activityAwardDto.setTotalCount(activityAward.getTotalCount());
        activityAwardDto.setSendOutCount(activityAward.getSendOutCount());
        activityAwardDto.setOdds(activityAward.getOdds());
        activityAwardDto.setActivityId(activityAward.getActivityId());
        activityAwardDto.setAddTime(activityAward.getAddTime());
        activityAwardDto.setExtraId(activityAward.getExtraId());
        activityAwardDto.setDescription(activityAward.getDescription());
        activityAwardDto.setThumbUrl(activityAward.getThumbUrl());
        activityAwardDto.setName(activityAward.getName());
        activityAwardDto.setSort(activityAward.getSort());
        return activityAwardDto;
    }


    private ActivityAward initActivityAward(ActivityAwardDto activityAwardDto) {
        ActivityAward activityAward = new ActivityAward();
        activityAward.setId(activityAwardDto.getId());
        activityAward.setType(activityAwardDto.getType());
        activityAward.setTotalCount(activityAwardDto.getTotalCount());
        activityAward.setSendOutCount(activityAwardDto.getSendOutCount());
        activityAward.setOdds(activityAwardDto.getOdds());
        activityAward.setActivityId(activityAwardDto.getActivityId());
        activityAward.setAddTime(activityAwardDto.getAddTime());
        activityAward.setExtraId(activityAwardDto.getExtraId());
        activityAward.setDescription(activityAwardDto.getDescription());
        activityAward.setThumbUrl(activityAwardDto.getThumbUrl());
        activityAward.setName(activityAwardDto.getName());
        activityAward.setSort(activityAwardDto.getSort());
        return activityAward;
    }
}
