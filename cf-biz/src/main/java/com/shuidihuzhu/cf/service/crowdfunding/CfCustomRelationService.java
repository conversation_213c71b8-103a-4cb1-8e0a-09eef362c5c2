package com.shuidihuzhu.cf.service.crowdfunding;

import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.biz.crowdfunding.CfCustomRelationBiz;
import com.shuidihuzhu.cf.enums.crowdfunding.BaseInfoTemplateConst;
import com.shuidihuzhu.cf.enums.crowdfunding.CfCustomRelationEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfVersion;
import com.shuidihuzhu.cf.enums.crowdfunding.RelationShowTypeEnum;
import com.shuidihuzhu.cf.model. CfCustomRelation;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by dongcf on 2020/7/21
 */
@Service
public class CfCustomRelationService {

    @Autowired
    private CfCustomRelationBiz cfCustomRelationBiz;

    public String getFundingShowDesc(int caseId, CfInfoExt cfInfoExt, CrowdfundingAuthor crowdfundingAuthor, CfFirsApproveMaterial approveMaterial) {
        // 没有患者信息直接返回空
        String authorName = getAuthorName(crowdfundingAuthor, approveMaterial);
        if(StringUtils.isBlank(authorName)) {
            return "";
        }
        // 没有自定义关系，根据是否本人发起案例返回文案
        CfCustomRelation customRelation = cfCustomRelationBiz.getLast(caseId);
        if(null == customRelation) {
            return getDefaultShow(authorName, "的亲友", cfInfoExt, approveMaterial);
        }
        // 自定义关系配置昵称，返回空
        RelationShowTypeEnum showTypeEnum = RelationShowTypeEnum.getByType(customRelation.getShowType());
        if(showTypeEnum == RelationShowTypeEnum.FOUNDER_NICKNAME || showTypeEnum == RelationShowTypeEnum.OTHER) {
            return "";
        }
        // 自定义关系是本人，返回本人姓名
        if(customRelation.getRelationType() == CfCustomRelationEnum.SELF.getType()){
            return handleResult(authorName, "");
        }
        // 自定义关系不是本人，展示关系描述文案
        return handleResult(authorName, "的" + customRelation.getRelationDesc());
    }

    public String getShowDesc(int caseId, String crowdFundingNickName, CfInfoExt cfInfoExt, CrowdfundingAuthor crowdfundingAuthor, CfFirsApproveMaterial approveMaterial) {
        if(caseId <= 0 || StringUtils.isBlank(crowdFundingNickName)) {
            return handleResult(crowdFundingNickName, "");
        }
        String authorName = getAuthorName(crowdfundingAuthor, approveMaterial);
        if(StringUtils.isBlank(authorName)) {
            return handleResult(crowdFundingNickName, "");
        }
        CfCustomRelation customRelation = cfCustomRelationBiz.getLast(caseId);
        if(null == customRelation) {
            return getDefaultShow(authorName, "的亲友", cfInfoExt, approveMaterial);
        }
        RelationShowTypeEnum showTypeEnum = RelationShowTypeEnum.getByType(customRelation.getShowType());
        if(showTypeEnum == RelationShowTypeEnum.FOUNDER_NICKNAME || showTypeEnum == RelationShowTypeEnum.OTHER) {
            return handleResult(crowdFundingNickName, "");
        }
        if(customRelation.getRelationType() == CfCustomRelationEnum.SELF.getType()){
            return handleResult(authorName, "");
        }
        return handleResult(authorName, "的" + customRelation.getRelationDesc());
    }


    private String getDefaultShow(String authorName, String customSuffix, CfInfoExt cfInfoExt, CfFirsApproveMaterial approveMaterial) {
        if(null == approveMaterial) {
            return handleResult(authorName, "");
        }
        if(cfInfoExt != null && CfVersion.definition_3100.getCode() == cfInfoExt.getCfVersion()) {
            BaseInfoTemplateConst.CfBaseInfoRelationshipEnum relation = BaseInfoTemplateConst.CfBaseInfoRelationshipEnum.getByCode(approveMaterial.getUserRelationTypeForC());
            if(relation == BaseInfoTemplateConst.CfBaseInfoRelationshipEnum.SELF) {
                return handleResult(authorName, "");
            }
            if(StringUtils.isNotBlank(customSuffix)) {
                return handleResult(authorName, customSuffix);
            }
            String suffix = "".equals(relation.getWord()) ? "" : "的" + relation.getWord();
            return handleResult(authorName, suffix);
        }
        UserRelTypeEnum userRelTypeEnum = UserRelTypeEnum.getUserRelTypeEnum(approveMaterial.getUserRelationType());
        if(userRelTypeEnum == UserRelTypeEnum.SELF) {
            return handleResult(authorName, "");
        }
        if(StringUtils.isNotBlank(customSuffix)) {
            return handleResult(authorName, customSuffix);
        }
        return handleResult(authorName, "的" + userRelTypeEnum.getMsg());
    }

    /**
     * 处理文案长度
     *
     * @param prefix
     * @param suffix
     * @return
     */
    private String handleResult(String prefix, String suffix) {
        int totalLength = 9;
        int suffixLength = suffix.length();
        if(prefix.length() <= totalLength - suffixLength) {
            return prefix + suffix;
        }
        String result = StringUtils.substring(prefix, 0, totalLength - suffixLength - 1);
        return result + "..." + suffix;
    }

    /**
     * 1,以前患者信息为准
     * 2,患者信息没有就以前置信息为准
     * 3,都没有返回筹款人昵称
     *
     * @return
     */
    private String getAuthorName(CrowdfundingAuthor crowdfundingAuthor, CfFirsApproveMaterial approveMaterial) {
        if(crowdfundingAuthor != null) {
            return crowdfundingAuthor.getName();
        }
        if(approveMaterial != null) {
            return approveMaterial.getPatientRealName();
        }
        return "";
    }

    public String getShowDescV2(int caseId, CfInfoExt cfInfoExt, CrowdfundingAuthor author, CfFirsApproveMaterial material) {
        if(caseId <= 0) {
            return null;
        }
        String authorName = getAuthorName(author, material);
        if(StringUtils.isBlank(authorName)) {
            return null;
        }
        CfCustomRelation customRelation = cfCustomRelationBiz.getLast(caseId);
        if(null == customRelation) {
            return getDefaultShow(authorName, "的亲友", cfInfoExt, material);
        }
        RelationShowTypeEnum showTypeEnum = RelationShowTypeEnum.getByType(customRelation.getShowType());
        if(showTypeEnum == RelationShowTypeEnum.FOUNDER_NICKNAME || showTypeEnum == RelationShowTypeEnum.OTHER) {
            return null;
        }
        if(customRelation.getRelationType() == CfCustomRelationEnum.SELF.getType()){
            return handleResult(authorName, "");
        }
        return handleResult(authorName, "的" + customRelation.getRelationDesc());
    }
}
