package com.shuidihuzhu.cf.service.crowdfunding.listener.afteradd;

import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoBaseVo;
import com.shuidihuzhu.cf.service.crowdfunding.event.AddListenerOrder;
import com.shuidihuzhu.cf.service.crowdfunding.event.CrowdFundingAddEvent;
import com.shuidihuzhu.cf.service.crowdfunding.listener.base.AbstractCrowdFundingAddEventListener;
import com.shuidihuzhu.cf.service.risk.user.tag.ISendUserTagAlarmService;
import com.shuidihuzhu.client.cf.risk.client.UserTagClient;
import com.shuidihuzhu.client.cf.risk.model.result.RiskRpcResponse;
import com.shuidihuzhu.client.cf.risk.model.result.UserTagHistory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.event.SmartApplicationListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 存储userTagHistory
 */
@Slf4j
@Service
public class UserTagClientListener extends AbstractCrowdFundingAddEventListener implements SmartApplicationListener {

    @Resource
    private UserTagClient userTagClient;

    @Resource
    private ISendUserTagAlarmService sendUserTagAlarmService;

    @Override
    public void onApplicationEvent(ApplicationEvent _event) {

        CrowdFundingAddEvent crowdFundingAddEvent = (CrowdFundingAddEvent) _event;
        CrowdfundingInfoBaseVo infoBaseVo = crowdFundingAddEvent.getCrowdfundingInfoBaseVo();
        CrowdfundingInfo cfCase = crowdFundingAddEvent.getCrowdfundingInfo();

        UserInfoModel userThirdModel = crowdFundingAddEvent.getUserInfoModel();

        if (infoBaseVo != null && cfCase != null) {
            try {
                RiskRpcResponse<UserTagHistory> response = userTagClient.writeUserTag(cfCase.getId(), infoBaseVo.getUserId());
                if(response == null || response.getCode() != 0 || response.getData() == null){
                    return;
                }

                sendUserTagAlarmService.sendAlarm(userThirdModel.getNickname() ,response.getData(), cfCase);

            } catch (Exception e) {
                log.error("userTagClient.writeUserTag error:", e);
            }
        }
    }

    @Override
    public int getOrder() {
        return AddListenerOrder.UserTagClient.getValue();
    }
}
