package com.shuidihuzhu.cf.service.lifecycle;

import com.shuidihuzhu.cf.util.CFWxService;
import org.springframework.context.SmartLifecycle;
import org.springframework.stereotype.Service;

/**
 * @author: <PERSON>
 * @date: 2018/5/22 20:43
 */
@Service
public class CfWxServiceLifecycle implements SmartLifecycle {

	private volatile boolean running;

	@Override
	public boolean isAutoStartup() {
		return true;
	}

	@Override
	public void stop(Runnable callback) {
		if (isRunning()) {
			running = false;
			CFWxService.shutdownAll();
			callback.run();
		}
	}

	@Override
	public void start() {
		if (!isRunning()) {
			running = true;
		}
	}

	@Override
	public void stop() {
		CFWxService.shutdownAll();
	}

	@Override
	public boolean isRunning() {
		return running;
	}

	@Override
	public int getPhase() {
		return 0;
	}
}
