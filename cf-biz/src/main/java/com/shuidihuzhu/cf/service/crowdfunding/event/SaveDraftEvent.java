package com.shuidihuzhu.cf.service.crowdfunding.event;

import lombok.Data;
import lombok.Getter;
import lombok.ToString;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @date 2019/12/16
 */

@Getter
@ToString
public class SaveDraftEvent extends ApplicationEvent {

    private long userId;
    private String pageFlag;
    private int cfVersion;

    public SaveDraftEvent(Object source, long userId, String pageFlag, int cfVersion) {
        super(source);
        this.userId = userId;
        this.pageFlag = pageFlag;
        this.cfVersion = cfVersion;
    }
}
