package com.shuidihuzhu.cf.service.caseinfo;

import com.shuidihuzhu.cf.dao.caseinfo.InfoCapitalCompensationDAO;
import com.shuidihuzhu.cf.domain.caseinfo.CfInfoCapitalCompensationDO;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoStatusEnum;
import com.shuidihuzhu.cf.response.OpResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2019-04-18  16:53
 */
@Service
@Slf4j
public class InfoCapitalCompensationServiceImpl implements InfoCapitalCompensationService{

    @Resource
    private InfoCapitalCompensationDAO dao;

    private static final String TAG = "InfoCapitalCompensationServiceImpl";

    @Override
    public CfInfoCapitalCompensationDO getInfo(int caseId) {
        CfInfoCapitalCompensationDO v = dao.getByCaseId(caseId);
        if (v == null) {
            v = new CfInfoCapitalCompensationDO();
            v.setStatus(CrowdfundingInfoStatusEnum.UN_SAVE.getCode());
            v.setCaseId(caseId);
            return v;
        }
        return v;
    }

    @Override
    public OpResult<CfInfoCapitalCompensationDO> save(CfInfoCapitalCompensationDO info) {
        log.info("{} save {}", TAG, info);
        int caseId = info.getCaseId();
        if (caseId <= 0) {
            return OpResult.createFailResult(CfErrorCode.SYSTEM_PARAM_ERROR, "no caseId");
        }

        CfInfoCapitalCompensationDO v = dao.getByCaseId(caseId);
        if (v == null) {
            // 保存后状态改为 已保存
            info.setStatus(CrowdfundingInfoStatusEnum.UN_SUBMITTED.getCode());
            int insert = dao.insert(info);
            return OpResult.createStorageResult(insert > 0, info);
        }

        // 如果已存在需要检查状态 已提交不能修改
        CrowdfundingInfoStatusEnum currentStatusEnum = CrowdfundingInfoStatusEnum.getByCode(v.getStatus());
        if (currentStatusEnum == CrowdfundingInfoStatusEnum.SUBMITTED) {
            log.info("{} save status error {}", TAG, v);
            return OpResult.createFailResult(CfErrorCode.CF_INFO_CAPITAL_COMPENSATION_HAS_SUBMIT);
        }

        info.setStatus(CrowdfundingInfoStatusEnum.UN_SUBMITTED.getCode());
        dao.updateByCaseId(info);
        return OpResult.createSucResult(info);
    }

    @Override
    public void onSubmit(int caseId) {
        if (caseId <= 0) {
            return ;
        }

        CfInfoCapitalCompensationDO v = dao.getByCaseId(caseId);
        if (v == null) {
            return;
        }

        // 如果已存在需要检查状态 已提交不能修改
        CrowdfundingInfoStatusEnum currentStatusEnum = CrowdfundingInfoStatusEnum.getByCode(v.getStatus());
        if (currentStatusEnum == CrowdfundingInfoStatusEnum.UN_SAVE) {
            log.info("{} onsubmit status error {}", TAG, v);
            return ;
        }
        dao.updateStatusByCaseId(caseId, CrowdfundingInfoStatusEnum.SUBMITTED.getCode());
    }
}
