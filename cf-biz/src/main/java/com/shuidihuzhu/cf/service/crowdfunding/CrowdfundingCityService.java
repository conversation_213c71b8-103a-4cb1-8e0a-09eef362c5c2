package com.shuidihuzhu.cf.service.crowdfunding;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingCityDao;
import com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingCityStreetDao;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCity;
import com.shuidihuzhu.cf.util.ListUtil;
import com.shuidihuzhu.client.cf.api.model.CardMsgVO;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;


@Slf4j
@Service
public class CrowdfundingCityService {
    private static final Logger LOGGER = LoggerFactory.getLogger(CrowdfundingCityService.class);
    private static String cacheKey = "crowdfunding-city-list-";
    private static Map<Integer, List<CrowdfundingCity>> cityMap = Maps.newHashMap();
    private static final Object lock = new Object();

    private static final String CITY_CACHE_KEY = "city-cache-key";

    private final LoadingCache<Integer,List<CrowdfundingCity>> getCityByRealParentIdCache = CacheBuilder
            .newBuilder()
            .refreshAfterWrite(3, TimeUnit.HOURS)
            .build(new CacheLoader<>() {
                @Override
                public List<CrowdfundingCity> load(@NotNull Integer realParentId) {
                    return cityDao.getByRealParentId(realParentId);
                }
            });

    private final LoadingCache<String, List<CrowdfundingCity>> getCityStreetByCodeCache = CacheBuilder
            .newBuilder()
            .refreshAfterWrite(3, TimeUnit.HOURS)
            .build(new CacheLoader<>() {
                @Override
                public List<CrowdfundingCity> load(@NotNull String areaCode) {
                    return crowdfundingCityStreetDao.selectByAreaCode(areaCode);
                }
            });

    private final LoadingCache<String, List<CrowdfundingCity>> getAllCityCache = CacheBuilder
            .newBuilder()
            .refreshAfterWrite(3, TimeUnit.HOURS)
            .build(new CacheLoader<>() {
                @Override
                public List<CrowdfundingCity> load(@NotNull String realParentId) {
                    return getAllCityFromDB();
                }
            });

    @Autowired
    private CrowdfundingCityDao cityDao;
    @Resource
    private CrowdfundingCityStreetDao crowdfundingCityStreetDao;

    @Resource(
            name = "cfRedissonHandler"
    )
    private RedissonHandler redissonHandler;

    public CrowdfundingCityService() {
    }

    public List<CrowdfundingCity> getProvince() {
        List<CrowdfundingCity> list = this.getCache(0);
        return list == null ? Collections.emptyList() : list;
    }

    public List<CrowdfundingCity> getChildren(int parentId) {
        List<CrowdfundingCity> list = this.getCache(parentId);
        return list == null ? Collections.emptyList() : list;
    }

    private void setCache() {
        synchronized (lock) {
            List<CrowdfundingCity> all = this.getAllCityFromDB();
            cityMap.clear();
            all.forEach((city) -> {
                List<CrowdfundingCity> list = (List) cityMap.get(city.getParentId());
                if (list == null) {
                    list = Lists.newArrayList();
                    cityMap.put(city.getParentId(), list);
                }

                ((List) list).add(city);
            });

            try {
                LOGGER.info("add citys to redis...");
                long leaseTimeSeconds = 3600L;
                Iterator var5 = cityMap.entrySet().iterator();

                while (var5.hasNext()) {
                    Map.Entry<Integer, List<CrowdfundingCity>> entry = (Map.Entry) var5.next();
                    Integer key = (Integer) entry.getKey();
                    List<CrowdfundingCity> value = (List) entry.getValue();
                    this.redissonHandler.addListEX(cacheKey + key, value, leaseTimeSeconds);
                }

                this.redissonHandler.setEX(cacheKey, "", leaseTimeSeconds * 1000L);
            } catch (Exception var10) {
                LOGGER.error("set city redis error", var10);
            }

        }
    }

    private List<CrowdfundingCity> getCache(int parentId) {
        List<CrowdfundingCity> redisList = this.redissonHandler.getList(cacheKey + parentId, CrowdfundingCity.class);
        LOGGER.info("get citys from redis");
        if (CollectionUtils.isNotEmpty(redisList)) {
            return redisList;
        } else {
            String flag = (String) this.redissonHandler.get(cacheKey, String.class);
            if (StringUtils.isNotBlank(flag)) {
                return Collections.emptyList();
            } else {
                List<CrowdfundingCity> localList = (List) cityMap.get(parentId);
                LOGGER.info("get citys from local");
                if (CollectionUtils.isNotEmpty(localList)) {
                    return localList;
                } else {
                    this.setCache();
                    localList = (List) cityMap.get(parentId);
                    return localList;
                }
            }
        }
    }

    // 原 com.shuidihuzhu.common.web.biz.CityBiz
    public List<CrowdfundingCity> getListByIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return this.cityDao.getByIds(ids);
    }

    public List<CrowdfundingCity> listByCityName(String cityName, int limit) {
        List<CrowdfundingCity> crowdfundingCityList = cityDao.listByCityName(cityName, limit);
        if (CollectionUtils.isEmpty(crowdfundingCityList)) {
            return getAllCity().stream()
                    .filter(item -> item.containNewCityName(cityName))
                    .limit(limit)
                    .collect(Collectors.toList());
        }
        return crowdfundingCityList;
    }

    public Map<Integer, CrowdfundingCity> getMapByIds(List<Integer> ids) {
        List<CrowdfundingCity> citys = this.getListByIds(ids);
        Map<Integer, CrowdfundingCity> map = Maps.newHashMap();
        citys.forEach(city -> {
            map.put(city.getId(), city);
        });
        return map;
    }

    public CrowdfundingCity getByCode(String code) {
        CrowdfundingCity cityByCode = cityDao.getByCode(code);
        if (cityByCode != null) {
            return cityByCode;
        }
        return getAllCity()
                .stream()
                .filter(item -> item.containNewCityCode(code))
                .findFirst()
                .orElse(null);
    }

    public List<CrowdfundingCity> getListByCodes(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Lists.newArrayList();
        }
        List<CrowdfundingCity> listByCodes = cityDao.getListByCodes(codes);
        List<CrowdfundingCity> getByCodeFromCache = getAllCity().stream()
                .filter(item -> item.containNewCityCodes(codes))
                .collect(Collectors.toList());
        listByCodes.addAll(getByCodeFromCache);
        return listByCodes.stream().filter(ListUtil.distinctByKey(CrowdfundingCity::getId)).collect(Collectors.toList());
    }

    public CrowdfundingCity getById(Integer id) {
        return cityDao.getById(id);
    }

    public CrowdfundingCity getTargetLevelByCityId(Integer cityId, int targetLevel) {
        for (int i = 0; i<3; i++) {
            CrowdfundingCity city = cityDao.getById(cityId);
            if (city == null) {
                return null;
            }
            if (city.getLevel() <= targetLevel) {
                return city;
            }
            cityId = city.getParentId();
        }
        return null;
    }

    public List<CrowdfundingCity> getAllLevel1City() {
        return cityDao.getAllLevel1City();
    }


    public Map<Integer, List<CrowdfundingCity>> getAllLevelCityByCityIds(List<Integer> cityIds) {
        Map<Integer, List<CrowdfundingCity>> result = Maps.newHashMap();
        if (CollectionUtils.isEmpty(cityIds)) {
            return result;
        }

        for (Integer cityId : cityIds) {
            result.put(cityId, Lists.reverse(getAllReverseByCity(cityId)));
        }

        return result;
    }

    public List<CrowdfundingCity> getAllReverseByCity(int cityId) {
        List<CrowdfundingCity> result = Lists.newArrayList();

        CrowdfundingCity city = cityDao.getById(cityId);
        if (city != null) {
            result.add(city);
            result.addAll(getAllReverseByCity(city.getParentId()));
        }

        return result;
    }

    public List<CrowdfundingCity> getRealChildren(int realParentId) {

        List<CrowdfundingCity> cityList = cityDao.getByRealParentId(realParentId);

        return Optional.ofNullable(cityList).orElse(Lists.newArrayList());
    }

    public CardMsgVO getCityByIdCard(String idCard) {
        CardMsgVO cardMsgVO = new CardMsgVO();
        if (StringUtils.isEmpty(idCard) || idCard.length() < 6) {
            return cardMsgVO;
        }
        try {
            if (idCard.length() >= 14) {
                String birthday = idCard.length() != 15 ? idCard.substring(6, 14) : "19" + idCard.substring(6, 12);
                Date birthdate = new SimpleDateFormat("yyyyMMdd").parse(birthday);
                GregorianCalendar currentDay = new GregorianCalendar();
                currentDay.setTime(birthdate);
                int year = currentDay.get(Calendar.YEAR);
                LocalDate localDate = LocalDate.now();
                cardMsgVO.setAge(localDate.getYear() - year);
            }

            String code = idCard.substring(0, 6);
            CrowdfundingCity county = getByCode(code);
            if (Objects.nonNull(county)) {
                cardMsgVO.setCounty(county.getName());
                CrowdfundingCity city = getById(county.getRealParentId());
                cardMsgVO.setCity(Objects.nonNull(city) ? city.getName() : "");
                if (Objects.nonNull(city)) {
                    CrowdfundingCity province = getById(city.getRealParentId());
                    cardMsgVO.setProvinceName(Objects.nonNull(province) ? province.getName() : "");
                }
            }
        } catch (Exception e) {
            log.info("getCityByIdCard ex {}", idCard, e);
        }

        return cardMsgVO;
    }

    public List<CrowdfundingCity> getCityStreetByCode(String areaCode) {
        List<CrowdfundingCity> cityList = new ArrayList<>();
        try {
            cityList = getCityStreetByCodeCache.get(areaCode);
        } catch (ExecutionException e) {
            log.error(this.getClass().getSimpleName()+"  getCityStreetByCode.get({})", areaCode, e);
        }
        return cityList;
    }

    public List<CrowdfundingCity> getByRealParentId(int realParentId) {
        List<CrowdfundingCity> cityList = new ArrayList<>();
        try {
            cityList = getCityByRealParentIdCache.get(realParentId);
        } catch (ExecutionException e) {
            log.error(this.getClass().getSimpleName()+"  getCityByParentIdCache.get({})", realParentId, e);
        }
        return cityList;
    }

    public CrowdfundingCity findByIdAndDelete(int id) {
        return cityDao.findByIdAndDelete(id);
    }

    public List<CrowdfundingCity> getCityByParentIds(List<Integer> parentIds) {
        if (CollectionUtils.isEmpty(parentIds)) {
            return Collections.emptyList();
        }
        List<CrowdfundingCity> crowdfundingCityList = getAllCity();
        if (CollectionUtils.isEmpty(crowdfundingCityList)) {
            return Collections.emptyList();
        }
        return crowdfundingCityList.stream().filter(item -> parentIds.contains(item.getParentId())).collect(Collectors.toList());
    }

    public Map<String, Integer> getCityNameMapCityId() {
        List<CrowdfundingCity> crowdfundingCityList = getAllCity();
        if (CollectionUtils.isEmpty(crowdfundingCityList)) {
            return Collections.emptyMap();
        }
        Map<String, Integer> resultMap = Maps.newHashMap();
        List<CrowdfundingCity> oldCrowdfundingCityList = crowdfundingCityList.stream().filter(item -> item.getParentId() != 0).collect(Collectors.toList());
        resultMap.putAll(oldCrowdfundingCityList.stream().collect(Collectors.toMap(CrowdfundingCity::getName, CrowdfundingCity::getId, (oldObj, newObj) -> newObj)));
        for (CrowdfundingCity crowdfundingCity : oldCrowdfundingCityList) {
            List<CrowdfundingCity.NewCityVersionInfo> newCityVersionInfoList = crowdfundingCity.getNewCityVersionInfoList();
            if (CollectionUtils.isEmpty(newCityVersionInfoList)) {
                continue;
            }
            newCityVersionInfoList.forEach(item -> {
                resultMap.put(item.getNewName(), crowdfundingCity.getId());
            });
        }
        return resultMap;
    }

    public List<CrowdfundingCity> listByCityNameAndLevel(String cityName, List<Integer> levels, Integer limit) {
        List<CrowdfundingCity> crowdfundingCityList = cityDao.listByCityNameAndLevel(cityName, levels, limit);
        if (limit == null) {
            limit = 5;
        }
        if (CollectionUtils.isEmpty(crowdfundingCityList)) {
            return getAllCity().stream()
                    .filter(item -> levels.contains(item.getRealLevel()) && item.containNewCityName(cityName))
                    .limit(limit)
                    .collect(Collectors.toList());
        }
        return crowdfundingCityList;
    }

    public List<CrowdfundingCity> listByCityNames(List<String> cityNames) {
        if (CollectionUtils.isEmpty(cityNames)) {
            return Lists.newArrayList();
        }
        List<CrowdfundingCity> allCity = getAllCity();
        if (CollectionUtils.isEmpty(allCity)) {
            return Lists.newArrayList();
        }
        List<CrowdfundingCity> resultCity = Lists.newArrayList();
        // 优先过滤老名称，只有当老名称不存在才过滤新名称
        allCity.forEach(item -> {
            if (cityNames.contains(item.getName())) {
                resultCity.add(item);
            }
        });
        List<String> matchCityNames = resultCity.stream().map(CrowdfundingCity::getName).collect(Collectors.toList());
        List<String> notMatchCityNames = cityNames.stream().filter(item -> !matchCityNames.contains(item)).collect(Collectors.toList());
        if (resultCity.size() < cityNames.size()) {
            allCity.forEach(item -> {
                if (item.containNewCityNames(notMatchCityNames)) {
                    resultCity.add(item);
                }
            });
        }
        return resultCity.stream()
                .filter(ListUtil.distinctByKey(CrowdfundingCity::getId))
                .collect(Collectors.toList());
    }

    public List<CrowdfundingCity> getByProvinceAndCityNameByLike(String city, int parentId) {
        if (StringUtils.isEmpty(city) || parentId < 0) {
            return Collections.emptyList();
        }
        List<CrowdfundingCity> crowdfundingCityList = cityDao.getByProvinceAndCityNameByLike(city, parentId);
        if (CollectionUtils.isEmpty(crowdfundingCityList)) {
            return getAllCity().stream()
                    .filter(item -> item.containNewCityName(city) && item.getParentId() == parentId
                    && item.getLevel() == 1)
                    .distinct()
                    .collect(Collectors.toList());
        }
        return crowdfundingCityList;
    }

    private List<CrowdfundingCity> getAllCity() {
        List<CrowdfundingCity> crowdfundingCityList = Lists.newArrayList();
        try {
            crowdfundingCityList = getAllCityCache.get(CITY_CACHE_KEY);
        } catch (ExecutionException e) {
            crowdfundingCityList = getAllCityFromDB();
            log.error(this.getClass().getSimpleName() + " getAllCity", e);
        }
        return crowdfundingCityList.stream().filter(item -> item.getValid() == 1).collect(Collectors.toList());
    }

    /**
     * 分批获取所有城市数据
     * @return
     */
    public List<CrowdfundingCity> getAllCityFromDB() {
        int pageSize = 500;
        int fetchId = 0;
        List<CrowdfundingCity> result = Lists.newArrayList();
        List<CrowdfundingCity> cityByFetchId = Lists.newArrayList();
        while (cityByFetchId.size() <= pageSize) {
            cityByFetchId = cityDao.getCityByFetchId(fetchId, pageSize);
            if (CollectionUtils.isEmpty(cityByFetchId)) {
                break;
            }
            fetchId = cityByFetchId.get(cityByFetchId.size() - 1).getId();
            result.addAll(cityByFetchId);
        }
        return result;
    }
}
