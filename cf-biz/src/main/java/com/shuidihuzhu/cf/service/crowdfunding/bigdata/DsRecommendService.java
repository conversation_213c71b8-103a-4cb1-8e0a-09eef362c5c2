package com.shuidihuzhu.cf.service.crowdfunding.bigdata;

import com.shuidihuzhu.client.dataservice.ds.v1.DsApiClient;
import com.shuidihuzhu.client.model.InnerRecHzDO;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author: l<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2018/8/7 18:09
 */
@Service
@Slf4j
public class DsRecommendService {
    @Autowired
    private DsApiClient dsApiClient;

    /**
     * 水滴筹捐款成功页，给互助的出价推荐
     *
     * @param userId
     * @param nickname
     * @param ua
     * @param channel
     * @param ip
     * @param isNew
     * @param shareDv
     * @param targetAmount
     * @param totalAmount
     * @param totalCnt
     * @param orderId
     * @param amount
     * @param anonymous
     * @param comment
     * @return
     */
    public Response getPrice(long userId, String nickname, String ua, String channel, String ip, int isNew, int shareDv,
                             int targetAmount, int totalAmount, int totalCnt, long orderId, int amount, int anonymous,
                             String comment) {
        com.shuidihuzhu.client.model.Response response = dsApiClient.price(userId, nickname, ua, channel, ip, isNew,
                shareDv, targetAmount, totalAmount, totalCnt, orderId, amount, anonymous, comment);
        if (null == response) {
            return null;
        }
        Response result = new Response();
        result.setCode(response.getCode());
        result.setMsg(response.getMsg());
        result.setData(response.getData());
        return result;
    }

    /**
     * 水滴筹捐款成功页，给互助的出价推荐
     *
     * @param userId
     * @param nickname
     * @param ua
     * @param channel
     * @param ip
     * @param isNew
     * @param shareDv
     * @param targetAmount
     * @param totalAmount
     * @param totalCnt
     * @param orderId
     * @param amount
     * @param anonymous
     * @param comment
     * @return
     */
    public Response<InnerRecHzDO> getPriceLevel(long userId, String nickname, String ua, String channel, String ip, int isNew, int shareDv,
                                  int targetAmount, int totalAmount, int totalCnt, long orderId, int amount, int anonymous,
                                  String comment) {
        com.shuidihuzhu.client.model.Response<InnerRecHzDO> response = dsApiClient.priceLevel(userId, nickname, ua, channel, ip, isNew,
                shareDv, targetAmount, totalAmount, totalCnt, orderId, amount, anonymous, comment);
        if (null == response) {
            return null;
        }
        Response<InnerRecHzDO> result = new Response<>();
        result.setCode(response.getCode());
        result.setMsg(response.getMsg());
        result.setData(response.getData());
        return result;
    }
}
