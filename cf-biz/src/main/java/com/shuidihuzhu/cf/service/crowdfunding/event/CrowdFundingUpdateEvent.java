package com.shuidihuzhu.cf.service.crowdfunding.event;

import com.shuidihuzhu.account.model.UserThirdModel;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoBaseVo;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import lombok.Data;
import org.springframework.context.ApplicationEvent;

/**
 * 更新筹款事件
 */
public class CrowdFundingUpdateEvent extends ApplicationEvent {
    /**
     *
     */
    private static final long serialVersionUID = 1L;
    private CrowdfundingInfoBaseVo crowdfundingInfoBaseVo;
    private CrowdfundingInfo crowdfundingInfo;

    public CrowdfundingInfoBaseVo getCrowdfundingInfoBaseVo() {
        return crowdfundingInfoBaseVo;
    }

    public void setCrowdfundingInfoBaseVo(CrowdfundingInfoBaseVo crowdfundingInfoBaseVo) {
        this.crowdfundingInfoBaseVo = crowdfundingInfoBaseVo;
    }

    public CrowdfundingInfo getCrowdfundingInfo() {
        return crowdfundingInfo;
    }

    public void setCrowdfundingInfo(CrowdfundingInfo crowdfundingInfo) {
        this.crowdfundingInfo = crowdfundingInfo;
    }

    public Message getMessage() {
        return message;
    }

    public void setMessage(Message message) {
        this.message = message;
    }

    private Message message;


    public CrowdFundingUpdateEvent(Object source, CrowdfundingInfoBaseVo crowdfundingInfoBaseVo,
                                   CrowdfundingInfo crowdfundingInfo, Message message) {
        super(source);
        this.crowdfundingInfoBaseVo = crowdfundingInfoBaseVo;
        this.crowdfundingInfo = crowdfundingInfo;
        this.message = message;
    }
}
