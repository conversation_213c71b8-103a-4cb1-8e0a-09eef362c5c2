package com.shuidihuzhu.cf.service.crowdfunding;

import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoHospitalPayeeBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoStatusBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.material.IPayeeMaterialCenterService;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoDataStatusTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoStatusEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingRelationType;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoHospitalPayee;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoStatus;
import com.shuidihuzhu.cf.response.crowdfunding.CrowdfundingInfoResponse;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.common.web.enums.ErrorCode;
import com.shuidihuzhu.common.web.enums.LoginResult;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by niejiangnan on 2017/9/26.
 */
@Service
@Slf4j
public class CfInfoHospitalPayeeService {
    @Autowired
    private CrowdfundingInfoHospitalPayeeBiz crowdfundingInfoHospitalPayeeBiz;
    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private CrowdfundingInfoStatusBiz crowdfundingInfoStatusBiz;
    @Autowired
    private IPayeeMaterialCenterService payeeMaterialService;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;


    public Response<CrowdfundingInfoResponse.Data> saveHospitalPayee(CrowdfundingInfoHospitalPayee crowdfundingInfoHospitalPayee,long userId ){

        Response r = validate(crowdfundingInfoHospitalPayee);

        if (r.notOk()){
            return r;
        }

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(crowdfundingInfoHospitalPayee.getInfoUuid());
        if (crowdfundingInfo == null) {
            return NewResponseUtil.makeError(ErrorCode.SYSTEM_PARAM_ERROR);
        }
        log.info("crowdfundingInfo id：{}", crowdfundingInfo.getId());
        if (userId <= 0) {
            log.warn("CrowdfundingV4InfoUpdateController addOrUpdateHospitalDrawcashInfo userId is {}", userId);
            return NewResponseUtil.makeResponse(LoginResult.LOGIN.code, LoginResult.LOGIN.msg, null);
        }
        log.info("CrowdfundingV4InfoUpdateController addOrUpdateHospitalDrawcashInfo addOrUpdate");
        String hospitalBankCard = crowdfundingInfoHospitalPayee.getHospitalBankCard();
        hospitalBankCard =  StringUtils.trimToEmpty(hospitalBankCard).replaceAll("\\s+","")
                .replaceAll("–","-");
        crowdfundingInfoHospitalPayee.setHospitalBankCard(hospitalBankCard);
        String encryptHospitalBankCard = oldShuidiCipher.aesEncrypt(crowdfundingInfoHospitalPayee.getHospitalBankCard());
        if (encryptHospitalBankCard.length() > 90) {
            return NewResponseUtil.makeFail("银行账户超出合法长度，请再次核对");
        }
        crowdfundingInfoHospitalPayee.setHospitalBankCard(encryptHospitalBankCard);
        CrowdfundingInfoResponse response = addOrUpdate(userId, crowdfundingInfoHospitalPayee, crowdfundingInfo);
        if (response == null) {
            return NewResponseUtil.makeSuccess(null);
        }
        return NewResponseUtil.makeResponse(response.getErrorCode().getCode(), response.getErrorCode().getMsg(),
                response.getData());
    }

    private Response validate(CrowdfundingInfoHospitalPayee crowdfundingInfoHospitalPayee){

        if (crowdfundingInfoHospitalPayee == null) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }

        if (StringUtils.isBlank(crowdfundingInfoHospitalPayee.getInfoUuid())
                || StringUtils.isBlank(crowdfundingInfoHospitalPayee.getDepartment())
                || StringUtils.isBlank(crowdfundingInfoHospitalPayee.getBedNum())
                || StringUtils.isBlank(crowdfundingInfoHospitalPayee.getHospitalizationNum())
                || StringUtils.isBlank(crowdfundingInfoHospitalPayee.getHospitalAccountName())
                || StringUtils.isBlank(crowdfundingInfoHospitalPayee.getHospitalBankCard())
                || StringUtils.isBlank(crowdfundingInfoHospitalPayee.getHospitalBankBranchName())) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR_IS_NULL);
        }

        if (crowdfundingInfoHospitalPayee.getHospitalAccountName().length()>30
                || crowdfundingInfoHospitalPayee.getDepartment().length() > 30
                || crowdfundingInfoHospitalPayee.getBedNum().length() >30
                || crowdfundingInfoHospitalPayee.getHospitalizationNum().length() >32
        ){
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR_TOO_LONG);
        }

        return NewResponseUtil.makeSuccess("");
    }

    public CrowdfundingInfoResponse addOrUpdate(long userId, CrowdfundingInfoHospitalPayee crowdfundingInfoHospitalPayee,
                                                CrowdfundingInfo crowdfundingInfo) {
        String infoUuid = crowdfundingInfoHospitalPayee.getInfoUuid();
        CfErrorCode verifyResult = verifyInfoId(crowdfundingInfo, userId);
        if (!verifyResult.equals(CfErrorCode.SUCCESS)) {
            CrowdfundingInfoResponse response = new CrowdfundingInfoResponse();
            response.setErrorCode(verifyResult);
            return response;
        }
        crowdfundingInfoHospitalPayee.setRelationType(CrowdfundingRelationType.getCode(CrowdfundingRelationType.LOCATION_HOSPITAL_ACCOUNT));
        if (crowdfundingInfoHospitalPayeeBiz.getByInfoUuid(infoUuid) == null) {
            crowdfundingInfoHospitalPayeeBiz.add(crowdfundingInfoHospitalPayee);
            if (crowdfundingInfoStatusBiz.getByInfoUuidAndType(infoUuid,
                    CrowdfundingInfoDataStatusTypeEnum.PAYEE_INFO_SUBMIT.getCode()) == null) {
                // 提交收款人信息状态
                CrowdfundingInfoStatus crowdfundingInfoStatus = new CrowdfundingInfoStatus();
                crowdfundingInfoStatus.setInfoUuid(infoUuid);
                crowdfundingInfoStatus.setType(CrowdfundingInfoDataStatusTypeEnum.PAYEE_INFO_SUBMIT.getCode());
                crowdfundingInfoStatus.setStatus(CrowdfundingInfoStatusEnum.UN_SUBMITTED.getCode());
                // 新增案例id
                crowdfundingInfoStatus.setCaseId(crowdfundingInfo.getId());
                this.crowdfundingInfoStatusBiz.add(crowdfundingInfoStatus);
            } else {
                this.crowdfundingInfoStatusBiz.updateByInfoId(infoUuid,
                        CrowdfundingInfoDataStatusTypeEnum.PAYEE_INFO_SUBMIT.getCode(),
                        CrowdfundingInfoStatusEnum.UN_SUBMITTED);
            }
        } else {
            crowdfundingInfoHospitalPayeeBiz.update(crowdfundingInfoHospitalPayee);
            this.crowdfundingInfoStatusBiz.updateByInfoId(infoUuid,
                    CrowdfundingInfoDataStatusTypeEnum.PAYEE_INFO_SUBMIT.getCode(),
                    CrowdfundingInfoStatusEnum.UN_SUBMITTED);
        }
        crowdfundingInfo.setRelationType(CrowdfundingRelationType.LOCATION_HOSPITAL_ACCOUNT);
        crowdfundingInfoBiz.updateRelationType(crowdfundingInfo);

        payeeMaterialService.addOrUpdateHospitalAccount(crowdfundingInfo.getId(), crowdfundingInfoHospitalPayee, null);
        return null;
    }

    //检验能否被修改
    private CfErrorCode verifyInfoId(CrowdfundingInfo crowdfundingInfo, long requestUserId) {
        if (crowdfundingInfo == null) {
            return CfErrorCode.CF_NOT_FOUND;
        }
        if (crowdfundingInfo.getStatus() != CrowdfundingStatus.APPROVE_PENDING
                && crowdfundingInfo.getStatus() != CrowdfundingStatus.APPROVE_DENIED) {
            return CfErrorCode.CF_CAN_NOT_EDIT;
        }
        if (crowdfundingInfo.getUserId() != requestUserId) {
            return CfErrorCode.CF_INFO_PARAM_ERROR_IDENTITY_INVALID;
        }
        return CfErrorCode.SUCCESS;
    }
}
