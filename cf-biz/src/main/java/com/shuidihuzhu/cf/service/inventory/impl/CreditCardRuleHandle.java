package com.shuidihuzhu.cf.service.inventory.impl;

import com.shuidihuzhu.cf.biz.crowdfunding.impl.CrowdfundingInfoBizImpl;
import com.shuidihuzhu.cf.biz.crowdfunding.impl.CrowdfundingInfoHospitalPayeeBizImpl;
import com.shuidihuzhu.cf.biz.crowdfunding.impl.CrowdfundingInfoPayeeBizImpl;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingRelationType;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoHospitalPayee;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoPayee;
import com.shuidihuzhu.cf.model.inventory.SecondInventoryDetail;
import com.shuidihuzhu.cf.service.inventory.InventoryRuleHandle;
import com.shuidihuzhu.cf.util.DesensitizationUtil;
import com.shuidihuzhu.cipher.ShuidiCipher;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Description: 银行卡号
 * @Author: panghairui
 * @Date: 2023/1/12 11:06 上午
 */
@Slf4j
@Service("creditCardRuleHandle")
public class CreditCardRuleHandle implements InventoryRuleHandle {

    @Resource
    private ShuidiCipher shuidiCipher;

    @Resource
    private CrowdfundingInfoBizImpl crowdfundingInfoBiz;

    @Resource
    private CrowdfundingInfoPayeeBizImpl crowdfundingInfoPayeeBiz;

    @Resource
    private CrowdfundingInfoHospitalPayeeBizImpl crowdfundingInfoHospitalPayeeBiz;

    @Override
    public void handle(SecondInventoryDetail secondInventoryDetail, long time) {

        // 取该userId最近的案例
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getLastByUserId(secondInventoryDetail.getUserId());
        if (Objects.isNull(crowdfundingInfo)) {
            return;
        }

        // 银行卡号区分对公和非对公，只展示最新的

        // 非对公
        CrowdfundingInfoPayee crowdfundingInfoPayee = crowdfundingInfoPayeeBiz.getByInfoUuid(crowdfundingInfo.getInfoId());
        // 对公
        CrowdfundingInfoHospitalPayee crowdfundingInfoHospitalPayee = crowdfundingInfoHospitalPayeeBiz.getByInfoUuid(crowdfundingInfo.getInfoId());
        if (Objects.isNull(crowdfundingInfoPayee) && Objects.isNull(crowdfundingInfoHospitalPayee)) {
            return;
        }

        if (Objects.nonNull(crowdfundingInfoPayee) && Objects.nonNull(crowdfundingInfoHospitalPayee)) {
            String bankCard = (crowdfundingInfoPayee.getDateCreated().getTime() > crowdfundingInfoHospitalPayee.getCreateTime().getTime())
                    ? crowdfundingInfoPayee.getBankCard()
                    : crowdfundingInfoHospitalPayee.getHospitalBankCard();
            long bankTime = Math.max(crowdfundingInfoPayee.getDateCreated().getTime(), crowdfundingInfoHospitalPayee.getCreateTime().getTime());
            if (StringUtils.isEmpty(bankCard) || bankTime < time) {
                return;
            }

            bankCard = shuidiCipher.decrypt(bankCard);
            secondInventoryDetail.setContent(DesensitizationUtil.desensitizationNumber(bankCard));
            secondInventoryDetail.setSituation("已收集1条");
            return;
        }

        String bankCard = Objects.isNull(crowdfundingInfoPayee) ? crowdfundingInfoHospitalPayee.getHospitalBankCard() : crowdfundingInfoPayee.getBankCard();
        long bankTime = Objects.isNull(crowdfundingInfoPayee) ? crowdfundingInfoHospitalPayee.getCreateTime().getTime() : crowdfundingInfoPayee.getDateCreated().getTime();
        if (StringUtils.isEmpty(bankCard) || bankTime < time) {
            return;
        }

        bankCard = shuidiCipher.decrypt(bankCard);
        secondInventoryDetail.setContent(DesensitizationUtil.desensitizationNumber(bankCard));
        secondInventoryDetail.setSituation("已收集1条");

    }
}
