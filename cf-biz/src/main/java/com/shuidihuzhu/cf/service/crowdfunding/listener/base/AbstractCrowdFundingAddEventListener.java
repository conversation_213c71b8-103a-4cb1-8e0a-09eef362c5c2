package com.shuidihuzhu.cf.service.crowdfunding.listener.base;

import com.shuidihuzhu.cf.service.crowdfunding.event.CrowdFundingAddEvent;
import com.shuidihuzhu.cf.service.crowdfunding.event.CrowdFundingAddPublisher;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.event.SmartApplicationListener;

/**
 * <AUTHOR>
 * @date 2019-08-19
 */
public abstract class AbstractCrowdFundingAddEventListener implements SmartApplicationListener {

    @Override
    public boolean supportsSourceType(Class<?> sourceType) {
        return sourceType == CrowdFundingAddPublisher.class;
    }

    @Override
    public boolean supportsEventType(Class<? extends ApplicationEvent> eventType) {
        return CrowdFundingAddEvent.class.isAssignableFrom(eventType);
    }
}
