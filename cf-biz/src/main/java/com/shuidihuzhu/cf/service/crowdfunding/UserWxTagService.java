package com.shuidihuzhu.cf.service.crowdfunding;

import com.alibaba.druid.util.StringUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidi.weixin.common.bean.result.WxError;
import com.shuidi.weixin.common.exception.WxErrorException;
import com.shuidi.weixin.mp.api.WxMpService;
import com.shuidi.weixin.mp.bean.tag.WxTagListUser;
import com.shuidihuzhu.account.model.UserThirdModel;
import com.shuidihuzhu.account.model.service.OpenIdUserIdModel;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.WxConstants;
import com.shuidihuzhu.cf.constants.crowdfunding.DSTokenInfo;
import com.shuidihuzhu.cf.delegate.SimpleUserAccountDelegate;
import com.shuidihuzhu.cf.delegate.UserThirdDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.UserTagEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.UserTagGroup;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.util.ListUtil;
import com.shuidihuzhu.cf.vo.UserInfoVo;
import com.shuidihuzhu.client.account.v1.accountservice.OpenIdUserIdResponse;
import com.shuidihuzhu.data.analytics.javasdk.core.Analytics;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import com.shuidihuzhu.wx.biz.ShuidiWxService;
import com.shuidihuzhu.wx.enums.AccountThirdTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Ahrievil
 * @date : 2018/5/10 16:26
 */
@Slf4j
@Service
public class UserWxTagService {

    private static final Integer THIRD_TYPE = WxConstants.FUNDRAISER_THIRD_TYPE;

    @Autowired
    private ShuidiWxService shuidiWxService;
    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private UserThirdDelegate userThirdDelegate;
    @Autowired
    private SimpleUserAccountDelegate simpleUserAccountDelegate;
    @Autowired(required = false)
    private Producer producer;
    @Autowired
    private Analytics analytics;

    public boolean addTagToPreChouKuanUser(long userId, String openId, WxMpService wxMpService, UserTagEnum userTagEnum) {
        if (userId == 0) {
            return false;
        }
        log.debug("addTagToPreChouKuanUser userId:{} userTagEnum:{}", userId, userTagEnum);
        boolean addToBigData = addTagToUser(userId, userTagEnum);
        boolean addToWx = false;
        try {
            addToWx = wxMpService.addTagToOpenIdList(userTagEnum.getTagId(), Lists.newArrayList(openId));
        } catch (WxErrorException e) {
            handlerWxError(e, userId);
        } catch (Exception e) {
            log.debug("UserWxTagService addTagToPreChouKuanUser error", e);
        }
        return addToBigData && addToWx;
    }

    public boolean addTagToPreChouKuanUserList(List<String> openIdList, WxMpService wxMpService) {
        UserTagEnum userTagEnum = UserTagEnum.getRandomByGroup(1);
        return addTagToUser(openIdList, wxMpService, userTagEnum);
    }

    public boolean addTagToIngChouKuan(long userId, String openId, WxMpService wxMpService, UserTagEnum userTagEnum) {
        if (userId == 0) {
            return false;
        }
        log.debug("UserWxTagService addTagToIngChouKuan userId:{} userTagEnum:{}", userId, userTagEnum);
        boolean addToBigData = addTagToUser(userId, userTagEnum);
        boolean addToWx = false;
        try {
            addToWx = wxMpService.addTagToOpenIdList(userTagEnum.getTagId(), Lists.newArrayList(openId));
        } catch (WxErrorException e) {
            handlerWxError(e, userId);
        } catch (Exception e) {
            log.debug("UserWxTagService addTagToIngChouKuan error", e);
        }
        return addToBigData || addToWx;
    }

    public boolean addTagToIngChouKuanUserList(List<String> openIdList, WxMpService wxMpService) {
        UserTagEnum userTagEnum = UserTagEnum.getRandomByGroup(2);
        return addTagToUser(openIdList, wxMpService, userTagEnum);
    }

    public boolean addTagToEndChouKuan(List<UserThirdModel> userThirdModels, WxMpService wxMpService, UserTagEnum userTagEnum) {
        if (CollectionUtils.isEmpty(userThirdModels)) {
            return false;
        }
        log.debug("UserWxTagService addTagToEndChouKuan randomByGroup:{}", userTagEnum);
        List<List<UserThirdModel>> partition = Lists.partition(userThirdModels, 3000);
        partition.forEach(list -> {
            List<List<String>> userThirdPartition = Lists.partition(list.stream()
                    .map(UserThirdModel::getOpenId).collect(Collectors.toList()), 50);
            userThirdPartition.forEach(openIdList -> {
                try {
                    wxMpService.addTagToOpenIdList(userTagEnum.getTagId(), openIdList);
                } catch (WxErrorException e) {
                    handlerWxError(e, 0);
                } catch (Exception e) {
                    log.debug("UserWxTagService addTagToEndChouKuan error", e);
                }
            });
            List<Long> userIds = list.stream().map(UserThirdModel::getUserId).collect(Collectors.toList());
            userIds.forEach(val -> addTagToUser(val, userTagEnum));
        });
        return true;
    }

    public boolean addTagToUser(List<String> openIdList, WxMpService wxMpService, UserTagEnum userTagEnum) {
        if (CollectionUtils.isEmpty(openIdList)) {
            return false;
        }
        log.debug("UserWxTagService addTagToEndCaseUser openIdList size:{} userTagEnum:{}", openIdList.size(), userTagEnum);
        List<OpenIdUserIdModel> thirdModelByOpenids = simpleUserAccountDelegate.getUserIdsByOpenIds(openIdList);
        List<Long> userIdList = thirdModelByOpenids.stream().map(OpenIdUserIdModel::getUserId).collect(Collectors.toList());
        List<String> openIds = thirdModelByOpenids.stream().map(OpenIdUserIdModel::getOpenId).collect(Collectors.toList());
        List<List<String>> openIdPartition = Lists.partition(openIds, 50);
        openIdPartition.forEach(list -> {
            try {
                wxMpService.addTagToOpenIdList(userTagEnum.getTagId(), list);
            } catch (Exception e) {
                log.debug("UserWxTagService addTagToUser error", e);
            }
        });
        userIdList.forEach(val -> addTagToUser(val, userTagEnum));
        return true;
    }

    public boolean addTagToEndCaseUser(List<String> openIdList, WxMpService wxMpService) {

        UserTagEnum userTagEnum = UserTagEnum.getRandomByGroup(3);
        return addTagToUser(openIdList, wxMpService, userTagEnum);
    }

    public boolean addTagToJuanKuan(long userId, String openId, WxMpService wxMpService) {
        if (userId == 0) {
            return false;
        }
        UserTagEnum userTagEnum = UserTagEnum.getRandomByGroup(4);
        log.debug("UserWxTagService addTagToJuanKuan userId:{} randomByGroup:{}", userId, userTagEnum);
        boolean bigDataResult = addTagToUser(userId, userTagEnum);
        boolean addToWx = false;
        try {
            addToWx = wxMpService.addTagToOpenIdList(userTagEnum.getTagId(), Lists.newArrayList(openId));
        } catch (WxErrorException e) {
            handlerWxError(e, userId);
        } catch (Exception e) {
            log.debug("UserWxTagService addTagToJuanKuan error", e);
        }
        return bigDataResult || addToWx;
    }

    public boolean removeLabel(long userId) {
        if (userId == 0) {
            return false;
        }
        return addTagToUser(userId, UserTagEnum.NO_TAG);
    }

    public boolean addTagToUser(long userId, UserTagEnum tag) {
        analytics.profileSet(DSTokenInfo.NEW_TOKEN, String.valueOf(userId), DSTokenInfo.BIZ, DSTokenInfo.PROPER_TY_CHOUKUAN_STATUS, tag.getWord());
        return true;
    }

    public boolean subscribeHandler(long userId) {
        UserThirdModel thirdModelWithUserId = userThirdDelegate.getThirdModelWithUserId(userId, THIRD_TYPE);
        if (thirdModelWithUserId == null) {
            return false;
        }
        String openId = thirdModelWithUserId.getOpenId();
        int group = 1;
        //查看是否有发起过案例
        List<CrowdfundingInfo> crowdfundingInfos = crowdfundingInfoBiz.selectByUserId(userId);
        WxMpService wxMpService = shuidiWxService.getWxService(THIRD_TYPE);
        List<Long> tagId;
        try {
            tagId = wxMpService.getTagIdListByOpenId(openId);
        } catch (WxErrorException e) {
            handlerWxError(e, userId);
            return false;
        } catch (Exception e) {
            log.debug("UserWxTagService subscribeHandler error", e);
            return false;
        }
        if (CollectionUtils.isEmpty(crowdfundingInfos)) {
            //查看是否有潜在筹款人的标签
            if (! UserTagEnum.isConatinsByGroup(tagId, group)) {
                UserTagEnum userTagEnum = UserTagEnum.getRandomByGroup(1);
                return addTagToPreChouKuanUser(userId, openId, wxMpService, userTagEnum);
            }
        } else {
            deleteTagFromOpenId(Lists.newArrayList(openId), UserTagEnum.PRE_CHOUKUAN_A.getTagId(), wxMpService);
            deleteTagFromOpenId(Lists.newArrayList(openId), UserTagEnum.PRE_CHOUKUAN_B.getTagId(), wxMpService);
            if (crowdfundingInfos.stream().anyMatch(val -> val.getEndTime().after(new Date()))) {
                if (! UserTagEnum.isConatinsByGroup(tagId, 2)) {
                    // 2标识的是正在发起的标签组
                    UserTagEnum ing = UserTagEnum.getRandomByGroup(2);
                    addTagToIngChouKuan(userId, openId, wxMpService, ing);
                }
            } else {
                if (! UserTagEnum.isConatinsByGroup(tagId, 3)) {
                    addTagToEndChouKuan(Lists.newArrayList(thirdModelWithUserId), wxMpService, UserTagEnum.getRandomByGroup(3));
                }
            }
        }
        return false;
    }

    public boolean raiseCaseHandler(String openId, long userId) {
        if (StringUtils.isEmpty(openId)) {
            return false;
        }
        WxMpService wxMpService = shuidiWxService.getWxService(THIRD_TYPE);
        List<Long> tagId;
        try {
            tagId = wxMpService.getTagIdListByOpenId(openId);
        } catch (WxErrorException e) {
            handlerWxError(e, userId);
            return false;
        } catch (Exception e) {
            log.debug("UserWxTagService raiseCaseHandler subscribeHandler error", e);
            return false;
        }
        //查看有没有相应的标签
        if (tagId.stream().noneMatch(UserTagEnum::isTagIdContains)) {
            UserTagEnum raiseCaseGroup = UserTagEnum.getRandomByGroup(2);
            return addTagToIngChouKuan(userId, openId, wxMpService, raiseCaseGroup);
        } else {
            //查看是哪个组的标签
            if (UserTagEnum.isConatinsByGroup(tagId, 1)) {
                UserTagEnum pre = UserTagEnum.getTagIdByGroupInList(tagId, 1);
                if (pre == null) {
                    return false;
                }
                int testGroup = pre.getTestGroup();
                deleteTagFromOpenId(Lists.newArrayList(openId), pre.getTagId(), wxMpService);
                UserTagEnum byGroupAndTest = UserTagEnum.getByGroupAndTest(2, testGroup);
                return addTagToIngChouKuan(userId, openId, wxMpService, byGroupAndTest);
            } else if (UserTagEnum.isConatinsByGroup(tagId, 3)) {
                UserTagEnum end = UserTagEnum.getTagIdByGroupInList(tagId, 3);
                if (end == null) {
                    return false;
                }
                int testGroup = end.getTestGroup();
                deleteTagFromOpenId(Lists.newArrayList(openId), end.getTagId(), wxMpService);
                UserTagEnum byGroupAndTest = UserTagEnum.getByGroupAndTest(2, testGroup);
                return addTagToIngChouKuan(userId, openId, wxMpService, byGroupAndTest);
            }
        }

        return true;
    }


    public boolean endRaiseHandler(long userId) {
        if (userId <= 0) {
            return false;
        }
        List<CrowdfundingInfo> crowdfundingInfos = crowdfundingInfoBiz.selectByUserId(userId);
        UserThirdModel userThirdModel = userThirdDelegate.getThirdModelWithUserId(userId, THIRD_TYPE);
        if (userThirdModel == null) {
            return false;
        }
        Date now = new Date();
        WxMpService wxMpService = shuidiWxService.getWxService(THIRD_TYPE);
        if (crowdfundingInfos.stream().noneMatch(val -> val.getEndTime().after(now))) {
            List<Long> tagId;
            try {
                tagId = wxMpService.getTagIdListByOpenId(userThirdModel.getOpenId());
            } catch (WxErrorException e) {
                handlerWxError(e, userId);
                return false;
            } catch (Exception e) {
                log.debug("UserWxTagService endRaiseHandler error", e);
                return false;
            }
            if (tagId.stream().anyMatch(UserTagEnum::isTagIdContains)) {
                if (UserTagEnum.isConatinsByGroup(tagId, 1)) {
                    UserTagEnum pre = UserTagEnum.getTagIdByGroupInList(tagId, 1);
                    if (pre == null) {
                        return false;
                    }
                    int testGroup = pre.getTestGroup();
                    deleteTagFromOpenId(Lists.newArrayList(userThirdModel.getOpenId()), pre.getTagId(), wxMpService);
                    UserTagEnum byGroupAndTest = UserTagEnum.getByGroupAndTest(3, testGroup);
                    return addTagToEndChouKuan(Lists.newArrayList(userThirdModel), wxMpService, byGroupAndTest);
                } else if (UserTagEnum.isConatinsByGroup(tagId, 2)) {
                    UserTagEnum ing = UserTagEnum.getTagIdByGroupInList(tagId, 2);
                    if (ing == null) {
                        return false;
                    }
                    int testGroup = ing.getTestGroup();
                    deleteTagFromOpenId(Lists.newArrayList(userThirdModel.getOpenId()), ing.getTagId(), wxMpService);
                    UserTagEnum byGroupAndTest = UserTagEnum.getByGroupAndTest(3, testGroup);
                    return addTagToEndChouKuan(Lists.newArrayList(userThirdModel), wxMpService, byGroupAndTest);
                }
            } else {
                UserTagEnum userTagEnum = UserTagEnum.getRandomByGroup(3);
                return addTagToEndChouKuan(Lists.newArrayList(userThirdModel), wxMpService, userTagEnum);
            }
        }

        return true;
    }

    /**
     * @param userIdList 已经结束的案例的用户
     * @return
     */
    public boolean endRaiseHandler(List<Long> userIdList) {
        List<Long> notFinishUserId = ListUtil.getList(3000,
                (start, size) -> new ArrayList<>(crowdfundingInfoBiz.selectUserIdByThirdTypeWhenNotFinishCase(start, size, THIRD_TYPE)));
        //去除还在筹款的用户id，置之不理
        userIdList.removeAll(notFinishUserId);
        WxMpService wxMpService = shuidiWxService.getWxService(THIRD_TYPE);
        List<String> openIdList = getOpenIds(userIdList);
        //剩余的OpenId,应该再次分组打标签
        List<String> otherOpenIdList = Lists.newArrayList(openIdList);
        Set<String> preChouKuanAOpenId = getOpenIdListByTagIdList(Lists.newArrayList(UserTagEnum.PRE_CHOUKUAN_A.getTagId()));
        Set<String> preChouKuanBOpenId = getOpenIdListByTagIdList(Lists.newArrayList(UserTagEnum.PRE_CHOUKUAN_B.getTagId()));
        Set<String> ingChouKuanAOpenId = getOpenIdListByTagIdList(Lists.newArrayList(UserTagEnum.ING_CHOUKUAN_A.getTagId()));
        Set<String> ingChouKuanBOpenId = getOpenIdListByTagIdList(Lists.newArrayList(UserTagEnum.ING_CHOUKUAN_B.getTagId()));
        Set<String> endChouKuanOpenId = getOpenIdListByTagIdList(Lists.newArrayList(UserTagEnum.END_CHOUKUAN_A.getTagId(), UserTagEnum.END_CHOUKUAN_B.getTagId()));
        //剩余的openId，这些openId是没有标签的
        otherOpenIdList.removeAll(preChouKuanAOpenId);
        otherOpenIdList.removeAll(preChouKuanBOpenId);
        otherOpenIdList.removeAll(ingChouKuanAOpenId);
        otherOpenIdList.removeAll(ingChouKuanBOpenId);
        otherOpenIdList.removeAll(endChouKuanOpenId);
        //获取这批用户中有前在筹款A标签的用户
        List<String> proAShouldBeEndA = Lists.newArrayList(openIdList);
        proAShouldBeEndA.retainAll(preChouKuanAOpenId);
        //获取这批用户中有前在筹款B标签的用户
        List<String> proBShouldBeEndB = Lists.newArrayList(openIdList);
        proBShouldBeEndB.retainAll(preChouKuanBOpenId);
        //获取这批用户中有正在筹款A标签的用户
        List<String> ingAShouleBeEndA = Lists.newArrayList(openIdList);
        ingAShouleBeEndA.retainAll(ingChouKuanAOpenId);
        //获取这批用户中有正在筹款B标签的用户
        List<String> ingBShouldBeEndB = Lists.newArrayList(openIdList);
        ingBShouldBeEndB.retainAll(ingChouKuanBOpenId);
        List<String> shouldBeEndA = unionOfList(proAShouldBeEndA, ingAShouleBeEndA);
        List<String> shouldBeEndB = unionOfList(proBShouldBeEndB, ingBShouldBeEndB);
        //添加相应的标签
        log.debug("endRaiseHandler shouldBeEndA size:{} shouldBeEndB size:{} otherOpenIdList size:{}",
                shouldBeEndA.size(), shouldBeEndB.size(), otherOpenIdList.size());
        addTagToUser(shouldBeEndA, wxMpService, UserTagEnum.END_CHOUKUAN_A);
        addTagToUser(shouldBeEndB, wxMpService, UserTagEnum.END_CHOUKUAN_B);
        List<List<String>> partition = Lists.partition(otherOpenIdList, 1000);
        partition.forEach(list -> addTagToEndCaseUser(list, wxMpService));
        //删除掉之前的标签
        deleteTagFromOpenId(new ArrayList<>(proAShouldBeEndA), UserTagEnum.PRE_CHOUKUAN_A.getTagId(), wxMpService);
        deleteTagFromOpenId(new ArrayList<>(proBShouldBeEndB), UserTagEnum.PRE_CHOUKUAN_B.getTagId(), wxMpService);
        deleteTagFromOpenId(new ArrayList<>(ingAShouleBeEndA), UserTagEnum.ING_CHOUKUAN_A.getTagId(), wxMpService);
        deleteTagFromOpenId(new ArrayList<>(ingBShouldBeEndB), UserTagEnum.ING_CHOUKUAN_B.getTagId(), wxMpService);
        return true;
    }

    public boolean handleOldSubscribeUser(List<String> openIdList) {
        WxMpService wxMpService = shuidiWxService.getWxService(THIRD_TYPE);
        Set<String> openIdWithWxTag = getOpenIdListByTagIdList(UserTagEnum.getTagIdByGroups(Lists.newArrayList(1, 2, 3)));
        openIdList.removeAll(openIdWithWxTag);
        log.debug("handleOldSubscribeUser openIdList size:{}", openIdList.size());
        List<List<String>> partition = Lists.partition(openIdList, 1000);
        partition.forEach(list -> addTagToPreChouKuanUserList(list, wxMpService));
        return true;
    }

    private List<String> getOpenIds(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return Lists.newArrayList();
        }

        List<List<Long>> userIdPartition = Lists.partition(userIdList, 3000);
        List<UserThirdModel> userThirdModels = Lists.newArrayList();
        userIdPartition.forEach(list -> userThirdModels.addAll(userThirdDelegate.getThirdModelsByUserIdsAndType(list, THIRD_TYPE)));
        List<String> openIdList = userThirdModels.stream().map(UserThirdModel::getOpenId).distinct().collect(Collectors.toList());
        return openIdList;
    }

    public boolean handleOldRaiseUser(List<Long> userIdList) {
        WxMpService wxMpService = shuidiWxService.getWxService(THIRD_TYPE);
        List<String> openIdList = getOpenIds(userIdList);
        //剩余的OpenId,应该再次分组打标签
        List<String> otherOpenIdList = Lists.newArrayList(openIdList);
        //获取所有标签里的openId
        Set<String> preChouKuanAOpenId = getOpenIdListByTagIdList(Lists.newArrayList(UserTagEnum.PRE_CHOUKUAN_A.getTagId()));
        Set<String> preChouKuanBOpenId = getOpenIdListByTagIdList(Lists.newArrayList(UserTagEnum.PRE_CHOUKUAN_B.getTagId()));
        Set<String> IngChouKuanOpenId = getOpenIdListByTagIdList(Lists.newArrayList(UserTagEnum.ING_CHOUKUAN_A.getTagId(), UserTagEnum.ING_CHOUKUAN_B.getTagId()));
        Set<String> endChouKuanAOpenId = getOpenIdListByTagIdList(Lists.newArrayList(UserTagEnum.END_CHOUKUAN_A.getTagId()));
        Set<String> endChouKuanBOpenId = getOpenIdListByTagIdList(Lists.newArrayList(UserTagEnum.END_CHOUKUAN_B.getTagId()));
        //获取剩余的openId,这些openId是没有标签的openId
        otherOpenIdList.removeAll(preChouKuanAOpenId);
        otherOpenIdList.removeAll(preChouKuanBOpenId);
        otherOpenIdList.removeAll(IngChouKuanOpenId);
        otherOpenIdList.removeAll(endChouKuanAOpenId);
        otherOpenIdList.removeAll(endChouKuanBOpenId);
        //获取这批用户中有潜在筹款A标签的用户
        List<String> proAShouldBeIngA = Lists.newArrayList(openIdList);
        proAShouldBeIngA.retainAll(Lists.newArrayList(preChouKuanAOpenId));
        //获取这批用户中有结束筹款A标签的用户
        List<String> endAShouldBeIngA = Lists.newArrayList(openIdList);
        endAShouldBeIngA.retainAll(Lists.newArrayList(endChouKuanAOpenId));
        //获取这批用户中有正在筹款B标签的用户
        List<String> proBShouldBeIngB = Lists.newArrayList(openIdList);
        proBShouldBeIngB.retainAll(Lists.newArrayList(preChouKuanBOpenId));
        //获取这批用户中有结束筹款B标签的用户
        List<String> endBShouldBeIngB = Lists.newArrayList(openIdList);
        endBShouldBeIngB.retainAll(Lists.newArrayList(endChouKuanBOpenId));
        List<String> shouldBeIngA = unionOfList(proAShouldBeIngA, endAShouldBeIngA);
        List<String> shouldBeIngB = unionOfList(proBShouldBeIngB, endBShouldBeIngB);
        //添加相应的标签
        log.debug("handleOldRaiseUser shouldBeEndA size:{} shouldBeEndB size:{} otherOpenIdList size:{}",
                shouldBeIngA.size(), shouldBeIngB.size(), otherOpenIdList.size());
        addTagToUser(shouldBeIngA, wxMpService, UserTagEnum.ING_CHOUKUAN_A);
        addTagToUser(shouldBeIngB, wxMpService, UserTagEnum.ING_CHOUKUAN_B);
        List<List<String>> partition = Lists.partition(otherOpenIdList, 1000);
        partition.forEach(list -> addTagToIngChouKuanUserList(list, wxMpService));
        //删除掉之前的标签
        deleteTagFromOpenId(new ArrayList<>(proAShouldBeIngA), UserTagEnum.PRE_CHOUKUAN_A.getTagId(), wxMpService);
        deleteTagFromOpenId(new ArrayList<>(endAShouldBeIngA), UserTagEnum.END_CHOUKUAN_A.getTagId(), wxMpService);
        deleteTagFromOpenId(new ArrayList<>(proBShouldBeIngB), UserTagEnum.PRE_CHOUKUAN_B.getTagId(), wxMpService);
        deleteTagFromOpenId(new ArrayList<>(endBShouldBeIngB), UserTagEnum.END_CHOUKUAN_B.getTagId(), wxMpService);
        return true;
    }

    private List<String> unionOfList(List<String> one, List<String> two) {
        List<String> all = Lists.newArrayListWithCapacity(one.size() + two.size());
        all.addAll(one);
        all.addAll(two);
        return all;
    }

    public boolean handleDonation(long userId, int thirdType) {
        UserThirdModel userThirdModel = userThirdDelegate.getThirdModelWithUserId(userId, THIRD_TYPE);
        if (userThirdModel == null) {
            return false;
        }
        String openId = userThirdModel.getOpenId();
        WxMpService wxMpService = shuidiWxService.getWxService(THIRD_TYPE);
        List<Long> tagIdList = Lists.newArrayList();
        try {
            tagIdList = wxMpService.getTagIdListByOpenId(openId);
        } catch (WxErrorException e) {
            handlerWxError(e, userId);
        } catch (Exception e) {
            log.debug("UserWxTagService handleDonation error", e);
        }
        if (tagIdList.stream().anyMatch(UserTagEnum::isTagIdContains)) {
            return false;
        }
        this.addTagToJuanKuan(userId, userThirdModel.getOpenId(), wxMpService);
        return true;
    }

    public Set<String> getOpenIdListByTagIdList(List<Long> tagIdList) {
        WxMpService wxMpService = shuidiWxService.getWxService(THIRD_TYPE);
        Set<String> openIdList = Sets.newHashSet();
        tagIdList.forEach(val -> {
            String openIdIdx = "";
            do {
                try {
                    WxTagListUser wxTagListUser = wxMpService.getOpenIdFormTag(val, openIdIdx);
                    openIdIdx = wxTagListUser.getNextOpenid();
                    WxTagListUser.WxTagListUserData data = wxTagListUser.getData();
                    if (data != null) {
                        openIdList.addAll(data.getOpenidList());
                    }
                } catch (Exception e) {
                    log.debug("UserWxTagService getOpenIdListByTagIdList error", e);
                }
            } while (! StringUtils.isEmpty(openIdIdx));

        });
        return openIdList;
    }

    public void deleteTagFromOpenId(List<String> openIdList, long tagId, WxMpService wxMpService) {
        List<List<String>> partition = Lists.partition(openIdList, 50);
        partition.forEach(list -> {
            try {
                wxMpService.deleteTagFromOpenIdList(tagId, list);
            } catch (WxErrorException e) {
                log.debug("UserWxTagService deleteTagFromOpenId error", e);
            }
        });
    }

    public void addSubscribeTag(long userId, String openId) {
        try {
            UserInfoVo userInfoVo = new UserInfoVo(userId, openId, UserTagGroup.SUBSCRIBE, 0);
            log.debug("add tag to subscribe user userInfoVo:{}", userInfoVo);
            producer.send(new Message<>(MQTopicCons.CF,
                    MQTagCons.CF_ADD_WX_TAG_TO_USER, MQTagCons.CF_ADD_WX_TAG_TO_USER + "-" + openId + "-"
                    + System.currentTimeMillis(), userInfoVo, DelayLevel.M3));
        } catch (Exception e) {
            log.debug("CrowdfundingFinishService addSubscribeTag", e);
        }
    }

    public void handlerWxError(WxErrorException wxErrorException, long userId) {
        WxError error = wxErrorException.getError();
        if (error != null) {
            int errorCode = error.getErrorCode();
            if (errorCode == 43004 || errorCode == 50005) {
                log.debug("该用户没有关注，添加标签失败 userId：{}", userId);
            } else {
                log.debug("提阿尼啊标签失败", wxErrorException);
            }
        }
    }

    public void deleteByTagId(long tagId) {
        WxMpService wxService = shuidiWxService.getWxService(3);
        Set<String> openIdListByTagIdList = getOpenIdListByTagIdList(Lists.newArrayList(tagId));
        log.debug("deleteOpenIdByTagIdList openIdListByTagIdList size:{}", openIdListByTagIdList.size());
        List<String> openIdList = Lists.newArrayList(openIdListByTagIdList);
        deleteTagFromOpenId(openIdList, tagId, wxService);
    }

}
