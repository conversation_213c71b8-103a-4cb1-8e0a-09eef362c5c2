package com.shuidihuzhu.cf.service.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.CfInfoShareRecordShardingBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoSimpleBiz;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.dto.ShareRelationshipsDto;
import com.shuidihuzhu.cf.entity.ShareRelationships;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.FriendsBizTypeEnum;
import com.shuidihuzhu.cf.mapper.CfShareRelationshipsMapper;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSimpleModel;
import com.shuidihuzhu.cf.service.crowdfunding.bigdata.DSFriendsService;
import com.shuidihuzhu.cf.vo.UserFriendVo;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import com.shuidihuzhu.common.web.model.Response;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: wuyubin
 * @date: 2019-11-13 20:53
 */
@Service
@Slf4j
@RefreshScope
public class ShareRelationshipsService {

    @Autowired
    private CfInfoShareRecordShardingBiz cfInfoShareRecordShardingBiz;

    @Autowired
    private CrowdfundingInfoSimpleBiz crowdfundingInfoSimpleBiz;

    @Autowired
    private CfShareRelationshipsMapper cfShareRelationshipsMapper;

    @Resource
    private UserInfoDelegate userInfoDelegate;

    @Resource
    private DSFriendsService friendsService;

    @Resource(name = "cfShareViewRedissonHandler")
    private RedissonHandler redissonHandler;

    @Value("${apollo.share.relationships.limit:3}")
    private int limit;

    /**
     * *     - 一共展示3个转发人的头像和昵称
     * *     - 优先展示捐款人的一度好友的转发，按照转发时间倒序，展示前3个转发的一度好友；
     * *     - 如果不足3个，则按照时间展示其他的转发人；
     * *     - 如果最终数量不足3个，则有几个展示几个；
     * *     - 如果当前案例还没有转发人，则不展示该模块
     * *     - 昵称位置最多展示7个字，依次展示前3位的昵称，以“、”隔开，如果超过7个字则...展示，头像如获取不到则展示默认头像
     */
    public Response<List<UserFriendVo>> getShareRelationships(ShareRelationshipsDto dto) {

        //对象转换
        ShareRelationships shareRelationships = cfShareRelationshipsMapper.toEntity(dto);

        //参数校验
        boolean validate = shareRelationships.validateParam();
        if (!validate) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_ERROR);
        }

        String value = redissonHandler.get(getRedisKey((shareRelationships)), String.class);
        if (StringUtils.isNotEmpty(value)) {
            return NewResponseUtil.makeSuccess(JSON.parseArray(value, UserFriendVo.class));
        }

        //判断案例是否存在
        CfInfoSimpleModel simpleModel = crowdfundingInfoSimpleBiz.getFundingInfo(shareRelationships.getInfoUuid());
        if (Objects.isNull(simpleModel)) {
            return NewResponseUtil.makeError(CfErrorCode.CF_NOT_FOUND);
        }

        //获取一度好友（剔除访问人）
        List<Long> friends = getFriends(shareRelationships);

        //获取一度好友转发userId
        List<Long> friendShareUserIds = getFriendShare(friends, simpleModel);

        //获取其他人转发userId
        List<Long> otherShareUserIds = getCfInfoShareRecords(friendShareUserIds, simpleModel);

        //整合数据
        List<Long> userIds = Lists.newArrayList();
        userIds.addAll(friendShareUserIds);
        userIds.addAll(otherShareUserIds);

        //获取昵称跟头像
        List<UserFriendVo> vos = getUserFriendVo(userIds);

        //入缓存
        redissonHandler.setEX(getRedisKey(shareRelationships), JSON.toJSONString(vos), TimeUnit.MINUTES.toMillis(10));

        return NewResponseUtil.makeSuccess(vos);
    }


    /**
     * 获取昵称跟头像
     *
     * @return
     */
    private List<UserFriendVo> getUserFriendVo(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return null;
        }

        List<UserInfoModel> userInfoModels = userInfoDelegate.getUserInfoByUserIdBatch(userIds);
        Map<Long, UserInfoModel> userInfoModelMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(userInfoModels)) {
            userInfoModelMap = userInfoModels.stream()
                    .filter(v -> StringUtils.isNotEmpty(v.getNickname()) && StringUtils.isNotEmpty(v.getHeadImgUrl()))
                    .collect(Collectors.toMap(UserInfoModel::getUserId, Function.identity()));
        }

        Map<Long, UserInfoModel> finalUserInfoModelMap = userInfoModelMap;
        return userIds.stream()
                .filter(v -> Objects.nonNull(finalUserInfoModelMap.get(v)))
                .limit(limit)
                .map(v -> {
                    UserInfoModel userInfoModel = finalUserInfoModelMap.get(v);
                    return UserFriendVo.builder()
                            .nickName(userInfoModel.getNickname())
                            .headImgUrl(userInfoModel.getHeadImgUrl())
                            .build();
                })
                .collect(Collectors.toList());
    }


    private List<Long> getFriends(ShareRelationships shareRelationships) {
        //获取捐款人一度好友
        Set<Long> friends = friendsService.getFriendsUserIdV2(shareRelationships.getUserId(), 100, FriendsBizTypeEnum.FRIEND_ALL);
        if (CollectionUtils.isNotEmpty(friends)) {
            //过滤自己
            return friends.stream().filter(v -> !v.equals(shareRelationships.getUserId())).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }


    /**
     * //获取一度好友转发userId
     *
     * @param friends
     * @param simpleModel
     * @return
     */
    private List<Long> getFriendShare(List<Long> friends, CfInfoSimpleModel simpleModel) {
        if (CollectionUtils.isEmpty(friends)) {
            return Lists.newArrayList();
        }

        List<CfInfoShareRecord> friendShareRecords = cfInfoShareRecordShardingBiz.getListDescByInfoIdAndUserIds(simpleModel.getId(), friends);
        if (CollectionUtils.isEmpty(friendShareRecords)) {
            return Lists.newArrayList();
        }

        return friendShareRecords.stream()
                .map(CfInfoShareRecord::getUserId)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 获取其他人转发userId
     *
     * @param friendShareUserIds
     * @param simpleModel
     * @return
     */
    private List<Long> getCfInfoShareRecords(List<Long> friendShareUserIds, CfInfoSimpleModel simpleModel) {
        List<CfInfoShareRecord> cfInfoShareRecords = cfInfoShareRecordShardingBiz.getListByInfoId(simpleModel.getId(), Long.MAX_VALUE, 100);
        if (CollectionUtils.isEmpty(cfInfoShareRecords)) {
            return Lists.newArrayList();
        }
        return cfInfoShareRecords.stream()
                .map(CfInfoShareRecord::getUserId)
                .filter(userId -> !friendShareUserIds.contains(userId))
                .distinct()
                .collect(Collectors.toList());
    }

    private String getRedisKey(ShareRelationships shareRelationships) {
        return "share_relationships" + shareRelationships.getInfoUuid() + "_" + shareRelationships.getUserId();
    }

}
