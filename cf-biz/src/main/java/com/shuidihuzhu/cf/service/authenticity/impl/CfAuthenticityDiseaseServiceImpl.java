package com.shuidihuzhu.cf.service.authenticity.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import com.shuidihuzhu.cf.biz.crowdfunding.impl.CrowdfundingTreatmentBizImpl;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.material.feign.CfMaterialReadClient;
import com.shuidihuzhu.cf.client.material.model.materialField.CfRaiseBasicInfoField;
import com.shuidihuzhu.cf.client.material.model.materialField.MaterialExtKeyConst;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.model.CaseLabelsManagement;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingTreatment;
import com.shuidihuzhu.cf.model.disease.DiseaseNorm;
import com.shuidihuzhu.cf.service.authenticity.CfAuthenticityDiseaseService;
import com.shuidihuzhu.cf.service.crowdfunding.CfDiseaseNormService;
import com.shuidihuzhu.cf.service.disease.impl.DiseaseServiceImpl;
import com.shuidihuzhu.cf.service.label.CfLabelService;
import com.shuidihuzhu.cf.vo.authenticity.CfAuthenticityPatientInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Type;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/5/29 5:00 PM
 */
@Slf4j
@Service
public class CfAuthenticityDiseaseServiceImpl implements CfAuthenticityDiseaseService {

    @Resource
    private CfMaterialReadClient cfMaterialReadClient;
    @Resource
    private CrowdfundingTreatmentBizImpl crowdfundingTreatmentBiz;
    @Resource
    private CfDiseaseNormService cfDiseaseNormService;
    @Resource
    private DiseaseServiceImpl diseaseService;
    @Resource
    private CfLabelService cfLabelService;

    private final static List<String> tagText = Lists.newArrayList("ICU", "手术", "化疗", "放疗",
            "ECMO治疗", "靶向", "免疫", "CAR");
    private final static Map<String, String> tagTextMap = Map.of("ICU", "ICU",
            "手术", "手术",
            "化疗", "放化疗",
            "放疗", "放化疗",
            "ECMO治疗", "ECMO治疗",
            "靶向", "靶向治疗",
            "免疫", "免疫治疗",
            "CAR", "CAR-T治疗");

    @Override
    public CfAuthenticityPatientInfoVO getAuthenticityPatientInfo(CrowdfundingInfo crowdfundingInfo) {
        // 取 治疗方案 和 可发初审疾病
        RpcResult<Map<String, List<String>>> mapRpcResult = cfMaterialReadClient.selectValueByFields(crowdfundingInfo.getId(),
                List.of(MaterialExtKeyConst.RAISE_DISEASE_NAME_TAG, MaterialExtKeyConst.TREATMENT_INFO));
        // key: 归一后待录入疾病 value: 治疗方案
        Map<String, List<String>> treatmentInfoMap = getResultInfoMap(mapRpcResult, MaterialExtKeyConst.TREATMENT_INFO);
        // key: 初审疾病 value: 初审归一疾病
        Map<String, String> raiseDiseaseNameMap = getResultInfoMap(mapRpcResult, MaterialExtKeyConst.RAISE_DISEASE_NAME_TAG);
        // key: 材审疾病 value: 材审归一疾病
        Map<String, Set<String>> diseaseNormMap = getDiseaseNormMap(crowdfundingInfo);

        // 获取用于匹配治疗方案的疾病
        List<String> treatmentDiseases = getTreatmentDiseases(crowdfundingInfo, raiseDiseaseNameMap);

        // 匹配治疗方案
        List<CfAuthenticityPatientInfoVO.DiseaseDetail> diseaseDetails = Lists.newArrayList();

        CaseLabelsManagement labelsManagement = cfLabelService.getLabelsManagement(crowdfundingInfo.getInfoId());
        int diseaseSwitch = labelsManagement.getDisease() == 0 ? 1 : labelsManagement.getDisease();
        boolean diseaseSwitchIsOpenedInSea = diseaseSwitch != -1;
        // 是否隐藏 疾病科普 等信息
        boolean hideDiseaseExtraInfo = !diseaseSwitchIsOpenedInSea && !judgeMaterialsApproved(crowdfundingInfo.getStatus());
        if (hideDiseaseExtraInfo) {
            String raiseBasicDiseaseName = Strings.EMPTY;
            RpcResult<Map<String, List<String>>> raiseBasicDiseaseRpcResult = cfMaterialReadClient.selectValueByFields(crowdfundingInfo.getId(), Lists.newArrayList(CfRaiseBasicInfoField.raise_basic_disease_name));
            List<String> raiseBasicDiseaseList = Optional.ofNullable(raiseBasicDiseaseRpcResult)
                    .filter(RpcResult::isSuccess)
                    .map(RpcResult::getData)
                    .orElse(Maps.newHashMap())
                    .get(CfRaiseBasicInfoField.raise_basic_disease_name);
            if (CollectionUtils.isNotEmpty(raiseBasicDiseaseList)) {
                raiseBasicDiseaseName = raiseBasicDiseaseList.get(0);
            }
            CfAuthenticityPatientInfoVO.DiseaseDetail diseaseDetail = CfAuthenticityPatientInfoVO.DiseaseDetail.builder()
                    .diseaseName(raiseBasicDiseaseName)
                    .showDiseaseKnowledge(false)
                    .build();
            diseaseDetails.add(diseaseDetail);
        } else {
            for (String disease : treatmentDiseases) {
                // 获取展示疾病归一后疾病，用来查询疾病科普
                Set<String> diseaseNorm = getDiseaseNorm(crowdfundingInfo, diseaseNormMap, raiseDiseaseNameMap.get(disease), disease);

                // 获取该疾病治疗方案
                List<String> treatmentInfos = diseaseSwitchIsOpenedInSea? getTreatmentInfos(diseaseNorm, treatmentInfoMap) : Lists.newArrayList();
                // 获取C端展示疾病
                String showDisease = judgeMaterialsApproved(crowdfundingInfo.getStatus()) ? disease : raiseDiseaseNameMap.get(disease);
                // 获取疾病科普是否展示
                Boolean showDiseaseKnowledge = judgeShowDiseaseKnowledge(diseaseNorm);

                CfAuthenticityPatientInfoVO.DiseaseDetail diseaseDetail = CfAuthenticityPatientInfoVO.DiseaseDetail.builder()
                        .diseaseName(showDisease)
                        .treatmentInfos(treatmentInfos)
                        .diseaseNameNorm(Lists.newArrayList(diseaseNorm))
                        .showDiseaseKnowledge(showDiseaseKnowledge && diseaseSwitchIsOpenedInSea)
                        .build();
                diseaseDetails.add(diseaseDetail);
            }
        }


        return CfAuthenticityPatientInfoVO.builder()
                .diseaseDetails(diseaseDetails)
                .showDiseaseExtraInfo(diseaseSwitchIsOpenedInSea)
                .build();
    }

    /**
     * 判断材审是否通过
     */
    private Boolean judgeMaterialsApproved(CrowdfundingStatus status) {
        return status.value() == CrowdfundingStatus.CROWDFUNDING_STATED.value();
    }


    private Boolean judgeShowDiseaseKnowledge(Set<String> diseaseNorm) {
        if (CollectionUtils.isEmpty(diseaseNorm)) {
            return false;
        }

        Map<String, Integer> knowledgeNormMap = diseaseService.getHaveKnowledgeNorm(diseaseNorm);
        if (MapUtils.isEmpty(knowledgeNormMap)) {
            return false;
        }

        for (Map.Entry<String, Integer> entry : knowledgeNormMap.entrySet()) {
            if (entry.getValue() == 1) {
                return true;
            }
        }

        return false;
    }

    private Set<String> getDiseaseNorm(CrowdfundingInfo crowdfundingInfo, Map<String, Set<String>> diseaseNormMap, String raiseDisease, String materialDisease) {

        // 材审
        if (judgeMaterialsApproved(crowdfundingInfo.getStatus())) {

            if (!diseaseNormMap.containsKey(materialDisease)) {
                return Sets.newHashSet();
            }

            return Sets.newHashSet(diseaseNormMap.get(materialDisease));
        }

        // 初审展示疾病本来就是归一的
        return Sets.newHashSet(raiseDisease);
    }

    private List<String> getTreatmentInfos(Set<String> diseases, Map<String, List<String>> treatmentInfoMap) {

        if (CollectionUtils.isEmpty(diseases)) {
            return Lists.newArrayList();
        }

        List<String> treatmentInfoList = Lists.newArrayList();
        for (String disease : diseases) {
            if (!treatmentInfoMap.containsKey(disease)) {
                return Lists.newArrayList();
            }
            // 过滤可展示治疗方案
            List<String> treatmentInfos = getShowTreatmentTag(treatmentInfoMap.get(disease));
            if (CollectionUtils.isEmpty(treatmentInfos)) {
                return Lists.newArrayList();
            }

            treatmentInfoList.addAll(treatmentInfos);
        }

        // 方案最多展示2个
        if (treatmentInfoList.size() > 2) {
            Collections.shuffle(treatmentInfoList);
            treatmentInfoList = treatmentInfoList.subList(0, 2);
        }

        return treatmentInfoList;
    }

    private List<String> getTreatmentDiseases(CrowdfundingInfo crowdfundingInfo, Map<String, String> raiseDiseaseNameMap) {
        List<String> diseaseName = Lists.newArrayList();
        if (judgeMaterialsApproved(crowdfundingInfo.getStatus())) {
            // 材审通过 用材审未归一疾病匹配治疗方案
            diseaseName = getMaterialDisease(crowdfundingInfo.getId());
        } else {
            // 材审没通过 用初审未归一疾病匹配治疗方案
            diseaseName = Lists.newArrayList(raiseDiseaseNameMap.keySet());
        }

        // 超过3个疾病只取3个
        if (diseaseName.size() > 3) {
            diseaseName = diseaseName.subList(0, 3);
        }
        return diseaseName;
    }

    private Map<String, Set<String>> getDiseaseNormMap(CrowdfundingInfo crowdfundingInfo) {

        DiseaseNorm diseaseNorm = cfDiseaseNormService.getByCaseId(crowdfundingInfo.getId());
        if (Objects.isNull(diseaseNorm) || StringUtils.isEmpty(diseaseNorm.getDiseaseNormJson())) {
            return Maps.newHashMap();
        }

        Gson gson = new Gson();
        Type type = Map.class;
        Map<String, Set<String>> map = gson.fromJson(diseaseNorm.getDiseaseNormJson(), type);
        if (MapUtils.isEmpty(map)) {
            return Maps.newHashMap();
        }

        return map;
    }

    private <T> Map<String, T> getResultInfoMap(RpcResult<Map<String, List<String>>> mapRpcResult, String key) {
        Map<String, T> infoMap = Maps.newHashMap();

        List<String> infos = Optional.ofNullable(mapRpcResult)
                .filter(RpcResult::isSuccess)
                .map(RpcResult::getData)
                .map(map -> map.get(key))
                .orElse(Lists.newArrayList());
        if (CollectionUtils.isNotEmpty(infos)) {
            Gson gson = new Gson();
            Type type = Map.class;
            infoMap = gson.fromJson(infos.get(0), type);
        }
        return infoMap;
    }


    private List<String> getShowTreatmentTag(List<String> treatmentInfos) {

        treatmentInfos = treatmentInfos.stream()
                .flatMap(s -> Arrays.stream(s.split(",")))
                .map(String::trim)
                .collect(Collectors.toList());

        List<String> showTag = Lists.newArrayList();
        for (String treatment : treatmentInfos) {
            for (Map.Entry<String, String> entry : tagTextMap.entrySet()) {
                if (treatment.contains(entry.getKey())) {
                    showTag.add(entry.getValue());
                    break;
                }
            }
        }

        return showTag;
    }

    private List<String> getMaterialDisease(Integer caseId) {
        CrowdfundingTreatment crowdfundingTreatment = crowdfundingTreatmentBiz.get(caseId);
        if (Objects.isNull(crowdfundingTreatment)) {
            return Lists.newArrayList();
        }
        return List.of(Objects.requireNonNull(StringUtils.split(crowdfundingTreatment.getDiseaseName(), ",，")));
    }

}
