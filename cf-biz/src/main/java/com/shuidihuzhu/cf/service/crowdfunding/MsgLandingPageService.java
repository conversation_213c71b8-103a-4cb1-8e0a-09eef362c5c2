package com.shuidihuzhu.cf.service.crowdfunding;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRedisKvBiz;
import com.shuidihuzhu.cf.enums.crowdfunding.CfForwardOrRemindEnum;
import com.shuidihuzhu.cf.model.crowdfunding.marketing.LandingPageConfigContentItem;
import com.shuidihuzhu.cf.model.crowdfunding.marketing.LandingPageConfigModel;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;


@Service
@Slf4j
public class MsgLandingPageService {
    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;
    @Autowired
    private CfRedisKvBiz redisKvBiz;


    public LandingPageConfigModel getLandingPageModel(int subBizType, int platform) {
        CfForwardOrRemindEnum itemEnum = CfForwardOrRemindEnum.getEnum(subBizType);
        if (null == itemEnum || platform < 0 || platform > 1) {
            return null;
        }
        String platformStr = "android";
        if (0 == platform) {
            platformStr = "ios";
        }
        LandingPageConfigModel configModel = null;
        if (CfForwardOrRemindEnum.SELECT_FRIEND_PROFESSOR == itemEnum ||
                CfForwardOrRemindEnum.SELECT_FRIEND_FUNCTIONAL == itemEnum) {
            configModel = this.getSelectFriend(itemEnum, platformStr);
        }
        if (CfForwardOrRemindEnum.REMIND_SEE_PROFESSOR == itemEnum ||
                CfForwardOrRemindEnum.REMIND_SEE_FUNCTIONAL == itemEnum) {
            configModel = this.getRemindSee(itemEnum, platformStr);
        }
        return configModel;
    }

    /**
     * 多选好友转发
     * <p>
     * 第1步：点击【转发】
     * 第2步：点击【发送给朋友】
     * 第3步：点击【多选】
     * 第4步：【勾选】9个群或好友
     * 第5步：点击【发送】按钮
     * 第6步：写一些帮忙求转发的话
     * 第7步：点击【发送】
     *
     * @param itemEnum
     * @param platformStr
     * @return
     */
    private LandingPageConfigModel getSelectFriend(CfForwardOrRemindEnum itemEnum, String platformStr) {
        LandingPageConfigModel configModel = new LandingPageConfigModel();
        String keyPrefix = itemEnum.getDescription() + "_";
        String headImage = redisKvBiz.queryByKey(keyPrefix + "head", true);
        configModel.setHeadImage(headImage);

        keyPrefix = itemEnum.getDescription().replaceAll("professor", "")
                .replace("functional", "") + platformStr + "_";
        List<LandingPageConfigContentItem> itemList = Lists.newArrayList();
        itemList.add(new LandingPageConfigContentItem(Lists.newArrayList("点击", "**【立即转发】"),
                Lists.newArrayList(
                        redisKvBiz.queryByKey(keyPrefix + "1", true))));
        itemList.add(new LandingPageConfigContentItem(Lists.newArrayList("点击", "**【发送给朋友】"),
                Lists.newArrayList(
                        redisKvBiz.queryByKey(keyPrefix + "2", true))));
        itemList.add(new LandingPageConfigContentItem(Lists.newArrayList("点击", "**【多选】"),
                Lists.newArrayList(
                        redisKvBiz.queryByKey(keyPrefix + "3", true))));
        itemList.add(new LandingPageConfigContentItem(Lists.newArrayList("**【勾选】", "9个群或好友"),
                Lists.newArrayList(
                        redisKvBiz.queryByKey(keyPrefix + "4", true))));
        if ("android".equalsIgnoreCase(platformStr)){
            itemList.add(new LandingPageConfigContentItem(Lists.newArrayList("点击", "**【发送】", "按钮"),
                    Lists.newArrayList(
                            redisKvBiz.queryByKey(keyPrefix + "5", true))));
        }else {
            itemList.add(new LandingPageConfigContentItem(Lists.newArrayList("点击", "**【完成】", "按钮"),
                    Lists.newArrayList(
                            redisKvBiz.queryByKey(keyPrefix + "5", true))));
        }

        itemList.add(new LandingPageConfigContentItem(Lists.newArrayList("写一些帮忙求转发的话"),
                Lists.newArrayList(
                        redisKvBiz.queryByKey(keyPrefix + "6", true))));

        itemList.add(new LandingPageConfigContentItem(Lists.newArrayList("点击", "**【发送】"),
                Lists.newArrayList(
                        redisKvBiz.queryByKey(keyPrefix + "7", true))));

        configModel.setContent(itemList);
        /**
         * 教程完成：【明早上8点】还会有新的筹款技巧告诉您，一定要来听！
         * 现在就使用今天的筹款技巧去【转发筹款】吧！
         */
        configModel.setComplete(Lists.newArrayList(
                Lists.newArrayList("**【明早上8点】","还会有新的筹款技巧告诉您，一定要来听！"),
                Lists.newArrayList("现在就使用今天的筹款技巧去","**【转发筹款】","吧！")
                ));

        Map<String, Object> componentsMap = Maps.newHashMap();
        Map<String, String> shareComponent = Maps.newHashMap();
        shareComponent.put("content", "我已学会了，现在就去立即转发");
        componentsMap.put("share", shareComponent);

        configModel.setComponents(componentsMap);

        return configModel;
    }

    /**
     * 第1步：点击【立即转发】
     * 第2步：点击【分享到朋友圈】
     * 第3步：点击【提醒谁看】
     * 第4步：【勾选】十个亲戚、同学、同事、朋友
     * 第5步：点击【完成】按钮
     * 第6步：写一些帮忙求转发的话
     * 第7步：点击【发表】
     * <p>
     * 教程完成：【明早上8点】还会有新的筹款技巧告诉您，一定要来听！
     * 现在就使用今天的筹款技巧去【转发筹款】吧！
     *
     * @param itemEnum
     * @param platformStr
     * @return
     */
    private LandingPageConfigModel getRemindSee(CfForwardOrRemindEnum itemEnum, String platformStr) {
        LandingPageConfigModel configModel = new LandingPageConfigModel();
        String keyPrefix = itemEnum.getDescription() + "_";
        String headImage = redisKvBiz.queryByKey(keyPrefix + "head", true);
        configModel.setHeadImage(headImage);

        keyPrefix = itemEnum.getDescription().replaceAll("professor", "")
                .replace("functional", "") + platformStr + "_";
        List<LandingPageConfigContentItem> itemList = Lists.newArrayList();
        itemList.add(new LandingPageConfigContentItem(Lists.newArrayList("点击", "**【立即转发】"),
                Lists.newArrayList(
                        redisKvBiz.queryByKey(keyPrefix + "1", true))));
        itemList.add(new LandingPageConfigContentItem(Lists.newArrayList("点击", "**【分享到朋友圈】"),
                Lists.newArrayList(
                        redisKvBiz.queryByKey(keyPrefix + "2", true))));
        itemList.add(new LandingPageConfigContentItem(Lists.newArrayList("点击", "**【提醒谁看】"),
                Lists.newArrayList(
                        redisKvBiz.queryByKey(keyPrefix + "3", true))));
        itemList.add(new LandingPageConfigContentItem(Lists.newArrayList("**【勾选】", "十个亲戚、同学、同事、朋友"),
                Lists.newArrayList(
                        redisKvBiz.queryByKey(keyPrefix + "4", true))));
        if ("android".equalsIgnoreCase(platformStr)){
            itemList.add(new LandingPageConfigContentItem(Lists.newArrayList("点击", "**【确定】", "按钮"),
                    Lists.newArrayList(
                            redisKvBiz.queryByKey(keyPrefix + "5", true))));
        }else {
            itemList.add(new LandingPageConfigContentItem(Lists.newArrayList("点击", "**【完成】", "按钮"),
                    Lists.newArrayList(
                            redisKvBiz.queryByKey(keyPrefix + "5", true))));
        }
        itemList.add(new LandingPageConfigContentItem(Lists.newArrayList("写一些帮忙求转发的话"),
                Lists.newArrayList(
                        redisKvBiz.queryByKey(keyPrefix + "6", true))));
        itemList.add(new LandingPageConfigContentItem(Lists.newArrayList("点击", "**【发表】"),
                Lists.newArrayList(
                        redisKvBiz.queryByKey(keyPrefix + "7", true))));
        configModel.setContent(itemList);

        /**
         * 教程完成：【明早上8点】还会有新的筹款技巧告诉您，一定要来听！
         * 现在就使用今天的筹款技巧去【转发筹款】吧！
         */
        configModel.setComplete(Lists.newArrayList(
                Lists.newArrayList("**【明早上8点】", "还会有新的筹款技巧告诉您，一定要来听！"),
                Lists.newArrayList("现在就使用今天的筹款技巧去", "**【转发筹款】", "吧！")));

        Map<String, Object> componentsMap = Maps.newHashMap();
        Map<String, String> shareComponent = Maps.newHashMap();
        shareComponent.put("content", "我已学会了，现在就去立即转发");
        componentsMap.put("share", shareComponent);

        configModel.setComponents(componentsMap);

        return configModel;
    }
}
