package com.shuidihuzhu.cf.service.crowdfunding;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.dao.crowdfunding.CfCaseEndWhiteListDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfCaseEndWhiteList;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.lucene.util.RamUsageEstimator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CfCaseEndWhiteListService {
    @Autowired
    private CfCaseEndWhiteListDao cfCaseEndWhiteListDao;



    private LoadingCache<Integer, List<CfCaseEndWhiteList>> caseEndCache =
            CacheBuilder.newBuilder().maximumSize(10)
                    // 每5分钟refresh一次，但是是读触发的；如果没有读，则不会操作
                    .refreshAfterWrite(5, TimeUnit.MINUTES).build(new CacheLoader<Integer, List<CfCaseEndWhiteList>>() {
                @Override
                public List<CfCaseEndWhiteList> load(Integer key) {
                    List<CfCaseEndWhiteList> caseEndWhiteListLists = cfCaseEndWhiteListDao.getList();
                    if(CollectionUtils.isEmpty(caseEndWhiteListLists)){
                        return Lists.newArrayList();
                    }
                    log.info("caseEndWhiteListLists的对象大小:{}",
                            RamUsageEstimator.humanReadableUnits(RamUsageEstimator.sizeOfObject(caseEndWhiteListLists)));
                    return caseEndWhiteListLists;
                }});

    public List<CfCaseEndWhiteList> getList(){

        List<CfCaseEndWhiteList> result = null;
        try {
            result = caseEndCache.get(0);
        } catch (ExecutionException e) {
            log.warn("CfCaseEndWhiteList 查询异常", e);
        }
        return result == null ? Lists.newArrayList() : result;
    }
}
