package com.shuidihuzhu.cf.service.crowdfunding.casematerial.caseAuditListPage.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRefuseReasonMsgBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoDataStatusTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingRelationType;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.crowdfunding.casematerial.caseAuditListPage.ICfMaterialRejectListService;
import com.shuidihuzhu.cf.vo.v5.CfMaterialAuditListView;
import com.shuidihuzhu.cf.vo.v5.CfMaterialFieldName;
import com.shuidihuzhu.cf.vo.v5.CfMaterialParam;
import com.shuidihuzhu.cf.vo.v5.MaterialAuditEntry;
import com.shuidihuzhu.cf.vo.v5.MaterialModifySuggestType;
import com.shuidihuzhu.cf.vo.v5.MaterialRejectPositionType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
@Service
public class CfPayeeRejectListService implements ICfMaterialRejectListService {

    @Autowired
    private CrowdfundingInfoBiz fundingBiz;
    @Autowired
    private CfRefuseReasonMsgBiz reasonMsgBiz;

    private static List<Integer> PAYEE_SUGGEST = Lists.newArrayList(
            MaterialModifySuggestType.PAYEE_PATIENT.getCode(),
            MaterialModifySuggestType.PAYEE_PATIENT_RELATION.getCode(),
            MaterialModifySuggestType.PAYEE_HOSPITAL.getCode()
    );

    @Override
    public List<CfMaterialAuditListView.AuditModifyEntry> getRejectModifyEntry(CfMaterialParam param)  {

        int entryStatus = getEntryStatus(param.getMaterialAuditStatus());
        int suggestStatus = getEntrySuggestStatus(param.getMaterialAuditStatus());
        Set<Integer> suggestCodes = getSuggestCodes(param.getSuggestViews());

        if (MapUtils.isEmpty(param.getRejectDetails())) {
            return Lists.newArrayList();
        }

        if (suggestChangePayeeWay(suggestCodes)) {
            return getPayeeSuggestEntry(param, suggestCodes, suggestStatus);
        }
        if (hasRejectAllPayee(param)) {
            return getPayeeInfoEntry(entryStatus);
        }

        if (hasRejectHospital(param)) {
            return upGradeHospitalSuggestEntry(param, entryStatus);
        }

        CrowdfundingInfo info = fundingBiz.getFundingInfo(param.getInfoUuid());
        if (info == null) {
            return Lists.newArrayList();
        }

        PayeeEntryStyle curRelation = getPayeeModifyType(param, info.getRelationType());
        switch (curRelation) {

            case SELF:
                return getSelfPayeeEntry(param, entryStatus);
            case HOSPITAL_ACCOUNT:
                return getHospitalPayeeEntry(param, entryStatus);
            case RELATIVE:
                return getOtherPayeeEntry(param, suggestCodes, suggestStatus, entryStatus);
            case SUGGEST_HOSPITAL:
                return upGradeHospitalSuggestEntry(param, entryStatus);
            case BIG_ENTRY:
            default:
                return getPayeeInfoEntry(entryStatus);
        }
    }

    private boolean hasRejectAllPayee(CfMaterialParam param) {
        return param.getRejectDetails().containsKey(MaterialRejectPositionType.PAYEE_PATIENT_RELATION.getCode());
    }

    private boolean hasRejectHospital(CfMaterialParam param) {
        return param.getRejectDetails().containsKey(MaterialRejectPositionType.PAYEE_HOSPITAL.getCode());

    }
    private List<CfMaterialAuditListView.AuditModifyEntry> getPayeeInfoEntry(int entryStatus) {

        CfMaterialAuditListView.AuditModifyEntry modifyEntry = new CfMaterialAuditListView.AuditModifyEntry(
                MaterialAuditEntry.PAYEE_INFO, entryStatus);

        modifyEntry.setAllFields( Lists.newArrayList(
                new CfMaterialAuditListView.FieldItemStatus(CfMaterialFieldName.payeeInfo, entryStatus)));

        return Lists.newArrayList(modifyEntry);
    }

    private List<CfMaterialAuditListView.AuditModifyEntry> getSelfPayeeEntry(CfMaterialParam param,
                                                                             int entryStatus) {

        CfMaterialAuditListView.AuditModifyEntry modifyEntry = new CfMaterialAuditListView.AuditModifyEntry(
                    MaterialAuditEntry.PAYEE_PATIENT, entryStatus);
        modifyEntry.setAllFields(getPayeeCommonRejectFields(param.getRejectDetails(),
                    entryStatus));

        return Lists.newArrayList(modifyEntry);
    }

    private List<CfMaterialAuditListView.AuditModifyEntry> getHospitalPayeeEntry(CfMaterialParam param,
                                                                                 int entryStatus) {

        CfMaterialAuditListView.AuditModifyEntry modifyEntry = new CfMaterialAuditListView.AuditModifyEntry(
                    MaterialAuditEntry.PAYEE_HOSPITAL, entryStatus);
        modifyEntry.setAllFields(getPayeeHospitalRejectFields(param.getRejectDetails(), entryStatus));

        return Lists.newArrayList(modifyEntry);
    }

    private boolean suggestIdentityAuth(Set<Integer> suggestCodes) {
        return suggestCodes.contains(MaterialModifySuggestType.PAYEE_ID_CARD_PHOTO.getCode()) ||
                suggestCodes.contains(MaterialModifySuggestType.PAYEE_FACE_RECOGNITION.getCode());
    }

    private boolean suggestRelative(Set<Integer> suggestCodes) {
        return suggestCodes.contains(MaterialModifySuggestType.ACCOUNT_BOOK_PHOTO.getCode())
                || suggestCodes.contains(MaterialModifySuggestType.ACCOUNT_BOOK_VEDIO.getCode())
                || suggestCodes.contains(MaterialModifySuggestType.COUNTRY_COMMIT.getCode())
                || suggestCodes.contains(MaterialModifySuggestType.LETTER_OF_AUTH.getCode())
                || suggestCodes.contains(MaterialModifySuggestType.BORN_CARD.getCode())
                || suggestCodes.contains(MaterialModifySuggestType.MARRY_CARD.getCode());
    }

    private boolean suggestChangePayeeWay(Set<Integer> suggestCodes) {

        if (CollectionUtils.isEmpty(suggestCodes)) {
            return false;
        }

        for (Integer code : suggestCodes) {
            if (PAYEE_SUGGEST.contains(code)) {
                return true;
            }
        }

        return false;
    }

    private List<CfMaterialAuditListView.AuditModifyEntry> getPayeeSuggestEntry(CfMaterialParam param,
                                                                                Set<Integer> suggestCodes,
                                                                                int suggestStatus) {

        CfMaterialAuditListView.AuditModifyEntry modifyEntry = new CfMaterialAuditListView.AuditModifyEntry(
                MaterialAuditEntry.PAYEE_INFO_SUGGEST, suggestStatus);
        modifyEntry.setAllFields(getSuggestChangePayee(suggestCodes, suggestStatus));

//        modifyEntry.setModifySuggestDetails(param.getSuggestViews());

        return Lists.newArrayList(modifyEntry);
    }


    private List<CfMaterialAuditListView.FieldItemStatus> getSuggestChangePayee(Set<Integer> suggestCodes,
                                                                                int suggestStatus) {

        List<CfMaterialAuditListView.FieldItemStatus> allFieldItem = Lists.newArrayList();
        if (suggestCodes.contains(MaterialModifySuggestType.PAYEE_PATIENT.getCode())) {
            allFieldItem.add(new CfMaterialAuditListView.FieldItemStatus(CfMaterialFieldName.payeePatient,
                    suggestStatus));
        }

        if (suggestCodes.contains(MaterialModifySuggestType.PAYEE_PATIENT_RELATION.getCode())) {
            allFieldItem.add(new CfMaterialAuditListView.FieldItemStatus(CfMaterialFieldName.payeeRelative,
                    suggestStatus));
        }

        if (suggestCodes.contains(MaterialModifySuggestType.PAYEE_HOSPITAL.getCode())) {
            allFieldItem.add(new CfMaterialAuditListView.FieldItemStatus(CfMaterialFieldName.payeeHospital,
                    suggestStatus));
        }

        return allFieldItem;
    }

    public List<CfMaterialAuditListView.FieldItemStatus> getPayeeCommonRejectFields(Map<Integer, Set<String>> rejectDetails,
                                                                                    int fieldStatus) {

        List<CfMaterialAuditListView.FieldItemStatus> allFieldItem = Lists.newArrayList();

        if (rejectDetails.containsKey(MaterialRejectPositionType.PAYEE_NAME.getCode())) {
            allFieldItem.add(new CfMaterialAuditListView.FieldItemStatus(
                    CfMaterialFieldName.payeeName, fieldStatus));
        }

        if (rejectDetails.containsKey(MaterialRejectPositionType.PAYEE_ID_CARD.getCode())) {
            allFieldItem.add(new CfMaterialAuditListView.FieldItemStatus(
                    CfMaterialFieldName.payeeIdCardNo, fieldStatus));
        }

        if (rejectDetails.containsKey(MaterialRejectPositionType.PAYEE_MOBILE.getCode())) {
            allFieldItem.add(new CfMaterialAuditListView.FieldItemStatus(
                    CfMaterialFieldName.payeeMobile, fieldStatus));
        }

        if (rejectDetails.containsKey(MaterialRejectPositionType.PAYEE_BANK.getCode())) {
            allFieldItem.add(new CfMaterialAuditListView.FieldItemStatus(
                    CfMaterialFieldName.ownBankName, fieldStatus));
        }

        if (rejectDetails.containsKey(MaterialRejectPositionType.PAYEE_BANK_NO.getCode())) {
            allFieldItem.add(new CfMaterialAuditListView.FieldItemStatus(
                    CfMaterialFieldName.ownBankNo, fieldStatus));
        }

        return allFieldItem;
    }

    public List<CfMaterialAuditListView.FieldItemStatus> getPayeeRelativeRejectFields(Map<Integer, Set<String>> rejectDetails,
                                                                                      int fieldStatus) {
        List<CfMaterialAuditListView.FieldItemStatus> allFieldItem = getPayeeCommonRejectFields(rejectDetails, fieldStatus);

        if (rejectDetails.containsKey(MaterialRejectPositionType.PAYEE_RELATION.getCode())) {
            allFieldItem.add(new CfMaterialAuditListView.FieldItemStatus(
                    CfMaterialFieldName.payeePatientRelation, fieldStatus));
        }

        return allFieldItem;
    }

    private List<CfMaterialAuditListView.FieldItemStatus> getPayeeHospitalRejectFields(Map<Integer, Set<String>> rejectDetails,
                                                                           int fieldStatus) {
        List<CfMaterialAuditListView.FieldItemStatus> allFieldItem = Lists.newArrayList();

        if (rejectDetails.containsKey(MaterialRejectPositionType.HOSPITAL_ACCOUNT_NAME.getCode())) {
            allFieldItem.add(new CfMaterialAuditListView.FieldItemStatus(
                    CfMaterialFieldName.hospitalBank, fieldStatus));
        }

        if (rejectDetails.containsKey(MaterialRejectPositionType.HOSPITAL_BANK_CARD.getCode())) {
            allFieldItem.add(new CfMaterialAuditListView.FieldItemStatus(
                    CfMaterialFieldName.hospitalBankNo, fieldStatus));
        }

        if (rejectDetails.containsKey(MaterialRejectPositionType.HOSPITAL_BANK_BRANCH_NAME.getCode())) {
            allFieldItem.add(new CfMaterialAuditListView.FieldItemStatus(
                    CfMaterialFieldName.hospitalBankBranch, fieldStatus));
        }

        if (rejectDetails.containsKey(MaterialRejectPositionType.DEPARTMENT.getCode())) {
            allFieldItem.add(new CfMaterialAuditListView.FieldItemStatus(
                    CfMaterialFieldName.hosptalDepartment, fieldStatus));
        }

        if (rejectDetails.containsKey(MaterialRejectPositionType.BED_NUM.getCode())) {
            allFieldItem.add(new CfMaterialAuditListView.FieldItemStatus(
                    CfMaterialFieldName.hospitalBedNum, fieldStatus));
        }

        if (rejectDetails.containsKey(MaterialRejectPositionType.HOSPITALIZATION_NUM.getCode())) {
            allFieldItem.add(new CfMaterialAuditListView.FieldItemStatus(
                    CfMaterialFieldName.hospitalNum, fieldStatus));
        }
        return allFieldItem;
    }
// 授权委托书>村委会证明>出生证>结婚证>户口本视频>户口本
    private List<CfMaterialAuditListView.FieldItemStatus> getSuggestList(Set<Integer> suggestCodes) {
        List<CfMaterialAuditListView.FieldItemStatus> allFieldItem = Lists.newArrayList();

        if (suggestCodes.contains(MaterialModifySuggestType.LETTER_OF_AUTH.getCode())) {
            allFieldItem.add(new CfMaterialAuditListView.FieldItemStatus(
                    CfMaterialFieldName.letterOfAuth, CfMaterialAuditListView.ModifyStatus.NO_STATUS.getCode()));
        }
        if (suggestCodes.contains(MaterialModifySuggestType.COUNTRY_COMMIT.getCode())) {
            allFieldItem.add(new CfMaterialAuditListView.FieldItemStatus(
                    CfMaterialFieldName.countryCommit, CfMaterialAuditListView.ModifyStatus.NO_STATUS.getCode()));
        }
        if (suggestCodes.contains(MaterialModifySuggestType.BORN_CARD.getCode())) {
            allFieldItem.add(new CfMaterialAuditListView.FieldItemStatus(
                    CfMaterialFieldName.bornCard, CfMaterialAuditListView.ModifyStatus.NO_STATUS.getCode()));
        }
        if (suggestCodes.contains(MaterialModifySuggestType.MARRY_CARD.getCode())) {
            allFieldItem.add(new CfMaterialAuditListView.FieldItemStatus(
                    CfMaterialFieldName.marryCard, CfMaterialAuditListView.ModifyStatus.NO_STATUS.getCode()));
        }
        if (suggestCodes.contains(MaterialModifySuggestType.ACCOUNT_BOOK_VEDIO.getCode())) {
            allFieldItem.add(new CfMaterialAuditListView.FieldItemStatus(
                    CfMaterialFieldName.accountBookVedio, CfMaterialAuditListView.ModifyStatus.NO_STATUS.getCode()));
        }
        if (suggestCodes.contains(MaterialModifySuggestType.ACCOUNT_BOOK_PHOTO.getCode())) {
            allFieldItem.add(new CfMaterialAuditListView.FieldItemStatus(
                    CfMaterialFieldName.accountBook, CfMaterialAuditListView.ModifyStatus.NO_STATUS.getCode()));
        }

        return allFieldItem;
    }

    private List<CfMaterialAuditListView.AuditModifyEntry> getOtherPayeeEntry(CfMaterialParam param,
                                                                              Set<Integer> suggestCodes,
                                                                              int suggestStatus,
                                                                              int entryStatus) {
        List<CfMaterialAuditListView.AuditModifyEntry> allEntry = Lists.newArrayList();

        List<CfMaterialAuditListView.FieldItemStatus> fieldItemList = getPayeeRelativeRejectFields(param.getRejectDetails(), entryStatus);
        if (CollectionUtils.isNotEmpty(fieldItemList)) {
            CfMaterialAuditListView.AuditModifyEntry modifyEntry = new CfMaterialAuditListView.AuditModifyEntry(
                    MaterialAuditEntry.PAYEE_RELATIVE, entryStatus);
            modifyEntry.setAllFields(fieldItemList);
            allEntry.add(modifyEntry);
        }

        allEntry.addAll(getPayeeAuthEntry(param, suggestCodes, suggestStatus, entryStatus));

        allEntry.addAll(getPayeeRelationAuthEntry(param, suggestCodes, suggestStatus, entryStatus));

        return allEntry;
    }

    // 收款人身份验证
    private List<CfMaterialAuditListView.AuditModifyEntry> getPayeeAuthEntry(CfMaterialParam param,
                                                                             Set<Integer> suggestCodes,
                                                                             int suggestStatus,
                                                                             int entryStatus) {

        List<CfMaterialAuditListView.AuditModifyEntry> allEntry = Lists.newArrayList();

        if (suggestIdentityAuth(suggestCodes)) {
            CfMaterialAuditListView.AuditModifyEntry modifyEntry = new CfMaterialAuditListView.AuditModifyEntry(
                    MaterialAuditEntry.PAYEE_AUTH_SUGGEST, suggestStatus);
            modifyEntry.setAllFields(getPayeePhotoAuthFieldItem(suggestCodes, suggestStatus));
            allEntry.add(modifyEntry);
        } else if (param.getRejectDetails().containsKey(MaterialRejectPositionType.PAYEE_AUTH.getCode())) {
            CfMaterialAuditListView.AuditModifyEntry modifyEntry = new CfMaterialAuditListView.AuditModifyEntry(
                    MaterialAuditEntry.PAYEE_AUTH, entryStatus);
            modifyEntry.setAllFields( Lists.newArrayList(new CfMaterialAuditListView.FieldItemStatus(
                    CfMaterialFieldName.patientRepeatVerif, entryStatus)));

            allEntry.add(modifyEntry);
        } else {
            if (param.getRejectDetails().containsKey(MaterialRejectPositionType.ID_CARD_PHOTO.getCode())) {
                CfMaterialAuditListView.AuditModifyEntry modifyEntry = new CfMaterialAuditListView.AuditModifyEntry(
                        MaterialAuditEntry.PAYEE_ID_CARD, entryStatus);
                modifyEntry.setAllFields( Lists.newArrayList(new CfMaterialAuditListView.FieldItemStatus(
                        CfMaterialFieldName.payeeIdCardPhoto, entryStatus)));

                allEntry.add(modifyEntry);
            }

            if (param.getRejectDetails().containsKey(MaterialRejectPositionType.PAYEE_ID_CARD_PHOTO.getCode())) {
                CfMaterialAuditListView.AuditModifyEntry modifyEntry = new CfMaterialAuditListView.AuditModifyEntry(
                        MaterialAuditEntry.PAYEE_ID_CARD_PHOTO, entryStatus);
                modifyEntry.setAllFields( Lists.newArrayList(new CfMaterialAuditListView.FieldItemStatus(
                        CfMaterialFieldName.payeeWithIdCardPhoto, entryStatus)));
                allEntry.add(modifyEntry);
            }
        }

        return allEntry;
    }

    private List<CfMaterialAuditListView.FieldItemStatus> getPayeePhotoAuthFieldItem(Set<Integer> suggestCodes,
                                                                                     int suggestStatus) {
        List<CfMaterialAuditListView.FieldItemStatus> itemStatusList = Lists.newArrayList();

        if (suggestCodes.contains(MaterialModifySuggestType.PAYEE_ID_CARD_PHOTO.getCode())) {
            itemStatusList.add(new CfMaterialAuditListView.FieldItemStatus(
                    CfMaterialFieldName.payeeIdCard, suggestStatus));
        }

        if (suggestCodes.contains(MaterialModifySuggestType.PAYEE_FACE_RECOGNITION.getCode())) {
            itemStatusList.add(new CfMaterialAuditListView.FieldItemStatus(
                        CfMaterialFieldName.faceRecognition, suggestStatus));
        }

        return itemStatusList;
    }

     private List<CfMaterialAuditListView.AuditModifyEntry> getPayeeRelationAuthEntry(CfMaterialParam param,
                                                                                 Set<Integer> suggestCodes,
                                                                                 int suggestStatus,
                                                                                 int entryStatus) {
         List<CfMaterialAuditListView.AuditModifyEntry> allEntry = Lists.newArrayList();

         if (suggestRelative(suggestCodes)) {
             CfMaterialAuditListView.AuditModifyEntry modifyEntry = new CfMaterialAuditListView.AuditModifyEntry(
                     MaterialAuditEntry.PAYEE_RELATION_SHIP_SUGGEST, suggestStatus);
             modifyEntry.setAllFields(getSuggestList(suggestCodes));
//             modifyEntry.setModifySuggestDetails(param.getSuggestViews());
             allEntry.add(modifyEntry);

         } else if (param.getRejectDetails().containsKey(MaterialRejectPositionType.RELATION_SHIP_PROVE.getCode())) {
             CfMaterialAuditListView.AuditModifyEntry modifyEntry = new CfMaterialAuditListView.AuditModifyEntry(
                     MaterialAuditEntry.PAYEE_RELATION_SHIP, entryStatus);
             modifyEntry.setAllFields(Lists.newArrayList(new CfMaterialAuditListView.FieldItemStatus(
                     CfMaterialFieldName.relationVerification, entryStatus)));
             allEntry.add(modifyEntry);
         }

         return allEntry;
     }

     private PayeeEntryStyle getPayeeModifyType(CfMaterialParam param,
                                          CrowdfundingRelationType curPayeeType) {
         Pair<Set<CrowdfundingRelationType>, Boolean> rejectDetails = getCurrentRejectMaterialType(param);
         Set<CrowdfundingRelationType> rejectPayeeTypes = rejectDetails.getLeft();

         if (CollectionUtils.isEmpty(rejectPayeeTypes) || !rejectPayeeTypes.contains(curPayeeType)) {
             if (rejectPayeeTypes.size() == 1 && rejectPayeeTypes.contains(CrowdfundingRelationType.LOCATION_HOSPITAL_ACCOUNT)) {
                 log.info("这种情况要升级到建议医院收款的情况. msg:{}", JSON.toJSONString(param));
                 return PayeeEntryStyle.SUGGEST_HOSPITAL;
             }

             log.info("当前下发的位置和用户提交的收款人类型不一致 直接发下大入口 param:{} curPayeeType:{} rejectDetails:{}",
                     JSON.toJSONString(param), curPayeeType, JSON.toJSONString(rejectDetails));
             return PayeeEntryStyle.BIG_ENTRY;
         }

         return PayeeEntryStyle.getPayeeStyle(curPayeeType);
     }

     private Pair<Set<CrowdfundingRelationType>, Boolean> getCurrentRejectMaterialType(CfMaterialParam param) {
         Set<CrowdfundingRelationType> result = Sets.newHashSet();

         for (Integer rejectCode : param.getRejectDetails().keySet()) {
             if (MaterialRejectPositionType.hasRejectSelfPayee(rejectCode)) {
                 result.add(CrowdfundingRelationType.self);
             }

             if (MaterialRejectPositionType.hasRejectRelativePayee(rejectCode)) {
                result.add(CrowdfundingRelationType.other);
             }

             if (MaterialRejectPositionType.hasRejectHospitalPayee(rejectCode)) {
                 result.add(CrowdfundingRelationType.LOCATION_HOSPITAL_ACCOUNT);
             }
         }
         Boolean hasSuggestRelative = false;
         if (CollectionUtils.isNotEmpty(param.getSuggestViews())) {

             for (CfMaterialAuditListView.ModifySuggest suggest : param.getSuggestViews()) {
                 if (MaterialModifySuggestType.hasSuggestRelativePayee(suggest.getSuggestCode())) {
                     hasSuggestRelative = true;
                 }
             }
         }

         return Pair.of(result, hasSuggestRelative);
     }

     private List<CfMaterialAuditListView.AuditModifyEntry> upGradeHospitalSuggestEntry(CfMaterialParam param,
                                                                                         int entryStatus) {

//         reasonMsgBiz.updatePayeeHospitalSuggestModify(param.getInfoUuid());
         CfMaterialAuditListView.AuditModifyEntry modifyEntry = new CfMaterialAuditListView.AuditModifyEntry(
                 MaterialAuditEntry.PAYEE_HOSPITAL_TOTAL, entryStatus);
         modifyEntry.setAllFields(Lists.newArrayList(new CfMaterialAuditListView.FieldItemStatus(CfMaterialFieldName.payeeHospital,
                 entryStatus)));

         return Lists.newArrayList(modifyEntry);
     }


     private enum PayeeEntryStyle {
         BIG_ENTRY,

         SELF,

         HOSPITAL_ACCOUNT,

         RELATIVE,

         SUGGEST_HOSPITAL;

         public static PayeeEntryStyle getPayeeStyle(CrowdfundingRelationType type) {

             switch (type) {
                 case self:
                     return SELF;
                 case other:
                     return RELATIVE;
                 case LOCATION_HOSPITAL_ACCOUNT:
                     return HOSPITAL_ACCOUNT;
                 default:
                     log.info("当前收款类型为type:{}", type);
                     return BIG_ENTRY;
             }
         }
     }

}
