package com.shuidihuzhu.cf.service.crowdfunding.listener.afteradd;

import com.shuidihuzhu.cf.biz.crowdfunding.CfInfoExtBiz;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoBaseVo;
import com.shuidihuzhu.cf.service.crowdfunding.event.AddListenerOrder;
import com.shuidihuzhu.cf.service.crowdfunding.event.CrowdFundingAddEvent;
import com.shuidihuzhu.cf.service.crowdfunding.listener.base.AbstractCrowdFundingAddEventListener;
import com.shuidihuzhu.cf.service.deposit.ICfDepositAccountService;
import com.shuidihuzhu.common.web.enums.Platform;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.event.SmartApplicationListener;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2019年12月16日10:50:01
 * <p>
 * 案例创建成功后注册托管账户
 */
@Slf4j
@Service
public class CaseDepositAccountCreateListener extends AbstractCrowdFundingAddEventListener implements SmartApplicationListener {
    @Autowired
    private ICfDepositAccountService iCfDepositAccountService;
    @Autowired
    private CfInfoExtBiz cfInfoExtBiz;

    @Override
    public void onApplicationEvent(ApplicationEvent applicationEvent) {
        CrowdFundingAddEvent crowdFundingAddEvent = (CrowdFundingAddEvent) applicationEvent;
        CrowdfundingInfo cfCase = crowdFundingAddEvent.getCrowdfundingInfo();
        if (null == cfCase) {
            return;
        }
        String infoUuid = cfCase.getInfoId();
        //查询的是主库
        CfInfoExt cfInfoExt = cfInfoExtBiz.getByInfoUuid(infoUuid);
        if (null == cfInfoExt) {
            log.error("案例创建成功后注册托管账户获取cfInfoExt为空，caseId:{}, infoUuid:{}", cfCase.getId(), infoUuid);
            return;
        }
        String productName = cfInfoExt.getProductName();
        int caseId = cfCase.getId();
        iCfDepositAccountService.asyncCreateCaseAccount(caseId, infoUuid, productName);
    }


    @Override
    public int getOrder() {
        return AddListenerOrder.AddBaseAfter.getValue();
    }
    public static void main(String[] args) {
        CrowdfundingInfoBaseVo  crowdfundingInfoBaseVo = new CrowdfundingInfoBaseVo();
        Integer platform = crowdfundingInfoBaseVo == null ? Integer.valueOf(0) : crowdfundingInfoBaseVo.getPlatform();
        log.debug("platform:{}",String.valueOf(platform));
    }
}
