package com.shuidihuzhu.cf.service.crowdfunding;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.account.model.UserThirdModel;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoStatusBiz;
import com.shuidihuzhu.cf.clinet.event.center.enums.UserOperationTypeEnum;
import com.shuidihuzhu.cf.constants.WxConstants;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.delegate.UserThirdDelegate;
import com.shuidihuzhu.cf.delegate.finance.ICfDrawCashFeignDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.facade.risk.CfCaseRiskFacade;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoStatus;
import com.shuidihuzhu.cf.service.crowdfunding.casematerial.CfMaterialStatusService;
import com.shuidihuzhu.cf.service.event.EventCenterService;
import com.shuidihuzhu.cf.service.msg.MsgClientV2Service;
import com.shuidihuzhu.cf.util.crowdfunding.CFPushRecordFactory;
import com.shuidihuzhu.cf.util.crowdfunding.CrowdfundingUtil;
import com.shuidihuzhu.client.cf.api.model.MaterialVersion;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.wx.enums.AccountThirdTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * @Author: lianghongchao
 * @Date: 2018/7/30 19:21
 */
@Slf4j
@Service
@RefreshScope
public class CrowdfundingApproveService {

    @Autowired
    private UserInfoDelegate userInfoDelegate;
    @Autowired
    private CFPushRecordFactory cfPushRecordFactory;
    @Autowired
    private UserThirdDelegate userThirdDelegate;
    @Autowired
    private CrowdfundingInfoStatusBiz crowdfundingInfoStatusBiz;
    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;
    @Value("${sms.modelnum.approvepass:approvepass}")
    private String modelNum;

    @Autowired
    private CfMaterialStatusService materialStatusService;

    @Resource
    private EventCenterService eventCenterService;

    @Autowired
    private ICfDrawCashFeignDelegate cfDrawCashFeignDelegate;

    @Resource
    private MsgClientV2Service msgClientV2Service;

    public void pushNotice(CrowdfundingInfo info) {
        log.info("筹款审核通过,infoUuid:{}", info.getInfoId());
        MaterialVersion materialVersion = materialStatusService.getMaterialsVersion(info.getInfoId());
        //新版材料审核通过
        if (materialVersion.isMaterial100()) {
            eventCenterService.sendEventCenter(UserOperationTypeEnum.MATERIAL_REVIEW_PASS.getCode(),info);
            this.pushSuccessAppMsg(info);
            this.pushSuccess2857AppMsg(info);
            return;
        }
    }

    private void sendSms(long userId) {
        UserInfoModel userAccount = userInfoDelegate.getUserInfoByUserId(userId);
        if (userAccount != null) {
            msgClientV2Service.sendSmsMsg(modelNum, Lists.newArrayList(userAccount.getCryptoMobile()), true);
        }
    }

    public void pushSuccessAppMsg(CrowdfundingInfo crowdfundingInfo) {
        try {
            cfPushRecordFactory.newInfoApplySuccess(crowdfundingInfo);
        } catch (Exception e) {
            log.error("审核通过通知APP推送ERROR", e);
        }
    }

    //审核通过状态检查状态机
    public CrowdfundingStatus checkDataUpdateCaseStatus(CrowdfundingInfo crowdfundingInfo, CfInfoExt cfInfoExt) {
        List<CrowdfundingInfoStatus> infoStatusList = crowdfundingInfoStatusBiz.getByInfoUuidMaster(crowdfundingInfo.getInfoId());
        CrowdfundingStatus crowdfundingStatus;

        List<Integer> infoStatusIntList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(infoStatusList)) {
            infoStatusIntList = infoStatusList.stream()
                    .map(item -> item.getStatus())
                    .collect(Collectors.toList());
        }

        List<CrowdfundingInfoDataStatusTypeEnum> requiredDataList = CrowdfundingUtil.getRequiredCaseList(cfInfoExt);

        if (CollectionUtils.isEmpty(infoStatusIntList)) {
            //材料不齐全,且没有驳回,案例为审批中
            crowdfundingStatus = CrowdfundingStatus.APPROVE_PENDING;
        } else if (infoStatusIntList.contains(CrowdfundingInfoStatusEnum.REJECTED.getCode())) {
            //材料状态有驳回时,案例驳回审核
            crowdfundingStatus = CrowdfundingStatus.APPROVE_DENIED;
        } else if (infoStatusIntList.contains(CrowdfundingInfoStatusEnum.UN_SUBMITTED.getCode())
                || infoStatusIntList.contains(0)) {
            //材料没有驳回,但是有保存未提交时,案例为审批中
            crowdfundingStatus = CrowdfundingStatus.APPROVE_PENDING;
        } else if (infoStatusIntList.contains(CrowdfundingInfoStatusEnum.SUBMITTED.getCode())) {
            //老案例,材料四条
            if (requiredDataList.size() == infoStatusIntList.size()) {
                //材料齐全,案例为提交审核
                crowdfundingStatus = CrowdfundingStatus.SUBMITTED;
            } else {
                //材料不齐全,且没有驳回,案例为审批中
                crowdfundingStatus = CrowdfundingStatus.APPROVE_PENDING;
            }

        } else { //材料状态为空或者全部为2
            if (requiredDataList.size() == infoStatusIntList.size()) {
                //材料齐全,案例为通过
                crowdfundingStatus = CrowdfundingStatus.CROWDFUNDING_STATED;
            } else {
                //材料不齐全,且没有驳回,案例为审批中
                crowdfundingStatus = CrowdfundingStatus.APPROVE_PENDING;
            }
        }
        log.info("checkDataUpdateCaseStatus updateStatus {}-->{}\t crowfundingId:{}",
                crowdfundingInfo.getStatus(), crowdfundingStatus, crowdfundingInfo.getId());
        this.crowdfundingInfoBiz.updateStatus(crowdfundingInfo.getId(), crowdfundingStatus, crowdfundingInfo.getStatus());
        return crowdfundingStatus;
    }

    private void pushSuccess2857AppMsg(CrowdfundingInfo crowdfundingInfo) {
        //判断是否是分流用户 不是分流用户不发消息
        if (!cfDrawCashFeignDelegate.getFundingABTest(crowdfundingInfo.getId())) {
            return;
        }

        //判断是否有提现草稿 有草稿不发消息
        if (cfDrawCashFeignDelegate.getFundingValidParam(crowdfundingInfo.getId())) {
            return;
        }

        //判断好友证实是否有三个人  完成不发消息
        if (cfDrawCashFeignDelegate.getFundingVerifyData(crowdfundingInfo.getId())) {
            return;
        }

        Map<Long, Map<Integer, String>> appMsgMap = Maps.newHashMap();

        Map<Integer, String> paramMap = Maps.newHashMap();
        paramMap.put(1, DateUtil.getCurrentDateTimeStr());
        paramMap.put(2, crowdfundingInfo.getInfoId());

        appMsgMap.put(crowdfundingInfo.getUserId(), paramMap);
        msgClientV2Service.sendAppParamsMsg("JBD2837", appMsgMap);
    }

}
