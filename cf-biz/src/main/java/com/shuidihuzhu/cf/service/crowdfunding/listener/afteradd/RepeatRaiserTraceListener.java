package com.shuidihuzhu.cf.service.crowdfunding.listener.afteradd;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.crowdfunding.RepeatRaiserPayeeTraceService;
import com.shuidihuzhu.cf.service.crowdfunding.event.AddListenerOrder;
import com.shuidihuzhu.cf.service.crowdfunding.event.CrowdFundingAddEvent;
import com.shuidihuzhu.cf.service.crowdfunding.listener.base.AbstractCrowdFundingAddEventListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.event.SmartApplicationListener;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2019/8/4 下午4:38
 * @desc
 */
@Slf4j
@Service
public class RepeatRaiserTraceListener extends AbstractCrowdFundingAddEventListener implements SmartApplicationListener {

    @Autowired
    private RepeatRaiserPayeeTraceService repeatRaiserPayeeTraceService;

    @Override
    public void onApplicationEvent(ApplicationEvent event) {

        CrowdFundingAddEvent crowdFundingAddEvent = (CrowdFundingAddEvent) event;
        CrowdfundingInfo cfCase = crowdFundingAddEvent.getCrowdfundingInfo();

        if(Objects.nonNull(cfCase) && StringUtils.isNotEmpty(cfCase.getInfoId())){
            repeatRaiserPayeeTraceService.repeatRaiserTrace(cfCase);
        }
    }

    @Override
    public int getOrder() {
        return AddListenerOrder.RepeatRaiserTrace.getValue();
    }
}
