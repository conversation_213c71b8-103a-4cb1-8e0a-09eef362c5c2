package com.shuidihuzhu.cf.service.crowdfunding;

import com.google.common.base.Strings;
import com.shuidihuzhu.cf.biz.crowdfunding.CfWxReplyBiz;
import com.shuidihuzhu.cf.model.crowdfunding.CfWxReply;
import com.shuidihuzhu.common.web.util.cache.ICache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * update by zhangzhi 20180320
 * 微信自动回复表 本地缓存
 */
@Service
public class CfWxReplyService extends AbstractCfCache<String, List<CfWxReply>> implements ICache<String, List<CfWxReply>> {
	private static final Logger LOGGER = LoggerFactory.getLogger(CfWxReplyService.class);

	@Autowired
	private CfWxReplyBiz cfWxReplyBiz;

	@Override
	public List<CfWxReply> get(String key) {
		if (Strings.isNullOrEmpty(key)) {
			return Collections.emptyList();
		}
		try {
			return getValue(key);
		} catch (ExecutionException e) {
			LOGGER.error("getCfWxReplyList from cache error!", e);
		}

		return Collections.emptyList();
	}

	@Override
	protected List<CfWxReply> queryData(String key) {
		if (Strings.isNullOrEmpty(key)) {
			return Collections.emptyList();
		}

		return this.cfWxReplyBiz.getAll();
	}
	public final static String KEY="cfWxReplyList";
}
