package com.shuidihuzhu.cf.service.crowdfunding;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.shuidihuzhu.cf.biz.crowdfunding.CfSwitcherBiz;
import com.shuidihuzhu.cf.enums.crowdfunding.CfSwitcherEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfSwitcher;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

@Service
public class CfSwitcherService {
	private static final Logger LOGGER = LoggerFactory.getLogger(CfSwitcherService.class);

	@Autowired
	private CfSwitcherBiz cfSwitcherBiz;

	public CfSwitcher get(CfSwitcherEnum cfSwitcherEnum) {
		String key = "cfSwitcherList";
		List<CfSwitcher> list = null;
		try {
			list = dataCache.get(key);
		} catch (ExecutionException e) {
			LOGGER.info("", e);
		}
		if (CollectionUtils.isEmpty(list)) {
			list = this.cfSwitcherBiz.getAll();
		}
		if (!CollectionUtils.isEmpty(list)) {
			for (CfSwitcher cfSwitcher : list) {
				if (cfSwitcherEnum.getName().equals(cfSwitcher.getName())) {
					return cfSwitcher;
				}
			}
		}
		return null;
	}

	private LoadingCache<String, List<CfSwitcher>> dataCache = CacheBuilder.newBuilder().maximumSize(10)
			// 每秒钟refresh一次，但是是读触发的；如果没有读，则不会操作
			.refreshAfterWrite(1, TimeUnit.MINUTES).build(new CacheLoader<String, List<CfSwitcher>>() {

				@Override
				public List<CfSwitcher> load(String key) throws Exception {
					LOGGER.info("CfSwitcherCache Loading For key{}", key);
					return getDataFromDB();
				}

			});

	private List<CfSwitcher> getDataFromDB() {
		return this.cfSwitcherBiz.getAll();
	}
}
