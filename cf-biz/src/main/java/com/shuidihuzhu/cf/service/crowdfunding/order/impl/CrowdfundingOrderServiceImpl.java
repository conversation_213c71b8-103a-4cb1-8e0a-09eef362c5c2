package com.shuidihuzhu.cf.service.crowdfunding.order.impl;

import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingOrderBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.verification.CrowdFundingVerificationBiz;
import com.shuidihuzhu.cf.constants.crowdfunding.status.RelationShip;
import com.shuidihuzhu.cf.dto.OrderSearchDto;
import com.shuidihuzhu.cf.enums.crowdfunding.FriendsBizTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingUserView;
import com.shuidihuzhu.cf.service.crowdfunding.CrowdfundingInfoDetailService;
import com.shuidihuzhu.cf.service.crowdfunding.bigdata.DSFriendsService;
import com.shuidihuzhu.cf.service.crowdfunding.order.CrowdfundingOrderService;
import com.shuidihuzhu.cf.vo.UserFriendOrderVo;
import com.shuidihuzhu.charity.client.model.activity.ActivityVenueCaseModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;

/**
 * @Author：liuchangjun
 * @Date：2021/7/15
 */
@Service
@Slf4j
public class CrowdfundingOrderServiceImpl implements CrowdfundingOrderService {

    @Autowired
    private CrowdfundingOrderBiz crowdfundingOrderBiz;
    @Autowired
    private DSFriendsService dsFriendsService;
    @Autowired
    private CrowdfundingInfoDetailService crowdfundingInfoDetailService;
    @Autowired
    private CrowdFundingVerificationBiz crowdFundingVerificationBiz;

    @Override
    public UserFriendOrderVo getByFriendsUserId(OrderSearchDto orderSearchDto) {
        if(Objects.isNull(orderSearchDto)){
            return null;
        }
        //获取好友id
        Set<Long> friendsUserIds = dsFriendsService.getFriendsUserId(orderSearchDto.getCurrentUserId(),300,
                FriendsBizTypeEnum.FRIEND_ORDER_LIST);
        orderSearchDto.setFriendsUserIds(friendsUserIds);
        UserFriendOrderVo userFriendOrderVo = getUserFriendOrder(orderSearchDto);
        //消除userId;
        reSetUserId(userFriendOrderVo);
        return userFriendOrderVo;
    }

    //获取证实人的捐款记录
    @Override
    public UserFriendOrderVo getByVerificatioUserId(long cfId ,String infoUuid,int size) {

        List<CrowdFundingVerification> allVerifications = crowdFundingVerificationBiz.queryAllCrowdFundingVerificationByInfoUuid(infoUuid);
        if(CollectionUtils.isEmpty(allVerifications)){
            return new UserFriendOrderVo();
        }
        allVerifications = allVerifications.stream().collect(
                Collectors.collectingAndThen(
                        Collectors.toCollection(() ->
                                new TreeSet<>(Comparator.comparing(CrowdFundingVerification::getVerifyUserId))), ArrayList::new));
        Map<Long, Integer> activityVenueCaseModelMap = allVerifications.stream().collect(Collectors
                .toMap(CrowdFundingVerification::getVerifyUserId,CrowdFundingVerification::getRelationShip));

       Set<Long> userIds = allVerifications.stream().map(CrowdFundingVerification::getVerifyUserId)
                .collect(Collectors.toSet());
        //获取非匿名的10条order
        OrderSearchDto orderSearchDto = new OrderSearchDto();
        orderSearchDto.setFriendsUserIds(userIds);
        orderSearchDto.setAnonymous(0);
        orderSearchDto.setCfId(cfId);
        orderSearchDto.setSize(size);
        UserFriendOrderVo userFriendOrderVo = getUserFriendOrder(orderSearchDto);
        //填充关系
        if(CollectionUtils.isNotEmpty(userFriendOrderVo.getCrowdfundingUserViews())){
            for(CrowdfundingUserView crowdfundingUserView : userFriendOrderVo.getCrowdfundingUserViews()){
                Integer ship = activityVenueCaseModelMap.get(crowdfundingUserView.getUserId());
                RelationShip relationShip = RelationShip.codeOf(ship);
                if(Objects.nonNull(relationShip)){
                    crowdfundingUserView.setShip(relationShip.getDescription());
                }

            }
        }
        //消除userId;
        reSetUserId(userFriendOrderVo);
        return userFriendOrderVo;
    }

    private void reSetUserId(UserFriendOrderVo userFriendOrderVo) {
        if(CollectionUtils.isEmpty(userFriendOrderVo.getCrowdfundingUserViews())){
            return;
        }
        for(CrowdfundingUserView crowdfundingUserView : userFriendOrderVo.getCrowdfundingUserViews()){
            crowdfundingUserView.setUserId(0);
        }
    }

    @NotNull
    private UserFriendOrderVo getUserFriendOrder(OrderSearchDto orderSearchDto) {
        UserFriendOrderVo userFriendOrderVo = new UserFriendOrderVo();
        if(orderSearchDto == null){
            return userFriendOrderVo;
        }
        //查数据库
        List<CrowdfundingOrder> orders = crowdfundingOrderBiz.getByFriendsUserId(orderSearchDto);
        if(CollectionUtils.isEmpty(orders)){
            return userFriendOrderVo;
        }
        int sum = orders.stream().mapToInt(CrowdfundingOrder::getAmount).sum();
        userFriendOrderVo.setTotalAmount(sum/100);
        //数据处理
        orders = filterOrder(orders,orderSearchDto.getSize());
        userFriendOrderVo.setFriendCount(orders.size());
        List<CrowdfundingUserView> lists = crowdfundingInfoDetailService.fillUpListData(orders);
        userFriendOrderVo.setCrowdfundingUserViews(lists);
        return userFriendOrderVo;
    }

    //根据user_id去重
    private List<CrowdfundingOrder> filterOrder(List<CrowdfundingOrder> orders, int maxFriendsLimit) {
       if(CollectionUtils.isEmpty(orders)){
           return orders;
       }
        orders = orders.stream().collect(
                Collectors.collectingAndThen(
                        Collectors.toCollection(() ->
                                new TreeSet<>(Comparator.comparing(CrowdfundingOrder::getUserId))), ArrayList::new)
        );
        orders = orders.stream().sorted(Comparator.comparing(CrowdfundingOrder::getId).reversed()).collect(Collectors.toList());
        if(orders.size()>maxFriendsLimit){
            orders=orders.subList(0,maxFriendsLimit);
        }
        return orders;
    }

}
