package com.shuidihuzhu.cf.service.hostcase;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.biz.crowdfunding.CfUserStatBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoSimpleBiz;
import com.shuidihuzhu.cf.constants.ModuleKeyCons;
import com.shuidihuzhu.cf.constants.crowdfunding.RedisKeyCons;
import com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingInfoDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSimpleModel;
import com.shuidihuzhu.cf.model.crowdfunding.CfUserStat;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.vo.v5.HotCaseVO;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.frame.client.api.platform.NewUserClient;
import com.shuidihuzhu.frame.client.enums.UserExistStatusEnum;
import com.shuidihuzhu.frame.client.model.platform.ModuleNewUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@RefreshScope
@Slf4j
public class HotCaseService {

    @Autowired
    private CfUserStatBiz cfUserStatBiz;

    @Resource(name = "redis-cf-api-new")
    private RedissonHandler redissonHandler;

    private static final int YES = 1;

    private static final int NO = 0;

    /**
     * 爆款鹰眼对照组key
     */
    private static final String ZERO = "0";

    private static final String DEFAULT = "default";

    private static final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMddHHmm");

    @Resource
    private CrowdfundingInfoDao crowdfundingInfoDao;

    @Resource
    private NewUserClient newUserClient;

    @Autowired
    private CrowdfundingInfoSimpleBiz crowdfundingInfoSimpleBiz;

    /**
     * 爆款案例 M分钟之前时间节点阀值
     */
    @Value("${hot.case.before.minute:10}")
    private int beforeMinute;

    /**
     * 爆款案例 M分钟之前大于N单的阀值
     */
    @Value("${hot.case.max.pay.success.count:105}")
    private long macPaySuccessCount;

    @Value("${hot.case.eagle.key:Popularcase2}")
    private String hotCaseEagleKey;


    /**
     * @description: 获取爆款案例
     * @author: sunpeifu
     * @date: 2021/5/28 11:14
     */
    public HotCaseVO getHotCase(Long userId, String caseInfoId) {

        HotCaseVO hotCaseVO = new HotCaseVO();


        CfInfoSimpleModel crowdfundingInfo = crowdfundingInfoSimpleBiz.getFundingInfo(caseInfoId);
        if (Objects.isNull(crowdfundingInfo)) {
            return null;
        }

        // 2 是否首捐
        isFirstDonation(userId, hotCaseVO);

        boolean isHot = isHotCase(crowdfundingInfo.getId());

        // 3 查看是否进入过实验,进入过直接返回
        String modelKey = getModelKey(userId,crowdfundingInfo.getId());
        Response<ModuleNewUser> response = newUserClient.get(modelKey, userId);
        if(Objects.nonNull(response.getData())) {
            ModuleNewUser moduleNewUser = response.getData();
            hotCaseVO.setEagleKey(moduleNewUser.getExtInfo());
            hotCaseVO.setIsHotCase(isHot ? YES : NO);
            return hotCaseVO;
        }

        // 3 查询是否爆款 -> 爆款走鹰眼逻辑 ,否则原有逻辑
        if(!isHot) {
            // 非爆款 走原有逻辑
            hotCaseVO.setEagleKey(ZERO);
            hotCaseVO.setIsHotCase(NO);
            return hotCaseVO;
        }

        // 4 设置用户分组 用户分组后组别固定不变, 在对照一直在对照,在实验一直在实验
        setEagleKey(modelKey, userId, hotCaseVO);

        hotCaseVO.setIsHotCase(YES);

        return hotCaseVO;
    }

    private void setEagleKey(String modelKey, Long userId, HotCaseVO hotCaseVO) {

        // 获取用户鹰眼分组
        String eagleKey = "10-[20]-50-100-200-500";
        // 非原始版本,才记录分流
        if(!ZERO.equals(eagleKey)) {
            newUserClient.create(modelKey, userId, UserExistStatusEnum.NEW.getStatus(), eagleKey);
        }
        hotCaseVO.setEagleKey(eagleKey);
    }

    private String getModelKey(long userId, int caseId) {
        return ModuleKeyCons.HOT_CASE_MODEL + "-" + userId + "-" + caseId;
    }

    private CrowdfundingInfo AssertCaseExist(String caseInfoId) {
        return crowdfundingInfoDao.getFundingInfo(caseInfoId);
    }

    /**
     * @description: 是否为爆款案例, 统计redis value 求和大于阀值即为爆款
     * @date: 2021/6/1 14:25
     */
    private boolean isHotCase(long caseId) {

        ArrayList<String> keys = Lists.newArrayList();
        LocalDateTime now = LocalDateTime.now();

        for (int i = 1; i <= beforeMinute; i++) {

            // 之前的第 i 分钟格式化字符串
            LocalDateTime current = now.minusMinutes(i);
            String currentMin = dateTimeFormatter.format(current);

            String key = getRedisKey(caseId, currentMin);
            keys.add(key);
        }

        // map key -> redis前缀+案例infoId+年月日时分 , value为对应的捐款成功数
        Map<String, Integer> map = null;
        try{
            map = redissonHandler.mget(keys);
        }catch(Exception e){
            log.info("HotCaseService isHotCase mget error, keys: {}, errorMsg: {}",keys, e.getMessage());
        }
        if (Objects.isNull(map)){
            map = new HashMap<>(1);
        }
        Integer paySuccessCount = map.values().stream().mapToInt(Integer::intValue).sum();

        if(paySuccessCount >= macPaySuccessCount) {
            return true;
        }

        return false;
    }

    private void isFirstDonation(Long userId, HotCaseVO hotCaseVO) {
        List<CfUserStat> cfUserStat = cfUserStatBiz.getByUserIds(Sets.newHashSet(userId));
        if(CollectionUtils.isEmpty(cfUserStat) || cfUserStat.get(0).getDonationCount() == 0) {
            hotCaseVO.setIsFirstDonation(YES);
        } else {
            hotCaseVO.setIsFirstDonation(NO);
        }
    }

    private String getRedisKey(long caseId, String localDateTimeMinString) {

        // 拼接Key -> 前缀 + 案例id + yyyy-MM-dd HH:mm 字符串
        return new StringBuilder()
                .append(RedisKeyCons.CF_API_HOT_CASE)
                .append(caseId)
                .append("#")
                .append(localDateTimeMinString)
                .toString();
    }

    /**
     * @description: 格式换LocalDateTime -> String  精确到分 不包含秒
     * @author: sunpeifu
     * @date: 2021/6/3 12:02
     */
    private String formatLocalDateTime2String(LocalDateTime localDateTime) {
        return dateTimeFormatter.format(localDateTime);
    }

    /**
     * 支付成功回调统计每分钟支付成功数量
     */
    public void incretmentPaySuccessOrderCountEveryMinute(CrowdfundingOrder successOrder) {

        // 拼接redisKey
        String localDateTimeString = formatLocalDateTime2String(LocalDateTime.now());
        String redisKey = getRedisKey(successOrder.getCrowdfundingId(), localDateTimeString);

        // 自增
        long l = redissonHandler.incrAndSetTimeWhenNotExists(redisKey, TimeUnit.HOURS.toMillis(2));
        log.info("paySuccessOrderCountEveryMinute redisKey: {} , count: {}", redisKey, l);
    }
}
