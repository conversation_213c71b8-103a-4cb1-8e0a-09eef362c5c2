package com.shuidihuzhu.cf.service.crowdfunding;

import com.shuidihuzhu.cf.dao.crowdfundingshare.LockNewMapper;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.sql.Date;
import java.sql.Timestamp;
import java.util.Calendar;

/**
 * @package: com.shuidihuzhu.account.utils
 * @Author: liujiawei
 * @Date: 2019-01-10  13:37
 */
@RefreshScope
@Service
@Slf4j
public class LockUtil {

    @Autowired
    private LockNewMapper lockNewMapper;

    public enum KeyType {
        SINGLE(0),
        ONE_SECOND(1),
        ONE_DAY(2);

        private Integer value;

        KeyType(int code) {
            this.value = code;
        }

        public static KeyType valueOf(int code) {
            for (KeyType type : values()) {
                if (type.getValue() == code) {
                    return type;
                }
            }
            return SINGLE;
        }

        public Integer getValue() {
            return value;
        }

        public void setValue(Integer value) {
            this.value = value;
        }

    }

    /**
     * account-service 中的key
     */
    public static final class LockKey {
        /**
         * 微信消息,拼接形式：key-sb_biz_type-userId
         */
        public static final String WX_MASSAGE_KEY = "wxMassageKey-";

    }

    /**
     * mysql锁，利用UniqueKey实现唯一插入
     *
     * @param userKey
     * @param keyType
     * @return
     * @throws Exception
     */
    public boolean tryLock(String userKey, KeyType keyType) throws Exception {
        if (StringUtils.isEmpty(userKey) || keyType == null || userKey.length() > 50) {
            throw new Exception("参数错误");
        }
        String sysKey = buildSysKey(userKey, keyType);
        Calendar cal = Calendar.getInstance();
        if (keyType.equals(KeyType.SINGLE)) {
            cal.set(Calendar.HOUR_OF_DAY, 0);
            cal.set(Calendar.MINUTE, 0);
            cal.set(Calendar.SECOND, 0);
            cal.set(Calendar.MILLISECOND, 0);
            cal.add(Calendar.YEAR, 10);
        } else if (keyType.equals(KeyType.ONE_SECOND)) {
            cal.set(Calendar.MILLISECOND, 0);
            cal.add(Calendar.SECOND, 1);
        } else if (keyType.equals(KeyType.ONE_DAY)) {
            cal.set(Calendar.HOUR_OF_DAY, 0);
            cal.set(Calendar.MINUTE, 0);
            cal.set(Calendar.SECOND, 0);
            cal.set(Calendar.MILLISECOND, 0);
            cal.add(Calendar.DAY_OF_YEAR, 1);
        }
        Timestamp expiryTime = new Timestamp(cal.getTimeInMillis());
        int res = lockNewMapper.tryLock(userKey, sysKey, keyType.getValue(), expiryTime);
        return res != 0;
    }

    private String buildSysKey(String userKey, KeyType keyType) throws Exception {
        if (StringUtils.isEmpty(userKey) || keyType == null) {
            throw new Exception("构造lock-key参数错误！");
        }
        if (keyType.equals(KeyType.SINGLE)) {
            return userKey + "-" + KeyType.SINGLE.toString();
        }
        if (keyType.equals(KeyType.ONE_SECOND)) {
            return userKey + "-" + DateUtil.getTimeConnStringFromTimestamp(new Timestamp(System.currentTimeMillis()));
        }
        if (keyType.equals(KeyType.ONE_DAY)) {
            return userKey + "-" + DateUtil.getYMDStringByDate(new Date(System.currentTimeMillis()));
        }
        return userKey;
    }
}
