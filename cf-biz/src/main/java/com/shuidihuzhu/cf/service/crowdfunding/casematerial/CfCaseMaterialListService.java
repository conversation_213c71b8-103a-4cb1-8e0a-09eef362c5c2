package com.shuidihuzhu.cf.service.crowdfunding.casematerial;

import com.alibaba.fastjson.JSON;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.biz.crowdfunding.CfInfoExtBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRefuseReasonMsgBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoStatusBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingRefuseReasonItemBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.casematerial.CfRefuseReasonEntitySlaveBiz;
import com.shuidihuzhu.cf.client.material.utils.MaterialCollectionUtils;
import com.shuidihuzhu.cf.delegate.finance.IFinanceDrawCashFeignDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.CfDrawCashConstant;
import com.shuidihuzhu.cf.enums.crowdfunding.CfFinishStatus;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoDataStatusTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoStatusEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.finance.model.vo.CfDrawCashApplyVo;
import com.shuidihuzhu.cf.model.crowdfunding.CfCommonStoreModel;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSimpleModel;
import com.shuidihuzhu.cf.model.crowdfunding.CfRefuseReasonMsg;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingRefuseReasonItem;
import com.shuidihuzhu.cf.model.crowdfunding.casematerial.CfRefuseReasonEntitySlave;
import com.shuidihuzhu.cf.model.crowdfunding.material.AuditSuggestModifyDetail;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoBaseVo;
import com.shuidihuzhu.cf.service.crowdfunding.CfCommonStoreService;
import com.shuidihuzhu.cf.service.crowdfunding.casematerial.caseAuditListPage.ICfMaterialRejectListService;
import com.shuidihuzhu.cf.service.crowdfunding.casematerial.caseAuditListPage.impl.CfFundUseRejectListService;
import com.shuidihuzhu.cf.service.crowdfunding.casematerial.caseAuditListPage.impl.CfPatientRejectListService;
import com.shuidihuzhu.cf.service.crowdfunding.casematerial.caseAuditListPage.impl.CfPayeeRejectListService;
import com.shuidihuzhu.cf.service.crowdfunding.casematerial.caseAuditListPage.impl.CfTitleContentRejectListService;
import com.shuidihuzhu.cf.service.crowdfunding.casematerial.caseAuditListPage.impl.CfTreatmentRejectListService;
import com.shuidihuzhu.cf.util.crowdfunding.CrowdfundingUtil;
import com.shuidihuzhu.cf.vo.v5.CfMaterialAuditListView;
import com.shuidihuzhu.cf.vo.v5.CfMaterialParam;
import com.shuidihuzhu.cf.vo.v5.MaterialAuditEntry;
import com.shuidihuzhu.cf.vo.v5.MaterialModifySuggestType;
import com.shuidihuzhu.cf.vo.v5.MaterialRejectPositionType;
import com.shuidihuzhu.common.web.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CfCaseMaterialListService {

    @Autowired
    private CrowdfundingInfoStatusBiz crowdfundingInfoStatusBiz;
    @Autowired
    private CfInfoExtBiz cfInfoExtBiz;
    @Autowired
    private CrowdfundingRefuseReasonItemBiz reasonItemBiz;
    @Autowired
    private CfRefuseReasonMsgBiz cfRefuseReasonMsgBiz;
    @Autowired
    private CfPayeeRejectListService payeeListService;
    @Autowired
    private CfPatientRejectListService patientListService;
    @Autowired
    private CfTitleContentRejectListService titleContentListService;
    @Autowired
    private CfTreatmentRejectListService treatmentListService;
    @Autowired
    private CfFundUseRejectListService fundUseRejectListService;
    @Autowired
    private CrowdfundingInfoBiz fundingInfoBiz;
    @Autowired
    private CfCommonStoreService storeService;
    @Autowired
    private IFinanceDrawCashFeignDelegate drawCashFeignDelegate;
    @Autowired
    private CfRefuseReasonEntitySlaveBiz entitySlaveBiz;

    public static final String ENTRY_SPLIT = "_entry_";
    public static final String AUDIT_ENTRY = "audit_list_%s";

    private static List<Integer> REJECT_SUBMIT_STATUS = Lists.newArrayList(
            CrowdfundingInfoStatusEnum.REJECTED.getCode(),
            CrowdfundingInfoStatusEnum.UN_SUBMITTED.getCode()
    );

    // 0810 材审优化 将这三项材料 合并成一项材料
    private static Set<CrowdfundingInfoDataStatusTypeEnum> MEDICAL_MATERIALS = Sets.newHashSet(
            CrowdfundingInfoDataStatusTypeEnum.PATIENT_INFO_SUBMIT,
            CrowdfundingInfoDataStatusTypeEnum.TREATMENT_INFO_SUBMIT,
            CrowdfundingInfoDataStatusTypeEnum.FUND_USE_SUBMIT);

    private LoadingCache<Integer, String> refusePositionCache = CacheBuilder.newBuilder().maximumSize(200)
                    .refreshAfterWrite(10, TimeUnit.MINUTES).build(new CacheLoader<Integer, String>() {
                        @Override
                        public String load(Integer itemId) {
                            log.info("C端驳回位置的id:{}", itemId);
                            if (itemId == null) {
                                return "";
                            }
                            List<CrowdfundingRefuseReasonItem> reasonItems = reasonItemBiz.getListByIds(Lists.newArrayList(itemId));
                            return CollectionUtils.isEmpty(reasonItems) ? "" : reasonItems.get(0).getContent();
                        }});

    public CfMaterialAuditListView getAuditListView(CfInfoSimpleModel simpleModel) {

        CfMaterialAuditListView listView = new CfMaterialAuditListView();

        Map<Integer, Integer> dataTypeStatus = crowdfundingInfoStatusBiz.getMapByInfoUuid(simpleModel.getInfoId());

        CfInfoExt infoExt = cfInfoExtBiz.getByInfoUuid(simpleModel.getInfoId());
        CrowdfundingInfo info = fundingInfoBiz.getFundingInfoById(simpleModel.getId());

        List<CrowdfundingInfoDataStatusTypeEnum> requiredDataTypes = CrowdfundingUtil.getRequiredCaseList(infoExt);
        if (CollectionUtils.isEmpty(requiredDataTypes) || info == null) {
            log.error("案例不能找到需要提交的材料项。caseId:{} caseInfo:{} infoExt:{}", simpleModel.getId(),
                    JSON.toJSONString(info), JSON.toJSONString(infoExt));
            return null;
        }

        listView.setMaterialStatus(info.getStatus().value());
        listView.setCaseAmount(info.getAmount());
        listView.setCaseEndTime(info.getEndTime());
        listView.setApplyDrawCash(info.getStatus().equals(CrowdfundingStatus.CROWDFUNDING_STATED));
        listView.setCaseFinishStatus(CfFinishStatus.getByValue(infoExt.getFinishStatus()).name());
        Response<CfDrawCashApplyVo> cashApplyResponse = drawCashFeignDelegate.getApplyInfo(simpleModel.getId());
        log.info("查询案例的状态 caseId:{} cashApplyVoFeignResponse:{} ", simpleModel.getId(),
                JSON.toJSONString(cashApplyResponse));
        CfDrawCashApplyVo cfDrawCashApplyVo = cashApplyResponse.getData();
        if (cfDrawCashApplyVo == null) {
            listView.setWithdrawApplyStatus(CfDrawCashConstant.ApplyStatus.EMPTY_VALUE.name());
        } else {
            listView.setWithdrawApplyStatus(CfDrawCashConstant.ApplyStatus.getByCode(cfDrawCashApplyVo.getApplyStatus()).name());
        }

        listView.setDataAuditDetails(getAllDataStatus(simpleModel.getId(), requiredDataTypes, dataTypeStatus));
        listView.setAllAuditEntrys(getAllAuditEntry(simpleModel, requiredDataTypes, dataTypeStatus));

        fillModifyStatus(simpleModel, listView);

        return listView;
    }

    private List<CfMaterialAuditListView.MinorMaterialStatus> getAllDataStatus(int caseId,
                                                                               List<CrowdfundingInfoDataStatusTypeEnum> requiredDataTypes,
                                                                               Map<Integer, Integer> dataTypeStatus) {
        List<CfMaterialAuditListView.MinorMaterialStatus> dataAuditDetails = Lists.newArrayList();

        // 图文
        Integer baseInfo = dataTypeStatus.get(CrowdfundingInfoDataStatusTypeEnum.BASE_INFO_SUBMIT.getCode());
        if (baseInfo != null) {
            dataAuditDetails.add(new CfMaterialAuditListView.MinorMaterialStatus(CfMaterialAuditListView
                    .MaterialTypeForC.TITLE_CONTENT.getCode(), baseInfo));
        } else {
            log.error("案例没有图文材料的crowdfundingInfoStatus状态.caseId:{}", caseId);
        }

        // 收款人状态
        Integer payee = Objects.requireNonNullElse(dataTypeStatus.get(CrowdfundingInfoDataStatusTypeEnum
                .PAYEE_INFO_SUBMIT.getCode()), CrowdfundingInfoStatusEnum.UN_SAVE.getCode());
        dataAuditDetails.add(new CfMaterialAuditListView.MinorMaterialStatus(CfMaterialAuditListView
                .MaterialTypeForC.PAYEE_INFO.getCode(), payee));

        // 医疗证明
        dataAuditDetails.add(new CfMaterialAuditListView.MinorMaterialStatus(CfMaterialAuditListView
                .MaterialTypeForC.MEDICAL_TREATMENT.getCode(), getMedicalStatus(requiredDataTypes, dataTypeStatus)));

        return dataAuditDetails;
    }

    private int getMedicalStatus(List<CrowdfundingInfoDataStatusTypeEnum> requiredDataTypes,
                                 Map<Integer, Integer> dataTypeStatus) {
        int passCnt = 0, submitCnt = 0, materialCnt = 0;

        // 将资金用途、患者信息、诊断证明 三项材料合成一项医疗材料
        for (CrowdfundingInfoDataStatusTypeEnum dataType : requiredDataTypes) {
            if (!MEDICAL_MATERIALS.contains(dataType)) {
                continue;
            }
            // 有一项是未填写，整体就是未填写
            Integer status = dataTypeStatus.get(dataType.getCode());
            if (status == null || status == CrowdfundingInfoStatusEnum.UN_SAVE.getCode()) {
                return CrowdfundingInfoStatusEnum.UN_SAVE.getCode();
            }
            ++materialCnt;

            if (status == CrowdfundingInfoStatusEnum.REJECTED.getCode()) {
                return CrowdfundingInfoStatusEnum.REJECTED.getCode();
            }
            if (status == CrowdfundingInfoStatusEnum.PASSED.getCode()) {
                ++passCnt;
            }
            if (status == CrowdfundingInfoStatusEnum.SUBMITTED.getCode()) {
                ++submitCnt;
            }
        }
        // 所有项都是通过， 整体才是通过
        if (passCnt == materialCnt) {
            return CrowdfundingInfoStatusEnum.PASSED.getCode();
        }
        // 除了通过的材料 剩下的材料都是已提交 则整体材料项就是已提交
        if (passCnt + submitCnt == materialCnt) {
            return CrowdfundingInfoStatusEnum.SUBMITTED.getCode();
        }

        return CrowdfundingInfoStatusEnum.UN_SUBMITTED.getCode();
    }

    // 各种驳回入口
    public List<CfMaterialAuditListView.AuditModifyEntry> getAllAuditEntry(CfInfoSimpleModel simpleModel,
                                                                            List<CrowdfundingInfoDataStatusTypeEnum> requireTypes,
                                                                            Map<Integer, Integer> allDataStatus) {

        List<CfMaterialAuditListView.AuditModifyEntry> auditStatus = Lists.newArrayList();
        if (requireTypes.contains(CrowdfundingInfoDataStatusTypeEnum.PAYEE_INFO_SUBMIT)) {
            auditStatus.addAll(getAuditEntryByDataType(simpleModel, allDataStatus,
                    CrowdfundingInfoDataStatusTypeEnum.PAYEE_INFO_SUBMIT, payeeListService));
        }

        if (requireTypes.contains( CrowdfundingInfoDataStatusTypeEnum.PATIENT_INFO_SUBMIT)) {
           auditStatus.addAll(getAuditEntryByDataType(simpleModel, allDataStatus,
                    CrowdfundingInfoDataStatusTypeEnum.PATIENT_INFO_SUBMIT, patientListService));
        }

        if (requireTypes.contains( CrowdfundingInfoDataStatusTypeEnum.TREATMENT_INFO_SUBMIT)) {
            auditStatus.addAll(getAuditEntryByDataType(simpleModel, allDataStatus,
                    CrowdfundingInfoDataStatusTypeEnum.TREATMENT_INFO_SUBMIT, treatmentListService));
        }

        if (requireTypes.contains( CrowdfundingInfoDataStatusTypeEnum.FUND_USE_SUBMIT)) {
            auditStatus.addAll(getAuditEntryByDataType(simpleModel, allDataStatus,
                    CrowdfundingInfoDataStatusTypeEnum.FUND_USE_SUBMIT, fundUseRejectListService));
        }

        if (requireTypes.contains( CrowdfundingInfoDataStatusTypeEnum.BASE_INFO_SUBMIT)) {
            auditStatus.addAll(getAuditEntryByDataType(simpleModel, allDataStatus,
                    CrowdfundingInfoDataStatusTypeEnum.BASE_INFO_SUBMIT, titleContentListService));
        }

        return auditStatus;
    }

    private List<CfMaterialAuditListView.AuditModifyEntry> getAuditEntryByDataType(CfInfoSimpleModel simpleModel,
                                                                                   Map<Integer, Integer> dataTypeStatus,
                                                                                   CrowdfundingInfoDataStatusTypeEnum dataType,
                                                                                   ICfMaterialRejectListService listService) {
        List<CfMaterialAuditListView.AuditModifyEntry> result = Lists.newArrayList();
        Integer minorStatus = dataTypeStatus.get(dataType.getCode());
        if (!REJECT_SUBMIT_STATUS.contains(minorStatus)) {
            log.info("当前案例的材料的状态不是驳回、修改状态.caseId:{} dataType:{} status:{}", simpleModel.getId(), dataType, minorStatus);
            return result;
        }

        Pair<Map<Integer, Set<String>>, List<CfMaterialAuditListView.ModifySuggest>> minorAudits = getRejectDetails(
                simpleModel.getInfoId(), dataType);
        if (MapUtils.isEmpty(minorAudits.getLeft())) {
            log.info("当前案例的材料项没有驳回理由 caseId:{} dataType:{} status:{}", simpleModel.getId(), dataType, minorStatus);
            return result;
        }

        CfRefuseReasonMsg reasonMsg = null;
        if (canChangeMaterialType(dataType)) {
            reasonMsg = cfRefuseReasonMsgBiz.selectByInfoUuidAndType(simpleModel.getInfoId(), dataType.getCode());
            List<CfMaterialAuditListView.AuditModifyEntry> auditEntrySnapshot = getAuditEntrySnapshot(simpleModel, reasonMsg);
            if (auditEntrySnapshot != null) {
                log.info("当前案例可以取已经保存的修改入口。caseId:{} dataType:{} result:{}", simpleModel.getId(), dataType,
                        JSON.toJSONString(auditEntrySnapshot));
                return auditEntrySnapshot;
            }
        }

        CfMaterialParam param = new CfMaterialParam();
        param.setCaseId(simpleModel.getId());
        param.setInfoUuid(simpleModel.getInfoId());
        param.setDataType(dataType);
        param.setMaterialAuditStatus(minorStatus);
        param.setRejectDetails(minorAudits.getLeft());
        param.setSuggestViews(minorAudits.getRight());
        List<CfMaterialAuditListView.AuditModifyEntry> auditEntry = listService.getRejectModifyEntry(param);
        if (reasonMsg != null && canChangeMaterialType(dataType)) {
            saveAuditEntry(simpleModel, reasonMsg, auditEntry);
        }
        return auditEntry;
    }

    private boolean canChangeMaterialType(CrowdfundingInfoDataStatusTypeEnum dataType) {
        return dataType == CrowdfundingInfoDataStatusTypeEnum.PAYEE_INFO_SUBMIT ||
                dataType == CrowdfundingInfoDataStatusTypeEnum.PATIENT_INFO_SUBMIT;
    }

    private void saveAuditEntry(CfInfoSimpleModel simpleModel,
                                CfRefuseReasonMsg reasonMsg,
                                List<CfMaterialAuditListView.AuditModifyEntry> auditEntry) {
        try {
            log.info("保存的案例的修改入口. caseId:{} reasonMsg:{} result:{}", JSON.toJSONString(simpleModel),
                    JSON.toJSONString(reasonMsg), JSON.toJSONString(auditEntry));
            storeService.saveCailiao(simpleModel.getUserId(), getAuditEntryStoreKey(reasonMsg.getId()),
                    JSON.toJSONString(auditEntry), simpleModel.getInfoId());
        } catch (Throwable e) {
            log.error("保存入口异常 simple:{}", JSON.toJSONString(simpleModel), e);
        }
    }

    private List<CfMaterialAuditListView.AuditModifyEntry> getAuditEntrySnapshot(CfInfoSimpleModel simpleModel,
                                                                                 CfRefuseReasonMsg reasonMsg) {

        List<CfMaterialAuditListView.AuditModifyEntry> result = null;

        if (reasonMsg == null) {
            return null;
        }

        CfCommonStoreModel storeModel = storeService.getByUserIdAndKey(simpleModel.getUserId(), getAuditEntryStoreKey(reasonMsg.getId()),
                simpleModel.getInfoId());

        if (storeModel != null) {
            try {
                result = JSON.parseArray(storeModel.getStoreValue(), CfMaterialAuditListView.AuditModifyEntry.class);
            } catch (Throwable e) {
                log.error("json解析错误 simplemodel:{}", JSON.toJSONString(simpleModel), e);
            }

        }

        return result;
    }

    private String getAuditEntryStoreKey(long id) {
        return String.format(AUDIT_ENTRY, id);
    }

    public Pair<Map<Integer, Set<String>>, List<CfMaterialAuditListView.ModifySuggest>> getRejectDetails(
            String infoUuid, CrowdfundingInfoDataStatusTypeEnum typeEnum) {

        CfRefuseReasonMsg reasonMsg = cfRefuseReasonMsgBiz.selectByInfoUuidAndType(infoUuid, typeEnum.getCode());
        if (reasonMsg == null || StringUtils.isBlank(reasonMsg.getItemReason())) {
            log.info("不能找到案例的驳回理由.infoUuid:{} typeEnum:{}", infoUuid, typeEnum);
            return Pair.of(null, null);
        }

        Map<Integer, Set<String>> rejectDetails = Maps.newHashMap();
        List<CfMaterialStatusService.ItemReason> itemReasonList = Lists.newArrayList();
        try {
            itemReasonList = JSON.parseArray(reasonMsg.getItemReason(), CfMaterialStatusService.ItemReason.class);
        } catch (Exception e) {
            log.error("驳回理由解析错误 infoUuid:{}", infoUuid, e);
        }

        // 一定需要驳回理由
        if (CollectionUtils.isEmpty(itemReasonList)) {
            log.error("不能找到案例的驳回理由.infoUuid:{} typeEnum:{}", infoUuid, typeEnum);
            return Pair.of(null, null);
        }

        for (CfMaterialStatusService.ItemReason reason : itemReasonList) {
            Integer rejectPosition = getMaterialRejectPosition(reason.getItemIds());
            if (rejectPosition == null) {
                log.warn("不能映射到具体的驳回位置.infoUuid:{} itemId:{}", infoUuid, reason.getItemIds());
                continue;
            }
            MaterialCollectionUtils.putValuesToSet(rejectDetails, rejectPosition,
                    Sets.newHashSet(reason.getReason().values()));
        }

        return Pair.of(rejectDetails, getCaseSuggestList(reasonMsg));
    }

    private List<CfMaterialAuditListView.ModifySuggest> getCaseSuggestList(CfRefuseReasonMsg reasonMsg) {

        List<CfMaterialAuditListView.ModifySuggest> suggestList = Lists.newArrayList();
        if (StringUtils.isBlank(reasonMsg.getSuggestModify())) {
            return suggestList;
        }

        AuditSuggestModifyDetail suggest = JSON.parseObject(reasonMsg.getSuggestModify(), AuditSuggestModifyDetail.class);
        if (suggest != null && CollectionUtils.isNotEmpty(suggest.getAllModifyIds())) {

            Set<MaterialModifySuggestType> allSuggestTypes = Sets.newHashSet();

            Map<Integer, Set<Integer>> modifyIdTPositions = Maps.newHashMap();
            for (Map.Entry<Integer, Set<Integer>> entry : suggest.getRejectTModify().entrySet()) {

                for (Integer modifyId : entry.getValue()) {
                    allSuggestTypes.add(MaterialModifySuggestType.valueOfCode(modifyId));
                    MaterialCollectionUtils.putValueToSet(modifyIdTPositions, modifyId, entry.getKey());
                }
            }

            List<MaterialModifySuggestType> sortSuggestTypes = allSuggestTypes.stream().sorted(Comparator.comparing(MaterialModifySuggestType::getSort))
                    .collect(Collectors.toList());

            for (MaterialModifySuggestType suggestType : sortSuggestTypes) {
                suggestList.add(new CfMaterialAuditListView.ModifySuggest(suggestType,
                        getMsgByEntityIds(modifyIdTPositions.get(suggestType.getCode()))));
            }
        }

        return suggestList;
    }

    private Set<String> getMsgByEntityIds(Set<Integer> entityIds) {

        Set<String> rejectMsgs = Sets.newHashSet();

        List<CfRefuseReasonEntitySlave> allEntitys = entitySlaveBiz.selectByReasonIds(entityIds);
        for (CfRefuseReasonEntitySlave entity : allEntitys) {
            rejectMsgs.add(entity.getContent());
        }

        return rejectMsgs;
    }

    private Integer getMaterialRejectPosition(int dbId) {

        try {
            String context = refusePositionCache.get(dbId);
            MaterialRejectPositionType rejectType = MaterialRejectPositionType.getMaterialPosition(context);
            return rejectType != null ? rejectType.getCode() : null;
        } catch (Throwable e) {
            log.error("获取案例的驳回位置异常.dbId:{}", dbId, e);
        }
        return null;
    }

    public static String getEntryKey(String infoUuid, int entryCode) {
        return infoUuid + ENTRY_SPLIT + entryCode;
    }

    private Integer getEntryCode(String key) {
        String[] values = key.split(ENTRY_SPLIT);
        return Integer.valueOf(values[1]);
    }

    private void fillModifyStatus(CfInfoSimpleModel simpleModel, CfMaterialAuditListView listView) {
        List<String> allKeys = Lists.newArrayList();
        if (CollectionUtils.isEmpty(listView.getAllAuditEntrys())) {
            return;
        }
        listView.getAllAuditEntrys().forEach(item->{allKeys.add(getEntryKey(simpleModel.getInfoId(), item.getEntryCode()));});

        List<CfCommonStoreModel> storeModels =  storeService.getByKeys(simpleModel.getUserId(), allKeys,simpleModel.getInfoId());
        Set<Integer> hasModify = Sets.newHashSet();

        storeModels.forEach(item->{hasModify.add(getEntryCode(item.getStoreKey()));});

        for (CfMaterialAuditListView.AuditModifyEntry modifyEntry : listView.getAllAuditEntrys()) {

            modifyEntry.setEntryStatus(hasModify.contains(modifyEntry.getEntryCode())
                    ? CfMaterialAuditListView.ModifyStatus.HAS_MODIFY.getCode()
                    : CfMaterialAuditListView.ModifyStatus.NEED_MODIFY.getCode());
            for (CfMaterialAuditListView.FieldItemStatus itemStatus : modifyEntry.getAllFields()) {
                if (MaterialAuditEntry.isSuggestEntry(modifyEntry.getEntryCode())) {
                    itemStatus.setFieldStatus(
                            hasModify.contains(modifyEntry.getEntryCode())
                                    ? CfMaterialAuditListView.ModifyStatus.HAS_MODIFY.getCode()
                                    : CfMaterialAuditListView.ModifyStatus.SUGGEST_MODIFY.getCode()
                    );
                } else {
                    itemStatus.setFieldStatus(modifyEntry.getEntryStatus());
                }
            }
        }
    }

    public void fillBaseInfoRejectDetail(CrowdfundingInfoBaseVo crowdfundingInfoBaseVo) {
        Pair<Map<Integer, Set<String>>, List<CfMaterialAuditListView.ModifySuggest>> pair =
                getRejectDetails(crowdfundingInfoBaseVo.getInfoUuid(), CrowdfundingInfoDataStatusTypeEnum.BASE_INFO_SUBMIT);
        if (pair != null && MapUtils.isNotEmpty(pair.getLeft())) {
            Map<Integer, List<String>> rejects = Maps.newHashMap();
            for (Map.Entry<Integer, Set<String>> entry : pair.getLeft().entrySet()) {
                rejects.put(entry.getKey(), Lists.newArrayList(entry.getValue()));
            }
            crowdfundingInfoBaseVo.setRejectDetails(rejects);
        }
    }

}
