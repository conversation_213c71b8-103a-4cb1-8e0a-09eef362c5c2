package com.shuidihuzhu.cf.service.activity;

import com.shuidihuzhu.cf.vo.activity.ActivityCertificateVO;

/**
 * @author: fengxuan
 * @create 2019-10-17 13:30
 **/
public interface IActivityCertificationService {

    /**
     * 返回证书上相关信息，首次捐（转、浏览）时间、次数、昵称、是否生成过证书、证书编号
     */
    ActivityCertificateVO getCertificateInfo(long userId);

    /**
     *保存前端传过来的证书编号
     */
    void saveDocumentId(long userId, String documentId);
}
