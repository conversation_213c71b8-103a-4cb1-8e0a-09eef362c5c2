package com.shuidihuzhu.cf.service.crowdfunding;

import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.CfInfoStatBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingOrderShardingBiz;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.finance.model.CfDonorRefundApply;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSimpleModel;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.redisson.api.RMap;
import org.redisson.api.RSet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 设计文档：https://wdh.feishu.cn/wiki/wikcn8rtYhnJHIQP09WlRfCWmub
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class CfCaseDonatorService {

    @Resource(name = "cf2RedissonHandler")
    private RedissonHandler redissonHandler;
    @Autowired
    private CfInfoStatBiz cfInfoStatBiz;
    @Resource(name = "crowdfundingOrderShardingBizImpl")
    private CrowdfundingOrderShardingBiz crowdfundingOrderShardingBiz;
    @Autowired
    private UserInfoDelegate userInfoDelegate;
    @Resource(name = "cfOlapCaseStat")
    private RedissonHandler cfOlapCaseStatRedissonHandler;

    public boolean handleDonatorCountOnPaySuccess(CfInfoSimpleModel cfInfoSimpleModel, CrowdfundingOrder order) {
        long userId = order.getUserId();
        int caseId = cfInfoSimpleModel.getId();

        String redisKey = getRedisKey(caseId);
        String isLoading = getLoadingRedisKey(caseId);

        if (redissonHandler.exists(redisKey)) {
            if (redissonHandler.exists(isLoading)) {
                return false;
            }
            incRedisAndSqlOnPaySuccess(userId, caseId, redisKey);
            return true;
        }
        return handleByLock(userId, caseId, redisKey, isLoading, "paySuccess");
    }

    public boolean handleDonatorCountOnRefundSuccess(CfDonorRefundApply cfDonorRefundApply) {
        int caseId = cfDonorRefundApply.getInfoId();
        long userId = cfDonorRefundApply.getUserId();

        String redisKey = getRedisKey(caseId);
        String isLoading = getLoadingRedisKey(caseId);
        return handleByLock(userId, caseId, redisKey, isLoading, "refundSuccess");
    }

    private boolean handleByLock(long userId, int caseId, String redisKey, String isLoading, String channel) {
        String tryLock = null;
        String lockKey = "handle_donator_count_" + caseId;
        try {
            // 自动续锁
            tryLock = redissonHandler.tryLock(lockKey, 0, -1);
            // 未获取到锁
            if (StringUtils.isBlank(tryLock)) {
                log.warn("lockKey未获取到，caseId:{},userId:{} ", caseId, userId);
                return false;
            }
            if ("paySuccess".equals(channel)) {
                if (redissonHandler.exists(redisKey)) {
                    return false;
                }
                // 查询订单表从库并更新redis和冗余表
                writeRedisBySelect(caseId, redisKey);
                return true;
            }
            // 退款成功场景 isLoading1h过期
            redissonHandler.setEX(isLoading, true, TimeUnit.HOURS.toMillis(1));
            rewriteOrUpdateOnRefundSuccess(caseId, userId, redisKey);
            redissonHandler.del(isLoading);
            return true;
        } catch (Exception e) {
            log.error("unlock error, lockKey:{}", lockKey, e);
        } finally {
            try {
                // 解锁
                if (StringUtils.isNotBlank(tryLock)) {
                    redissonHandler.unLock(lockKey, tryLock);
                }
            } catch (Exception ex) {
                log.error("unlock error, lockKey:{}", lockKey, ex);
            }
        }
        return false;
    }

    private void incRedisAndSqlOnPaySuccess(long userId, int caseId, String redisKey) {
        // 用户是否对该案例捐过款
        if (!redissonHandler.sadd(redisKey, userId)) {
            log.info("该用户已存在有效捐款，本次捐款不更新，userId:{},caseId:{}", userId, caseId);
            return;
        }
        // 没捐过更新表
        log.info("该用户不存在有效捐款 更新本次捐款情况，userId:{},caseId:{}", userId, caseId);
        cfInfoStatBiz.incDonatorCount(caseId);
    }

    private void writeRedisBySelect(int caseId, String redisKey) {
        log.info("{}不存在该redisKey，查询订单表", redisKey);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<Long> donatorListBySql = crowdfundingOrderShardingBiz.getSingleRefundSuccessAndPaySuccessUserIdListByCrowdfundingId(caseId);
        stopWatch.stop();
        log.debug("查询订单表时长:{},caseId:{}", stopWatch.getTime(), caseId);
        updateRedisAndCfInfoStat(caseId, redisKey, new HashSet<>(donatorListBySql));
    }

    private void rewriteOrUpdateOnRefundSuccess(int caseId, long userId, String redisKey) {
        // 订单表查询退款后用户id列表
        log.info("用户退款，查询订单表，userId:{},caseId:{}", userId, caseId);
        List<Long> donatorListBySql = crowdfundingOrderShardingBiz.getSingleRefundSuccessAndPaySuccessUserIdListByCrowdfundingId(caseId);
        handleRefundUser(caseId, userId, redisKey, donatorListBySql);
    }

    private void handleRefundUser(int caseId, long userId, String redisKey, List<Long> donatorListBySql) {
        if (!redissonHandler.exists(redisKey)) {
            log.info("{}不存在该redisKey，重写redis和冗余表", redisKey);
            updateRedisAndCfInfoStat(caseId, redisKey, new HashSet<>(donatorListBySql));
            return;
        }
        // 用户有多笔捐款退一笔不处理
        if (donatorListBySql.contains(userId)) {
            log.info("用户有多笔有效捐款，本次退款不更新，userId:{},caseId:{}", userId, caseId);
            return;
        }
        // 用户单次捐款后退款更新
        log.info("用户有单笔有效捐款，更新本次退款情况，userId:{},caseId:{}", userId, caseId);
        if (redissonHandler.srem(redisKey, userId)) {
            cfInfoStatBiz.decDonatorCount(caseId);
        }
    }

    private void updateRedisAndCfInfoStat(int caseId, String redisKey, Set<Long> donatorListBySql) {
        redissonHandler.saddAll(redisKey, donatorListBySql);
        cfInfoStatBiz.updateDonatorCount(caseId, redissonHandler.getSet(redisKey).size());
        // 设置过期时间
        RSet<Object> set = redissonHandler.getRedissonClient().getSet(redisKey);
        set.expire(30, TimeUnit.DAYS);
    }

    private String getRedisKey(int caseId) {
        return "donatorCount" + caseId;
    }

    private String getLoadingRedisKey(int caseId) {
        return "getLoadingRedisKey" + caseId;
    }

    /**
     * 获取案例维度，捐款人头像redis key
     *
     * @param caseId -
     * @return -
     */
    public String getDonorHeadUrlKey(int caseId) {
        return "cf_donor_head_img_url_" + caseId;
    }

    public String getDonorHeadUrlLockKey(int caseId) {
        return "cf_donor_head_img_url_lock_" + caseId;
    }

    /**
     * 获取案例捐款人的微信头像
     *
     * @param caseId -
     * @return -
     */
    public List<String> getDonorHeadUrls(int caseId) {
        // 通过redis获取
        String donorHeadUrlKey = getDonorHeadUrlKey(caseId);
        boolean exists = cfOlapCaseStatRedissonHandler.exists(donorHeadUrlKey);
        if (!exists) {
            boolean refresh = this.donorUserHeadUrlRefresh(caseId);
            if (!refresh) {
                return Collections.emptyList();
            }
        }
        RMap<String, String> userIdHeadUrlMap = cfOlapCaseStatRedissonHandler.getRedissonClient().getMap(donorHeadUrlKey);
        if (MapUtils.isNotEmpty(userIdHeadUrlMap)) {
            return userIdHeadUrlMap
                    .entrySet(20)
                    .stream()
                    .map(Map.Entry::getValue)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    /**
     * 刷新redis存储用户头像
     *
     * @param caseId -
     */
    private boolean donorUserHeadUrlRefresh(int caseId) {
        String identify = null;
        String donorHeadUrlKey = getDonorHeadUrlKey(caseId);
        String donorHeadUrlLockKey = getDonorHeadUrlLockKey(caseId);
        try {
            identify = cfOlapCaseStatRedissonHandler.tryLock(donorHeadUrlLockKey, 0, -1);
            if (StringUtils.isNotBlank(identify)) {
                boolean exists = cfOlapCaseStatRedissonHandler.exists(donorHeadUrlKey);
                if (exists) {
                    return true;
                }
                List<UserInfoModel> userInfoModels = loadUserHeadUrl(caseId);
                RMap<Object, Object> rMap = cfOlapCaseStatRedissonHandler.getRedissonClient().getMap(donorHeadUrlKey);
                if (CollectionUtils.isEmpty(userInfoModels)) {
                    // 占位
                    rMap.put("-1", "");
                } else {
                    rMap.putAll(userInfoModels
                            .stream()
                            .filter(r -> StringUtils.isNotBlank(r.getHeadImgUrl()))
                            .collect(Collectors.toMap(r -> String.valueOf(r.getUserId()), UserInfoModel::getHeadImgUrl, (a, b) -> b)));
                }
                rMap.expire(1, TimeUnit.DAYS);
                return true;
            }
        } catch (Exception e) {
            log.warn("redis try lock error, ", e);
        } finally {
            if (StringUtils.isNotBlank(identify)) {
                try {
                    cfOlapCaseStatRedissonHandler.unLock(donorHeadUrlLockKey, identify);
                } catch (Exception e) {
                    log.warn("redis unlock lock error, ", e);
                }
            }
        }
        return false;
    }

    private List<UserInfoModel> loadUserHeadUrl(int caseId) {
        List<UserInfoModel> result = new ArrayList<>(100);
        Set<Long> userIdSet = new HashSet<>(100);
        int offset = 0, size = 100, imgSize = 50;
        while (result.size() < imgSize) {
            List<Long> userIds = crowdfundingOrderShardingBiz.getValidUserIdByCaseIdAndNotAnonymous(caseId, size, offset);
            if (CollectionUtils.isEmpty(userIds)) {
                break;
            }
            List<UserInfoModel> userInfoModels = userInfoDelegate.getUserInfoByUserIdBatch(userIds.stream().distinct().collect(Collectors.toList()));
            if (CollectionUtils.isNotEmpty(userInfoModels)) {
                userInfoModels.stream()
                        .filter(r -> StringUtils.isNotBlank(r.getHeadImgUrl()))
                        .filter(r -> userIdSet.add(r.getUserId()))
                        .forEach(result::add);
            }
            offset += size;
            if (userIds.size() < size) {
                break;
            }
        }
        return result;
    }

    /**
     * 插入捐款人头像
     *
     * @param cfInfoSimpleModel -
     * @param order -
     * @return -
     */
    public boolean putUserHeadUrl(CfInfoSimpleModel cfInfoSimpleModel, CrowdfundingOrder order) {
        if (Objects.isNull(cfInfoSimpleModel) || Objects.isNull(order)) {
            return false;
        }
        boolean anonymous = order.isAnonymous();
        if (anonymous) {
            return false;
        }
        long userId = order.getUserId();
        if (userId <= 0) {
            return false;
        }
        int caseId = cfInfoSimpleModel.getId();

        UserInfoModel userInfoModel = userInfoDelegate.getUserInfoByUserId(userId);
        if (Objects.isNull(userInfoModel) || StringUtils.isBlank(userInfoModel.getHeadImgUrl())) {
            return false;
        }
        String donorHeadUrlKey = getDonorHeadUrlKey(caseId);
        boolean exists = cfOlapCaseStatRedissonHandler.exists(donorHeadUrlKey);
        if (!exists) {
            boolean refresh = this.donorUserHeadUrlRefresh(caseId);
            if (!refresh) {
                // 锁竞争失败也直接丢弃，不需要保证头像数量与订单的一致性
                return false;
            }
        }
        RMap<Object, Object> rMap = cfOlapCaseStatRedissonHandler.getRedissonClient().getMap(donorHeadUrlKey);
        if (rMap.size() > 100) {
            return true;
        }
        rMap.put(String.valueOf(userInfoModel.getUserId()), userInfoModel.getHeadImgUrl());
        return true;
    }

    /**
     * 删除捐款人头像
     *
     * @param cfDonorRefundApply -
     * @return -
     */
    public boolean removeUserHeadUrl(CfDonorRefundApply cfDonorRefundApply) {
        if (Objects.isNull(cfDonorRefundApply)
                || cfDonorRefundApply.getInfoId() <= 0
                || cfDonorRefundApply.getUserId() <= 0 || cfDonorRefundApply.getOrderId() <= 0) {
            return false;
        }
        String donorHeadUrlKey = getDonorHeadUrlKey(cfDonorRefundApply.getInfoId());
        RMap<Object, Object> rMap = cfOlapCaseStatRedissonHandler.getRedissonClient().getMap(donorHeadUrlKey);
        if (Objects.nonNull(rMap) && rMap.size() > 0) {
            rMap.remove(String.valueOf(cfDonorRefundApply.getUserId()));
        }
        return true;
    }
}
