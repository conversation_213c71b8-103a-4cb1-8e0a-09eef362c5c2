package com.shuidihuzhu.cf.service.crowdfunding;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingOrderBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingPayRecordBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingUserBiz;
import com.shuidihuzhu.cf.delegate.ICFFinanceFeignDelegate;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.model.CfRefundRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingUser;
import com.shuidihuzhu.cf.vo.CfRefundResultVo;
import com.shuidihuzhu.cf.vo.CrowdfundingUserVo;
import com.shuidihuzhu.cf.vo.PayRecordVo;
import com.shuidihuzhu.cf.vo.RefundRecordVo;
import com.shuidihuzhu.common.web.util.MoneyUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: lixuan
 * @date: 2018/8/1 20:58
 */
@Slf4j
@Service
public class CfRefundRecordService {

    @Autowired
    private CrowdfundingPayRecordBiz crowdfundingPayRecordBiz;
    @Autowired
    private CrowdfundingOrderBiz crowdfundingOrderBiz;
    @Autowired
    private CrowdfundingUserBiz crowdfundingUserBiz;
    @Autowired
    private CrowdfundingInfoDetailService crowdfundingInfoDetailService;
    @Autowired
    private ICFFinanceFeignDelegate cfFinanceFeignDelegate;

    public List<CfRefundResultVo> getData(String infoUuid, Long anchorId, int size) {
        // 退款数据
        List<CfRefundRecord> cfRefundRecordList = cfFinanceFeignDelegate.getRefundRecordByUuidAndAnchorId(infoUuid,
                anchorId, size);
        if(CollectionUtils.isEmpty(cfRefundRecordList)) {
            return Collections.emptyList();
        }
        // CrowdfundingPayRecord数据
        List<String> payUids = cfRefundRecordList.stream().map(CfRefundRecord::getMhtOrderNo).collect(Collectors.toList());
        List<CrowdfundingPayRecord> crowdfundingPayRecords = this.crowdfundingPayRecordBiz.getPaySuccessByPayUids(payUids);
        if(CollectionUtils.isEmpty(crowdfundingPayRecords)) {
            return Collections.emptyList();
        }
        List<Long> orderIds = crowdfundingPayRecords.stream().map(CrowdfundingPayRecord::getCrowdfundingOrderId)
                .collect(Collectors.toList());
        Map<String, CrowdfundingPayRecord> crowdfundingPayRecordMap = crowdfundingPayRecords.stream().collect(
                Collectors.toMap(CrowdfundingPayRecord::getPayUid, Function.identity()));
        // CrowdfundingOrder数据
        List<CrowdfundingOrder> crowdfundingOrders = this.crowdfundingOrderBiz.getListByIds(orderIds,null,null);
        Map<Long, CrowdfundingOrder> crowdfundingOrderMap = crowdfundingOrders.stream().collect(Collectors.toMap
                (CrowdfundingOrder::getId, Function.identity()));
        if(CollectionUtils.isEmpty(crowdfundingOrders)) {
            return Collections.emptyList();
        }
        // 用户数据
        List<CrowdfundingUser> crowdfundingUsers = this.crowdfundingInfoDetailService
                .convert2CrowdfundingUserFromUserThird(crowdfundingOrders);
        CrowdfundingUserVo userInfo = this.crowdfundingUserBiz.getUserInfo(crowdfundingUsers);

        // 数据聚合
        List<CfRefundResultVo> cfRefundResultVos = Lists.newArrayList();
        for (CfRefundRecord cfRefundRecord : cfRefundRecordList) {
            try {
                CfRefundResultVo cfRefundResultVo = buildCfRefundResultVo(crowdfundingPayRecordMap, crowdfundingOrderMap,
                        userInfo, cfRefundRecord);
                if(cfRefundResultVo != null) {
                    cfRefundResultVos.add(cfRefundResultVo);
                }
            } catch (Exception e) {
                log.error("", e);
            }
        }
        return cfRefundResultVos;
    }

    private CfRefundResultVo buildCfRefundResultVo(Map<String, CrowdfundingPayRecord> crowdfundingPayRecordMap,
                                                   Map<Long, CrowdfundingOrder> crowdfundingOrderMap,
                                                   CrowdfundingUserVo userInfo, CfRefundRecord cfRefundRecord) {
        long id = cfRefundRecord.getId();
        String mhtRefundNo = cfRefundRecord.getMhtRefundNo();
        String mhtOrderNo = cfRefundRecord.getMhtOrderNo();
        Date finishTime = cfRefundRecord.getFinishTime();
        int refundAmountInFen = cfRefundRecord.getRefundAmount();
        double refundAmount = MoneyUtil.divide(refundAmountInFen + "", "100", 2, BigDecimal.ROUND_HALF_UP).doubleValue();
        CrowdfundingPayRecord crowdfundingPayRecord = crowdfundingPayRecordMap.get(mhtOrderNo);
        if(crowdfundingPayRecord == null) {
            return null;
        }
        long crowdfundingOrderId = crowdfundingPayRecord.getCrowdfundingOrderId();
        String payUid = crowdfundingPayRecord.getPayUid();
        int realPayAmount = crowdfundingPayRecord.getRealPayAmount();
        double payAmount = MoneyUtil.divide(realPayAmount + "", "100", 2, BigDecimal.ROUND_HALF_UP).doubleValue();
        Date callbackTime = crowdfundingPayRecord.getCallbackTime();
        Timestamp payTime = new Timestamp(callbackTime.getTime());
        CrowdfundingOrder crowdfundingOrder = crowdfundingOrderMap.get(crowdfundingOrderId);
        if(crowdfundingOrder == null) {
            return null;
        }
        long userId = crowdfundingOrder.getUserId();
        CrowdfundingUser crowdfundingUser = userInfo.getUserIdMap().get(userId);
        String headImgUrl = "";
        String nickname = "";
        if(crowdfundingUser != null) {
            headImgUrl = crowdfundingUser.getHeadImgUrl();
            nickname = crowdfundingUser.getNickname();
        }
        return CfRefundResultVo.builder()
                .anchorId(id)
                .headUrl(StringUtils.trimToEmpty(headImgUrl))
                .nickName(StringUtils.trimToEmpty(nickname))
                .payRecordVo(PayRecordVo.builder().payTime(payTime).payUid(payUid).amount
                        (payAmount).build())
                .refundRecordVoList(ImmutableList.of(
                        RefundRecordVo.builder().amount(refundAmount).refundNo(mhtRefundNo)
                                .refundTime(finishTime == null ? null : new Timestamp(finishTime.getTime()))
                                .build())
                )
                .build();
    }

}
