package com.shuidihuzhu.cf.service.caseinfo;

import com.ctrip.framework.apollo.ConfigService;
import com.google.common.base.Splitter;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.biz.crowdfunding.CfFirstApproveBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoSimpleBiz;
import com.shuidihuzhu.cf.client.stat.enums.StatDataEnum;
import com.shuidihuzhu.cf.client.stat.facade.Sos;
import com.shuidihuzhu.cf.client.stat.model.IStatProperties;
import com.shuidihuzhu.cf.dao.crowdfunding.*;
import com.shuidihuzhu.cf.dao.crowdfunding.comment.CrowdfundingCommentShareDao;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.enums.PatientLabelEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingCommentType;
import com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.param.CrowdFundingVerificationParam;
import com.shuidihuzhu.cf.repository.CrowdfundingCommentShareRepository;
import com.shuidihuzhu.cf.util.SDEncryptUtils;
import com.shuidihuzhu.cf.util.crowdfunding.CfIdCardUtil;
import com.shuidihuzhu.client.dataservice.faceApi.v1.FaceApiClient;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class LoveStoryVerifyService {

    @Autowired
    private EvaluateLabelPatientRecordDao evaluateLabelPatientRecordDao;
    @Autowired
    private EvaluateLabelUserRecordDao evaluateLabelUserRecordDao;
    @Autowired
    private EvaluateLabelDao evaluateLabelDao;
    @Autowired
    private CrowdfundingInfoDao crowdfundingInfoDao;

    @Autowired
    private CrowdfundingInfoSimpleBiz crowdfundingInfoSimpleBiz;
    @Autowired
    private FaceApiClient faceApiClient;
    @Autowired
    private CrowdfundingCommentShareDao crowdfundingCommentShareDao;

    @Autowired
    private CrowdfundingOrderShardingUserIdDao crowdfundingOrderShardingUserIdDao;
    @Autowired
    private CfFirstApproveBiz cfFirstApproveBiz;

    @Resource(name = "redis-cf-api-new")
    private RedissonHandler redissonHandler;

    @Autowired
    private CrowdfundingCommentShareRepository crowdfundingCommentShareRepository;

    private ImmutableList<EvaluateLabelDO> allLabel;

    @PostConstruct
    public void init(){
        loadLabelData();
        ConfigService.getAppConfig().addChangeListener(
                changeEvent -> loadLabelData(),
                Sets.newHashSet("apollo.love-story.reload-flag")
        );
    }

    private void loadLabelData() {
        allLabel = ImmutableList.copyOf(evaluateLabelDao.getAll());
    }

    private final LoadingCache<Long, List<ReplyInfo>> replyListLocalCache = CacheBuilder.newBuilder()
            .refreshAfterWrite(1, TimeUnit.MINUTES)
            .expireAfterWrite(1, TimeUnit.MINUTES)
            .build(CacheLoader.from(this::getReplyListByUserIdByRedis));

    public int testCnt(long userId){
        return getDonateAndShareCountByUserId(userId);
    }

    public void saveLabel(CrowdFundingVerificationParam crowdFundingVerificationParam) {

        CfInfoSimpleModel simpleInfo = crowdfundingInfoSimpleBiz.getFundingInfo(crowdFundingVerificationParam.getInfoUuid());
        if(simpleInfo == null){
            return ;
        }
        int caseId = simpleInfo.getId();
        long userId = simpleInfo.getUserId();

        String label = crowdFundingVerificationParam.getLabel();

        if(StringUtils.isEmpty(label)){
            log.info("label is null");
            return ;
        }
        //分隔按，传来的标签文案字符串
        List<String> labelIdList = Splitter.on(",").splitToList(label);

        List<EvaluateLabelPatientRecordDO> evaluateLabelPatientRecordDOList = Lists.newArrayList();
        List<EvaluateLabelUserRecordDO> evaluateLabelUserRecordDOList = Lists.newArrayList();

        for (String labelId : labelIdList) {

            //存患者标签记录表
            EvaluateLabelPatientRecordDO evaluateLabelPatientRecordDO = new EvaluateLabelPatientRecordDO();
            evaluateLabelPatientRecordDO.setCaseId(caseId);
            evaluateLabelPatientRecordDO.setLabelId(Integer.parseInt(labelId));
            evaluateLabelPatientRecordDOList.add(evaluateLabelPatientRecordDO);

            //存用户标签记录表
            EvaluateLabelUserRecordDO evaluateLabelUserRecordDO = new EvaluateLabelUserRecordDO();
            evaluateLabelUserRecordDO.setCaseId(caseId);
            evaluateLabelUserRecordDO.setUserId(userId);
            evaluateLabelUserRecordDO.setLabelId(Integer.parseInt(labelId));
            evaluateLabelUserRecordDOList.add(evaluateLabelUserRecordDO);

        }
        //批量存入
        evaluateLabelPatientRecordDao.insertOrUpdate(evaluateLabelPatientRecordDOList);
        evaluateLabelUserRecordDao.insertBatch(evaluateLabelUserRecordDOList);
    }

    public LoveStoryInfoVerifySimpleVO getSimpleInfos(String infoUuid) {
        CfInfoSimpleModel simpleInfo = crowdfundingInfoSimpleBiz.getFundingInfo(infoUuid);

        if(simpleInfo == null){
            return null;
        }
        long userId = simpleInfo.getUserId();
        int caseId = simpleInfo.getId();
        int donateAndShareCount = getDonateAndShareCountByUserId(userId);
        LoveStoryInfoVerifySimpleVO view = new LoveStoryInfoVerifySimpleVO();
        view.setCnt(donateAndShareCount);

        //按标签选择次数对list排序(一样时取最新)
        List<EvaluateLabelDO> allLabelList = getAllLabel();
        Map<Long, EvaluateLabelDO> LabelIdLabelMap = allLabelList.stream().collect(Collectors.toMap(EvaluateLabelDO::getId, Function.identity()));

        List<EvaluateLabelPatientRecordDO> evaluateLabelPatientRecordDOList = evaluateLabelPatientRecordDao.getListByCaseIdOrderByCnt(caseId);
        List<LabelInfo> labelInfoList = Lists.newArrayList();
        for (EvaluateLabelPatientRecordDO evaluateLabelPatientRecordDO : evaluateLabelPatientRecordDOList) {
            EvaluateLabelDO evaluateLabelDO = LabelIdLabelMap.get((long) evaluateLabelPatientRecordDO.getLabelId());
            if (evaluateLabelDO == null) {
                continue;
            }
            LabelInfo labelInfo = new LabelInfo();
            labelInfo.setCnt(evaluateLabelPatientRecordDO.getLabelCnt());
            labelInfo.setLabel(evaluateLabelDO.getLabelName());
            labelInfo.setLabelId(evaluateLabelPatientRecordDO.getLabelId());
            labelInfoList.add(labelInfo);
        }
        view.setLabel(labelInfoList);
        return view;
    }

    private ImmutableList<EvaluateLabelDO> getAllLabel() {
        return allLabel;
    }

    private int getDonateAndShareCountByUserId(long userId) {
        ArrayList<StatDataEnum> dataEnums = Lists.newArrayList(StatDataEnum.SHARE_COUNT, StatDataEnum.DONATE_COUNT);
        IStatProperties properties = Sos.facade().getProperties(userId, 0, dataEnums);
//        Integer shareCount = properties.getInt(StatDataEnum.SHARE_COUNT).orElse(0);
        Integer donateCount = properties.getInt(StatDataEnum.DONATE_COUNT).orElse(0);
//        return shareCount + donateCount;
        return donateCount;
    }

    public LoveStoryInfoVerify getInfos(String infoUuid) {

        LoveStoryInfoVerifySimpleVO simpleInfos = getSimpleInfos(infoUuid);
        if(simpleInfos == null){
            return null;
        }

        LoveStoryInfoVerify view = new LoveStoryInfoVerify();

        view.setCnt(simpleInfos.getCnt());
        view.setLabel(simpleInfos.getLabel());

        CfInfoSimpleModel fundingInfo = crowdfundingInfoSimpleBiz.getFundingInfo(infoUuid);

        //获取发起人姓名
        view.setName(getRaiserName(fundingInfo.getId()));

        long userId = fundingInfo.getUserId();

        List<ReplyInfo> replyList = getReplyListByUserIdWithLocalCache(userId);
        view.setReply(replyList);
        return view;
    }

    private String getRaiserName(int caseId) {
        CfFirsApproveMaterial firstApproveInfo = cfFirstApproveBiz.getByInfoId(caseId);
        if(firstApproveInfo == null){
            return "求助家庭";
        }
        int userRelationType = firstApproveInfo.getUserRelationType();
        if (userRelationType == UserRelTypeEnum.SELF.getValue()) {
            return firstApproveInfo.getPatientRealName();
        }
        return firstApproveInfo.getSelfRealName();
    }

    private List<ReplyInfo> getReplyListByUserIdWithLocalCache(long userId) {
        try {
            return replyListLocalCache.get(userId);
        } catch (ExecutionException e) {
            log.error("getReplyListByUserIdWithLocalCache", e);
        }
        return Lists.newArrayList();
    }

    private List<ReplyInfo> getReplyListByUserIdByRedis(long userId) {
        String key = getReplyKeyByUserId(userId);
        RList<ReplyInfo> cacheList = redissonHandler.getRedissonClient().getList(key);
        if (cacheList.remainTimeToLive() < 30 * 60 * 1000L) {
            List<ReplyInfo> list = getReplyListByUserId(userId);
            cacheList.clear();
            cacheList.addAll(list);
            cacheList.expire(1, TimeUnit.DAYS);
            return list;
        }
        return cacheList.readAll();
    }

    private String getReplyKeyByUserId(long userId) {
        return "user-reply-" + userId;
    }

    private List<ReplyInfo> getReplyListByUserId(long userId) {
        List<ReplyInfo> list = Lists.newArrayList();
        //返回求助人对患者的回复（同一个案例取最新）
        //获取患者捐款订单号倒序最新20条
        List<CrowdfundingOrderShardingModel> orderShardingModels =
                crowdfundingOrderShardingUserIdDao.getCrowdfundingOrderShardingModelListByUserId(userId);
        if (CollectionUtils.isEmpty(orderShardingModels)) {
            return list;
        }
        Set<Long> caseIdSet = Sets.newHashSet();
        List<Long> orderIdList = Lists.newArrayList();
        for (CrowdfundingOrderShardingModel crowdfundingOrder : Lists.reverse(orderShardingModels)) {
            // 相同案例只要最新的一个
            if (caseIdSet.contains(crowdfundingOrder.getCrowdfundingId())) {
                continue;
            }
            caseIdSet.add(crowdfundingOrder.getCrowdfundingId());
            orderIdList.add(crowdfundingOrder.getId());
        }

        int commentType = CrowdfundingCommentType.CONTRIBUTE_RECORD.value();
        List<CrowdfundingComment> commentList = crowdfundingCommentShareRepository.getReplyByParentList(commentType, orderIdList, 5);
        if (CollectionUtils.isEmpty(commentList)) {
            return Lists.newArrayList();
        }
        return commentList.stream()
                .filter(v -> StringUtils.isNotBlank(v.getContent()))
                .map(v -> {
                    ReplyInfo replyInfo = new ReplyInfo();
                    replyInfo.setReply(v.getContent());
                    replyInfo.setDate(DateUtil.formatDate(v.getCreateTime()));
                    return replyInfo;
                })
                .collect(Collectors.toList());
    }

    public LabelAndRank getLabelRank(String infoUuid) {
        LabelAndRank view = new LabelAndRank();
        List<LabelRank> labelRankList = Lists.newArrayList();

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoDao.getFundingInfo(infoUuid);
        if(crowdfundingInfo == null){
            return null;
        }
        int caseId = crowdfundingInfo.getId();

        int age = getAgeMaxByCaseId(caseId);

        // 获取默认展示标签列表
        List<LabelRank> labelRandListByAge = getLabelRandListByAge(age);

        // 获取使用最多的标签
        LabelRank maxUsedLabel = getMaxUsedLabel(caseId);
        if (maxUsedLabel != null) {
            labelRandListByAge.add(0, maxUsedLabel);
        }
        List<LabelRank> labelRandList = labelRandListByAge.stream().distinct().limit(8).collect(Collectors.toList());
        labelRankList.addAll(labelRandList);

        view.setLabelRank(labelRankList);
        view.setLabelAll(getLabelAllMapWithoutAge());
        return view;
    }

    private int getAgeMaxByCaseId(int caseId) {
        //通过解密身份证计算年龄
        CfFirsApproveMaterial info = cfFirstApproveBiz.getByInfoId(caseId);
        if(info == null){
            return -1;
        }
        int patientIdType = info.getPatientIdType();
        if (patientIdType == UserIdentityType.birth.getCode()) {
            return 6;
        }
        if (patientIdType != UserIdentityType.identity.getCode()) {
            return -1;
        }
        String patientCryptoIdcard = info.getPatientCryptoIdcard();
        if (StringUtils.isBlank(patientCryptoIdcard)) {
            return -1;
        }
        String idCard = SDEncryptUtils.decrypt(patientCryptoIdcard);
        int age = CfIdCardUtil.getAge(idCard);
        if (age > 23) {
            return -1;
        }
        if (age > 18) {
            return 23;
        }
        if (age > 6) {
            return 18;
        }
        return 6;
    }

    @Nullable
    private LabelRank getMaxUsedLabel(int caseId) {
        //通过标签选择数量对所有标签排序(一样时取最新)
        EvaluateLabelPatientRecordDO maxUsedByCaseId = evaluateLabelPatientRecordDao.getMaxUsedByCaseId(caseId);
        if (maxUsedByCaseId == null) {
            return null;
        }
        EvaluateLabelDO v = evaluateLabelDao.getByLabelId(maxUsedByCaseId.getLabelId());
        return createLabelRandByDO(v);
    }

    private List<LabelAndRank.OneLevel> getLabelAllMapWithoutAge() {
        ArrayList<LabelAndRank.OneLevel> view = Lists.newArrayList();
        List<EvaluateLabelDO> all = getAllLabel();
        Set<Integer> excludeOneLevels = Sets.newHashSet(
                PatientLabelEnum.AGE.getId(),
                PatientLabelEnum.FAMILY.getId()
        );

        Map<Integer, List<LabelRank>> map = all.stream()
                .filter(v -> !excludeOneLevels.contains(v.getCharacterId()))
                .sorted(Comparator.comparing(EvaluateLabelDO::getCharacterId))
                .map(this::createLabelRandByDO)
                .collect(Collectors.groupingBy(v -> PatientLabelEnum.parse(v.getCharacterId()).getId()));

        Arrays.stream(PatientLabelEnum.values())
                .filter(v -> !excludeOneLevels.contains(v.getId()))
                .sorted(Comparator.comparing(PatientLabelEnum::getPriority))
                .forEachOrdered(v -> {
                    List<LabelRank> labelList = map.get(v.getId());
                    if (CollectionUtils.isEmpty(labelList)) {
                        return;
                    }
                    LabelAndRank.OneLevel oneLevel = new LabelAndRank.OneLevel();
                    oneLevel.setLabelList(labelList);
                    oneLevel.setName(v.getCharacter());
                    view.add(oneLevel);
                });
        return view;
    }

    private List<LabelRank> getLabelRandListByAge(int age) {

        List<EvaluateLabelDO> all = getAllLabel();

        return all.stream()
                .sorted((a, b) -> {
                    boolean aIsAge = a.getAgeMax() == age;
                    boolean bIsAge = b.getAgeMax() == age;
                    if (aIsAge && bIsAge) {
                        return (int) (a.getId() - b.getId());
                    }
                    if (aIsAge) {
                        return -1;
                    }
                    if (bIsAge) {
                        return 1;
                    }
                    return b.getLabelPriority() - a.getLabelPriority();
                })
                .limit(8)
                .map(this::createLabelRandByDO)
                .collect(Collectors.toList());
    }

    private LabelRank createLabelRandByDO(EvaluateLabelDO evaluateLabelDO) {
        LabelRank labelRank = new LabelRank();
        labelRank.setLabelId(evaluateLabelDO.getId());
        labelRank.setLabel(evaluateLabelDO.getLabelName());
        labelRank.setCharacterId(evaluateLabelDO.getCharacterId());
        labelRank.setCharacter(PatientLabelEnum.parse(evaluateLabelDO.getCharacterId()).getCharacter());
        labelRank.setCharacterPriority(PatientLabelEnum.parse(evaluateLabelDO.getCharacterId()).getPriority());
        return labelRank;
    }

}
