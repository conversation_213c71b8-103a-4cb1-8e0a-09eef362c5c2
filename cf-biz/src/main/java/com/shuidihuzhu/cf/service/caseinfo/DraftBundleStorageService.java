package com.shuidihuzhu.cf.service.caseinfo;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.client.ugc.feign.MaterialBundleFeignClient;
import com.shuidihuzhu.cf.client.ugc.model.subject.material.MaterialBundleDO;
import com.shuidihuzhu.cf.client.ugc.model.subject.material.MaterialUsageTypeEnum;
import com.shuidihuzhu.cf.client.ugc.service.MaterialBundleClient;
import com.shuidihuzhu.cf.constants.crowdfunding.RaiseCons;
import com.shuidihuzhu.cf.enhancer.utils.HelpResponseUtils;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.param.raise.*;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 存啥取啥
 * 每次保存得写老草稿表
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class DraftBundleStorageService {

    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private MaterialBundleFeignClient materialBundleFeignClient;

    @Autowired
    private MaterialBundleClient materialBundleClient;

    private MaterialUsageTypeEnum DRAFT_TYPE = MaterialUsageTypeEnum.DRAFT;

    public Response<RaiseCaseParam> saveByPage(RaiseCaseParam raiseCaseParam) {
        long userId = raiseCaseParam.getUserId();
        String pageFlag = raiseCaseParam.getPageFlag();
        HashMap<String, String> info = Maps.newHashMap();

        Response<RaiseCaseParam> draftResp = getDraft(userId);
        RaiseCaseParam fullDraft = draftResp.getData();
        RaiseCaseParam draft = mergeDraft(fullDraft, pageFlag, raiseCaseParam);

        // 敏感字段
        encryptDraft(draft);

        // 写入当前页数据
        String currentJson = transJsonStringByPageFlag(pageFlag, draft);
        info.put(pageFlag, currentJson);

        // 写入历史页数据
        Map<String, Boolean> pageFillInDetail = draft.getPageFillInDetail();
        pageFillInDetail
                .keySet()
                .stream()
                .filter(pageFillInDetail::get)
                .forEach(page -> {
                    String json = transJsonStringByPageFlag(page, draft);
                    info.put(page, json);
                });

        MaterialBundleDO bundle = saveDraft(userId, info, fullDraft != null ? fullDraft.getBundleId() : 0);
        if (bundle == null) {
            log.info("draft save bundle fail userId:{}", userId);
            return NewResponseUtil.makeError(CfErrorCode.STORAGE_ERROR);
        }
        log.info("draft save bundle success bundle:{}", bundle);
        return NewResponseUtil.makeSuccess(draft);
    }

    public Response<RaiseCaseParam> getDraft(long userId) {
        Response<MaterialBundleDO> resp = materialBundleFeignClient.getLastByUserIdAndType(userId, DRAFT_TYPE.getValue());
        log.info("draft get bundle resp:{}", JSON.toJSON(resp));
        if (resp.notOk()) {
            log.info("draft get bundle fail");
            return HelpResponseUtils.makeRelayError(resp);
        }
        MaterialBundleDO bundle = resp.getData();
        if (bundle == null) {
            log.info("draft get bundle empty");
            return NewResponseUtil.makeSuccess(null);
        }
        Map<String, String> dataMap = bundle.getDataMap();
        RaiseCaseParam raiseCaseParam = createByJsonMap(dataMap);
        raiseCaseParam.setUserId(userId);
        raiseCaseParam.setBundleId(bundle.getId());
        return NewResponseUtil.makeSuccess(raiseCaseParam);
    }

    public Response<RaiseCaseParam> getDraft(long userId, List<String> pageNames) {
        Response<RaiseCaseParam> draftResp = getDraft(userId);
        if (draftResp.notOk()) {
            return draftResp;
        }
        RaiseCaseParam data = draftResp.getData();
        if (data != null) {
            filterPages(pageNames, data);
        }
        return draftResp;
    }

    private RaiseCaseParam mergeDraft(RaiseCaseParam full, String pageName, RaiseCaseParam single) {
        if (full == null) {
            return single;
        }
        full.setClientIp(single.getClientIp());
        full.setChannel(single.getChannel());
        full.setPageFlag(single.getPageFlag());
        full.setCfVersion(single.getCfVersion());
        if (StringUtils.equals(RaiseCons.PageNameV2.BASIC_INFO, pageName)) {
            full.setBasicInfoParam(single.getBasicInfoParam());
        }
        if (StringUtils.equals(RaiseCons.PageNameV2.TITLE_IMAGE, pageName)) {
            full.setTitleImageParam(single.getTitleImageParam());
        }
        if (StringUtils.equals(RaiseCons.PageNameV2.FIRST_APPROVE, pageName)) {
            full.setMedicalInfoParam(single.getMedicalInfoParam());
        }
        if (StringUtils.equals(RaiseCons.PageNameV2.ASSERT_INFO, pageName)) {
            full.setAssetInfoParam(single.getAssetInfoParam());
        }
        return full;
    }

    private RaiseCaseParam createByJsonMap(Map<String, String> dataMap) {
        RaiseCaseParam v = new RaiseCaseParam();
        v.setBasicInfoParam(JSON.parseObject(dataMap.get(RaiseCons.PageNameV2.BASIC_INFO), RaiseBasicInfoParam.class));
        v.setTitleImageParam(JSON.parseObject(dataMap.get(RaiseCons.PageNameV2.TITLE_IMAGE), RaiseTitleImageParam.class));
        v.setMedicalInfoParam(JSON.parseObject(dataMap.get(RaiseCons.PageNameV2.FIRST_APPROVE), RaiseMedicalInfoParam.class));
        v.setAssetInfoParam(JSON.parseObject(dataMap.get(RaiseCons.PageNameV2.ASSERT_INFO), RaiseAssetInfoParam.class));

        Map<String, Boolean> pageFillInDetail = v.getPageFillInDetail();
        for (String page : RaiseCons.PageNameV2.ALL_PAGE) {
            Object data = getByPageName(page, v);
            pageFillInDetail.put(page, data != null);
        }
        decryptDraft(v);
        return v;
    }

    private String transJsonStringByPageFlag(String pageFlag, RaiseCaseParam raiseCaseParam) {
        return JSON.toJSONString(getByPageName(pageFlag, raiseCaseParam));
    }


    private Object getByPageName(String pageName, RaiseCaseParam raiseCaseParam) {
        if (StringUtils.equals(RaiseCons.PageNameV2.BASIC_INFO, pageName)) {
            return raiseCaseParam.getBasicInfoParam();
        }
        if (StringUtils.equals(RaiseCons.PageNameV2.TITLE_IMAGE, pageName)) {
            return raiseCaseParam.getTitleImageParam();
        }
        if (StringUtils.equals(RaiseCons.PageNameV2.FIRST_APPROVE, pageName)) {
            return raiseCaseParam.getMedicalInfoParam();
        }
        if (StringUtils.equals(RaiseCons.PageNameV2.ASSERT_INFO, pageName)) {
            return raiseCaseParam.getAssetInfoParam();
        }
        return null;
    }

    public RaiseCaseParam filterPages(List<String> pageNames, RaiseCaseParam raiseCaseParam) {
        if (CollectionUtils.isEmpty(pageNames)) {
            pageNames = Lists.newArrayList();
        }
        for (String pageName : RaiseCons.PageNameV2.ALL_PAGE) {
            if (pageNames.contains(pageName)) {
                continue;
            }
            if (StringUtils.equals(RaiseCons.PageNameV2.BASIC_INFO, pageName)) {
                raiseCaseParam.setBasicInfoParam(null);
            }
            if (StringUtils.equals(RaiseCons.PageNameV2.TITLE_IMAGE, pageName)) {
                raiseCaseParam.setTitleImageParam(null);
            }
            if (StringUtils.equals(RaiseCons.PageNameV2.FIRST_APPROVE, pageName)) {
                raiseCaseParam.setMedicalInfoParam(null);
            }
            if (StringUtils.equals(RaiseCons.PageNameV2.ASSERT_INFO, pageName)) {
                raiseCaseParam.setAssetInfoParam(null);
            }
        }
        return raiseCaseParam;
    }

    private MaterialBundleDO saveDraft(long userId, Map<String, String> dataMap, long bundleId) {
        if (bundleId <= 0){
            return materialBundleClient.saveBundle(DRAFT_TYPE, userId, 0, dataMap);
        }
        MaterialBundleDO v = new MaterialBundleDO();
        v.setId(bundleId);
        v.setUsageType(DRAFT_TYPE.getValue());
        v.setUserId(userId);
        v.setCaseId(0);
        v.setDataMap(dataMap);
        return materialBundleClient.saveBundle(v);
    }

    private RaiseCaseParam encryptDraft(RaiseCaseParam raiseCaseParam) {
        RaiseBasicInfoParam basicInfoParam = raiseCaseParam.getBasicInfoParam();
        if (basicInfoParam != null) {
            basicInfoParam.setSelfMobile(aesEncrypt(basicInfoParam.getSelfMobile()));
            basicInfoParam.setRaisePatientIdCard(aesEncrypt(basicInfoParam.getRaisePatientIdCard()));
            basicInfoParam.setRaisePatientBornCard(aesEncrypt(basicInfoParam.getRaisePatientBornCard()));
        }
        RaiseMedicalInfoParam medicalInfoParam = raiseCaseParam.getMedicalInfoParam();
        if (medicalInfoParam != null) {
            medicalInfoParam.setSelfIdCard(aesEncrypt(medicalInfoParam.getSelfIdCard()));
            medicalInfoParam.setPatientIdCard(aesEncrypt(medicalInfoParam.getPatientIdCard()));
            medicalInfoParam.setPatientBornCard(aesEncrypt(medicalInfoParam.getPatientBornCard()));
        }
        return raiseCaseParam;
    }

    private RaiseCaseParam decryptDraft(RaiseCaseParam raiseCaseParam) {
        RaiseBasicInfoParam basicInfoParam = raiseCaseParam.getBasicInfoParam();
        if (basicInfoParam != null) {
            basicInfoParam.setSelfMobile(aesDecrypt(basicInfoParam.getSelfMobile()));
            basicInfoParam.setRaisePatientIdCard(aesDecrypt(basicInfoParam.getRaisePatientIdCard()));
            basicInfoParam.setRaisePatientBornCard(aesDecrypt(basicInfoParam.getRaisePatientBornCard()));
        }
        RaiseMedicalInfoParam medicalInfoParam = raiseCaseParam.getMedicalInfoParam();
        if (medicalInfoParam != null) {
            medicalInfoParam.setSelfIdCard(aesDecrypt(medicalInfoParam.getSelfIdCard()));
            medicalInfoParam.setPatientIdCard(aesDecrypt(medicalInfoParam.getPatientIdCard()));
            medicalInfoParam.setPatientBornCard(aesDecrypt(medicalInfoParam.getPatientBornCard()));
        }
        return raiseCaseParam;
    }

    private String aesDecrypt(String value) {
        return shuidiCipher.decrypt(value);
    }

    private String aesEncrypt(String value) {
        return shuidiCipher.encrypt(value);
    }
}
