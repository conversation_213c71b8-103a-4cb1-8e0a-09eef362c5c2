package com.shuidihuzhu.cf.service.crowdfunding.content.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.dao.crowdfunding.CfCaseContentStyleDao;
import com.shuidihuzhu.cf.model.CfContentStyle;
import com.shuidihuzhu.cf.service.crowdfunding.content.CfContentStyleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @Description:
 * @Author: panghairui
 * @Date: 2023/11/8 11:49 AM
 */
@Slf4j
@Service
public class CfContentStyleServiceImpl implements CfContentStyleService {

    @Resource
    private CfCaseContentStyleDao cfCaseContentStyleDao;

    @Override
    public CfContentStyle getStyleByCaseId(Integer caseId) {

        CfContentStyle cfContentStyle = cfCaseContentStyleDao.getByCaseId(caseId);
        if (Objects.isNull(cfContentStyle)) {
            return null;
        }

        List<String> keyWords = Lists.newArrayList();
        try {
            keyWords = Optional.ofNullable(cfContentStyle.getKeywords())
                    .map(f -> (List<String>) JSONObject.parse(f))
                    .orElse(Lists.newArrayList());
        } catch (Exception e) {
            log.error("CfContentStyleServiceImpl getStyleByCaseId ", e);
        }

        if (CollectionUtils.isEmpty(keyWords)) {
            return null;
        }
        cfContentStyle.setKeyWordList(keyWords);
        return cfContentStyle;
    }

    @Override
    public void addOrUpdateStyle(CfContentStyle cfContentStyle) {

        Integer caseId = cfContentStyle.getCaseId();
        if (Objects.isNull(caseId)) {
            log.info("CfContentStyleServiceImpl addOrUpdateStyle caseId is null");
            return;
        }

        CfContentStyle oldStyle = cfCaseContentStyleDao.getByCaseId(caseId);
        if (Objects.isNull(oldStyle)) {
            cfCaseContentStyleDao.insertCfContentStyle(cfContentStyle);
        } else {
            cfCaseContentStyleDao.updateCfContentStyle(cfContentStyle);
        }

    }


}
