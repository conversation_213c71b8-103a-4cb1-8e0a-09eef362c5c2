package com.shuidihuzhu.cf.service.crowdfunding;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.enums.crowdfunding.AuthorFamilyMemberEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.AuthorOccupationEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.BaseInfoTemplateConst;
import com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplatize;
import com.shuidihuzhu.cf.model.crowdfunding.CfTemplateBaseInfo;
import org.apache.commons.lang3.StringUtils;
import org.apache.coyote.OutputBuffer;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Ahrievil
 */
@Service
public class CfContentPlaceHolderHandleService {
    private static final List<String> MARK = Lists.newArrayList(",", ".", "，", "。", "!", "?", "！", "？");
    private static final List<String> COMMA = Lists.newArrayList(",", "，");

    public List<CfBaseInfoTemplatize> getContents(List<CfBaseInfoTemplatize> contentList, CfTemplateBaseInfo cfTemplateBaseInfo) {
        return contentList.parallelStream().peek(val -> {
            String text = val.getContent();
            int amount = cfTemplateBaseInfo.getAmount();
            String authorName = cfTemplateBaseInfo.getAuthorName();
            String diseaseName = cfTemplateBaseInfo.getDiseaseName();
            //非必要
            Integer age = cfTemplateBaseInfo.getAge();
            String hometown = cfTemplateBaseInfo.getHometown();
            String disasterDay = cfTemplateBaseInfo.getDisasterDay();
            Long cost = cfTemplateBaseInfo.getCost();

            String name = cfTemplateBaseInfo.getName();
            String hospitalName = cfTemplateBaseInfo.getHospitalName();

            text = handleContent(BaseInfoTemplateConst.AMOUNT, text, amount <= 0 ? "" : String.valueOf(amount));
            text = handleContent(BaseInfoTemplateConst.NAME, text, name);
            text = handleContent(BaseInfoTemplateConst.AUTHOR, text, authorName);
            text = handleContent(BaseInfoTemplateConst.DISEASE, text, diseaseName);
            text = handleContent(BaseInfoTemplateConst.HOSPITAL, text, hospitalName);
            text = handleContent(BaseInfoTemplateConst.AGE, text, age != null ? String.valueOf(age) : null);
            text = handleContent(BaseInfoTemplateConst.HOMETOWN, text, hometown);
            text = handleContent(BaseInfoTemplateConst.DISASTER_DAY, text, disasterDay);
            text = handleContent(BaseInfoTemplateConst.COST, text, cost != null ? String.valueOf(cost) : null);

            text = handleContent(BaseInfoTemplateConst.SYMPTOMS_BEFORE_DIAGNOSIS, text, StringUtils.trimToEmpty(cfTemplateBaseInfo.getSymptomsBeforeDiagnosis()));
            text = handleContent(BaseInfoTemplateConst.FIRST_DISEASE, text, StringUtils.trimToEmpty(cfTemplateBaseInfo.getFirstDisease()));
            text = handleContent(BaseInfoTemplateConst.FIRST_HOSPITAL, text, StringUtils.trimToEmpty(cfTemplateBaseInfo.getFirstHospital()));
            text = handleContent(BaseInfoTemplateConst.FOLLOW_COST, text, cfTemplateBaseInfo.getFollowCost() <= 0 ? ""
                    : String.valueOf(cfTemplateBaseInfo.getFollowCost()));
            text = handleContent(BaseInfoTemplateConst.HOME_DEBT, text, cfTemplateBaseInfo.getHomeDebt() <= 0 ? ""
                    : String.valueOf(cfTemplateBaseInfo.getHomeDebt()));
            text = handleContent(BaseInfoTemplateConst.NOW_TREATMENT, text, StringUtils.trimToEmpty(cfTemplateBaseInfo.getNowTreatment()));
            text = handleContent(BaseInfoTemplateConst.LATER_TREATMENT, text, StringUtils.trimToEmpty(cfTemplateBaseInfo.getLaterTreatment()));

            val.setContent(text);
        }).collect(Collectors.toList());
    }

    public List<CfBaseInfoTemplatize> getContents4Section(List<CfBaseInfoTemplatize> contentList, CfTemplateBaseInfo cfTemplateBaseInfo){
        return contentList.parallelStream().peek(val -> {

            Map<String, String> familyMemberMap = cfTemplateBaseInfo.getFamilyMemberMap();
            Map<String, String> authorOccupationMap = cfTemplateBaseInfo.getAuthorOccupationMap();
            String medicalArrears = cfTemplateBaseInfo.getMedicalArrears();

            String text = val.getContent();
            List<String> contents = Lists.newArrayList(text.split("\\$\\$\\$"));

            //处理家庭成员
            contents = handleContent4Section(BaseInfoTemplateConst.FATHER, contents, familyMemberMap.get(AuthorFamilyMemberEnum.FATHER.getEnglish()));
            contents = handleContent4Section(BaseInfoTemplateConst.MOTHER, contents, familyMemberMap.get(AuthorFamilyMemberEnum.MOTHER.getEnglish()));
            contents = handleContent4Section(BaseInfoTemplateConst.HUSBAND, contents, familyMemberMap.get(AuthorFamilyMemberEnum.HUSBAND.getEnglish()));
            contents = handleContent4Section(BaseInfoTemplateConst.WIFE, contents, familyMemberMap.get(AuthorFamilyMemberEnum.WIFE.getEnglish()));
            contents = handleContent4Section(BaseInfoTemplateConst.SON, contents, familyMemberMap.get(AuthorFamilyMemberEnum.SON.getEnglish()));
            contents = handleContent4Section(BaseInfoTemplateConst.DAUGHTER, contents, familyMemberMap.get(AuthorFamilyMemberEnum.DAUGHTER.getEnglish()));
            //处理患者职业
            contents = handleContent4Section(BaseInfoTemplateConst.FARMER, contents, authorOccupationMap.get(AuthorOccupationEnum.FARMER.getEnglish()));
            contents = handleContent4Section(BaseInfoTemplateConst.WORKER, contents, authorOccupationMap.get(AuthorOccupationEnum.WORKER.getEnglish()));
            contents = handleContent4Section(BaseInfoTemplateConst.TEACHER, contents, authorOccupationMap.get(AuthorOccupationEnum.TEACHER.getEnglish()));
            contents = handleContent4Section(BaseInfoTemplateConst.DOCTOR, contents, authorOccupationMap.get(AuthorOccupationEnum.DOCTOR.getEnglish()));
            contents = handleContent4Section(BaseInfoTemplateConst.STUDENT, contents, authorOccupationMap.get(AuthorOccupationEnum.STUDENT.getEnglish()));
            contents = handleContent4Section(BaseInfoTemplateConst.UNSTABLE, contents, authorOccupationMap.get(AuthorOccupationEnum.UNSTABLE.getEnglish()));
            //处理医疗欠款
            contents = handleContent4Section(BaseInfoTemplateConst.ARREARS, contents, medicalArrears);

            val.setContent(StringUtils.join(contents, ""));
        }).collect(Collectors.toList());
    }

    public List<String> handleContent4Section(String regex, List<String> contents, String replace){
        int index = -1;
        for (int i = 0 ; i < contents.size(); i++){
            if(contents.get(i).contains(regex)){
                index = i;
                break;
            }
        }
        if(index >= 0 && StringUtils.isNotEmpty(replace)){
            String text = contents.get(index).replace(regex, replace);
            contents.set(index, text);
        }
        if(index >= 0 && StringUtils.isEmpty(replace)){
            contents.remove(index);
        }
        return contents;
    }

    public String handleContent(String regex, String content, String replace) {
        if (StringUtils.isBlank(content)) {
            return "";
        }
        if (StringUtils.isNotBlank(replace)) {
            replace = Matcher.quoteReplacement(replace);
            return content.replaceAll(regex, replace);
        } else {
            replace = "";
        }
        List<String> handleList = Splitter.on(regex).splitToList(content);
        handleList = Lists.newArrayList(handleList);
        String result;
        if (handleList.size() == 1) {
            return content;
        } else {
            for (int i = 0; i < handleList.size(); i ++) {
                if(i == 0) {
                    handleList.set(0, removeTail(handleList.get(0)));
                } else if (i != handleList.size() - 1) {
                    handleList.set(i, removeHead(removeTail(handleList.get(i))));
                } else if (i == handleList.size() - 1){
                    handleList.set(i, removeHead(handleList.get(i)));
                }
            }
            result = removeComma(handleList.stream().collect(Collectors.joining(replace)));
        }
        return result;
    }

    private String removeTail(String content) {
        int length = content.length();
        Optional<Integer> max = MARK.parallelStream().map(content::lastIndexOf)
                .max(Comparator.comparing(Function.identity()));
        if (max.isPresent()) {
            if (max.get() == -1) {
                return "";
            } else {
                length = max.get() + 1;
            }
        }
        return content.substring(0, length);
    }

    private String removeHead(String content) {
        int length = 0;
        Optional<Integer> min = MARK.parallelStream().map(content::indexOf).filter(val -> val != -1)
                .min(Comparator.comparing(Function.identity()));
        if (min.isPresent()) {
            if (min.get() == -1) {
                return "";
            } else {
                length = min.get() + 1;
            }
        } else {
            return "";
        }
        return content.substring(length, content.length());
    }

    private String removeComma(String content) {
        Optional<Integer> max = COMMA.parallelStream().map(content::lastIndexOf).filter(val -> val != -1)
                .max(Comparator.comparing(Function.identity()));
        if (max.isPresent()) {
            if (content.length() == max.get() + 1) {
//                return content.substring(0, max.get() - 1);
                return content.substring(0, max.get());
            }
        }
        return content;
    }
}
