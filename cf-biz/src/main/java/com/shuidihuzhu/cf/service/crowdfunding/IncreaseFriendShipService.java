package com.shuidihuzhu.cf.service.crowdfunding;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.FriendsBizTypeEnum;
import com.shuidihuzhu.cf.model.UserFriendTagModel;
import com.shuidihuzhu.cf.service.ai.impl.FaceApiServiceImpl;
import com.shuidihuzhu.cf.service.crowdfunding.bigdata.DSFriendsService;
import com.shuidihuzhu.cf.vo.AdvertisementFriendsVO;
import com.shuidihuzhu.cf.vo.UserFriendVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: wuyubin
 * @date: 2019-11-13 20:53
 */
@Service
@Slf4j
@RefreshScope
public class IncreaseFriendShipService {

    @Resource
    private UserInfoDelegate userInfoDelegate;

    @Resource
    private DSFriendsService friendsService;

    @Resource
    private FaceApiServiceImpl faceApiService;

    @Value("#{'${apollo.increase.friend.ship.blacklist:}'.split(',')}")
    private List<Long> blacklistUserIds;

    private static final List<String> tags = Lists.newArrayList("cf_is_show_cf_position", "cf_is_click_cf_position", "basic_is_name_age_sex");

    public List<UserFriendVo> getIncreaseFriendShip(long userId, int pSize) {
        //判断捐款人有没有进入黑名单
        if(blacklistUserIds.contains(userId)){
            log.info("捐款人进入黑名单 userId:{}",userId);
            return null;
        }
        if (pSize <= 0) {
            return null;
        }
        //获取捐款人一度好友
        Set<Long> friends = friendsService.getFriendsUserIdV2(userId, 100, FriendsBizTypeEnum.FRIEND_ALL);
        if (CollectionUtils.isEmpty(friends)) {
            return null;
        }
        List<Long> finalFriends = filterValidFriend(friends, userId);
        if (CollectionUtils.isEmpty(finalFriends)) {
            return null;
        }
        //获取一度好友列表
        List<UserInfoModel> userInfoModels = userInfoDelegate.getUserInfoByUserIdBatch(finalFriends);

        List<UserInfoModel> userInfoModelList = userInfoModels.stream()
                .filter(userInfoModel -> StringUtils.isNoneEmpty(userInfoModel.getNickname(), userInfoModel.getHeadImgUrl()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userInfoModelList)) {
            return null;
        }
        int minSize = Math.min(userInfoModelList.size(), pSize);
        return getUserFriendVo(userInfoModelList.subList(0, minSize));
    }

    public AdvertisementFriendsVO getComplianceFriendShowV2(long userId, int pSize, String searchTags) {

        if (pSize <= 0) {
            return null;
        }
        List<String> tagList = StringUtils.isEmpty(searchTags)? tags : Lists.newArrayList(searchTags.split(","));

        // 判断捐款人有没有进入黑名单
        if (blacklistUserIds.contains(userId)) {
            log.info("getComplianceFriendShow 捐款人进入黑名单 userId:{}", userId);
            return null;
        }

        AdvertisementFriendsVO result = new AdvertisementFriendsVO(new ArrayList<>(),0, 0, 0);
        // 获取捐款人一度好友
        Set<Long> friends = friendsService.getFriendsUserIdV2(userId, 100, FriendsBizTypeEnum.FRIEND_ALL);
        if (CollectionUtils.isEmpty(friends)) {
            return null;
        }
        result.setFriendNumFromDB(friends.size());

        // 好友过滤自己和黑名单
        final List<Long> finalFriends = filterValidFriend(friends, userId);
        if (CollectionUtils.isEmpty(finalFriends)) {
            return result;
        }

        // 获取一度好友列表
        List<UserInfoModel> userInfoModels = userInfoDelegate.getUserInfoByUserIdBatch(finalFriends);
        List<UserInfoModel> userInfoModelList = userInfoModels.stream()
                .filter(userInfoModel -> StringUtils.isNoneEmpty(userInfoModel.getNickname(), userInfoModel.getHeadImgUrl()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userInfoModelList)) {
            return result;
        }

        List<Long> userIds = userInfoModelList.stream()
                .map(UserInfoModel::getUserId)
                .collect(Collectors.toList());
        result.setFriendNumAfterFilter(userIds.size());

        // 获取一度好友标签值
        List<Map<String, String>> userTagMapList = faceApiService.batchQueryUserProps(userIds, tagList);
        if (CollectionUtils.isEmpty(userTagMapList)) {
            return result;
        }

        // 填充好友标签
        List<UserFriendTagModel> userFriendTagModels = getUserFriendTags(userTagMapList);
        if (CollectionUtils.isEmpty(userFriendTagModels)) {
            return result;
        }

        // 填充头像和昵称
        List<UserFriendVo> userFriendVos = getUserFriendVoList(userInfoModelList, userFriendTagModels);
        if (CollectionUtils.isEmpty(userFriendVos)) {
            return result;
        }

        // 按照标签优先级排序
        friendTagSort(userFriendVos);

        int minSize = Math.min(userFriendVos.size(), pSize);
        List<UserFriendVo> userFriendVoList = userFriendVos.subList(0, minSize);
        result.setFriendNum2Front(userFriendVoList.size());
        result.setFriendVoList(userFriendVoList);
        return result;

    }


    private static void friendTagSort(List<UserFriendVo> userFriendVos) {
        userFriendVos.sort(new Comparator<UserFriendVo>() {
            @Override
            public int compare(UserFriendVo o1, UserFriendVo o2) {
                // 按照sdbIsHaveInsured字段进行排序
                if (o1.getSdbIsHaveInsured() == 1 && o2.getSdbIsHaveInsured() == 0) {
                    return -1;
                } else if (o1.getSdbIsHaveInsured() == 0 && o2.getSdbIsHaveInsured() == 1) {
                    return 1;
                }
                // 按照basicIsNameAgeSex字段进行排序
                if (o1.getBasicIsNameAgeSex() == 1 && o2.getBasicIsNameAgeSex() == 0) {
                    return -1;
                } else if (o1.getBasicIsNameAgeSex() == 0 && o2.getBasicIsNameAgeSex() == 1) {
                    return 1;
                }

                // 按照clickCfPosition字段进行排序
                if (o1.getClickCfPosition() == 1 && o2.getClickCfPosition() == 0) {
                    return -1;
                } else if (o1.getClickCfPosition() == 0 && o2.getClickCfPosition() == 1) {
                    return 1;
                }

                // 按照showCfPosition字段进行排序
                if (o1.getShowCfPosition() == 1 && o2.getShowCfPosition() == 0) {
                    return -1;
                } else if (o1.getShowCfPosition() == 0 && o2.getShowCfPosition() == 1) {
                    return 1;
                }

                // 如果以上条件都不满足，则按照默认顺序排序
                return 0;
            }
        });
    }

    private List<UserFriendTagModel> getUserFriendTags(List<Map<String, String>> userTagMapList) {
        List<UserFriendTagModel> userFriendTagModels = Lists.newArrayList();
        for (Map<String, String> userTagMap : userTagMapList) {
            if (Objects.isNull(userTagMap)) {
                continue;
            }
            UserFriendTagModel userFriendTagModel = new UserFriendTagModel();

            String userIdStr = userTagMap.getOrDefault("userId", "0");
            userFriendTagModel.setUserId(NumberUtils.isDigits(userIdStr) ? Long.parseLong(userIdStr) : 0);
            String showCfPosition = userTagMap.getOrDefault("cf_is_show_cf_position", "0");
            userFriendTagModel.setShowCfPosition(StringUtils.equals(showCfPosition, "1") ? 1 : 0);
            String clickCfPosition = userTagMap.getOrDefault("cf_is_click_cf_position", "0");
            userFriendTagModel.setClickCfPosition(StringUtils.equals(clickCfPosition, "1") ? 1 : 0);
            String basicIsNameAgeSex = userTagMap.getOrDefault("basic_is_name_age_sex", "0");
            userFriendTagModel.setBasicIsNameAgeSex(StringUtils.equals(basicIsNameAgeSex, "1") ? 1 : 0);
            String sdbIsHaveInsured = userTagMap.getOrDefault("sdb_is_have_insured", "0");
            userFriendTagModel.setSdbIsHaveInsured(StringUtils.equals(sdbIsHaveInsured, "1") ? 1 : 0);

            userFriendTagModels.add(userFriendTagModel);
        }
        return userFriendTagModels;
    }

    /**
     * 填充VO对象
     */
    private List<UserFriendVo> getUserFriendVoList(List<UserInfoModel> userInfoModelList, List<UserFriendTagModel> userFriendTagModels) {

        Map<Long, UserFriendTagModel> userFriendTagModelMap = userFriendTagModels.stream().collect(Collectors.toMap(UserFriendTagModel::getUserId, Function.identity(), (o1, o2) -> o2));

        List<UserFriendVo> userFriendVoList = Lists.newArrayList();
        userInfoModelList.forEach(friend -> {
            UserFriendVo userFriendVo = new UserFriendVo();
            userFriendVo.setNickName(friend.getNickname());
            userFriendVo.setHeadImgUrl(friend.getHeadImgUrl());

            UserFriendTagModel userFriendTagModel = userFriendTagModelMap.getOrDefault(friend.getUserId(), UserFriendTagModel.builder().build());
            userFriendVo.setShowCfPosition(userFriendTagModel.getShowCfPosition());
            userFriendVo.setClickCfPosition(userFriendTagModel.getClickCfPosition());
            userFriendVo.setBasicIsNameAgeSex(userFriendTagModel.getBasicIsNameAgeSex());
            userFriendVo.setSdbIsHaveInsured(userFriendTagModel.getSdbIsHaveInsured());
            userFriendVoList.add(userFriendVo);
        });
        return userFriendVoList;

    }

    /**
     * 过滤在黑名单的一度好友，并排除自己
     */
    private List<Long> filterValidFriend(Set<Long> friends, long userId) {

        //判断捐款人的一度好友有没有进入黑名单
        Set<Long> blacklistUserIdList = Sets.newHashSet();
        for (Long friend : friends) {
            if (Objects.isNull(friend)) {
                continue;
            }
            if (blacklistUserIds.contains(friend)) {
                blacklistUserIdList.add(friend);
            }
        }

        friends.removeAll(blacklistUserIdList);
        if (CollectionUtils.isEmpty(friends)) {
            return null;
        }

        //过滤自己
        List<Long> finalFriends = friends.stream().filter(v -> !v.equals(userId)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(finalFriends)) {
            return null;
        }

        return finalFriends;
    }

    public List<UserInfoModel> getTaskIncreaseFriendShip(long userId) {
        //获取捐款人一度好友
        Set<Long> friends = friendsService.getFriendsUserIdV2(userId, 100, FriendsBizTypeEnum.FRIEND_ALL);
        if (CollectionUtils.isEmpty(friends)) {
            return Lists.newArrayList();
        }
        //过滤自己
        List<Long> fianlFriends = friends.stream().filter(v -> !v.equals(userId)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(fianlFriends)) {
            return Lists.newArrayList();
        }
        //获取一度好友列表
        List<UserInfoModel> userInfoModels = userInfoDelegate.getUserInfoByUserIdBatch(fianlFriends);

        log.info("筹款人的一度好友列表 userId:{} userInfoModels:{}",userId,userInfoModels);

        List<UserInfoModel> userInfoModelList = userInfoModels.stream().filter(userInfoModel ->
                StringUtils.isNotEmpty(userInfoModel.getNickname())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userInfoModelList)) {
            return Lists.newArrayList();
        }

        return userInfoModelList;
    }

    /**
     * 获取昵称跟头像
     * @param userInfoModelList
     * @return
     */
    private List<UserFriendVo> getUserFriendVo(List<UserInfoModel> userInfoModelList) {

        List<UserFriendVo> userFriendVoList = Lists.newArrayList();
        userInfoModelList.forEach(friend -> {
            UserFriendVo userFriendVo = new UserFriendVo();
            userFriendVo.setNickName(friend.getNickname());
            userFriendVo.setHeadImgUrl(friend.getHeadImgUrl());
            userFriendVoList.add(userFriendVo);
        });
        return userFriendVoList;
    }
}
