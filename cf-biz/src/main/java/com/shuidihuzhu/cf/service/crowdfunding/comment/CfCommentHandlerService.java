package com.shuidihuzhu.cf.service.crowdfunding.comment;

import com.google.common.base.Splitter;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingProgress;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingCommentView;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoView;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingUserView;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdFundingProgressVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdFundingVerificationVo;
import com.shuidihuzhu.cf.util.crowdfunding.IdEncryptUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 对评论、捐款、证实、筹款数据做处理
 * <p>
 */
@Service
@Slf4j
@RefreshScope
public class CfCommentHandlerService {
    @Value("${comment.handler.open-replace:false}")
    private boolean openReplace;
    @Value("${comment.handler.replace-string:国.?民政部@&水滴.?慈善@&民政.?指定@&民政.?认可@&民政.?推荐}")
    private String replaceString;


    /**
     * 筹款进展
     *
     * @param crowdFundingProgress
     */
    public void replaceProgress(CrowdFundingProgress crowdFundingProgress) {
        if (null == crowdFundingProgress) {
            return;
        }
        if (!openReplace || StringUtils.isEmpty(replaceString)) {
            return;
        }
        List<String> replaceStringList = Splitter.on("@&").splitToList(replaceString);
        crowdFundingProgress.setContent(getReplaceString(crowdFundingProgress.getContent(), replaceStringList));
    }

    /**
     * 筹款进展
     *
     * @param crowdFundingProgressVoList
     */
    public void replaceProgressVo(List<CrowdFundingProgressVo> crowdFundingProgressVoList) {
        if (CollectionUtils.isEmpty(crowdFundingProgressVoList)) {
            return;
        }

        if (!openReplace || StringUtils.isEmpty(replaceString)) {
            return;
        }
        List<String> replaceStringList = Splitter.on("@&").splitToList(replaceString);
        for (CrowdFundingProgressVo crowdFundingProgressVo : crowdFundingProgressVoList) {
            if (null == crowdFundingProgressVo) {
                continue;
            }
            if (StringUtils.isNotEmpty(crowdFundingProgressVo.getContent())) {
                crowdFundingProgressVo.setContent(getReplaceString(crowdFundingProgressVo.getContent(), replaceStringList));
            }
            if (CollectionUtils.isEmpty(crowdFundingProgressVo.getComments())) {
                continue;
            }
            replaceCommentView(crowdFundingProgressVo.getComments());
        }
        log.debug("");
    }

    /**
     * 评论
     *
     * @param crowdfundingCommentViewList
     */
    public void replaceCommentView(List<CrowdfundingCommentView> crowdfundingCommentViewList) {
        if (CollectionUtils.isEmpty(crowdfundingCommentViewList)) {
            return;
        }

        if (!openReplace || StringUtils.isEmpty(replaceString)) {
            return;
        }
        List<String> replaceStringList = Splitter.on("@&").splitToList(replaceString);
        for (CrowdfundingCommentView crowdfundingCommentView : crowdfundingCommentViewList) {
            if (null == crowdfundingCommentView) {
                continue;
            }
            if (StringUtils.isNotEmpty(crowdfundingCommentView.getContent())) {
                crowdfundingCommentView.setContent(getReplaceString(crowdfundingCommentView.getContent(), replaceStringList));
            }
        }
    }

    /**
     * 评论
     *
     * @param crowdfundingCommentView
     */
    public void replaceCommentView(CrowdfundingCommentView crowdfundingCommentView) {
        if (null == crowdfundingCommentView) {
            return;
        }

        if (!openReplace || StringUtils.isEmpty(replaceString)) {
            return;
        }
        List<String> replaceStringList = Splitter.on("@&").splitToList(replaceString);
        if (StringUtils.isNotEmpty(crowdfundingCommentView.getContent())) {
            crowdfundingCommentView.setContent(getReplaceString(crowdfundingCommentView.getContent(), replaceStringList));
        }
    }

    private String getReplaceString(String content, List<String> replaceStringList) {
        if (StringUtils.isEmpty(content)) {
            return content;
        }
        try {
            for (String string : replaceStringList) {
                content = content.replaceAll(string, "**");
            }
            return content;
        } catch (Exception e) {
            log.error("", e);
        }
        return content;
    }


    /**
     * @param crowdfundingUserViews
     */
    public void replaceUserView(List<CrowdfundingUserView> crowdfundingUserViews) {
        if (CollectionUtils.isEmpty(crowdfundingUserViews)) {
            return;
        }

        if (!openReplace || StringUtils.isEmpty(replaceString)) {
            return;
        }
        List<String> replaceStringList = Splitter.on("@&").splitToList(replaceString);
        for (CrowdfundingUserView crowdfundingUserView : crowdfundingUserViews) {
            if (null == crowdfundingUserView) {
                continue;
            }
            if (StringUtils.isNotEmpty(crowdfundingUserView.getComment())) {
                crowdfundingUserView.setComment(getReplaceString(crowdfundingUserView.getComment(), replaceStringList));
            }
            if (CollectionUtils.isEmpty(crowdfundingUserView.getComments())) {
                continue;
            }
            replaceCommentView(crowdfundingUserView.getComments());
        }
    }

    /**
     * @param verificationVoList
     */
    public void replaceVerificationVo(List<CrowdFundingVerificationVo> verificationVoList) {
        if (CollectionUtils.isEmpty(verificationVoList)) {
            return;
        }

        if (!openReplace || StringUtils.isEmpty(replaceString)) {
            return;
        }
        List<String> replaceStringList = Splitter.on("@&").splitToList(replaceString);
        for (CrowdFundingVerificationVo fundingVerificationVo : verificationVoList) {
            if (null == fundingVerificationVo) {
                continue;
            }
            if (StringUtils.isNotEmpty(fundingVerificationVo.getDescription())) {
                fundingVerificationVo.setDescription(getReplaceString(fundingVerificationVo.getDescription(), replaceStringList));
            }
            fundingVerificationVo.setInfoId(IdEncryptUtil.encrypt(fundingVerificationVo.getId()));
        }
    }

    /**
     * 案例info的title与内容的处理
     *
     * @param crowdfundingInfoList
     */
    public void replaceCrowdFunding(List<CrowdfundingInfo> crowdfundingInfoList) {
        if (CollectionUtils.isEmpty(crowdfundingInfoList)) {
            return;
        }

        for (CrowdfundingInfo crowdfundingInfo : crowdfundingInfoList) {
            if (null == crowdfundingInfo) {
                continue;
            }
            replaceCrowdFunding(crowdfundingInfo);
        }
    }
    /**
     * 案例info的title与内容的处理
     *
     * @param crowdfundingInfoViews
     */
    public void replaceCrowdFundingView( List<CrowdfundingInfoView> crowdfundingInfoViews) {
        if (CollectionUtils.isEmpty(crowdfundingInfoViews)) {
            return;
        }

        for (CrowdfundingInfoView crowdfundingInfoView : crowdfundingInfoViews) {
            if (null == crowdfundingInfoView) {
                continue;
            }
            if (!openReplace || StringUtils.isEmpty(replaceString)) {
                return;
            }
            List<String> replaceStringList = Splitter.on("@&").splitToList(replaceString);
            if (StringUtils.isNotEmpty(crowdfundingInfoView.getTitle())) {
                crowdfundingInfoView.setTitle(getReplaceString(crowdfundingInfoView.getTitle(), replaceStringList));
            }

            if (StringUtils.isNotEmpty(crowdfundingInfoView.getContent())) {
                crowdfundingInfoView.setContent(getReplaceString(crowdfundingInfoView.getContent(), replaceStringList));
            }
        }
    }
    /**
     * 案例info的title与内容的处理
     *
     * @param crowdfundingInfo
     */
    public void replaceCrowdFunding(CrowdfundingInfo crowdfundingInfo) {
        if (null == crowdfundingInfo) {
            return;
        }

        if (!openReplace || StringUtils.isEmpty(replaceString)) {
            return;
        }
        List<String> replaceStringList = Splitter.on("@&").splitToList(replaceString);
        if (StringUtils.isNotEmpty(crowdfundingInfo.getTitle())) {
            crowdfundingInfo.setTitle(getReplaceString(crowdfundingInfo.getTitle(), replaceStringList));
        }

        if (StringUtils.isNotEmpty(crowdfundingInfo.getContent())) {
            crowdfundingInfo.setContent(getReplaceString(crowdfundingInfo.getContent(), replaceStringList));
        }

        if (StringUtils.isNotBlank(crowdfundingInfo.getContentImage())) {
            crowdfundingInfo.setContentImage(getReplaceString(crowdfundingInfo.getContentImage(), replaceStringList));
        }
    }


    public static void main(String[] args) {
        String str = "我是美国民政部";
        CfCommentHandlerService cfCommentHandlerService = new CfCommentHandlerService();
        List<String> replaceStringList = Splitter.on("@&").splitToList("国.?民政@&水滴.?慈善@&民政.?指定@&民政.?认可@&民政.?推荐");
        System.out.println(cfCommentHandlerService.getReplaceString(str, replaceStringList));

        String pattern = ".*国.?民政.*";
        Pattern r = Pattern.compile(pattern);
        Matcher m = r.matcher(str);
        System.out.println(m.matches());
    }


    /**
     * 捐款订单过滤关键词
     *
     * @param crowdfundingUserViewList
     */
    public void replaceCrowdFundingUserViews(List<CrowdfundingUserView> crowdfundingUserViewList) {
        if (CollectionUtils.isEmpty(crowdfundingUserViewList)) {
            return;
        }

        for (CrowdfundingUserView crowdfundingUserView : crowdfundingUserViewList) {
            if (null == crowdfundingUserView) {
                continue;
            }
            replaceCrowdFundingUserView(crowdfundingUserView);
        }
    }

    /**
     * 捐款订单
     *
     * @param crowdfundingUserView
     */
    public void replaceCrowdFundingUserView(CrowdfundingUserView crowdfundingUserView) {
        if (null == crowdfundingUserView) {
            return;
        }

        if (!openReplace || StringUtils.isEmpty(replaceString)) {
            return;
        }
        List<String> replaceStringList = Splitter.on("@&").splitToList(replaceString);
        if (StringUtils.isNotEmpty(crowdfundingUserView.getComment())) {
            crowdfundingUserView.setComment(getReplaceString(crowdfundingUserView.getComment(), replaceStringList));
        }

        if (CollectionUtils.isEmpty(crowdfundingUserView.getComments())) {
            return;
        }
        //订单的评论
        replaceCommentView(crowdfundingUserView.getComments());
    }
}