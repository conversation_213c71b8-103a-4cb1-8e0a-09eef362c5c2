package com.shuidihuzhu.cf.service.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.configuration.EsConfig;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.client.cf.api.model.CrowdFundingVerificationEsSearch;
import com.shuidihuzhu.client.cf.api.model.CrowdFundingVerificationIndexSearchResult;
import com.shuidihuzhu.esdk.EsClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.ibatis.jdbc.SQL;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @time 2019/10/9 下午8:22
 * @desc
 */
@Slf4j
@Service
public class PlatformEsService {

    @Resource(name = EsConfig.ES_BEAN_NAME)
    private EsClient esClient;

    public List<CfFirsApproveMaterial> queryCaseByRaiser(CfFirsApproveMaterial cfFirsApproveMaterial){

        if(Objects.isNull(cfFirsApproveMaterial)
                || (StringUtils.isBlank(cfFirsApproveMaterial.getSelfRealName())
                && StringUtils.isBlank(cfFirsApproveMaterial.getSelfCryptoIdcard())
                && cfFirsApproveMaterial.getUserId() == 0)){
            return Lists.newArrayList();
        }
//
//        String sql = buildMaterialSql(cfFirsApproveMaterial);
//        SearchDto searchDto = new SearchDto();
//        searchDto.setQuerySql(sql);
//        Response response = biApiClient.esQueryCustom(searchDto);
//
//        if(Objects.isNull(response) || 0 != response.getCode()){
//            log.info("PlatformEsService.queryCaseByRaiser es查询失败 sql:{},code:{}", sql, Objects.nonNull(response) ? response.getCode() : -1);
//            return Lists.newArrayList();
//        }
//
//        List<Map<String, Object>> esRes = (List<Map<String, Object>>) response.getData();
//        if(CollectionUtils.isEmpty(esRes)){
//            log.info("PlatformEsService.queryCaseByRaiser es查询结果为空 sql:{},code:{}", sql, Objects.nonNull(response) ? response.getCode() : -1);
//            return Lists.newArrayList();
//        }

        BoolQueryBuilder boolQueryBuilder = buildFirstApproveQuery(cfFirsApproveMaterial);
        List<Map<String, Object>> esRes = getQueryResultFromEs("shuidi_crowdfunding_cf_first_approve_material_alias",
                boolQueryBuilder);
        if (CollectionUtils.isEmpty(esRes)) {
            return Lists.newArrayList();
        }

        return buildMaterial(esRes);
    }

    public List<CfFirsApproveMaterial> queryCaseByAuthor(CfFirsApproveMaterial cfFirsApproveMaterial){
        if(Objects.isNull(cfFirsApproveMaterial)){
            return Lists.newArrayList();
        }

//        String sql = buildQueryCaseByAuthorSql(cfFirsApproveMaterial);
//        SearchDto searchDto = new SearchDto();
//        searchDto.setQuerySql(sql);
//        Response response = biApiClient.esQueryCustom(searchDto);
//
//        if(Objects.isNull(response) || 0 != response.getCode()){
//            log.info("PlatformEsService.queryCaseByAuthor es查询失败 sql:{},code:{}", sql, Objects.nonNull(response) ? response.getCode() : -1);
//            return Lists.newArrayList();
//        }
//
//        List<Map<String, Object>> esRes = (List<Map<String, Object>>) response.getData();
//        if(CollectionUtils.isEmpty(esRes)){
//            log.info("PlatformEsService.queryCaseByAuthor es查询结果为空 sql:{},code:{}", sql, Objects.nonNull(response) ? response.getCode() : -1);
//            return Lists.newArrayList();
//        }

        BoolQueryBuilder boolQueryBuilder = buildCaseByAuthorQuery(cfFirsApproveMaterial);
        List<Map<String, Object>> esRes = getQueryResultFromEs("shuidi_crowdfunding_cf_first_approve_material_alias",
                boolQueryBuilder);
        if (CollectionUtils.isEmpty(esRes)) {
            return Lists.newArrayList();
        }

        return buildMaterial(esRes);
    }

    public List<CrowdfundingInfoPayee> queryCaseByPayee(CrowdfundingInfoPayee payeeInfo){

        if(Objects.isNull(payeeInfo)){
            return Lists.newArrayList();
        }
//
//        String sql = buildQueryCaseByPayeeSql(payeeInfo);
//        SearchDto searchDto = new SearchDto();
//        searchDto.setQuerySql(sql);
//        Response response = biApiClient.esQueryCustom(searchDto);
//
//        if(Objects.isNull(response) || 0 != response.getCode()){
//            log.info("PlatformEsService.queryCaseByPayee es查询失败 sql:{},code:{}", sql, Objects.nonNull(response) ? response.getCode() : -1);
//            return Lists.newArrayList();
//        }

        BoolQueryBuilder boolQueryBuilder = buildCaseByPayeeQuery(payeeInfo);
        List<Map<String, Object>> esRes = getQueryResultFromEs("shuidi_crowdfunding_crowdfunding_info_payee_alias",
                boolQueryBuilder);
        if (CollectionUtils.isEmpty(esRes)) {
            return Lists.newArrayList();
        }

        return builPayeeInfo(esRes);
    }

    public List<UserRealInfo> queryUserRealInfo(UserRealInfo userRealInfo){
        if(Objects.isNull(userRealInfo)){
            return Lists.newArrayList();
        }
        BoolQueryBuilder boolQueryBuilder = buildUserRealInfoQuery(userRealInfo);
        List<Map<String, Object>> dataMap = getQueryResultFromEs("shuidi_crowdfunding_user_real_info_alias", boolQueryBuilder);
//        String sql = buildQueryUserRealInfo(userRealInfo);
//        SearchDto searchDto = new SearchDto();
//        searchDto.setQuerySql(sql);
//        Response response = biApiClient.esQueryCustom(searchDto);
//
//        if(Objects.isNull(response) || 0 != response.getCode()){
//            log.info("PlatformEsService.queryUserRealInfo es查询失败 sql:{},code:{}", sql, Objects.nonNull(response) ? response.getCode() : -1);
//            return Lists.newArrayList();
//        }

//        List<Map<String, Object>> esRes = (List<Map<String, Object>>) response.getData();
        if(CollectionUtils.isEmpty(dataMap)){
            log.info("PlatformEsService.queryUserRealInfo es查询结果为空");
            return Lists.newArrayList();
        }

        return buildUserRealInfo(dataMap);
    }


    public CrowdFundingVerificationIndexSearchResult queryCrowdFundingVerification(CrowdFundingVerificationEsSearch crowdFundingVerificationEsSearch){
        BoolQueryBuilder boolQueryBuilder = buildCrowdFundingVerificationQuery(crowdFundingVerificationEsSearch);
        Optional<Pair<Long, List<Map<String, Object>>>> optional = getQueryResultFromEs("shuidi_crowdfunding_crowd_funding_verification_alias", boolQueryBuilder
                ,crowdFundingVerificationEsSearch.getFrom(),crowdFundingVerificationEsSearch.getSize());
        if (optional.isEmpty()) {
            return null;
        }

        return buildCrowdFundingVerification(optional.get());
    }

    private List<CfFirsApproveMaterial> buildMaterial(List<Map<String, Object>> esRes){
        List<CfFirsApproveMaterial> materials = Lists.newArrayList();

        for (Map<String, Object> map : esRes){

            Integer id= (Integer) map.get("id");
            Long userId= Long.valueOf(map.get("user_id") + "");
            Integer date = (Integer) map.get("date");
            Long ip = Long.valueOf(map.get("ip") + "");
            Integer infoId = (Integer) map.get("info_id");
            String infoUuid = (String) map.get("info_uuid");
            String selfRealName = (String) map.get("self_real_name");
            String selfCryptoIdcard = (String) map.get("self_crypto_idcard");
            String patientRealName = (String) map.get("patient_real_name");
            String patientBornCard = (String) map.get("patient_born_card");
            Integer patientIdType = (Integer) map.get("patient_id_type");
            String patientCryptoIdcard = (String) map.get("patient_crypto_idcard");
            Integer userReltionType = (Integer) map.get("user_relation_type");
            Integer patientHasIdCard = (Integer) map.get("patient_has_idcard");
            String imageUrl = (String) map.get("image_url");
            Integer status = (Integer) map.get("status");
            String targetAmountDesc = (String) map.get("target_amount_desc");
            Integer rejectReasonType = (Integer) map.get("reject_reason_type");
            String rejectMessage = (String) map.get("reject_message");
            Integer poverty = (Integer) map.get("poverty");
            String povertyImageUrl = (String) map.get("poverty_image_url");

            CfFirsApproveMaterial material = new CfFirsApproveMaterial();
            material.setId(Objects.nonNull(id) ? id : 0);
            material.setUserId(Objects.nonNull(userId) ? userId : 0);
            material.setDate(Objects.nonNull(date) ? date : 0);
            material.setIp(Objects.nonNull(ip) ? ip : 0);
            material.setInfoId(Objects.nonNull(infoId) ? infoId : 0);
            material.setInfoUuid(infoUuid);
            material.setSelfRealName(selfRealName);
            material.setSelfCryptoIdcard(selfCryptoIdcard);
            material.setPatientRealName(patientRealName);
            material.setPatientBornCard(patientBornCard);
            material.setPatientIdType(Objects.nonNull(patientIdType) ? patientIdType : 0);
            material.setPatientCryptoIdcard(patientCryptoIdcard);
            material.setUserRelationType(Objects.nonNull(userReltionType) ? userReltionType : 0);
            material.setPatientHasIdCard(Objects.nonNull(patientHasIdCard) && 1 == patientHasIdCard ? true : false);
            material.setImageUrl(imageUrl);
            material.setStatus(Objects.nonNull(status) ? status : 0);
            material.setTargetAmountDesc(targetAmountDesc);
            material.setRejectReasonType(Objects.nonNull(rejectReasonType) ? rejectReasonType : 0);
            material.setRejectMessage(rejectMessage);

            try {
                material.setCreateTime(new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss.SSSZ").parse((String) map.get("create_time")));
                material.setUpdateTime(new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss.SSSZ").parse((String) map.get("update_time")));
            } catch (Exception e){
                log.info("PlatformEsService.queryMaterialByParam parse date error.", e);
            }
            material.setPoverty(Objects.nonNull(poverty) ? poverty : 0);
            material.setPovertyImageUrl(povertyImageUrl);
            materials.add(material);
        }

        return materials;
    }

    private List<CrowdfundingInfoPayee> builPayeeInfo(List<Map<String, Object>> esRes){

        List<CrowdfundingInfoPayee> payees = Lists.newArrayList();

        for (Map<String, Object> map : esRes){
            Long id = Long.valueOf(map.get("id") + "");
            String infoUuid = (String) map.get("info_uuid");
            Integer relationType = (Integer) map.get("relation_type");
            String name = (String) map.get("name");
            String idcard = (String) map.get("id_card");
            Integer idType = (Integer) map.get("id_type");
            String mobile = (String) map.get("mobile");
            String bankName = (String) map.get("bank_name");
            String bankBranchName = (String) map.get("bank_branch_name");
            String bankCard = (String) map.get("bank_card");
            Integer caseId = (Integer) map.get("case_id");

            CrowdfundingInfoPayee payee = new CrowdfundingInfoPayee();
            payee.setId(id);
            payee.setInfoUuid(infoUuid);
            payee.setRelationType(relationType);
            payee.setName(name);
            payee.setIdCard(idcard);
            payee.setIdType(idType);
            payee.setMobile(mobile);
            payee.setBankName(bankName);
            payee.setBankBranchName(bankBranchName);
            payee.setBankCard(bankCard);
            try {
                payee.setDateCreated(new Timestamp(new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss.SSSZ").parse((String) map.get("date_created")).getTime()));
                payee.setLastModified(new Timestamp(new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss.SSSZ").parse((String) map.get("last_modified")).getTime()));
            } catch (Exception e){
                log.info("PlatformEsService.builPayeeInfo parse date error.", e);
            }
            payee.setCaseId(caseId);

            payees.add(payee);

        }

        return payees;
    }

    private List<CrowdfundingComment> buildCommentInfo(List<Map<String, Object>> esRes){

        List<CrowdfundingComment> comments = Lists.newArrayList();

        for (Map<String, Object> map : esRes){

            Long id = (Long) map.get("id");
            Integer crowdfundingId = (Integer) map.get("crowdfunding_id");
            Long parentId = (Long) map.get("parent_id");
            Long userId = Long.valueOf(map.get("user_id") + "");
            Integer userThirdId = (Integer) map.get("user_third_id");
            Integer userThirdType = (Integer) map.get("user_third_type");
            Long commentId = (Long) map.get("comment_id");
            String content = (String) map.get("content");
            Integer isDeleted = (Integer) map.get("is_deleted");
            Integer type = (Integer) map.get("type");

            CrowdfundingComment comment = new CrowdfundingComment();
            comment.setId(id);
            comment.setCrowdfundingId(crowdfundingId);
            comment.setParentId(parentId);
            comment.setUserId(userId);
            comment.setUserThirdId(userThirdId);
            comment.setUserThirdType(userThirdType);
            comment.setCommentId(commentId);
            comment.setContent(content);
            comment.setIsDeleted(1 == isDeleted ? true : false);
            comment.setType(type);

            try {
                comment.setCreateTime(new Timestamp(new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss.SSSZ").parse((String) map.get("create_time")).getTime()));
            } catch (Exception e){
                log.info("PlatformEsService.buildCommentInfo parse date error.", e);
            }

            comments.add(comment);
        }

        return comments;
    }

    private CrowdFundingVerificationIndexSearchResult buildCrowdFundingVerification(Pair<Long, List<Map<String, Object>>> pair) {

        List<CrowdFundingVerification> crowdFundingVerificationList = Lists.newArrayList();

        List<Map<String, Object>> esRes = pair.getRight();
        for (Map<String, Object> map : esRes) {

            Integer id = (Integer) map.get("id");
            Long patientUserId = Long.valueOf(map.get("patient_user_id").toString());
            Long verifyUserId = Long.valueOf(map.get("verify_user_id").toString());
            Integer relationShip = (Integer) map.get("relation_ship");

            String userName = (String) map.get("user_name");
            String infoUuid = (String) map.get("crowd_funding_info_id");
            String description = (String) map.get("description");
            Integer valid = (Integer) map.get("valid");

            CrowdFundingVerification crowdFundingVerification = new CrowdFundingVerification();
            crowdFundingVerification.setId(id);
            crowdFundingVerification.setCrowdFundingInfoId(infoUuid);
            crowdFundingVerification.setDescription(description);
            crowdFundingVerification.setUserName(userName);
            crowdFundingVerification.setValid(valid);
            crowdFundingVerification.setRelationShip(relationShip);
            crowdFundingVerification.setVerifyUserId(Objects.nonNull(verifyUserId) ? verifyUserId : 0L);
            crowdFundingVerification.setPatientUserId(Objects.nonNull(patientUserId) ? patientUserId : 0L);

            try {
                Timestamp createTime = new Timestamp(new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss.SSSZ").parse((String) map.get("create_time")).getTime());
                crowdFundingVerification.setCreateTime(createTime);
            } catch (Exception e) {
                log.info("PlatformEsService.buildCrowdFundingVerification parse date error.", e);
            }

            crowdFundingVerificationList.add(crowdFundingVerification);
        }

        CrowdFundingVerificationIndexSearchResult crowdFundingVerificationIndexSearchResult = new CrowdFundingVerificationIndexSearchResult();
        crowdFundingVerificationIndexSearchResult.setTotal(pair.getLeft());
        crowdFundingVerificationIndexSearchResult.setModels(crowdFundingVerificationList);
        return crowdFundingVerificationIndexSearchResult;
    }

    private List<UserRealInfo> buildUserRealInfo(List<Map<String, Object>> esRes){

        List<UserRealInfo> userRealInfos = Lists.newArrayList();

        for (Map<String, Object> map : esRes){

            Integer id = (Integer) map.get("id");
            Long userId = Long.valueOf(map.get("user_id") + "");
            String orderId = (String) map.get("order_id");
            String name = (String) map.get("name");
            String cryptoIdCard = (String) map.get("crypto_id_card");
            Integer idcardVerifyStatus = (Integer) map.get("idcard_verify_status");
            String resultCode = (String) map.get("result_code");
            String resultMsg = (String) map.get("result_msg");

            UserRealInfo userRealInfo = new UserRealInfo();
            userRealInfo.setId(id);
            userRealInfo.setUserId(userId);
            userRealInfo.setOrderId(orderId);
            userRealInfo.setName(name);
            userRealInfo.setCryptoIdCard(cryptoIdCard);
            userRealInfo.setIdcardVerifyStatus(idcardVerifyStatus);
            userRealInfo.setResultCode(resultCode);
            userRealInfo.setResultMsg(resultMsg);

            try {
                userRealInfo.setDateCreated(new Timestamp(new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss.SSSZ").parse((String) map.get("date_created")).getTime()));
                userRealInfo.setLastModified(new Timestamp(new SimpleDateFormat("yyyy-MM-dd'T'hh:mm:ss.SSSZ").parse((String) map.get("last_modified")).getTime()));
            } catch (Exception e){
                log.info("PlatformEsService.buildUserRealInfo parse date error.", e);
            }

            userRealInfos.add(userRealInfo);
        }

        return userRealInfos;
    }

    private BoolQueryBuilder buildFirstApproveQuery(CfFirsApproveMaterial firstMaterial) {

        BoolQueryBuilder subBoolQuery = QueryBuilders.boolQuery();
        subBoolQuery.must(QueryBuilders.termQuery("is_delete", 0));
        if (firstMaterial.getUserId() > 0) {
            subBoolQuery.must(QueryBuilders.termQuery("user_id", firstMaterial.getUserId()));
        }
        if (StringUtils.isNotBlank(firstMaterial.getSelfRealName())) {
            subBoolQuery.must(QueryBuilders.termQuery("self_real_name",
                    firstMaterial.getSelfRealName()));
        }
        if (StringUtils.isNotBlank(firstMaterial.getSelfCryptoIdcard())) {
            subBoolQuery.must(QueryBuilders.termQuery("self_crypto_idcard",
                    firstMaterial.getSelfCryptoIdcard()));
        }


        BoolQueryBuilder subBoolQuery1 = QueryBuilders.boolQuery();
        subBoolQuery1.must(QueryBuilders.termQuery("is_delete", 0));
        if (firstMaterial.getUserId() > 0) {
            subBoolQuery1.must(QueryBuilders.termQuery("user_id", firstMaterial.getUserId()));
        }

        subBoolQuery1.must(QueryBuilders.termQuery("user_relation_type", 1));
        if (StringUtils.isNotBlank(firstMaterial.getSelfRealName())) {
            subBoolQuery1.must(QueryBuilders.termQuery("patient_real_name", firstMaterial.getSelfRealName()));
        }
        if (StringUtils.isNotBlank(firstMaterial.getSelfCryptoIdcard())) {
            subBoolQuery1.must(QueryBuilders.termQuery("patient_crypto_idcard", firstMaterial.getSelfCryptoIdcard()));
        }

        return QueryBuilders.boolQuery().should(subBoolQuery).should(subBoolQuery1);
    }

    private String buildMaterialSql(CfFirsApproveMaterial cfFirsApproveMaterial){

        long userId = cfFirsApproveMaterial.getUserId();
        String selfRealName = cfFirsApproveMaterial.getSelfRealName();
        String selfCryptoIdcard = cfFirsApproveMaterial.getSelfCryptoIdcard();

        StringBuilder where = new StringBuilder();
        where.append("is_delete = 0 AND ((");
        if(userId > 0){
            where.append("user_id = " + userId);
        }

        if(StringUtils.isNotEmpty(selfRealName)){
            if(where.toString().contains("user_id")){
                where.append(" AND ");
            }
            where.append("self_real_name = ").append("\"").append(selfRealName).append("\"");
        }

        if(StringUtils.isNotEmpty(selfCryptoIdcard)){
            if(where.toString().contains("user_id") || where.toString().contains("self_real_name")){
                where.append(" AND ");
            }

            where.append("self_crypto_idcard = ").append("\"").append(selfCryptoIdcard).append("\"");
        }

        where.append(" ) OR (user_relation_type = 1");

        if(userId > 0){
            where.append(" AND user_id = " + userId);
        }

        if(StringUtils.isNotEmpty(selfRealName)){
            where.append(" AND patient_real_name = ").append("\"").append(selfRealName).append("\"");
        }

        if(StringUtils.isNotEmpty(selfCryptoIdcard)){
            where.append(" AND patient_crypto_idcard = ").append("\"").append(selfCryptoIdcard).append("\"");
        }

        where.append("))");

        String sql = new SQL(){{
            SELECT("*");
            FROM("shuidi_crowdfunding_cf_first_approve_material");
            WHERE(where.toString());
        }}.toString();

        sql = sql + " LIMIT 2000";

        return sql;
    }

    private BoolQueryBuilder buildCaseByAuthorQuery(CfFirsApproveMaterial firstMaterial) {

        String patientName = firstMaterial.getPatientRealName();
        String patientIdcard = firstMaterial.getPatientCryptoIdcard();

        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

        boolQuery.must(QueryBuilders.termQuery("is_delete", 0));
        if (StringUtils.isNotEmpty(patientName)) {
            boolQuery.must(QueryBuilders.termQuery("patient_real_name", patientName));
        }

        if (StringUtils.isNotEmpty(patientIdcard)) {
            boolQuery.must(QueryBuilders.termQuery("patient_crypto_idcard", patientIdcard));
        }

        return boolQuery;
    }

    private String buildQueryCaseByAuthorSql(CfFirsApproveMaterial cfFirsApproveMaterial){
        String patientName = cfFirsApproveMaterial.getPatientRealName();
        String patientIdcard = cfFirsApproveMaterial.getPatientCryptoIdcard();

        StringBuilder where = new StringBuilder();
        where.append("is_delete = 0");
        if(StringUtils.isNotEmpty(patientName)){
            where.append(" AND patient_real_name = ").append("\"").append(patientName).append("\"");
        }

        if(StringUtils.isNotEmpty(patientIdcard)){
            where.append(" AND patient_crypto_idcard = ").append("\"").append(patientIdcard).append("\"");
        }

        String sql = new SQL(){{
            SELECT("*");
            FROM("shuidi_crowdfunding_cf_first_approve_material");
            WHERE(where.toString());
        }}.toString();

        sql = sql + " LIMIT 2000";

        return sql;
    }


    private BoolQueryBuilder buildCaseByPayeeQuery(CrowdfundingInfoPayee payeeInfo) {

        String name = payeeInfo.getName();
        String idCard = payeeInfo.getIdCard();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if (StringUtils.isNotEmpty(name)) {
            boolQueryBuilder.must(QueryBuilders.termQuery("name", name));
        }

        if (StringUtils.isNotEmpty(idCard)) {
            boolQueryBuilder.must(QueryBuilders.termQuery("id_card", idCard));
        }

        return boolQueryBuilder;
    }

    private String buildQueryCaseByPayeeSql(CrowdfundingInfoPayee payeeInfo){
        String name = payeeInfo.getName();
        String idCard = payeeInfo.getIdCard();

        StringBuilder where = new StringBuilder();
        if(StringUtils.isNotEmpty(name)){
            where.append(" name = ").append("\"").append(name).append("\"");
        }

        if(StringUtils.isNotEmpty(idCard)){
            where.append(" AND id_card = ").append("\"").append(idCard).append("\"");
        }

        String sql = new SQL(){{
            SELECT("*");
            FROM("shuidi_crowdfunding_crowdfunding_info_payee");
            WHERE(where.toString());
        }}.toString();

        sql = sql + " LIMIT 2000";

        return sql;
    }

    private BoolQueryBuilder buildUserRealInfoQuery(UserRealInfo userRealInfo){
        long userId = userRealInfo.getUserId();
        String cryptoIdCard = userRealInfo.getCryptoIdCard();
        String name = userRealInfo.getName();
        int verifyStatus = userRealInfo.getIdcardVerifyStatus();

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("idcard_verify_status", verifyStatus));
        boolQueryBuilder.must(QueryBuilders.termQuery("is_delete", 0));

        if (userId > 0){
            boolQueryBuilder.must(QueryBuilders.termQuery("user_id", userId));
        }
        if(StringUtils.isNotEmpty(cryptoIdCard)){
            boolQueryBuilder.must(QueryBuilders.termQuery("crypto_id_card", cryptoIdCard));
        }
        if(StringUtils.isNotEmpty(name)){
            boolQueryBuilder.must(QueryBuilders.termQuery("name", name));
        }
        return boolQueryBuilder;
    }

    private BoolQueryBuilder buildCrowdFundingVerificationQuery(CrowdFundingVerificationEsSearch crowdFundingVerificationEsSearch) {
        long verifyUserId = crowdFundingVerificationEsSearch.getVerifyUserId();
        String infoUuid = crowdFundingVerificationEsSearch.getCrowdFundingInfoId();
        String userName = crowdFundingVerificationEsSearch.getUserName();
        String description = crowdFundingVerificationEsSearch.getDescription();

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        if (verifyUserId > 0) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("verify_user_id", verifyUserId));
        }

        if (StringUtils.isNotEmpty(infoUuid)) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("crowd_funding_info_id", infoUuid));
        }

        if (StringUtils.isNotEmpty(userName)) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("user_name", userName));
        }

        if (StringUtils.isNotEmpty(description)) {
            boolQueryBuilder.must(QueryBuilders.matchPhraseQuery("description", description));
        }
        return boolQueryBuilder;
    }

    public List<Map<String, Object>> getQueryResultFromEs(String indexName, QueryBuilder queryBuilder) {

        try {
            SearchResponse searchResponse = esClient.pullByQueryBuilders(EsConfig.CLUSTER_NAME, indexName,
                    queryBuilder, 0, 2000);
            if (isEsResultEmpty(searchResponse)) {
                log.info("PlatformEsService.{} es查询返回值为空 param:{}", indexName, JSON.toJSONString(searchResponse));
                return Lists.newArrayList();
            }

            return buildEsResultAsList(searchResponse);
        } catch (Exception e) {
            log.info("PlatformEsService.{} es查询异常", indexName, e);
        }

        return Lists.newArrayList();
    }

    private boolean isEsResultEmpty(SearchResponse searchResponse) {
        return searchResponse == null
                || searchResponse.getHits() == null
                || searchResponse.getHits().getHits() == null
                || searchResponse.getHits().getHits().length == 0;
    }

    private List<Map<String, Object>> buildEsResultAsList(SearchResponse searchResponse) {
        return Arrays.stream(searchResponse.getHits().getHits()).map(SearchHit::getSourceAsMap)
                .collect(Collectors.toList());
    }

    public Optional<Pair<Long, List<Map<String, Object>>>> getQueryResultFromEs(String indexName, QueryBuilder queryBuilder,int from,int size) {

        try {
            Map<String, SortOrder> sortOrderMap = Maps.newLinkedHashMap();
            sortOrderMap.put("create_time", SortOrder.DESC);
            SearchResponse searchResponse = esClient.pullByQueryBuilders(EsConfig.CLUSTER_NAME, indexName, queryBuilder, from, size, sortOrderMap);
            if (isEsResultEmpty(searchResponse)) {
                log.info("PlatformEsService.{} es查询返回值为空 param:{}", indexName, JSON.toJSONString(searchResponse));
                return Optional.of(Pair.of(0L, Lists.newArrayList()));
            }

            return buildEsResultAsPair(searchResponse);
        } catch (Exception e) {
            log.info("PlatformEsService.{} es查询异常", indexName, e);
        }

        return Optional.of(Pair.of(0L, Lists.newArrayList()));
    }

    private Optional<Pair<Long, List<Map<String, Object>>>> buildEsResultAsPair(SearchResponse searchResponse) {
        if (searchResponse == null) {
            return Optional.of(Pair.of(0L, Lists.newArrayList()));
        }

        SearchHits hits = null;
        SearchHit[] searchHits = null;
        if ((hits = searchResponse.getHits()) == null || (searchHits = hits.getHits()) == null || searchHits.length == 0) {
            return Optional.of(Pair.of(0L, Lists.newArrayList()));
        }

        List<Map<String, Object>> sources = Arrays.stream(hits.getHits()).map(SearchHit::getSourceAsMap).collect(Collectors.toList());
        return Optional.of(Pair.of(hits.getTotalHits().value, sources));
    }

    public static void main(String[] args){

        long userId = 0;
        String selfRealName = "111";
        String selfCryptoIdcard = "";

        StringBuilder where = new StringBuilder();
        where.append("is_delete = 0 AND ((");
        if(userId > 0){
            where.append("user_id = " + userId);
        }

        if(StringUtils.isNotEmpty(selfRealName)){
            if(where.toString().contains("user_id")){
                where.append(" AND ");
            }
            where.append("self_real_name = ").append("\"").append(selfRealName).append("\"");
        }

        if(StringUtils.isNotEmpty(selfCryptoIdcard)){
            if(where.toString().contains("user_id") || where.toString().contains("self_real_name")){
                where.append(" AND ");
            }

            where.append("self_crypto_idcard = ").append("\"").append(selfCryptoIdcard).append("\"");
        }

        where.append(" ) OR (user_relation_type = 1");

        if(userId > 0){
            where.append(" AND user_id = " + userId);
        }

        if(StringUtils.isNotEmpty(selfRealName)){
            where.append(" AND patient_real_name = ").append("\"").append(selfRealName).append("\"");
        }

        if(StringUtils.isNotEmpty(selfCryptoIdcard)){
            where.append(" AND patient_crypto_idcard = ").append("\"").append(selfCryptoIdcard).append("\"");
        }

        where.append("))");

        System.out.println("************");
        System.out.println(where.toString());
        System.out.println("************");

    }
}
