package com.shuidihuzhu.cf.service.crowdfunding.listener.afteradd;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoBaseVo;
import com.shuidihuzhu.cf.service.crowdfunding.event.CrowdFundingAddEvent;
import com.shuidihuzhu.cf.service.crowdfunding.event.AddListenerOrder;
import com.shuidihuzhu.cf.service.crowdfunding.listener.base.AbstractCrowdFundingAddEventListener;
import com.shuidihuzhu.data.analytics.javasdk.core.CaseProfile;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.event.SmartApplicationListener;
import org.springframework.stereotype.Service;

/**
 * @deprecated 此需求是之前品墨一个需求实验所致，现已不用，20191001之后可以下掉
 */
@Slf4j
@Service
@Deprecated
public class ServiceLogListener extends AbstractCrowdFundingAddEventListener implements SmartApplicationListener  {

    /**
     * cf_allx 案例类型
     * value分别是  znfa 智能大气 或 sdfa 手动发起
     */
    private static final String CF_ALLX = "cf_allx";
    private static final String ZNFA = "znfa";
    private static final String SDFA = "sdfa";
    @Autowired
    private CaseProfile caseProfile;

    /**
     * 案例标签上报
     * @param cfInfoBaseVo
     * @param InfoId
     */
    public void servicelog(CrowdfundingInfoBaseVo cfInfoBaseVo, String  InfoId){
        try {
            //案例标签打点上报
            if (null == cfInfoBaseVo.getCfBaseInfoTemplateRecord()) {
                caseProfile.profileSet(InfoId, CF_ALLX, SDFA);
            } else {
                caseProfile.profileSet(InfoId, CF_ALLX, ZNFA);
            }
        } catch (Exception e) {
            log.error("",e);
        }
    }
    @Override
    public void onApplicationEvent(ApplicationEvent _event) {

        CrowdFundingAddEvent crowdFundingAddEvent = (CrowdFundingAddEvent) _event;
        CrowdfundingInfo cfCase = crowdFundingAddEvent.getCrowdfundingInfo();
        CrowdfundingInfoBaseVo infoBaseVo = crowdFundingAddEvent.getCrowdfundingInfoBaseVo();
        if (cfCase != null&&infoBaseVo!=null) {
            try {
                this.servicelog(infoBaseVo, cfCase.getInfoId());
            } catch (Exception e) {
                log.error("ServiceLogListener error:", e);
            }

        }


    }


    @Override
    public int getOrder() {
        return AddListenerOrder.ServiceLog.getValue();
    }
}
