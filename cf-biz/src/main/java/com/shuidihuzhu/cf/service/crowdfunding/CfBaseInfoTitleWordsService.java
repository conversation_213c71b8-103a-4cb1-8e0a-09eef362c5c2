package com.shuidihuzhu.cf.service.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRedisKvBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingTreatmentBiz;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfDiseaseWordVo;
import com.shuidihuzhu.cf.util.ListUtil;
import com.shuidihuzhu.cf.util.wordfilter.SensitivewordFilter;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.util.http.HttpResponseModel;
import com.shuidihuzhu.common.web.util.http.HttpUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * Created by Ahrievil on 2017/12/7
 */
@RefreshScope
@Service
public class CfBaseInfoTitleWordsService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CfBaseInfoTitleWordsService.class);
    private static final String KEY = "cf-base-info-title-word";
    public final static String URL = "http://cf-images.oss-cn-shanghai.aliyuncs.com/file/cfBaseInfoTitle.txt";

    private SensitivewordFilter sensitivewordFilter;

    @Autowired
    private CrowdfundingTreatmentBiz crowdfundingTreatmentBiz;
    @Autowired
    private CfRedisKvBiz cfRedisKvBiz;

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;

    @Value("${baseinfo.title.template:}")
    private String infoTitleTemplate;

    private Set<String> getDiseaseList() {
        Set<String> diseaseSet = Sets.newHashSet();
        String value = cfRedisKvBiz.queryByKey("CF_BASE_INFO_TITLE_WORD_KEY", true);
        if (StringUtils.isBlank(value)) {
            getFromDB(diseaseSet);
        } else {
            switch (value) {
                case "0":
                    getFromDB(diseaseSet);
                    break;
                case "1":
                    getFromFile(diseaseSet);
                    break;
                default:
                    getFromDB(diseaseSet);
            }
        }

        return diseaseSet;
    }

    public Set<String> get(String keyWord) {
        String set = null;
        try {
            set = redissonHandler.get(KEY, String.class);
        } catch (Exception e) {
            LOGGER.error("CfBaseInfoTitleWordsService get error msg", e);
        }
        if (StringUtils.isBlank(set)) {
            setCache();
            reload();
        }
        if(sensitivewordFilter == null) {
            sensitivewordFilter = SensitivewordFilter.build(this.getDiseaseList());
        }
        return sensitivewordFilter.getSensitiveWord(keyWord, 1);
    }

    private void setCache() {
        try {
            redissonHandler.setEX(KEY, "miao", 60 * 60 * 1000L);
        } catch (Exception e) {
            LOGGER.error("CfBaseInfoTitleWordsService setCache error msg", e);
        }
    }

    private void reload() {
        build();
        sensitivewordFilter.reload(this.getDiseaseList());
    }

    private void build() {
        if(sensitivewordFilter == null) {
            sensitivewordFilter = SensitivewordFilter.build(this.getDiseaseList());
        }
    }

    private void getFromFile(Set<String> diseaseSet) {
        HttpResponseModel httpGet = HttpUtil.httpGet(URL);
        if (httpGet.getStatusCode() == HttpStatus.SC_OK) {
            List<String> keyList = Splitter.on("\n").splitToList(httpGet.getBodyString());
            Set<String> keySet = Sets.newHashSet(keyList);
            diseaseSet.addAll(keySet);
        }
    }

    private void getFromDB(Set<String> diseaseSet) {
        List<CfDiseaseWordVo> diseaseWordVoList = JSON.parseArray(infoTitleTemplate, CfDiseaseWordVo.class);
        diseaseWordVoList.forEach(val -> diseaseSet.addAll(ListUtil.getList(3000,
                                                                            (start, size) -> crowdfundingTreatmentBiz.selectDiseaseName(val.getKeyWord(), 5, start, size))));
    }
}
