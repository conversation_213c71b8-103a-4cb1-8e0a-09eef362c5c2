package com.shuidihuzhu.cf.service.publictrust;

import com.shuidihuzhu.cf.dao.publictrust.CfPublicTrustPromotionStatisticsDao;
import com.shuidihuzhu.cf.enums.publictrust.PublicTrustClickType;
import com.shuidihuzhu.cf.model.publictrust.CfPublicTrustPromotionStatisticsDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.UUID;

@Slf4j
@Service
public class PublicTrustService {

    @Resource
    private CfPublicTrustPromotionStatisticsDao statisticsDao;

    private static final int YES = 1;

    private static final int NO = 0;


    public boolean isClick(int isClick, long userId) {

        // 点击调用接口 isClick -> 1
        if (YES == isClick) {
            CfPublicTrustPromotionStatisticsDO statisticsDO = statisticsDao.getByUserIdAndIsClickAndType(userId, isClick, PublicTrustClickType.READ_POINT.getValue());
            if (Objects.isNull(statisticsDO)) {
                int result = statisticsDao.insertSelective(buildDO(userId));
                return result > 0;
            } else {
                // 已点击过 返回true
                return true;
            }
        }

        // 查询时候传值isClick -> 0
        if (NO == isClick) {
            CfPublicTrustPromotionStatisticsDO statisticsDO = statisticsDao.getByUserIdAndIsClickAndType(userId, YES, PublicTrustClickType.READ_POINT.getValue());
            return Objects.nonNull(statisticsDO);

        }

        return false;
    }

    private CfPublicTrustPromotionStatisticsDO buildDO(long userId) {
        CfPublicTrustPromotionStatisticsDO newStatisticsDO = new CfPublicTrustPromotionStatisticsDO();
        newStatisticsDO.setUserId(userId);
        newStatisticsDO.setInfoId(UUID.randomUUID().toString());
        newStatisticsDO.setIsClick(YES);
        newStatisticsDO.setIsAvailable(YES);
        newStatisticsDO.setType(PublicTrustClickType.READ_POINT.getValue());
        newStatisticsDO.setIsDelete(NO);
        return newStatisticsDO;
    }
}
