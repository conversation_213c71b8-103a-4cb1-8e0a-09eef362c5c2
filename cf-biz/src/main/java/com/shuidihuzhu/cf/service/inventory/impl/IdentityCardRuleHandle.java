package com.shuidihuzhu.cf.service.inventory.impl;

import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.biz.crowdfunding.impl.CfFirstApproveBizImpl;
import com.shuidihuzhu.cf.biz.crowdfunding.impl.CrowdfundingAuthorBizImpl;
import com.shuidihuzhu.cf.biz.crowdfunding.impl.CrowdfundingInfoBizImpl;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.inventory.SecondInventoryDetail;
import com.shuidihuzhu.cf.service.inventory.InventoryRuleHandle;
import com.shuidihuzhu.cf.util.DesensitizationUtil;
import com.shuidihuzhu.cipher.ShuidiCipher;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Description: 身份证号
 * @Author: panghairui
 * @Date: 2023/1/12 11:00 上午
 */
@Slf4j
@Service("identityCardRuleHandle")
public class IdentityCardRuleHandle implements InventoryRuleHandle {

    @Resource
    private ShuidiCipher shuidiCipher;

    @Resource
    private CfFirstApproveBizImpl cfFirstApproveBiz;

    @Resource
    private CrowdfundingInfoBizImpl crowdfundingInfoBiz;

    @Resource
    private CrowdfundingAuthorBizImpl crowdfundingAuthorBiz;

    @Override
    public void handle(SecondInventoryDetail secondInventoryDetail, long time) {

        // 取该userId最近的案例
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getLastByUserId(secondInventoryDetail.getUserId());
        if (Objects.isNull(crowdfundingInfo)) {
            return;
        }

        // 取材料审核中患者身份证号
        CrowdfundingAuthor crowdfundingAuthor = crowdfundingAuthorBiz.get(crowdfundingInfo.getId());
        log.info("IdentityCardRuleHandle crowdfundingAuthor {}", JSONObject.toJSONString(crowdfundingAuthor));
        if (crowdfundingAuthor != null && StringUtils.isNotBlank(crowdfundingAuthor.getCryptoIdCard()) && crowdfundingInfo.getCreateTime().getTime() > time) {
            String idCard = shuidiCipher.decrypt(crowdfundingAuthor.getCryptoIdCard());
            secondInventoryDetail.setContent(DesensitizationUtil.desensitizationNumber(idCard));
            secondInventoryDetail.setSituation("已收集1条");
            return ;
        }

        // 取前置信息中患者身份证号
        CfFirsApproveMaterial firstApproveInfo = cfFirstApproveBiz.getByInfoId(crowdfundingInfo.getId());
        log.info("IdentityCardRuleHandle firstApproveInfo {}", JSONObject.toJSONString(firstApproveInfo));
        if (firstApproveInfo != null && Objects.nonNull(crowdfundingInfo.getCreateTime()) && StringUtils.isNotBlank(firstApproveInfo.getPatientCryptoIdcard()) && crowdfundingInfo.getCreateTime().getTime() > time) {
            String idCard = shuidiCipher.decrypt(firstApproveInfo.getPatientCryptoIdcard());
            secondInventoryDetail.setContent(DesensitizationUtil.desensitizationNumber(idCard));
            secondInventoryDetail.setSituation("已收集1条");
        }

    }
}
