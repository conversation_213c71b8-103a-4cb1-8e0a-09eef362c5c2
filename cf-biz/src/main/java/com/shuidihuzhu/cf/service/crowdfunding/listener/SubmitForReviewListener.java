package com.shuidihuzhu.cf.service.crowdfunding.listener;

import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingStatus;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.crowdfunding.CrowdfundingInfoService;
import com.shuidihuzhu.cf.service.crowdfunding.event.CrowdFundingUpdateEvent;
import com.shuidihuzhu.cf.service.crowdfunding.event.CrowdFundingUpdatePublisher;
import com.shuidihuzhu.cf.service.crowdfunding.event.UpdateListenerOrder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.event.SmartApplicationListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class SubmitForReviewListener implements SmartApplicationListener {

    @Resource
    private CrowdfundingInfoService crowdfundingInfoService;

    @Override
    public void onApplicationEvent(ApplicationEvent _event) {

        CrowdFundingUpdateEvent crowdFundingUpdateEvent = (CrowdFundingUpdateEvent) _event;
        CrowdfundingInfo crowdfundingInfo = crowdFundingUpdateEvent.getCrowdfundingInfo();

        if (crowdfundingInfo != null) {
            if (CrowdfundingStatus.APPROVE_DENIED.equals(crowdfundingInfo.getStatus())) {
                try {
                    crowdfundingInfoService.submitForReview(crowdfundingInfo);
                } catch (Exception e) {
                    log.error("SubmitForReviewListener  error:", e);
                }
            }
        }

    }


    @Override
    public boolean supportsSourceType(Class<?> sourceType) {
        return sourceType == CrowdFundingUpdatePublisher.class;
    }

    @Override
    public boolean supportsEventType(Class<? extends ApplicationEvent> eventType) {

        return CrowdFundingUpdateEvent.class.isAssignableFrom(eventType);

    }

    @Override
    public int getOrder() {
        return UpdateListenerOrder.SubmitForReview.getValue();
    }

}
