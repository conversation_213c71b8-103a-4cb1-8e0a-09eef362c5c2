package com.shuidihuzhu.cf.service.crowdfunding.bigdata;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.model.crowdfunding.vo.UserVisitListVo;
import com.shuidihuzhu.cf.vo.DsDonationDataVo;
import com.shuidihuzhu.client.dataservice.ds.v1.DsApiClient;
import com.shuidihuzhu.client.dataservice.faceApi.v1.FaceApiClient;
import com.shuidihuzhu.client.model.CFDO;
import com.shuidihuzhu.client.model.RealtimeDO;
import com.shuidihuzhu.client.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author: lianghongchao
 * @Date: 2018/8/6 21:15
 */
@Service
@Slf4j
public class DsUserMessageService {
    @Autowired
    private DsApiClient dsApiClient;

    @Autowired
    private FaceApiClient faceApiClient;

    /**
     * 浏览案例记录
     *
     * @param nextId
     * @param limit
     * @param userId
     * @return
     */
    public UserVisitListVo getUserVisitListV2(Integer nextId, int limit, long userId) {
        if (userId < 0 || limit < 0) {
            return null;
        }
        Response result = dsApiClient.listVisitCasesV2(userId, nextId, limit);
        if (null == result || 0 != result.getCode()) {
            log.debug("getUserVisitList result:{}", result);
            return null;
        }
        return JSONObject.parseObject(JSON.toJSONString(result), UserVisitListVo.class);
    }

    /**
     * @param userId
     * @param cardType
     * @param bank
     * @param gender
     * @param amount
     * @return
     */
    public String getUserIncome(long userId, String cardType, String bank, String gender, Integer amount) {
        if (userId < 0 || null == amount) {
            return null;
        }
        Response response = dsApiClient.assignWelcome(String.valueOf(userId), cardType, bank, gender, amount);
        if (null == response) {
            log.debug("getUserIncome response null");
            return null;
        }
        return JSON.toJSONString(response);
    }

    public CFDO getCaseExtProperty(long userId, long infoId) {
        Response<RealtimeDO> response = dsApiClientGetCaseExt(userId, infoId);
        log.debug("faceApiClient.getCaseExt userId:{}, caseId:{},result:{}", userId, infoId, response);

        if (null == response || 0 != response.getCode() || null == response.getData()) {
            return null;
        }

        return response.getData().getCf();
    }

    /**
     * donationUserId→捐款用户userId
     * <p>
     * fundingUserId -->案例发起人userId
     * <p>
     * crowdfundingId→捐款的案例id
     * <p>
     * lastDonationNum--->最后捐款的金额(元)
     * <p>
     * isShared -->是否对该案例有过分享(0-是  1-否)
     */
    public DsDonationDataVo getDonate(long donationUserId, long fundingUserId, long crowdfundingId,
                                      long lastOrderId, Double lastDonationNum, int isShared) {
        Response response = dsApiClient.getDonate(donationUserId, fundingUserId, crowdfundingId, lastOrderId,
                lastDonationNum, isShared);
        if (null == response || response.getCode() != 0) {
            return null;
        }

        try {
            return JSONObject.parseObject(JSON.toJSONString(response.getData()), DsDonationDataVo.class);
        } catch (Exception e) {
            log.error("getDonate is error", e);
            return null;
        }
    }

    public Response<RealtimeDO> dsApiClientGetCaseExt(long userId, long infoId) {
        try {

            Response<RealtimeDO> response = faceApiClient.getCaseExt(userId, infoId);
            return response;
        } catch (Exception e) {
            log.debug("faceApiClient.ogetCaseExt异常:{}",e.getMessage());
            return null;
        }

    }
}