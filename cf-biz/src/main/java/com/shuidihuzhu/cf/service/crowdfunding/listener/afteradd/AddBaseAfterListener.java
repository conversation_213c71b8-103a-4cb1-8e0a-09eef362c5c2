package com.shuidihuzhu.cf.service.crowdfunding.listener.afteradd;

import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.CfInfoExtBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingAttachmentBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.VolunteerBiz;
import com.shuidihuzhu.cf.client.ugc.caseprocessstatus.model.CaseProcessStatusEnum;
import com.shuidihuzhu.cf.client.ugc.caseprocessstatus.service.CaseProcessStatusClient;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.client.ugc.service.CommonOperationRecordClient;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CfVersion;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoBaseVo;
import com.shuidihuzhu.cf.risk.client.risk.RiskRaiserMobileIsInnerStaffFeignClient;
import com.shuidihuzhu.cf.risk.model.risk.RiskRaiserMobileCheckParam;
import com.shuidihuzhu.cf.risk.model.risk.RiskRaiserMobileCheckResult;
import com.shuidihuzhu.cf.service.caseinfo.CaseInfoApproveStageService;
import com.shuidihuzhu.cf.service.crowdfunding.CfInfoMirrorService;
import com.shuidihuzhu.cf.service.crowdfunding.event.CrowdFundingAddEvent;
import com.shuidihuzhu.cf.service.crowdfunding.event.AddListenerOrder;
import com.shuidihuzhu.cf.service.river.RiverReviewService;
import com.shuidihuzhu.client.cf.growthtool.model.CfVolunteerSimpleVo;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.cf.service.crowdfunding.listener.base.AbstractCrowdFundingAddEventListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.event.SmartApplicationListener;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.stream.Collectors;

/**
 *
 */
@Slf4j
@Service
public class AddBaseAfterListener extends AbstractCrowdFundingAddEventListener implements SmartApplicationListener {

    @Autowired
    private CfInfoMirrorService cfInfoMirrorService;

    @Autowired
    private CaseProcessStatusClient caseProcessStatusClient;

    @Autowired
    private CommonOperationRecordClient commonOperationRecordClient;

    @Autowired
    private CaseInfoApproveStageService caseInfoApproveStageService;

    @Autowired
    private CrowdfundingAttachmentBiz crowdfundingAttachmentBiz;

    @Autowired
    private RiverReviewService riverReviewService;

    @Autowired
    private CfInfoExtBiz cfInfoExtBiz;

    @Autowired
    private RiskRaiserMobileIsInnerStaffFeignClient riskRaiserMobileIsInnerStaffFeignClient;

    @Autowired
    private VolunteerBiz volunteerBiz;

    @Autowired
    private UserInfoDelegate userInfoDelegate;

    @Autowired
    private Producer producer;

    @Override
    public void onApplicationEvent(ApplicationEvent _event) {

        CrowdFundingAddEvent crowdFundingAddEvent = (CrowdFundingAddEvent) _event;
        CrowdfundingInfo cfCase = crowdFundingAddEvent.getCrowdfundingInfo();
        CrowdfundingInfoBaseVo infoBaseVo = crowdFundingAddEvent.getCrowdfundingInfoBaseVo();
        if (cfCase == null || infoBaseVo == null) {
            return;
        }
            int caseId = cfCase.getId();
            try {
                cfInfoMirrorService.addBaseAfter(infoBaseVo, cfCase.getInfoId());
            } catch (Exception e) {
                log.error("AddBaseAfterListener error:", e);
            }
            try {
                String images = crowdfundingAttachmentBiz
                        .getAttachmentsByType(cfCase.getId(), AttachmentTypeEnum.ATTACH_CF)
                        .stream()
                        .map(CrowdfundingAttachmentVo::getUrl)
                        .collect(Collectors.joining(","));
                caseInfoApproveStageService.insert(cfCase.getId(), cfCase.getTitle(), cfCase.getContent(), images);
            } catch (Exception e) {
                log.error("insert case info stage fail", e);
            }
            try {
                caseProcessStatusClient.update(cfCase.getId(), CaseProcessStatusEnum.RAISE);
            } catch (Exception e) {
                log.error("update caseProcessStatus raise fail", e);
            }
            try {
                commonOperationRecordClient.create()
                        .buildBasicUser(cfCase.getId(), OperationActionTypeEnum.SUBMIT_INITIAL_AUDIT).save();
            } catch (Exception e) {
                log.error("add record raise fail", e);
            }
            try  {
                CfInfoExt ext = cfInfoExtBiz.getByCaseId(caseId);
                if (ext != null && ext.getCfVersion() == CfVersion.definition_20181131.getCode()) {
                    // 创建材料记录
                    riverReviewService.onRaise(caseId);
                }
            } catch (Exception e) {
                log.error("add record raise fail", e);
            }
            try {
                checkRaiserRisk(cfCase, infoBaseVo);
            } catch (Exception e) {
                log.error("检查发起人风险", e);
            }

        // 发送案例id生成mq
        sendCaseIdCreateMq(cfCase);


    }

    private void sendCaseIdCreateMq(CrowdfundingInfo crowdfundingInfo) {
        Message msg = new Message(MQTopicCons.CF, MQTagCons.CF_CASE_ID_CREATE,
                "" + crowdfundingInfo.getId(),
                crowdfundingInfo);
        MessageResult result = producer.send(msg);
        log.info("案例id创建成功. msg:{} result:{}", msg, result);
    }

    private void checkRaiserRisk(CrowdfundingInfo cfCase, CrowdfundingInfoBaseVo infoBaseVo) {
        CfVolunteerSimpleVo volunteer = volunteerBiz.getSimpleVolunteerByCaseId(cfCase.getId());
        String volunteerName = Optional.ofNullable(volunteer).map(CfVolunteerSimpleVo::getName).orElse("");
        RiskRaiserMobileCheckParam checkParam = new RiskRaiserMobileCheckParam();
        checkParam.setChannel(RiskRaiserMobileCheckParam.Channel.SUBMIT_INITIAL);
        checkParam.setRaiserName(infoBaseVo.getSelfRealName());
        checkParam.setVolunteerName(volunteerName);

        long userId = cfCase.getUserId();
        UserInfoModel userInfo = userInfoDelegate.getUserInfoByUserId(userId);
        checkParam.setEncryptRaiserMobile(userInfo.getCryptoMobile());
        checkParam.setCaseId(cfCase.getId());
        Response<RiskRaiserMobileCheckResult> resp = riskRaiserMobileIsInnerStaffFeignClient.check(checkParam);
        log.info("检查发起人手机号风险 param {}, resp {}", checkParam, resp);
    }


    @Override
    public int getOrder() {
        return AddListenerOrder.AddBaseAfter.getValue();
    }
}
