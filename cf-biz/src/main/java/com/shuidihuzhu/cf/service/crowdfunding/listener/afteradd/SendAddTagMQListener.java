package com.shuidihuzhu.cf.service.crowdfunding.listener.afteradd;

import com.shuidihuzhu.cf.service.crowdfunding.listener.base.AbstractCrowdFundingAddEventListener;
import org.apache.commons.lang3.StringUtils;
import com.shuidihuzhu.account.model.UserThirdModel;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.enums.crowdfunding.UserTagGroup;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoBaseVo;
import com.shuidihuzhu.cf.service.crowdfunding.event.CrowdFundingAddEvent;
import com.shuidihuzhu.cf.service.crowdfunding.event.AddListenerOrder;
import com.shuidihuzhu.cf.vo.UserInfoVo;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.event.SmartApplicationListener;
import org.springframework.stereotype.Service;


/**
 * @deprecated 原来张文强&田宇的需求，找机会通知PM后下线。
 */
@Deprecated
@Slf4j
@Service
public class SendAddTagMQListener extends AbstractCrowdFundingAddEventListener implements SmartApplicationListener {

    @Autowired(required = false)
    private Producer producer;

    public void sendAddTagMQ(Integer userThirdType, UserThirdModel userThirdModel, String infoUuid) {
        if(producer==null)
        {
            log.info("sendAddTagMQListener producer is null");
            return;
        }
        try {
            if (userThirdType != null && userThirdModel != null) {
                try {
                    UserInfoVo userInfoVo = new UserInfoVo(userThirdModel.getUserId(), userThirdModel.getOpenId(), UserTagGroup.BASE_INFO, userThirdType);
                    log.info("add tag to base info user userInfoVo:{}", userInfoVo);
                    MessageResult messageResult= producer.send(new Message<>(MQTopicCons.CF,
                            MQTagCons.CF_ADD_WX_TAG_TO_USER, MQTagCons.CF_ADD_WX_TAG_TO_USER + "-" + infoUuid
                            + "-" + System.currentTimeMillis(), userInfoVo, DelayLevel.S1));
                    log.info("sendAddTagMQ messageResult:{}",messageResult);
                } catch (Exception e) {
                    log.error("base info user add wx tag error", e);
                }
            }
        } catch (Exception e) {
            log.error("sendAddTagMQ error", e);
        }
    }


    @Override
    public void onApplicationEvent(ApplicationEvent _event) {

        CrowdFundingAddEvent crowdFundingAddEvent = (CrowdFundingAddEvent) _event;
        CrowdfundingInfo crowdfundingInfo = crowdFundingAddEvent.getCrowdfundingInfo();
        CrowdfundingInfoBaseVo infoBaseVo = crowdFundingAddEvent.getCrowdfundingInfoBaseVo();
        UserThirdModel userThirdModel = crowdFundingAddEvent.getUserThirdModel();
        if (crowdfundingInfo != null && infoBaseVo != null && userThirdModel != null) {
            if (!StringUtils.isEmpty(infoBaseVo.getSelfRealName()) ||
                    !StringUtils.isEmpty(infoBaseVo.getPatientRealName())) {
            } else {
                try {
                    this.sendAddTagMQ(infoBaseVo.getUserThirdType(), userThirdModel, crowdfundingInfo.getInfoId());
                } catch (Exception e) {
                    log.error("sendAddTagMQ error:", e);
                }
            }
        }


    }

    @Override
    public int getOrder() {
        return AddListenerOrder.SendAddTagMQ.getValue();
    }
}