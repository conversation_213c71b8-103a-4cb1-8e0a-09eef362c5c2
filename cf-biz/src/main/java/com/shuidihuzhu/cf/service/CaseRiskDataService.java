package com.shuidihuzhu.cf.service;

import com.shuidihuzhu.cf.response.OpResult;

/**
 * <AUTHOR>
 * @date 2018-08-04  18:06
 */
public interface CaseRiskDataService {

    OpResult<String> getRiskData(String infoUuid);

    /**
     * @see com.shuidihuzhu.cf.constants.CaseRiskConstants.DataLevel
     *
     * @param data
     * @param dataLevel
     * @return
     */
    OpResult<Integer> analyseDrawCashRiskWithDataLevel(String data, String dataLevel);

    /**
     *
     * @see com.shuidihuzhu.cf.constants.CaseRiskConstants.DataLevel
     *
     * @param data
     * @param dataLevel
     * @return
     */
    OpResult<Integer> analyseInfoRiskWithDataLevel(String data, String dataLevel);

    boolean checkRiskPassed(int risk);

}
