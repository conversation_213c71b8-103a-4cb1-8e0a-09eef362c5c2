package com.shuidihuzhu.cf.service.activity;

import com.shuidihuzhu.cf.domain.activity.ActivityVenueSimpleCaseInfo;
import com.shuidihuzhu.cf.domain.activity.ActivityVenueSimpleOrderInfo;
import com.shuidihuzhu.cf.enums.activity.Activity111CaseTypeEnum;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by sven on 2020/4/4.
 *
 * <AUTHOR>
 */
public interface IActivityVenueDataProvider {

    Activity111CaseTypeEnum supportType();

    Map<Integer, ActivityVenueSimpleCaseInfo> getMaps(List<Integer> caseIds);

    boolean isFundraiser(long userId);

    List<ActivityVenueSimpleOrderInfo> getByUserIdFromSharding(Set<Long> userIds, int caseId);

    List<ActivityVenueSimpleOrderInfo> getByUserIdsFromSharding(Set<Long> userIds);
}
