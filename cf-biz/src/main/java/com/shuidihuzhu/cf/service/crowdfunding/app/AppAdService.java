package com.shuidihuzhu.cf.service.crowdfunding.app;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.app.AppAdBiz;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.app.AppBanner;
import com.shuidihuzhu.cf.model.crowdfunding.app.AppNews;
import com.shuidihuzhu.cf.model.crowdfunding.app.AppSampleCase;
import com.shuidihuzhu.cf.model.crowdfunding.app.AppSampleRaiser;
import com.shuidihuzhu.cf.model.crowdfunding.app.vo.AppBannerVo;
import com.shuidihuzhu.cf.model.crowdfunding.app.vo.AppNewsVo;
import com.shuidihuzhu.cf.model.crowdfunding.app.vo.AppSampleCaseVo;
import com.shuidihuzhu.cf.model.crowdfunding.app.vo.AppSampleRaiserVo;
import com.shuidihuzhu.cf.util.crowdfunding.CfUrlUtil;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by wangsf on 18/2/2.
 */
@Service
@Slf4j
public class AppAdService {

    @Resource(name = "appAdBiz")
    private AppAdBiz appAdBiz;
    @Resource
    private CrowdfundingInfoBiz crowdfundingInfoBiz;
    @Resource
    private UserInfoDelegate userInfoDelegate;
    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;
    private long _1MINUTE = 60;

    private DecimalFormat df = new DecimalFormat("#,###元");

    //获取banner
    public List<AppBannerVo> getBannersByName(String name) {

        if (StringUtils.isBlank(name)) {
            return Collections.emptyList();
        }

        String redisKey = "app-banner-" + name;
        List<AppBannerVo> appBannerVos = redissonHandler.getList(redisKey, AppBannerVo.class);
        if (CollectionUtils.isNotEmpty(appBannerVos)) {
            return appBannerVos;
        }

        List<AppBanner> appBanners = this.appAdBiz.findBannerByAppName(name);
        if (CollectionUtils.isEmpty(appBanners)) {
            return Collections.emptyList();
        }

        List<AppBannerVo> bannerVos = appBanners.stream()
                .map(AppBannerVo::new)
                .collect(Collectors.toList());

        redissonHandler.addListEX(redisKey, bannerVos, _1MINUTE * 2);

        return bannerVos;
    }

    //获取配置好的新闻
    public List<AppNewsVo> getAppNews(String name) {

        if (StringUtils.isBlank(name)) {
            return Collections.emptyList();
        }

        String redisKey = "app-news-v2-" + name;
        List<AppNews> appNewses = redissonHandler.getList(redisKey, AppNews.class);
        if (CollectionUtils.isNotEmpty(appNewses)) {
            return appNewses.stream().distinct().sorted((o1, o2) -> o2.getCreateTime().compareTo(o1.getCreateTime()))
                    .map(AppNewsVo::new).collect(Collectors.toList());
        }

        appNewses = this.appAdBiz.findNewsByAppName(name);
        if (org.springframework.util.CollectionUtils.isEmpty(appNewses)) {
            return Collections.emptyList();
        }
        redissonHandler.addListNX(redisKey, appNewses, _1MINUTE * 2);
        return appNewses.stream().map(AppNewsVo::new).collect(Collectors.toList());
    }

    public List<AppSampleCaseVo> getSampleCasesByName(String name) {

        if (StringUtils.isBlank(name)) {
            return Collections.emptyList();
        }

        String redisKey = "app-sample-case-v3-" + name;
        List<AppSampleCaseVo> appSampleCaseVos = redissonHandler.getList(redisKey, AppSampleCaseVo.class);
        if (CollectionUtils.isNotEmpty(appSampleCaseVos)) {
            return appSampleCaseVos;
        }

        appSampleCaseVos = this.getSampleCasesFromDB(name);

        redissonHandler.addListEX(redisKey, appSampleCaseVos, _1MINUTE * 2);

        return appSampleCaseVos;
    }

    public List<AppSampleCaseVo> getFakeCasesByName(String name) {

        if (StringUtils.isBlank(name)) {
            return Collections.emptyList();
        }

        String redisKey = "app-fake-case-" + name;
        List<AppSampleCaseVo> appSampleCaseVos = redissonHandler.getList(redisKey, AppSampleCaseVo.class);
        if (CollectionUtils.isNotEmpty(appSampleCaseVos)) {
            return appSampleCaseVos;
        }

        appSampleCaseVos = this.getFakeCasesFromDB(name);

        redissonHandler.addListEX(redisKey, appSampleCaseVos, _1MINUTE * 2);

        return appSampleCaseVos;

    }

    //获取配置的筹款人
    public List<AppSampleRaiserVo> getSampleRaiserByName(String name) {

        if (StringUtils.isBlank(name)) {
            return Collections.emptyList();
        }

        String redisKey = "app-sample-raiser-v2-" + name;
        List<AppSampleRaiserVo> appSampleRaiserVos = redissonHandler.getList(redisKey, AppSampleRaiserVo.class);
        if (CollectionUtils.isNotEmpty(appSampleRaiserVos)) {
            return appSampleRaiserVos;
        }

        appSampleRaiserVos = this.getAppSampleRaisersFromDB(name);

        redissonHandler.addListEX(redisKey, appSampleRaiserVos, _1MINUTE * 2);

        return appSampleRaiserVos;
    }

    private List<AppSampleRaiserVo> getAppSampleRaisersFromDB(String name) {
        List<AppSampleRaiser> appSampleRaisers = this.appAdBiz.findSampleRaiserByAppName(name);
        if (CollectionUtils.isEmpty(appSampleRaisers)) {
            return Collections.emptyList();
        }

        //拼响应
        List<AppSampleRaiserVo> appSampleRaiserVos = Lists.newLinkedList();
        for (AppSampleRaiser sampleRaiser : appSampleRaisers) {
            AppSampleRaiserVo appSampleRaiserVo = new AppSampleRaiserVo();
            if (sampleRaiser.getAmount() > 50000000) {
                continue;
            }
            String targetAmount = String.valueOf(sampleRaiser.getAmount());
            int length = targetAmount.length();
            String money = targetAmount.substring(0, length - 2) + "." + targetAmount.substring(length - 2);
            appSampleRaiserVo.setMoney(money);
            appSampleRaiserVo.setHeadImg(sampleRaiser.getHeadImg());
            appSampleRaiserVo.setName(sampleRaiser.getUsername());
            appSampleRaiserVo.setProvince(sampleRaiser.getProvince());

            appSampleRaiserVos.add(appSampleRaiserVo);
        }

        return appSampleRaiserVos;
    }

    public List<AppSampleCaseVo> getSampleCasesFromDB(String name) {

        if (StringUtils.isBlank(name)) {
            return Collections.emptyList();
        }

        List<AppSampleCase> sampleCases = this.appAdBiz.findSampleCaseByAppName(name);
        if (org.springframework.util.CollectionUtils.isEmpty(sampleCases)) {
            return Collections.emptyList();
        }

        List<String> infoUuids = sampleCases.stream()
                .map(AppSampleCase::getInfoUuid)
                .distinct()
                .collect(Collectors.toList());

        Map<String, CrowdfundingInfo> infoMap = this.crowdfundingInfoBiz.getMapByInfoUuIds(infoUuids);
        if (org.springframework.util.CollectionUtils.isEmpty(infoMap)) {
            return Collections.emptyList();
        }

        List<Integer> infoIds = Lists.newLinkedList();
        List<Long> userIds = Lists.newLinkedList();
        infoMap.forEach((key, value) -> {
            infoIds.add(value.getId());
            userIds.add(value.getUserId());
        });
        List<UserInfoModel> userInfoModels = this.userInfoDelegate.getUserInfoByUserIdBatch(userIds);
        if (org.springframework.util.CollectionUtils.isEmpty(userInfoModels)) {
            return Collections.emptyList();
        }

        //取用户和案例详情
        Map<Long, UserInfoModel> userInfoModelMap = Maps.newHashMap();
        userInfoModels.forEach(userInfoModel -> userInfoModelMap.put(userInfoModel.getUserId(), userInfoModel));

        //拼响应
        List<AppSampleCaseVo> appSampleCaseVos = Lists.newLinkedList();
        for (AppSampleCase appSampleCase : sampleCases) {
            CrowdfundingInfo crowdfundingInfo = infoMap.get(appSampleCase.getInfoUuid());
            if (crowdfundingInfo == null) {
                continue;
            }

            long userId = crowdfundingInfo.getUserId();
            UserInfoModel userInfoModel = userInfoModelMap.get(userId);
            if (userInfoModel == null) {
                continue;
            }

            AppSampleCaseVo appSampleCaseVo = new AppSampleCaseVo();
            appSampleCaseVo.setInfoUuid(appSampleCase.getInfoUuid());
            appSampleCaseVo.setInfoUrl(CfUrlUtil.getContribute(appSampleCase.getInfoUuid(), "", crowdfundingInfo.getType()));
            appSampleCaseVo.setLocation(appSampleCase.getLocation());
            appSampleCaseVo.setImgUrl(appSampleCase.getPicUrl());
            appSampleCaseVo.setDisease(appSampleCase.getDisease());
            appSampleCaseVo.setRaiserName(makeNotNull(userInfoModel.getNickname()));

            if (!StringUtils.isBlank(appSampleCase.getTitle())) {
                appSampleCaseVo.setTitle(appSampleCase.getTitle());
            } else {
                appSampleCaseVo.setTitle(makeNotNull(crowdfundingInfo.getTitle()));
            }

            if (!StringUtils.isBlank(appSampleCase.getDescription())) {
                appSampleCaseVo.setDescription(appSampleCase.getDescription());
            } else {
                appSampleCaseVo.setDescription("");
            }

            appSampleCaseVo.setGainAmount(df.format(crowdfundingInfo.getAmount() / 100));
            appSampleCaseVo.setTargetAmount(df.format(crowdfundingInfo.getTargetAmount() / 100));
            appSampleCaseVo.setDonationCount(crowdfundingInfo.getDonationCount() + "");
            appSampleCaseVo.setUserHeadImgUrl(makeNotNull(userInfoModel.getHeadImgUrl()));
            appSampleCaseVo.setCreateTime(DateUtil.formatDate(crowdfundingInfo.getCreateTime()));
            appSampleCaseVos.add(appSampleCaseVo);
        }

        return appSampleCaseVos;
    }

    public List<AppSampleCaseVo> getFakeCasesFromDB(String name) {

        if (StringUtils.isBlank(name)) {
            return Collections.emptyList();
        }

        List<AppSampleCase> sampleCases = this.appAdBiz.findSampleCaseByAppName(name);
        if (org.springframework.util.CollectionUtils.isEmpty(sampleCases)) {
            return Collections.emptyList();
        }

        //取用户和案例详情

        //拼响应
        List<AppSampleCaseVo> appSampleCaseVos = Lists.newLinkedList();
        for (AppSampleCase appSampleCase : sampleCases) {

            AppSampleCaseVo appSampleCaseVo = new AppSampleCaseVo();
            appSampleCaseVo.setInfoUuid(appSampleCase.getInfoUuid());
            appSampleCaseVo.setLocation(appSampleCase.getLocation());
            appSampleCaseVo.setImgUrl(appSampleCase.getPicUrl());
            appSampleCaseVo.setDisease(appSampleCase.getDisease());
            appSampleCaseVo.setRaiserName(appSampleCase.getUsername());

            if (!StringUtils.isBlank(appSampleCase.getTitle())) {
                appSampleCaseVo.setTitle(appSampleCase.getTitle());
            } else {
                appSampleCaseVo.setTitle("");
            }

            if (!StringUtils.isBlank(appSampleCase.getDescription())) {
                appSampleCaseVo.setDescription(appSampleCase.getDescription());
            } else {
                appSampleCaseVo.setDescription("");
            }

            appSampleCaseVo.setGainAmount(appSampleCase.getAmount());
            appSampleCaseVo.setTargetAmount(appSampleCase.getTargetAmount());
            appSampleCaseVo.setDonationCount(appSampleCase.getDonationCount());
            appSampleCaseVo.setUserHeadImgUrl(appSampleCase.getUserHeadImg());
            appSampleCaseVo.setCostTime(appSampleCase.getCostTime());

            appSampleCaseVos.add(appSampleCaseVo);
        }

        return appSampleCaseVos;
    }

    private String makeNotNull(String str) {
        return str == null ? "" : str;
    }
}
