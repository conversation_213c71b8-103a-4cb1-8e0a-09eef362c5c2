package com.shuidihuzhu.cf.service.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfFriendLoopInfoVo;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.xerial.snappy.Snappy;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

@Service
@Slf4j
@RefreshScope
public class CfFriendLoopRedisService {


    @Resource(name = "cfFriendLoopRedissonHandler")
    private RedissonHandler cfRedissonHandler;
    @Value("${friend.friend-loop.redis-expire-time:3600}")
    private long HOUR_TIMES;
    private static final String FRIEND_LOOP_V4 = "_friend_loop_v4";


    public void setRedissonValue(long userId, int crowdFundingId, List<CfFriendLoopInfoVo> friendResultList) {
        String key = this.getRedisKey(userId, crowdFundingId);
        String value=compress(friendResultList);
        cfRedissonHandler.setEX(key, value, HOUR_TIMES*1000L);
        log.debug("userId:{},crowdFundingId:{},缓存loop数据,设置标志位为true", userId, crowdFundingId);
    }

    public List<CfFriendLoopInfoVo> getRedissonResultList(long userId, int crowdFundingId) {
        String key = this.getRedisKey(userId, crowdFundingId);
        if (cfRedissonHandler.exists(key)) {
            log.debug("userId:{},crowdFundingId:{},存在缓存的loop数据,标志位为true", userId, crowdFundingId);
            String value=cfRedissonHandler.get(key,String.class);
            List<CfFriendLoopInfoVo> vos= uncompress(value);
            return vos;
        } else {
            log.debug("userId:{},crowdFundingId:{},不存在缓存的loop数据,标志位为空", userId, crowdFundingId);
            return null;
        }
    }

    private String getRedisKey(long userId, int crowdFundingId) {
        return userId + "_" + crowdFundingId + FRIEND_LOOP_V4;
    }

    private String compress(List<CfFriendLoopInfoVo> vos) {
        String result = "";
        if (CollectionUtils.isEmpty(vos)) {
            return result;
        }
        long start = System.currentTimeMillis();
        String json="";
        try {
            json = JSON.toJSONString(vos);
            byte[] bytes = Snappy.compress(json);
            result = Base64.getEncoder().encodeToString(bytes);
        } catch (Exception e) {
            log.error("friend_loop压缩异常:", e);
        }
        long end = System.currentTimeMillis();
        log.debug("friend_loop压缩前长度:{},压缩后长度:{},耗时:{}", json.length(),result.length(),(end - start));
        return result;
    }

    private List<CfFriendLoopInfoVo> uncompress(String value) {
        List<CfFriendLoopInfoVo> vos = new ArrayList<>();
        if(Strings.isNullOrEmpty(value)) {
            return vos;
        }
        long start = System.currentTimeMillis();
        try {
            byte[] bytes = Base64.getDecoder().decode(value);
            String result = Snappy.uncompressString(bytes);
            vos = JSON.parseArray(result, CfFriendLoopInfoVo.class);
        } catch (Exception e) {
            log.error("friend_loop解压异常:", e);
        }
        if(CollectionUtils.isEmpty(vos)){
            log.error("friend_loop解压异常,数据空");
        }
        long end = System.currentTimeMillis();
        log.debug("friend_loop解压耗时:{}", (end - start));
        return vos;
    }
}
