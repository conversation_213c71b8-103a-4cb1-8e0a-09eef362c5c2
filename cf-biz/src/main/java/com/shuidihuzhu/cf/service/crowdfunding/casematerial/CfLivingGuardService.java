package com.shuidihuzhu.cf.service.crowdfunding.casematerial;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.crowdfunding.CfInfoExtBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingAttachmentBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.material.CfMaterialPreModifyService;
import com.shuidihuzhu.cf.biz.crowdfunding.material.IMaterialCenterService;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.material.model.CfBasicLivingGuardModel;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAttachment;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoDataStatusTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.InitialAuditItem;
import com.shuidihuzhu.cf.model.river.RiverRejectReasonVO;
import com.shuidihuzhu.cf.model.river.RiverReviewDO;
import com.shuidihuzhu.cf.model.river.RiverStatusEnum;
import com.shuidihuzhu.cf.model.river.RiverUsageTypeEnum;
import com.shuidihuzhu.cf.service.river.RiverReviewService;
import com.shuidihuzhu.cf.vo.initialaudit.CfInsuranceLivingAuditVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class CfLivingGuardService {

    @Autowired
    private RiverReviewService reviewService;
    @Autowired
    private IMaterialCenterService materialCenterService;
    @Autowired
    private CfInfoExtBiz infoExtBiz;
    @Autowired
    private CfMaterialPreModifyService materialPreModifyService;
    @Autowired
    private CrowdfundingAttachmentBiz infoAttachment;

    private static List<Integer> validStatus = Lists.newArrayList(0, 1);

    public static CfErrorCode validateLivingGuard(CfBasicLivingGuardModel livingGuard) {

//        if (livingGuard == null) {
//            return CfErrorCode.LIVING_POVERTY_MUST_FILL;
//        }
//
//        if (livingGuard.getLivingAllowance() == null
//                || !validStatus.contains(livingGuard.getLivingAllowance())
//                || (livingGuard.getLivingAllowance() == 0 && StringUtils.isNotBlank(livingGuard.getAllowanceImg()))
//                || (livingGuard.getLivingAllowance() == 1 && StringUtils.isBlank(livingGuard.getAllowanceImg()))) {
//            return CfErrorCode.LIVING_POVERTY_MUST_FILL;
//        }
//
//        if (livingGuard.getHasPoverty() == null
//                || !validStatus.contains(livingGuard.getHasPoverty())
//                || (livingGuard.getHasPoverty() == 0 && StringUtils.isNotBlank(livingGuard.getPovertyImg()))
//                || (livingGuard.getHasPoverty() == 1 && StringUtils.isBlank(livingGuard.getPovertyImg()))) {
//            return CfErrorCode.LIVING_POVERTY_MUST_FILL;
//        }

        return CfErrorCode.SUCCESS;
    }

    public CfInsuranceLivingAuditVo.CfBasicLivingGuardVo selectLivingGuardVo(int caseId) {

        CfBasicLivingGuardModel guardModel = materialCenterService.selectLivingGuard(caseId);
        if (guardModel == null) {
            return null;
        }
        CfInsuranceLivingAuditVo.CfBasicLivingGuardVo guardVo = new CfInsuranceLivingAuditVo.CfBasicLivingGuardVo(guardModel);

        fillAuditStatus(caseId, guardVo);

        return guardVo;
    }

    public CfInsuranceLivingAuditVo.CfBasicLivingGuardVo selectPreModifyLivingOrSelf(int caseId) {

        CfInsuranceLivingAuditVo.CfBasicLivingGuardVo guardVo = materialPreModifyService.selectPreModifyLivingOrSelf(caseId);
        if (guardVo == null) {
            return null;
        }
        fillAuditStatus(caseId, guardVo);

        return guardVo;
    }

    /**
     * 填充审核状态
     * @param caseId
     * @param guardVo
     */
    private void fillAuditStatus(int caseId, CfInsuranceLivingAuditVo.CfBasicLivingGuardVo guardVo) {
        RiverReviewDO livingDo = reviewService.getByCaseIdAndUsageType(caseId, RiverUsageTypeEnum.DI_BAO.getValue());
        RiverReviewDO povertyDo = reviewService.getByCaseIdAndUsageType(caseId, RiverUsageTypeEnum.PIN_KUN.getValue());

        guardVo.setMaterialStatus(getMaterialStatus(livingDo, povertyDo));
        guardVo.setRejectDetail(getRejectDetail(livingDo, povertyDo));
    }

    public InitialAuditItem.MaterialStatus getMaterialStatus(RiverReviewDO livingDo, RiverReviewDO povertyDo) {

        if (livingDo == null || povertyDo == null) {
            return InitialAuditItem.MaterialStatus.DEFAULT;
        }

        if (livingDo.getInfoStatus() == RiverStatusEnum.PASS.getValue()
            && povertyDo.getInfoStatus() == RiverStatusEnum.PASS.getValue()) {
            return InitialAuditItem.MaterialStatus.PASS;
        }

        if (livingDo.getInfoStatus() == RiverStatusEnum.REJECT.getValue()
                || povertyDo.getInfoStatus() == RiverStatusEnum.REJECT.getValue()) {
            return InitialAuditItem.MaterialStatus.REJECT;
        }

        return InitialAuditItem.MaterialStatus.SUBMIT;
    }

    public Map<Integer, List<String>> getRejectDetail(RiverReviewDO livingDo, RiverReviewDO povertyDo) {

        Map<Integer, List<String>> rejectDetail = Maps.newHashMap();

        rejectDetail.putAll(RiverReviewDO.parseRejectDetail(livingDo));
        rejectDetail.putAll(RiverReviewDO.parseRejectDetail(povertyDo));

        return rejectDetail;
    }

    public CfErrorCode addOrUpdateLivingGuard(int caseId, CfInsuranceLivingAuditVo.CfBasicLivingGuardVo livingGuardModel) {

        materialCenterService.addOrUpdateLivingGuard(caseId, livingGuardModel);
        updateBasicLivingGuardImage(caseId, livingGuardModel);
        RiverReviewDO livingDo = reviewService.getByCaseIdAndUsageType(caseId, RiverUsageTypeEnum.DI_BAO.getValue());
        RiverReviewDO povertyDo = reviewService.getByCaseIdAndUsageType(caseId, RiverUsageTypeEnum.PIN_KUN.getValue());
        updateStatus(livingDo);
        updateStatus(povertyDo);

        return CfErrorCode.SUCCESS;
    }

    private void updateStatus(RiverReviewDO reviewDO) {

        if (reviewDO == null || reviewDO.getInfoStatus() != RiverStatusEnum.REJECT.getValue()) {
            log.info("当前的状态不是拒绝状态 不更新数据 param:{}", reviewDO);
            return;
        }

        String rejectDetail = JSON.toJSONString(new HashMap<Integer, List<RiverRejectReasonVO>>());
        if (infoExtBiz.canRepeatSubmitInitial(reviewDO.getCaseId())) {
            rejectDetail = reviewDO.getRejectDetail();
        }

        reviewService.updateById(reviewDO.getId(), RiverStatusEnum.SUBMITTED.getValue(), rejectDetail);
        log.info("更新低保的审核状态为已提交. param：{}", reviewDO);
    }

    private void updateBasicLivingGuardImage(int caseId, CfBasicLivingGuardModel livingGuardParam) {
        log.info("更新案例的低保材料图片.caseId:{} livingGuardParam:{}", caseId, livingGuardParam);
        infoAttachment.deleteByParentIdAndType(caseId,
                Lists.newArrayList(AttachmentTypeEnum.ATTACH_ALLOWANCE_IMG,
                        AttachmentTypeEnum.ATTACH_POVERTY_IMG));

        infoAttachment.add(getBasicLivingGuardAttachment(caseId,  livingGuardParam));
    }

    public static List<CrowdfundingAttachment> getBasicLivingGuardAttachment(int caseId, CfBasicLivingGuardModel livingGuardParam) {

        List<CrowdfundingAttachment> attachmentList = Lists.newArrayList();
        if (livingGuardParam == null) {
            return attachmentList;
        }

        attachmentList.addAll(
                CrowdfundingAttachment.buildAttachmentByImages(caseId,
                        livingGuardParam.getAllowanceImg(), AttachmentTypeEnum.ATTACH_ALLOWANCE_IMG));
        attachmentList.addAll(
                CrowdfundingAttachment.buildAttachmentByImages(caseId,
                        livingGuardParam.getPovertyImg(), AttachmentTypeEnum.ATTACH_POVERTY_IMG));

        return attachmentList;
    }

}
