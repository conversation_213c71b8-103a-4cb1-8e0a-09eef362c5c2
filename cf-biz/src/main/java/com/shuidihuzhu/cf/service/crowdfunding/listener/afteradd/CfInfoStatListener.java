package com.shuidihuzhu.cf.service.crowdfunding.listener.afteradd;

import com.shuidihuzhu.cf.dao.crowdfunding.CfInfoStatDao;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoStat;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.crowdfunding.event.CrowdFundingAddEvent;
import com.shuidihuzhu.cf.service.crowdfunding.event.AddListenerOrder;
import com.shuidihuzhu.cf.service.crowdfunding.listener.base.AbstractCrowdFundingAddEventListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.event.SmartApplicationListener;
import org.springframework.stereotype.Service;

/**
 * 添加统计
 */
@Slf4j
@Service
public class CfInfoStatListener extends AbstractCrowdFundingAddEventListener implements SmartApplicationListener {

    @Autowired
    private CfInfoStatDao cfInfoStatDao;

    public int add(int crowdfundingInfoId) {

        return cfInfoStatDao.add(CfInfoStat.getDefault(crowdfundingInfoId));
    }

    @Override
    public void onApplicationEvent(ApplicationEvent _event) {

        CrowdFundingAddEvent crowdFundingAddEvent = (CrowdFundingAddEvent) _event;
        CrowdfundingInfo cfCase = crowdFundingAddEvent.getCrowdfundingInfo();
        if (cfCase != null) {
            try {
                this.add(cfCase.getId());
            }catch (Exception e) {
                log.error("CfInfoStatListener error:", e);
            }
        }
    }

    @Override
    public int getOrder() {
        return AddListenerOrder.CfInfoStat.getValue();
    }
}
