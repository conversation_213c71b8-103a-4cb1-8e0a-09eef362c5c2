package com.shuidihuzhu.cf.service.inventory.impl;

import com.shuidihuzhu.cf.dao.inventory.CfInventoryDao;
import com.shuidihuzhu.cf.domain.UserDeviceInfo;
import com.shuidihuzhu.cf.model.inventory.SecondInventoryDetail;
import com.shuidihuzhu.cf.service.inventory.InventoryRuleHandle;
import com.shuidihuzhu.cf.util.DesensitizationUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Objects;

/**
 * @Description: 地理位置
 * @Author: panghairui
 * @Date: 2023/1/13 2:06 下午
 */
@Slf4j
@Service("locationRuleHandle")
public class LocationRuleHandle implements InventoryRuleHandle {

    @Resource
    private CfInventoryDao cfInventoryDao;

    @Override
    public void handle(SecondInventoryDetail secondInventoryDetail, long time) {

        UserDeviceInfo userDeviceInfo = cfInventoryDao.getOneDeviceInfoByUserId(secondInventoryDetail.getUserId(), secondInventoryDetail.getIdentification());
        if (Objects.isNull(userDeviceInfo)) {
            return;
        }

        int count = cfInventoryDao.getDeviceInfoCount(secondInventoryDetail.getUserId(), secondInventoryDetail.getIdentification(), new Timestamp(time));

        if (count <= 0) {
            return;
        }

        secondInventoryDetail.setSituation("已收集" + count + "条");
        secondInventoryDetail.setContent(DesensitizationUtil.desensitizationLocationInfo(userDeviceInfo.getContent()));

    }
}
