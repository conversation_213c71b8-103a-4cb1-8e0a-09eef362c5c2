package com.shuidihuzhu.cf.service.patientservicesplatform;

import com.shuidihuzhu.cf.biz.crowdfunding.CfInfoExtBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.impl.CrowdfundingInfoBizImpl;
import com.shuidihuzhu.cf.delegate.ICFFinanceFeignDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.finance.model.vo.CfFinanceCapitalStatusVo;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.patientservicesplatform.PatientServicesPlatformVo;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.MoneyUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RefreshScope
public class PatientServicesPlatformService {

    @Autowired
    private CrowdfundingInfoBizImpl crowdfundingInfoBiz;

    @Autowired
    private CfInfoExtBiz cfInfoExtBiz;

    @Autowired
    private ICFFinanceFeignDelegate cfFinanceFeignDelegate;

    @Value("${apollo.patient.services.platform.limit.day:10}")
    private int day;

    public PatientServicesPlatformVo getHomePage(long userId) {
        //      1. 筹款标题，点击进入案例对应案例详情页
        //      2. 全部案例，点击进入【我的筹款列表】
        //      3. 已筹金额（元），展示案例的筹款总额，保留正整数
        //      4. 可提现金额（元），展示案例余额，保留2位小数
        //      5. 提现，操作按钮，可提现余额>0时展示，点击后进入【筹款管理】页

        //案例id、案例标题、已筹金额、可提现金额、筹款状态、最新筹款的 infoUuid

        if (userId <= 0) {
            return null;
        }

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getLastByUserId(userId);
        if (Objects.isNull(crowdfundingInfo)) {
            return null;
        }

        Date date = DateUtil.addDay(crowdfundingInfo.getCreateTime(), day);
        boolean initiateLimit = new Date().after(date);

        CfInfoExt cfInfoExt = cfInfoExtBiz.getByInfoUuidFromSlave(crowdfundingInfo.getInfoId());
        boolean firstApproveStatus = false;
        if (Objects.nonNull(cfInfoExt)) {
            firstApproveStatus = cfInfoExt.getFirstApproveStatus() == FirstApproveStatusEnum.APPLY_SUCCESS.getCode();
        }

        //案例是否在筹
        boolean status = firstApproveStatus && crowdfundingInfo.getEndTime().getTime() > System.currentTimeMillis();

        CfFinanceCapitalStatusVo cfFinanceCapitalStatusVo = cfFinanceFeignDelegate.getFinanceCapitalStatus(crowdfundingInfo.getInfoId(), crowdfundingInfo.getId());
        //捐款总金额
        int donationAmountInFen = 0;
        //案例剩余金额
        int surplusAmountInFen = 0;
        if (cfFinanceCapitalStatusVo != null) {
            donationAmountInFen = cfFinanceCapitalStatusVo.getDonationAmountInFen();
            surplusAmountInFen = cfFinanceCapitalStatusVo.getSurplusAmountInFen();
        }

        //转成double类型
        double amountInDouble = MoneyUtil.divide(String.valueOf(donationAmountInFen), "100", 2, BigDecimal.ROUND_HALF_UP).doubleValue();
        double surplusAmountInDouble = MoneyUtil.divide(String.valueOf(surplusAmountInFen), "100", 2, BigDecimal.ROUND_HALF_UP).doubleValue();

        return PatientServicesPlatformVo.builder()
                .infoUuid(crowdfundingInfo.getInfoId())
                .title(crowdfundingInfo.getTitle())
                .amountInDouble(amountInDouble)
                .surplusAmountInDouble(surplusAmountInDouble)
                .status(status)
                .initiateLimit(initiateLimit)
                .build();
    }

}
