package com.shuidihuzhu.cf.service.crowdfunding;

import brave.internal.Nullable;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Preconditions;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.account.model.UserThirdModel;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.biz.crowdfunding.material.CfPropertyInsuranceAuditService;
import com.shuidihuzhu.cf.biz.crowdfunding.material.IPayeeMaterialCenterService;
import com.shuidihuzhu.cf.biz.task.CfInfoTaskBiz;
import com.shuidihuzhu.cf.client.adminpure.feign.InitialAuditWorkOrderFeignClient;
import com.shuidihuzhu.cf.client.adminpure.model.initial.PatientToVolunteerConst;
import com.shuidihuzhu.cf.client.adminpure.model.initial.PatientToVolunteerParam;
import com.shuidihuzhu.cf.client.base.result.OperationResult;
import com.shuidihuzhu.cf.client.material.model.MaterialPlanVersion;
import com.shuidihuzhu.cf.client.material.model.PropertyInsuranceCaseType;
import com.shuidihuzhu.cf.client.ugc.feign.MaterialBundleFeignClient;
import com.shuidihuzhu.cf.client.ugc.model.enums.HitMomentEnum;
import com.shuidihuzhu.cf.client.ugc.model.subject.material.MaterialBundleDO;
import com.shuidihuzhu.cf.client.ugc.model.subject.material.MaterialUsageTypeEnum;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.client.ugc.service.CommonOperationRecordClient;
import com.shuidihuzhu.cf.client.ugc.service.MaterialBundleClient;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.WxConstants;
import com.shuidihuzhu.cf.constants.crowdfunding.RaiseCons;
import com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingBaseInfoBackupDao;
import com.shuidihuzhu.cf.delegate.ICfFinanceFundStateDelegate;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.delegate.UserThirdDelegate;
import com.shuidihuzhu.cf.domain.activity.MonthReportSimpleCaseInfo;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.CfPayeeMirrorRemarkEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.facade.CrowdfundingInfoFacadeImpl;
import com.shuidihuzhu.cf.facade.caseinfo.CfInfoViewComparator;
import com.shuidihuzhu.cf.facade.eagle.EagleFacade;
import com.shuidihuzhu.cf.finance.client.feign.CfFinanceCapitalAccountFeignClient;
import com.shuidihuzhu.cf.finance.client.response.FeignResponse;
import com.shuidihuzhu.cf.finance.model.StateMachine;
import com.shuidihuzhu.cf.finance.model.deposit.CfPayeeDepositAccount;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.initialAudit.InitialAuditItem;
import com.shuidihuzhu.cf.model.crowdfunding.vo.*;
import com.shuidihuzhu.cf.mq.producer.impl.MQProdecer;
import com.shuidihuzhu.cf.param.raise.RaiseCaseParam;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.response.crowdfunding.CrowdfundingInfoResponse;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.cf.service.caseinfo.InfoCapitalCompensationService;
import com.shuidihuzhu.cf.service.caseinfo.RaiseDraftService;
import com.shuidihuzhu.cf.service.crowdfunding.casematerial.CfMaterialStatusService;
import com.shuidihuzhu.cf.service.crowdfunding.casematerial.CrowdfundingTreatmentService;
import com.shuidihuzhu.cf.service.crowdfunding.event.SaveDraftEventPublisher;
import com.shuidihuzhu.cf.service.crowdfunding.toufang.CfPrimaryChannelService;
import com.shuidihuzhu.cf.service.crypto.CryptoRelationService;
import com.shuidihuzhu.cf.service.deposit.CfBankCardVerifyService;
import com.shuidihuzhu.cf.service.deposit.CfDepositCommonService;
import com.shuidihuzhu.cf.service.deposit.CfPaDepositPayeeAccountService;
import com.shuidihuzhu.cf.service.label.CfLabelService;
import com.shuidihuzhu.cf.service.msg.MsgClientV2Service;
import com.shuidihuzhu.cf.service.task.CfInfoTaskService;
import com.shuidihuzhu.cf.service.user.UserCfVersionService;
import com.shuidihuzhu.cf.util.*;
import com.shuidihuzhu.cf.util.MobileUtil;
import com.shuidihuzhu.cf.util.crowdfunding.*;
import com.shuidihuzhu.cf.vo.UserInfoVo;
import com.shuidihuzhu.cf.vo.crowdfunding.CrowdfundingBilingualVo;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.cf.vo.initialaudit.CfPropertyInsuranceVO;
import com.shuidihuzhu.client.cf.api.model.MaterialVersion;
import com.shuidihuzhu.client.cf.growthtool.client.CfHospitalNormalFeignClient;
import com.shuidihuzhu.client.constant.CfClientMQTagCons;
import com.shuidihuzhu.client.model.hospital.CfUserSubmitHospitalVo;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.enums.LoginResult;
import com.shuidihuzhu.common.web.model.IdcardInfoExtractor;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.*;
import com.shuidihuzhu.data.analytics.javasdk.core.Analytics;
import com.shuidihuzhu.data.analytics.javasdk.core.UserTagTypeEnum;
import com.shuidihuzhu.data.servicelog.meta.cf.BackupSubmit;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import com.shuidihuzhu.wx.enums.AccountThirdTypeEnum;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.shuidihuzhu.cf.util.MobileUtil.illegal;

/**
 * Created by lixuan on 2016/12/06.
 */
@Slf4j
@RefreshScope
@Service
public class CrowdfundingInfoService {
    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private CrowdfundingAttachmentBiz crowdfundingAttachmentBiz;
    @Resource
    private CfHuKouService cfHuKouService;
    @Autowired
    private CfDepositCommonService cfDepositCommonService;
    @Autowired
    private CrowdfundingTreatmentBiz crowdfundingTreatmentBiz;
    @Autowired
    private CrowdfundingInfoStatusBiz crowdfundingInfoStatusBiz;
    @Autowired
    private CrowdfundingInfoPayeeBiz crowdfundingInfoPayeeBiz;
    @Autowired
    private UserThirdDelegate userThirdDelegate;
    @Autowired
    private CfInfoExtBiz cfInfoExtBiz;
    @Autowired
    private CfInfoOperationRecordBiz cfInfoOperationRecordBiz;
    @Autowired
    private CfRedisKvBiz cfRedisKvBiz;
    @Autowired
    private CrowdfundingOperationBiz crowdfundingOperationBiz;
    @Autowired
    private CfCreditSupplementBiz cfCreditSupplementBiz;
    @Autowired
    private CfInfoStatBiz cfInfoStatBiz;
    @Autowired
    private CfInfoMirrorService cfInfoMirrorService;
    @Autowired
    private CfBaseInfoTemplateRecordBiz cfBaseInfoTemplateRecordBiz;
    @Autowired
    private CfInfoTaskBiz cfInfoTaskBiz;
    @Autowired
    private CfInfoTaskService cfInfoTaskService;
    @Autowired
    private CfRefuseReasonMsgBiz cfRefuseReasonMsgBiz;
    @Autowired
    private CrowdfundingApproveService crowdfundingApproveService;
    @Autowired
    private CrowdfundingBaseInfoBackupDao crowdfundingBaseInfoBackupDao;
    @Autowired
    private CfPrimaryChannelService primaryChannelService;
    @Autowired
    private CryptoRelationService cryptoRelationService;
    @Autowired
    private InitialAuditWorkOrderFeignClient initialAuditWorkOrderFeignClient;
    @Resource(name = "cfRedissonHandler")
    private RedissonHandler cfRedissonHandler;
    @Resource(name = "cfInfoDetailRedissonHandler")
    private RedissonHandler cfInfoDetailRedissonHandler;

    @Autowired
    private CfLabelService labelService;

    @Value("${msg.launch.lazy.channel.prefix:public_mini_app}")
    private String msgLaunchLazyChannelPrefix;

    @Value("${double.language.city.config:{}}")
    private String doubleLanguageCityConfig;

    @Autowired(required = false)
    private Producer producer;
    @Autowired
    private CfFirstApproveBiz cfFirstApproveBiz;

    @Autowired
    private MQProdecer mqProdecer;

    @Autowired
    private Analytics analytics;

    @Autowired
    private ICfFinanceFundStateDelegate financeFundStateDelegate;
    @Autowired
    private CfFinanceCapitalAccountFeignClient cfFinanceCapitalAccountFeignClient;
    @Autowired
    private CrowdfundingTreatmentService crowdfundingTreatmentService;

    @Resource
    private InfoCapitalCompensationService infoCapitalCompensationService;

    @Autowired
    private IPayeeMaterialCenterService payeeMaterialCenterService;

    @Autowired
    private CommonOperationRecordClient commonOperationRecordClient;
    @Autowired
    private CfBankCardVerifyService cfBankCardVerifyService;
    @Autowired
    private CfPaDepositPayeeAccountService cfPaDepositPayeeAccountService;
    @Autowired
    private ShuidiCipher shuidiCipher;


    @Autowired
    private MaterialBundleClient materialBundleClient;

    @Autowired
    private MaterialBundleFeignClient materialBundleFeignClient;

    @Autowired
    private CrowdfundingInfoFacadeImpl crowdfundingInfoFacade;

    @Autowired
    private OldShuidiCipher oldShuidiCipher;
    @Autowired
    private UserCfVersionService userCfVersionService;

    @Autowired
    private ApplicationService applicationService;

    @Autowired
    private CfPropertyInsuranceAuditService insuranceAuditService;

    @Autowired
    private RaiseDraftService raiseDraftService;

    @Autowired
    private CfHospitalNormalFeignClient cfHospitalNormalFeignClient;

    @Autowired
    private JsonBundleUtils jsonBundleUtils;

    @Autowired
    private CrowdfundingInfoSimpleBiz crowdfundingInfoSimpleBiz;

    @Autowired
    private CfCommonStoreService cfCommonStoreService;

    @Autowired
    private CfMaterialStatusService materialStatusService;

    @Autowired
    private CfDiseaseNormService cfDiseaseNormService;

    @Resource
    private MsgClientV2Service msgClientV2Service;
    @Resource
    private EagleFacade eagleFacade;
    @Resource
    private CrowdfundingAuthorBiz crowdfundingAuthorBiz;

    @Autowired
    private UserInfoDelegate userInfoDelegate;

    @Autowired
    private CrowdfundingInfoDetailService crowdfundingInfoDetailService;

    public static final String CROWDFUNDING_CLEW_BARRAGE_CASE_KEY = "crowdfunding_clew_barrage_case_key";



    /**
     * 需要过滤的字段
     */
    private static final Set<String> FILTER_KEY_SET = Sets.newHashSet(
            "title",
            "content",
            "attachments",
            "targetAmount",
            "rejectDetails",
            "propertyInsuranceParam",
            "preAuditImageUrl",
            "povertyImageUrl"
    );

    /**
     * 需要加密存储的字段
     */
    private static final Set<String> SENSITIVE_KEY_SET = Sets.newHashSet(
            "selfIdCard",
            "patientIdCard",
            "patientBornCard"
    );

    private static final String CREDIT_INFO_JSON_PREFIX = "credit_info_";
    private static final String PAGE_FILL_IN_DETAIL_PREFIX = "page_fill_detail_";

    @Autowired
    private SaveDraftEventPublisher saveDraftEventPublisher;

    public static void trimEmptyChar(CrowdfundingInfoBaseVo cfInfoBaseVo) {
        if (cfInfoBaseVo.getSelfIdCard() != null) {
            cfInfoBaseVo.setSelfIdCard(cfInfoBaseVo.getSelfIdCard().trim());
        }
        if (cfInfoBaseVo.getSelfRealName() != null) {
            cfInfoBaseVo.setSelfRealName(cfInfoBaseVo.getSelfRealName().trim());
        }
        if (cfInfoBaseVo.getPatientIdCard() != null) {
            cfInfoBaseVo.setPatientIdCard(cfInfoBaseVo.getPatientIdCard().trim());
        }
        if (cfInfoBaseVo.getPatientRealName() != null) {
            cfInfoBaseVo.setPatientRealName(cfInfoBaseVo.getPatientRealName().trim());
        }
    }

    public CrowdfundingInfoResponse addOrUpdateBaseInfoAttachment(CrowdfundingInfo crowdfundingInfo, List<String> attachments,long currentUserId) {
        String infoUuid = crowdfundingInfo.getInfoId();
        log.info("addOrUpdateBaseInfoAttachment infoUuid:{}", infoUuid);
        CrowdfundingInfoResponse response = new CrowdfundingInfoResponse();
        response.setInfoUuid(infoUuid);
        //若当前案例处于已提交或者是审核通过状态  不让用户更新
        if (crowdfundingInfo.getStatus() == CrowdfundingStatus.SUBMITTED ||
                crowdfundingInfo.getStatus() == CrowdfundingStatus.CROWDFUNDING_STATED) {
            log.info("addOrUpdateBaseInfo info status is:{} infoUuid:{}", crowdfundingInfo.getStatus().value(), infoUuid);
            response.setErrorCode(CfErrorCode.CF_INFO_SUBMITTED);
            return response;
        }
        long userId = currentUserId;
        CfErrorCode verifyResult = verifyInfoId(crowdfundingInfo, userId);
        if (!verifyResult.equals(CfErrorCode.SUCCESS)) {
            response.setErrorCode(verifyResult);
            return response;
        }
        CfOperatingRecord cfOperatingRecord = this.cfInfoMirrorService.before(crowdfundingInfo,
                crowdfundingInfo.getUserId(), crowdfundingInfo.getPayeeName(),
                CfOperatingRecordEnum.Type.SUBMIT_BASE, CfOperatingRecordEnum.Role.USER);

        // 创建图文工单
        Message<CfOperatingRecord> message = new Message<>(MQTopicCons.CF, MQTagCons.CF_UPDATE_BASE_INFO_MSG,
                MQTagCons.CF_UPDATE_BASE_INFO_MSG + "_" + cfOperatingRecord.getId(),
                cfOperatingRecord);
        producer.send(message);
        this.cfInfoMirrorService.after(cfOperatingRecord);
        return response;
    }

    public int getStatusAfterFinish(String infoUuid, Date endTime) {
        if (StringUtils.isBlank(infoUuid)) {
            return 0;
        }

        CfInfoExt cfInfoExt = this.cfInfoExtBiz.getByInfoUuid(infoUuid);
        if (cfInfoExt == null) {
            return 0;
        }

        return getStatusAfterFinish(cfInfoExt, endTime);
    }

    /**
     *
     * @param cfInfoExt
     * @param endTime crowdfunding_info 的结束时间
     * @return
     */
    public int getStatusAfterFinish(CfInfoExt cfInfoExt, Date endTime) {
        if (cfInfoExt == null) {
            return 0;
        }
        int code = getStatusAfterFinishByInfoExt(cfInfoExt);
        // 未结束
        if (endTime != null && endTime.after(new Date())) {
            return code;
        } else if (code == 0 ){
            return CfDisplayStatusEnum.FINISH_EXPIRE.getValue();
        }
        return code;
    }

    public void fillStatusAndModifyListOrder(List<String> infoUuids,
                                                    List<CrowdfundingInfoView> crowdfundingInfoList,
                                                    long userId) {
        if (CollectionUtils.isEmpty(infoUuids)) {
            return;
        }

        List<CfInfoExt> cfInfoExts = this.cfInfoExtBiz.getListByInfoUuids(infoUuids);
        if (CollectionUtils.isEmpty(cfInfoExts)) {
            return;
        }
        fillDisplayStatusAndCaseType(cfInfoExts, crowdfundingInfoList);

        fillFirstApproveStatus(cfInfoExts, crowdfundingInfoList);

        modifyListOrder(cfInfoExts, crowdfundingInfoList, userId);
    }

    private void fillFirstApproveStatus(List<CfInfoExt> cfInfoExts, List<CrowdfundingInfoView> crowdfundingInfoList) {
        if (CollectionUtils.isEmpty(cfInfoExts) || CollectionUtils.isEmpty(crowdfundingInfoList)) {
            return;
        }

        Map<String, CfInfoExt> infoUuidMap = Maps.newHashMap();
        for (CfInfoExt cfExt : cfInfoExts) {
            infoUuidMap.put(cfExt.getInfoUuid(), cfExt);
        }

        Iterator<CrowdfundingInfoView> iterator = crowdfundingInfoList.iterator();
        while (iterator.hasNext()) {
            CrowdfundingInfoView cfInfoView = iterator.next();
            CfInfoExt infoExt = infoUuidMap.get(cfInfoView.getInfoId());
            if (infoExt == null) {
                continue;
            }
            FirstApproveStatusEnum enumValue = null;
            try {
                enumValue = FirstApproveStatusEnum.parse(infoExt.getFirstApproveStatus());
            } catch (Exception e) {
                log.error("案例首次审核状态异常, value:{}", infoExt.getFirstApproveStatus(), e);
                enumValue = FirstApproveStatusEnum.APPLY_SUCCESS;
            }
            cfInfoView.setFirstApproveStatus(enumValue);
        }
    }

    private void modifyListOrder(List<CfInfoExt> cfInfoExts, List<CrowdfundingInfoView> crowdfundingInfoList, long userId) {
        if (CollectionUtils.isEmpty(crowdfundingInfoList)) {
            return;
        }

        //1.未结束 优先 结束的案例
        //2.创建时间倒序
        crowdfundingInfoList.sort(new CfInfoViewComparator());

        Map<String, CfInfoExt> infoUuidMap = Maps.newHashMap();
        cfInfoExts.forEach(cfInfoExt -> infoUuidMap.put(cfInfoExt.getInfoUuid(), cfInfoExt));

        List<CfFirsApproveMaterial> cfFirsApproveList = cfFirstApproveBiz.getByUserId(userId);
        CfFirstApproveBiz.CaseRejectObject lastRejectObject = cfFirstApproveBiz.getLastRejectCase(cfFirsApproveList, infoUuidMap);

        List<CrowdfundingInfoView> applyFailViews = Lists.newArrayList();

        Iterator<CrowdfundingInfoView> iterator = crowdfundingInfoList.iterator();
        Date now = new Date();
        while (iterator.hasNext()) {
            CrowdfundingInfoView cfInfoView = iterator.next();

            if (now.after(cfInfoView.getEndTime())) {
                continue;
            }

            FirstApproveStatusEnum firstApproveStatusEnum = cfInfoView.getFirstApproveStatus();
            if (firstApproveStatusEnum == FirstApproveStatusEnum.APPLY_FAIL) {
                iterator.remove();
                if (!(lastRejectObject != null && StringUtils.isNotEmpty(lastRejectObject.getInfoId())
                        && cfInfoView.getInfoId().equals(lastRejectObject.getInfoId()))) {
                    applyFailViews.add(cfInfoView);
                }
            }
        }
        // 取不是最后初审未过的案例放在第一个
        if (CollectionUtils.isNotEmpty(applyFailViews)) {
            crowdfundingInfoList.addAll(0, applyFailViews);
        }
    }

    private void fillDisplayStatusAndCaseType(List<CfInfoExt> cfInfoExts, List<CrowdfundingInfoView> crowdfundingInfoList) {
        Map<String, CfInfoExt> infoUuIdMappings = Maps.newHashMap();
        for (CfInfoExt cfInfoExt : cfInfoExts) {
            infoUuIdMappings.put(cfInfoExt.getInfoUuid(), cfInfoExt);
        }

        if (CollectionUtils.isEmpty(crowdfundingInfoList)) {
            return;
        }

        for (CrowdfundingInfoView infoView : crowdfundingInfoList) {
            CfInfoExt cfInfoExt = infoUuIdMappings.get(infoView.getInfoId());
            int displayStatus = this.getStatusAfterFinish(cfInfoExt,infoView.getEndTime());
            infoView.setDisplayStatus(displayStatus);

            int caseType = insuranceAuditService.selectPropertyCaseType(cfInfoExt);
            infoView.setCaseRaiseMaterialType(caseType);
            // 筹款列表页案例的标签展示
            infoView.setCaseTagStatus(queryCaseTagStatus(caseType, infoView.getMaterialStatus(), infoView.getStatus()));
        }

    }

    private int queryCaseTagStatus(int caseType, InitialAuditItem.MaterialStatus insuranceStatus, int status) {

        // 增信材料不是 做为单独的一项材料的提交
        if (caseType != PropertyInsuranceCaseType.MATERIAL_SUBMIT_INSURANCE.getCode()) {
            return status;
        }

        if (status == CrowdfundingStatus.CROWDFUNDING_STATED.value()
                && insuranceStatus == InitialAuditItem.MaterialStatus.PASS) {
            return CrowdfundingStatus.CROWDFUNDING_STATED.value();

        } else if (status == CrowdfundingStatus.APPROVE_DENIED.value()
                || insuranceStatus == InitialAuditItem.MaterialStatus.REJECT) {
            return CrowdfundingStatus.APPROVE_DENIED.value();

        } else if (status == CrowdfundingStatus.SUBMITTED.value()
                && insuranceStatus == InitialAuditItem.MaterialStatus.SUBMIT) {
            return CrowdfundingStatus.SUBMITTED.value();
        }

        return CrowdfundingStatus.APPROVE_PENDING.value();
    }

    private int getStatusAfterFinishByInfoExt(CfInfoExt cfInfoExt) {
        if (cfInfoExt.getTransferStatus() != 0) { // 已经申请提现
            if (cfInfoExt.getTransferStatus() == CfTransferStatus.RAISER_APPLIED.getValue()) { // 申请提现
                return CfDisplayStatusEnum.TRANSFER_APPLIED.getValue();
            } else if (cfInfoExt.getTransferStatus() == CfTransferStatus.APPLICATION_PASSED.getValue()) { // 提现申请审核通过
                return CfDisplayStatusEnum.TRANSFER_APPLY_AUDITTED.getValue();
            } else if (cfInfoExt.getTransferStatus() == CfTransferStatus.TRANSFERRED.getValue()) { // 已提现
                return CfDisplayStatusEnum.TRANSFER_PROCESSING.getValue();
            }
            return 0;
        } else { // 已经申请退款
            if (cfInfoExt.getRefundStatus() != 0) {
                if (cfInfoExt.getRefundStatus() == CfRefundStatus.REFUND_APPLIED.getValue()) {
                    return CfDisplayStatusEnum.REFUND_APPLIED.getValue();
                } else if (cfInfoExt.getRefundStatus() == CfRefundStatus.REFUND_BY_PLATFORM.getValue()) {
                    return CfDisplayStatusEnum.REFUND_BY_OPERATOR.getValue();
                } else if (cfInfoExt.getRefundStatus() == CfRefundStatus.REFUND_USER_APPLIED_HANDLED.getValue()) {
                    return CfDisplayStatusEnum.REFUND_PROCESSING.getValue();
                } else if (cfInfoExt.getRefundStatus() == CfRefundStatus.REFUND_BY_PLATFORM_HANDLED.getValue()) {
                    return CfDisplayStatusEnum.REFUND_FOR_REJECTED_CASE_PROCESSING.getValue();
                } else {
                    return 0;
                }
            } else { // 其他结束逻辑
                if (cfInfoExt.getFinishStatus() == CfFinishStatus.FINISH_BY_RAISER.getValue()) {
                    return CfDisplayStatusEnum.FINISH_BY_RAISER.getValue();
                } else if (cfInfoExt.getFinishStatus() == CfFinishStatus.FINISH_BY_SHUIDI.getValue()) {
                    return CfDisplayStatusEnum.FINISH_BY_OPERATOR.getValue();
                } else if (cfInfoExt.getFinishStatus() == CfFinishStatus.FUNDING_REACH_TARGET.getValue()) {
                    return CfDisplayStatusEnum.FINISH_AMOUNT_REACH_TARGET.getValue();
                } else if (cfInfoExt.getFinishStatus() == CfFinishStatus.EXPIRE.getValue()) {
                    return CfDisplayStatusEnum.FINISH_EXPIRE.getValue();
                }
                return 0;
            }
        }
    }

    public void sendAddTagMQ(Integer userThirdType, UserThirdModel userThirdModel, String infoUuid) {
        try {
            if (userThirdType != null && userThirdModel != null) {
                try {
                    UserInfoVo userInfoVo = new UserInfoVo(userThirdModel.getUserId(), userThirdModel.getOpenId(), UserTagGroup.BASE_INFO, userThirdType);
                    log.info("add tag to base info user userInfoVo:{}", userInfoVo);
                    producer.send(new Message<>(MQTopicCons.CF,
                            MQTagCons.CF_ADD_WX_TAG_TO_USER, MQTagCons.CF_ADD_WX_TAG_TO_USER + "-" + infoUuid
                            + "-" + System.currentTimeMillis(), userInfoVo, DelayLevel.S1));
                } catch (Exception e) {
                    log.error("base info user add wx tag error", e);
                }
            }
        } catch (Exception e) {
            log.error("sendAddTagMQ error", e);
        }
    }

    // 查看一个案例是否需要发送创建成功消息
    public boolean shouldSendLaunchMsg(String channel) {
        try {
            if (!StringUtils.isEmpty(channel)) {
                String prefixes = msgLaunchLazyChannelPrefix;
                if (!StringUtils.isEmpty(prefixes)) {
                    List<String> prefixList = Splitter.on(",").splitToList(prefixes);
                    if (!CollectionUtils.isEmpty(prefixList)) {
                        for (String prefix : prefixList) {
                            prefix = prefix.trim();
                            if (!StringUtils.isEmpty(prefix) && channel.startsWith(prefix)) {
                                return false;
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("shouldSendLaunchMsg channel={}", channel, e);
        }
        return true;
    }

    @Deprecated
    // 实物筹款
    public void createExtData(CrowdfundingInfo crowdfundingInfo, String selfTag, Integer userThirdType,
                              String registerMobile) {
        createExtData(crowdfundingInfo, selfTag, userThirdType, registerMobile, "", "", "", 0L);
    }

    /**
     * 鹰眼分流
     *
     * @param userId
     * @return
     */
    @Deprecated
    public int eagle(long userId) {
        return CfVersion.definition_20181131.getCode();
    }

    @Deprecated
    public void createExtData(CrowdfundingInfo crowdfundingInfo, String selfTag, Integer userThirdType,
                              String registerMobile, String volunteerUniqueCode, String clientIp, String appVersion, Long preId) {
        // 添加资金账户
        try {
            FeignResponse<Integer> response1 = cfFinanceCapitalAccountFeignClient
                    .saveCapitalAccount(crowdfundingInfo.getInfoId());
            if (response1 == null || response1.notOk() || response1.getData() != 1) {
                log.error("资金账户添加失败infoId: {}", crowdfundingInfo.getInfoId());
            }
        } catch (Exception e) {
            log.error("finance服务异常,", e);
        }

        // 添加扩展表
        CfInfoExt cfInfoExt = new CfInfoExt();
        try {
            cfInfoExt.setInfoUuid(crowdfundingInfo.getInfoId());
            cfInfoExt.setSelfTag(StringUtils.trimToEmpty(selfTag));
            cfInfoExt.setProductName(CrowdfundingUtil.getProductName(crowdfundingInfo));
            cfInfoExt.setUserThirdType(userThirdType == null ? 0 : userThirdType);
            cfInfoExt.setClientIp(clientIp == null ? "" : clientIp);
            //如果不是app 走鹰眼分流
            if (StringUtils.isEmpty(appVersion)) {
                cfInfoExt.setCfVersion(eagle(crowdfundingInfo.getUserId()));
            } else {
                if (AppVersionUitl.greater(appVersion)) {
                    cfInfoExt.setCfVersion(CfVersion.definition_20181131.getCode());
                } else {
                    cfInfoExt.setCfVersion(CfVersion.new_20181120.getCode());
                }
            }
            if (!illegal(registerMobile)) {
                cfInfoExtBiz.setRegisterMobile(cfInfoExt, StringUtils.trimToEmpty(registerMobile));
            } else {
                cfInfoExtBiz.setRegisterMobile(cfInfoExt, "");
            }

            String primaryChannel = this.primaryChannelService.getPrimaryChannel(crowdfundingInfo);
            cfInfoExt.setPrimaryChannel(primaryChannel);
            cfInfoExt.setPreId(preId == null ? 0 : preId);

            cfInfoExt.setVolunteerUniqueCode(StringUtils.trimToEmpty(volunteerUniqueCode));
            cfInfoExt.setCaseId(crowdfundingInfo.getId());
            this.cfInfoExtBiz.add(cfInfoExt, CrowdfundingType.fromValue(crowdfundingInfo.getType()),
                    crowdfundingInfo.getUserId());
        } catch (Exception e) {
            log.error("", e);
        }

        // 添加状态表
        financeFundStateDelegate.addFundState(crowdfundingInfo.getId(), crowdfundingInfo.getInfoId(), StateMachine.State.INITIAL.getCode(),
                StateMachine.StateStop.NONE.getCode(), CfPayeeMirrorRemarkEnum.CF_STATE_ADD, cfInfoExt.getProductName());

        // 添加统计
        try {
            this.cfInfoStatBiz.add(CfInfoStat.getDefault(crowdfundingInfo.getId()));
        } catch (Exception e) {
            log.error("", e);
        }

        // 创建筹款任务
        try {
            this.cfInfoTaskService.buildAllTask(crowdfundingInfo);
        } catch (Exception e) {
            log.error("", e);
        }

    }

    private void recordSubmitOperation(String infoUuid, int caseId) {
        try {
            this.cfInfoOperationRecordBiz.insertOperationRecord(
                    new CfInfoOperationRecord(infoUuid, CfInfoOperationRecordEnum.SUBMIT_FOR_REVIEW));
            // 记录最后一次操作时间
            this.crowdfundingOperationBiz.updateCommitTime(infoUuid, caseId, DateUtil.nowTime());
        } catch (Exception e) {
            log.error("CrowdfundingInfoService submitForReview saveInfoOperationRecord error!", e);
        }
    }

    /**
     * 兼容现有逻辑  转换成vo
     * @param crowdfundingInfoPayee
     * @param userId
     * @param list
     * @return
     */
    public Response<CrowdfundingInfoResponse.Data> updatePayeeInfo(CrowdfundingInfoPayee crowdfundingInfoPayee ,
                                                                   long userId,
                                                                   List<CfCommonStoreModel> list,
                                                                   MaterialsParam materialsParam,
                                                                   String appVersion){
        int hasProveType = materialsParam.getHasProveType();
        CrowdfundingInfoPayeeVo crowdfundingInfoPayeeVo = new CrowdfundingInfoPayeeVo();
        BeanUtils.copyProperties(crowdfundingInfoPayee,crowdfundingInfoPayeeVo);

        crowdfundingInfoPayeeVo.setPayeeName(crowdfundingInfoPayee.getName());
        crowdfundingInfoPayeeVo.setIdType(UserIdentityType.getByCode(crowdfundingInfoPayee.getIdType()));
        crowdfundingInfoPayeeVo.setRelationType(CrowdfundingRelationType.getByCode(crowdfundingInfoPayee.getRelationType()));
        crowdfundingInfoPayeeVo.setMobile(shuidiCipher.decrypt(crowdfundingInfoPayee.getMobile()));
        crowdfundingInfoPayeeVo.setBankCard(shuidiCipher.decrypt(crowdfundingInfoPayee.getBankCard()));
        crowdfundingInfoPayeeVo.setIdCard(shuidiCipher.decrypt(crowdfundingInfoPayee.getIdCard()));
        if (StringUtils.isNotEmpty(crowdfundingInfoPayeeVo.getEmergencyPhone())){
            crowdfundingInfoPayeeVo.setEmergencyPhone(shuidiCipher.decrypt(crowdfundingInfoPayeeVo.getEmergencyPhone()));
        }

        Map<AttachmentTypeEnum, List<CrowdfundingAttachmentVo>> map = crowdfundingAttachmentBiz.getFundingAttachmentMap(crowdfundingInfoPayee.getCaseId());

        List<AttachmentTypeEnum> provesList =  Lists.newArrayList(  AttachmentTypeEnum.ATTACH_PAYEE_HUKOUBEN, AttachmentTypeEnum.ATTACH_PAYEE_JIEHUNZHENG,
                AttachmentTypeEnum.ATTACH_PAYEE_CHUSHENGZHENG, AttachmentTypeEnum.ATTACH_PAYEE_ZHENGMING, AttachmentTypeEnum.ATTACH_PAYEE_WEITUOSHU);
        //证明图片
        List<ProveVo> proves = provesList.stream().filter(r->CollectionUtils.isNotEmpty(map.get(r))).map(r->{
            ProveVo v = new ProveVo();
            v.setRelType(r.value());
            List<CrowdfundingAttachmentVo> l = map.get(r);
            List<String> attachments = l.stream().map(CrowdfundingAttachmentVo::getUrl).collect(Collectors.toList());
            v.setAttachments(attachments);
            return v;
        }).collect(Collectors.toList());
        crowdfundingInfoPayeeVo.setProves(proves);
        //身份证
        List<AttachmentTypeEnum> authenticationList =  Lists.newArrayList(AttachmentTypeEnum.ATTACH_PAYEE_ONLY_ID_CARD);
        List<ProveVo> authentication = authenticationList.stream().filter(r->CollectionUtils.isNotEmpty(map.get(r))).map(r->{
            ProveVo v = new ProveVo();
            v.setRelType(r.value());
            List<CrowdfundingAttachmentVo> l = map.get(r);
            List<String> attachments = l.stream().map(CrowdfundingAttachmentVo::getUrl).collect(Collectors.toList());
            v.setAttachments(attachments);
            return v;
        }).collect(Collectors.toList());
        crowdfundingInfoPayeeVo.setAuthentication(authentication);
        //手持身份证照片
        List<CrowdfundingAttachmentVo> cardList = map.get(AttachmentTypeEnum.ATTACH_PAYEE_ID_CARD);
        if (CollectionUtils.isNotEmpty(cardList)){
            crowdfundingInfoPayeeVo.setIdCardPhoto(cardList.get(0).getUrl());
        }
        List<CrowdfundingAttachmentVo> faceList = map.get(AttachmentTypeEnum.ATTACH_PAYEE_FACE_ID);
        if (CollectionUtils.isNotEmpty(faceList)){
            crowdfundingInfoPayeeVo.setIdCardPhoto(faceList.get(0).getUrl());
        }

        //如果没上传关系证明  才需要查询
        if (hasProveType == 0){
            //视频
            List<CrowdfundingAttachmentVo> videos = map.get(AttachmentTypeEnum.ATTACH_PAYEE_RELATION_VIDEO);
            if (CollectionUtils.isNotEmpty(videos)){
                crowdfundingInfoPayeeVo.setRelationVideos(videos.stream().map(CrowdfundingAttachmentVo::getUrl).collect(Collectors.toList()));
            }

            //合照
            List<CrowdfundingAttachmentVo> wts = map.get(AttachmentTypeEnum.ATTACH_PAYEE_WEITUOSHU_HEZHAO);
            if (CollectionUtils.isNotEmpty(wts)){
                crowdfundingInfoPayeeVo.setWeituoshuHezhao(wts.get(0).getUrl());
            }
        }
        //如果驳回的时候上传了身份证图片
        if (materialsParam.getCommonIdPic() == 1){
            crowdfundingInfoPayeeVo.setFaceIdResult(0);
        }

        //用提交参数覆盖
        list.stream().forEach(r-> BeenCopyUtil.jsonToClass(crowdfundingInfoPayeeVo,r.getStoreValue(),CrowdfundingInfoPayeeVo.class));
        //兼容前端驳回uuid没有的情况
        crowdfundingInfoPayeeVo.setInfoUuid(crowdfundingInfoPayee.getInfoUuid());

        return savePayeeInfo(crowdfundingInfoPayeeVo,userId,appVersion);
    }

    public Response<CrowdfundingInfoResponse.Data> savePayeeInfo(CrowdfundingInfoPayeeVo crowdfundingInfoPayeeVo,long userId,String appVersion){

        if (crowdfundingInfoPayeeVo == null || crowdfundingInfoPayeeVo.getInfoUuid() == null) {
            return NewResponseUtil.makeResponse(CfErrorCode.SYSTEM_PARAM_ERROR.getCode(),
                    CfErrorCode.SYSTEM_PARAM_ERROR.getMsg(), null);
        }

        if (userId <= 0) {
            log.warn("CrowdfundingV4InfoUpdateController addOrUpdatePayeeInfo userId is {}", userId);
            return NewResponseUtil.makeResponse(LoginResult.LOGIN.code, LoginResult.LOGIN.msg, null);
        }
        CfInfoSimpleModel cfInfoSimpleModel = crowdfundingInfoSimpleBiz.getFundingInfo(crowdfundingInfoPayeeVo.getInfoUuid());
        if (null == cfInfoSimpleModel || userId != cfInfoSimpleModel.getUserId()) {
            return NewResponseUtil.makeError(CfErrorCode.CF_NOT_FOUND);
        }
        log.info("CrowdfundingV4InfoUpdateController addOrUpdatePayeeInfo crowdfundingInfoPayeeVo:{}",
                crowdfundingInfoPayeeVo);
        String payeeName = StringUtils.trim(crowdfundingInfoPayeeVo.getPayeeName());
        crowdfundingInfoPayeeVo.setPayeeName(StringUtils.trimToEmpty(payeeName));
        String mobile = StringUtils.trim(crowdfundingInfoPayeeVo.getMobile());
        String bankCard = StringUtils.trim(crowdfundingInfoPayeeVo.getBankCard());
        String bankName = StringUtils.trim(crowdfundingInfoPayeeVo.getBankName());
        // String bankBranchName = crowdfundingInfoPayeeVo.getBankBranchName();
        String idCard = StringUtils.trim(crowdfundingInfoPayeeVo.getIdCard());
        String idCardPhoto = StringUtils.trim(crowdfundingInfoPayeeVo.getIdCardPhoto());
        UserIdentityType idType = crowdfundingInfoPayeeVo.getIdType();
        CrowdfundingRelationType relationType = crowdfundingInfoPayeeVo.getRelationType();
        List<String> attachments = crowdfundingInfoPayeeVo.getAttachments();
        if (StringUtils.isBlank(bankName) || StringUtils.isBlank(payeeName) || StringUtils.isBlank(mobile)
                || relationType == null) {
            return NewResponseUtil.makeError(CfErrorCode.CF_INFO_PARAM_ERROR_PAYEE);
        }
        if (StringUtils.isBlank(bankCard) || !BackCardUtil.checkBankCard(bankCard)) {
            return NewResponseUtil.makeError(CfErrorCode.CF_BANK_CARD_VERIFY_FAILED);
        }
        if (MobileUtil.illegal(mobile)) {
            return NewResponseUtil.makeError(CfErrorCode.USER_ACCOUNT_MOBILE_ERROR);
        }
        if (StringUtils.isNotEmpty(crowdfundingInfoPayeeVo.getEmergencyPhone()) && MobileUtil.illegal(crowdfundingInfoPayeeVo.getEmergencyPhone())){
            return NewResponseUtil.makeError(CfErrorCode.USER_ACCOUNT_MOBILE_ERROR);
        }
        // 判断用户是否可以提交收款人关系视频
        // 新版本不判断
        if (!MaterialPlanVersion.is0906MaterialAudit(cfInfoSimpleModel.getMaterialPlanId())){
            if (!crowdfundingInfoPayeeBiz.checkCanSubmitRelationVideo(crowdfundingInfoPayeeVo) ) {
                return NewResponseUtil.makeError(CfErrorCode.SUBMIT_PAYEE_INFO_FAILED);
            }
        }
        if (relationType == CrowdfundingRelationType.self) {
            if (StringUtils.isEmpty(idCard) || idType == null) {
                return NewResponseUtil.makeError(CfErrorCode.CF_INFO_PARAM_ERROR_PAYEE);
            }
            if ((idType == UserIdentityType.identity && IdCardUtil.illegal(idCard))
                    || (idType != UserIdentityType.identity && !idCard.matches("[a-zA-Z0-9]{1,30}"))) {
                return NewResponseUtil.makeError(CfErrorCode.CF_PARAM_ERROR_PAYEE_ID_CARD_ERROR);
            }

        } else {
            if (StringUtils.isEmpty(idCard) || idType == null) {
                return NewResponseUtil.makeError(CfErrorCode.CF_INFO_PARAM_ERROR_PAYEE);
            }

            if (!crowdfundingInfoPayeeVo.faceIdResultPass() && StringUtils.isEmpty(idCardPhoto)){
                return NewResponseUtil.makeError(CfErrorCode.CF_INFO_PARAM_ERROR_PAYEE);
            }

            if ((idType == UserIdentityType.identity && IdCardUtil.illegal(idCard))
                    || (idType != UserIdentityType.identity && !idCard.matches("[a-zA-Z0-9]{1,30}"))) {
                return NewResponseUtil.makeError(CfErrorCode.CF_PARAM_ERROR_PAYEE_ID_CARD_ERROR);
            }
            if (!MaterialPlanVersion.is0906MaterialAudit(cfInfoSimpleModel.getMaterialPlanId())){

                if (CollectionUtils.isEmpty(attachments) && CollectionUtils.isEmpty(crowdfundingInfoPayeeVo.getRelationVideos())) {
                    return NewResponseUtil.makeError(CfErrorCode.CF_PARAM_ERROR_MISS_PAYEE_RELATION_PICS);
                }
            }
            if (MaterialPlanVersion.is0906MaterialAudit(cfInfoSimpleModel.getMaterialPlanId())){
                if (CollectionUtils.isEmpty(crowdfundingInfoPayeeVo.getProves()) && CollectionUtils.isEmpty(crowdfundingInfoPayeeVo.getRelationVideos())) {
                    return NewResponseUtil.makeError(CfErrorCode.CF_PARAM_ERROR_MISS_PAYEE_RELATION_PICS);
                }
                if (CollectionUtils.isNotEmpty(crowdfundingInfoPayeeVo.getProves())){
                    Optional optional = crowdfundingInfoPayeeVo.getProves().stream().filter(r->CollectionUtils.isEmpty(r.getAttachments())).findAny();
                    if (optional.isPresent()){
                        return NewResponseUtil.makeError(CfErrorCode.CF_PARAM_ERROR_MISS_PAYEE_RELATION_PICS);
                    }
                }

                if (crowdfundingInfoPayeeVo.getRelativesType() <= 0){
                    return NewResponseUtil.makeError(CfErrorCode.PAYEE_RELATION_ERROR);
                }
            }
        }

        //身份证不足18位不让提交（过滤掉15位的身份证）
        if (idType == UserIdentityType.identity && !CfIdCardUtil.isValidIdCard(idCard)) {
            return NewResponseUtil.makeError(CfErrorCode.USER_INFO_ID_CARD_ERROR);
        }
        if (idType == UserIdentityType.identity && CfIdCardUtil.getCurrentAge(idCard) < 18) {
            return NewResponseUtil.makeError(CfErrorCode.PAYEE_NOT_ALLOW_OF_AGE);
        }
        // 替换为 大写X
        idCard = idCard.replaceAll("x","X");
        crowdfundingInfoPayeeVo.setIdCard(idCard);
        CrowdfundingInfoResponse response = addOrUpdatePayeeInfo(userId, crowdfundingInfoPayeeVo,appVersion);
        return NewResponseUtil.makeResponse(response.getErrorCode().getCode(), response.getErrorCode().getMsg(),
                response.getData());
    }

    /**
     * 填写收款人信息
     *
     * @param userId
     * @param crowdfundingInfoPayeeVo
     * @return
     */
    public CrowdfundingInfoResponse addOrUpdatePayeeInfo(long userId, CrowdfundingInfoPayeeVo crowdfundingInfoPayeeVo,String appVersion) {
        String infoUuid = crowdfundingInfoPayeeVo.getInfoUuid();
        CrowdfundingInfo crowdfundingInfo = this.crowdfundingInfoBiz.getFundingInfo(infoUuid);
        //在 银行卡鉴权时使用
        CrowdfundingInfo crowdfundingInfoOrg = new CrowdfundingInfo();
        BeanUtils.copyProperties(crowdfundingInfo, crowdfundingInfoOrg);
        if (crowdfundingInfo != null) {
            //若当前案例处于已提交或者是审核通过状态  不让用户更新
            if (crowdfundingInfo.getStatus() == CrowdfundingStatus.SUBMITTED ||
                    crowdfundingInfo.getStatus() == CrowdfundingStatus.CROWDFUNDING_STATED) {
                log.info("addOrUpdateBaseInfo info status is:{} infoUuid:{}", crowdfundingInfo.getStatus().value(), infoUuid);
                CrowdfundingInfoResponse response = new CrowdfundingInfoResponse();
                response.setErrorCode(CfErrorCode.CF_INFO_SUBMITTED);
                response.setInfoUuid(crowdfundingInfo.getInfoId());
                return response;
            }
        }

        CfErrorCode verifyResult = verifyInfoId(crowdfundingInfo, userId);
        if (!verifyResult.equals(CfErrorCode.SUCCESS)) {
            CrowdfundingInfoResponse response = new CrowdfundingInfoResponse();
            response.setErrorCode(verifyResult);
            return response;
        }

        int crowdfundingId = crowdfundingInfo.getId();
        String idCard = StringUtils.trim(crowdfundingInfoPayeeVo.getIdCard());
        String cryptoIdCard = oldShuidiCipher.aesEncrypt(idCard);
        UserIdentityType idType = crowdfundingInfoPayeeVo.getIdType();
        String idCardPhoto = StringUtils.trim(crowdfundingInfoPayeeVo.getIdCardPhoto());
        String bankCard = StringUtils.trim(crowdfundingInfoPayeeVo.getBankCard());
        String cryptoBankCard = oldShuidiCipher.aesEncrypt(bankCard);
        String bankName = StringUtils.trim(crowdfundingInfoPayeeVo.getBankName());
        String mobile = StringUtils.trim(crowdfundingInfoPayeeVo.getMobile());
        String cryptoMobile = oldShuidiCipher.aesEncrypt(mobile);
        String payeeName = StringUtils.trim(crowdfundingInfoPayeeVo.getPayeeName());
        List<String> attachments = crowdfundingInfoPayeeVo.getAttachments();
        CrowdfundingRelationType relationType = crowdfundingInfoPayeeVo.getRelationType();

        crowdfundingInfo.setPayeeName(StringUtils.trimToEmpty(payeeName));
        crowdfundingInfo.setPayeeIdCard(cryptoIdCard);
        crowdfundingInfo.setPayeeBankName(bankName);
        crowdfundingInfo.setPayeeBankBranchName("");
        crowdfundingInfo.setPayeeBankCard(cryptoBankCard);
        crowdfundingInfo.setPayeeMobile(cryptoMobile);
        crowdfundingInfo.setRelationType(relationType);
        crowdfundingInfo.setRelation(relationType.getDescription());
        CrowdfundingInfoPayee crowdfundingInfoPayee = this.crowdfundingInfoPayeeBiz.getByInfoUuid(infoUuid);

        // 银行卡鉴权 新的四要素，旧的三要素
        Response<CrowdfundingBankVerifyResultVo> resultVoResponse;
        CrowdfundingInfoResponse response = new CrowdfundingInfoResponse();
        if (cfDepositCommonService.isNeedDepositWithAcc(crowdfundingInfo.getId())) {

            // TODO 非三点几的版本提示 去H5 在 移动端上线后下掉
            if (StringUtils.isNotEmpty(appVersion) && !appVersion.startsWith("3")) {
                response.setErrorCode(CfErrorCode.CF_INFO_VERSION_ERROR_CREDIT_SUPPLEMENT);
                return response;
            }
            Response<CfPayeeDepositAccount> accountResponse = cfPaDepositPayeeAccountService.getOpenSuccessAccount(
                    payeeName, bankCard, idCard);
            log.info("openWithResponse:{}", JSON.toJSONString(accountResponse));
            if (accountResponse.notOk()) {
                response.setErrorCode(CfErrorCode.CF_PARAM_ERROR_PAYEE);
                response.setInfoUuid(crowdfundingInfo.getInfoId());
                return response;
            }
        } else {
            resultVoResponse = cfBankCardVerifyService.checkCardByThreeElements(
                    crowdfundingInfoOrg, crowdfundingInfoPayeeVo.getPayeeName(), crowdfundingInfoPayeeVo.getBankCard(),
                    crowdfundingInfoPayeeVo.getIdCard());
            log.info("openWithResponse:{}", JSON.toJSONString(resultVoResponse));
            if (resultVoResponse.notOk()) {
                response.setErrorCode(CfErrorCode.getByCode(resultVoResponse.getCode()));
                response.setInfoUuid(crowdfundingInfo.getInfoId());
                CrowdfundingInfoResponse.Data data = new CrowdfundingInfoResponse.Data();
                data.setExtra(resultVoResponse.getData());
                return response;
            }
        }

        response.setErrorCode(CfErrorCode.SUCCESS);

        if (crowdfundingInfoPayee == null) {

            crowdfundingInfoPayee = new CrowdfundingInfoPayee();
            crowdfundingInfoPayee.setBankBranchName("");
            crowdfundingInfoPayee.setBankCard(cryptoBankCard);
            crowdfundingInfoPayee.setBankName(bankName);
            crowdfundingInfoPayee.setIdCard(cryptoIdCard);
            crowdfundingInfoPayee.setIdType(UserIdentityType.getCode(idType));
            crowdfundingInfoPayee.setInfoUuid(infoUuid);
            crowdfundingInfoPayee.setMobile(cryptoMobile);
            crowdfundingInfoPayee.setName(payeeName);
            crowdfundingInfoPayee.setRelationType(CrowdfundingRelationType.getCode(relationType));
            crowdfundingInfoPayee.setCaseId(crowdfundingInfo.getId());
            crowdfundingInfoPayee.setEmergency(crowdfundingInfoPayeeVo.getEmergency());
            crowdfundingInfoPayee.setEmergencyPhone(oldShuidiCipher.aesEncrypt(crowdfundingInfoPayeeVo.getEmergencyPhone()));
            crowdfundingInfoPayee.setRelativesType(crowdfundingInfoPayeeVo.getRelativesType());
            crowdfundingInfoPayee.setFaceIdResult(crowdfundingInfoPayeeVo.getFaceIdResult());
            crowdfundingInfoPayee.setOtherIdPhoto(crowdfundingInfoPayeeVo.getOtherIdPhoto());

            this.crowdfundingInfoPayeeBiz.add(crowdfundingInfoPayee);

            if (crowdfundingInfoStatusBiz.getByInfoUuidAndType(infoUuid,
                    CrowdfundingInfoDataStatusTypeEnum.PAYEE_INFO_SUBMIT.getCode()) == null) {
                // 提交收款人信息状态
                CrowdfundingInfoStatus crowdfundingInfoStatus = new CrowdfundingInfoStatus();
                crowdfundingInfoStatus.setInfoUuid(infoUuid);
                crowdfundingInfoStatus.setType(CrowdfundingInfoDataStatusTypeEnum.PAYEE_INFO_SUBMIT.getCode());
                // crowdfundingInfoStatus.setStatus(CrowdfundingConstant.BaseStatus.valid.getScore());
                crowdfundingInfoStatus.setStatus(CrowdfundingInfoStatusEnum.UN_SUBMITTED.getCode());
                crowdfundingInfoStatus.setCaseId(crowdfundingId);
                this.crowdfundingInfoStatusBiz.add(crowdfundingInfoStatus);
            } else {
                this.crowdfundingInfoStatusBiz.updateByInfoId(infoUuid,
                        CrowdfundingInfoDataStatusTypeEnum.PAYEE_INFO_SUBMIT.getCode(),
                        CrowdfundingInfoStatusEnum.UN_SUBMITTED);
            }
        } else {
            crowdfundingInfoPayee.setBankBranchName("");
            crowdfundingInfoPayee.setBankCard(cryptoBankCard);
            crowdfundingInfoPayee.setBankName(bankName);
            crowdfundingInfoPayee.setIdCard(cryptoIdCard);
            crowdfundingInfoPayee.setIdType(UserIdentityType.getCode(idType));
            crowdfundingInfoPayee.setMobile(cryptoMobile);
            crowdfundingInfoPayee.setName(payeeName);
            crowdfundingInfoPayee.setRelationType(CrowdfundingRelationType.getCode(relationType));
            crowdfundingInfoPayee.setRelativesType(crowdfundingInfoPayeeVo.getRelativesType());
            crowdfundingInfoPayee.setEmergency(crowdfundingInfoPayeeVo.getEmergency());
            crowdfundingInfoPayee.setEmergencyPhone(oldShuidiCipher.aesEncrypt(crowdfundingInfoPayeeVo.getEmergencyPhone()));
            crowdfundingInfoPayee.setFaceIdResult(crowdfundingInfoPayeeVo.getFaceIdResult());
            crowdfundingInfoPayee.setOtherIdPhoto(crowdfundingInfoPayeeVo.getOtherIdPhoto());
            this.crowdfundingInfoPayeeBiz.update(crowdfundingInfoPayee);

            this.crowdfundingInfoStatusBiz.updateByInfoId(infoUuid,
                    CrowdfundingInfoDataStatusTypeEnum.PAYEE_INFO_SUBMIT.getCode(),
                    CrowdfundingInfoStatusEnum.UN_SUBMITTED);
            // // 如果是被驳回的状态，那么重新设置为待审核
            // if (needUpdateInfoStatus(crowdfundingInfo)) {
            // crowdfundingInfo.setStatus(CrowdfundingStatus.APPROVE_PENDING);
            // }
        }

        // 判断并标记收款人是否内部正式顾问, 不需要解析返回值
        if (crowdfundingInfoPayee.getIdType() == UserIdentityType.identity.getCode()) {
            PatientToVolunteerParam param = PatientToVolunteerParam.builder()
                    .caseId(crowdfundingInfoPayee.getCaseId())
                    .payeeName(crowdfundingInfoPayee.getName())
                    .useType(PatientToVolunteerConst.Source.APPROVE)
                    .payeeIdCard(crowdfundingInfoPayee.getIdCard())
                    .build();
            OperationResult<Integer> resp = initialAuditWorkOrderFeignClient.patientToVolunteer(param);
            log.info("检查修改的收款人是否为内部正式顾问 param:{} response:{}", JSON.toJSONString(param), JSON.toJSONString(resp));
        }

        //********{此处是先更新了 案例表的数据，之后再去确认要不要执行 银行卡鉴权}*************
        this.crowdfundingInfoBiz.updatePayeeInfo(crowdfundingInfo);
        //收款人变更时，更新关系
        cryptoRelationService.sendPayeeInfoRelation(crowdfundingInfo);

        // 收款人变更时 发送消息
        producer.send(new Message<>(MQTopicCons.CF,
                MQTagCons.CF_BASE_INFO_CHANGE_TAG, MQTagCons.CF_BASE_INFO_CHANGE_TAG + "-" +
                crowdfundingInfo.getId()
                + "-" + System.currentTimeMillis(), crowdfundingInfo.getId(), DelayLevel.S5));

        payeeMaterialCenterService.addOrUpdatePersonAccount(crowdfundingInfo.getId(), crowdfundingInfoPayee, null);

        // 添加附件
        if (relationType != CrowdfundingRelationType.self) {

            List<AttachmentTypeEnum> typeList = Lists.newArrayList(AttachmentTypeEnum.ATTACH_PAYEE_ID_CARD,
                    AttachmentTypeEnum.ATTACH_PAYEE_RELATION, AttachmentTypeEnum.ATTACH_PAYEE_RELATION_VIDEO,
                    AttachmentTypeEnum.ATTACH_PAYEE_HUKOUBEN, AttachmentTypeEnum.ATTACH_PAYEE_JIEHUNZHENG,
                    AttachmentTypeEnum.ATTACH_PAYEE_CHUSHENGZHENG, AttachmentTypeEnum.ATTACH_PAYEE_ZHENGMING,
                    AttachmentTypeEnum.ATTACH_PAYEE_WEITUOSHU, AttachmentTypeEnum.ATTACH_PAYEE_ONLY_ID_CARD,
                    AttachmentTypeEnum.ATTACH_PAYEE_WEITUOSHU_HEZHAO,AttachmentTypeEnum.ATTACH_PAYEE_FACE_ID);

            this.crowdfundingAttachmentBiz.deleteByParentIdAndType(crowdfundingId, typeList);

            List<CrowdfundingAttachment> attachmentList = Lists.newArrayList();
            if (StringUtils.isNotEmpty(idCardPhoto)){
                attachmentList.add(new CrowdfundingAttachment(crowdfundingId, AttachmentTypeEnum.ATTACH_PAYEE_ID_CARD, idCardPhoto));
            }
            if (CollectionUtils.isNotEmpty(attachments)){

                for (int i = 0; i < attachments.size(); i++) {
                    String url = attachments.get(i);
                    if (StringUtils.isBlank(url)){
                        continue;
                    }
                    attachmentList.add(new CrowdfundingAttachment(crowdfundingId, AttachmentTypeEnum.ATTACH_PAYEE_RELATION,
                            url, i));
                }
            }

            // 收款人关系视频
            if (CollectionUtils.isNotEmpty(crowdfundingInfoPayeeVo.getRelationVideos()) &&
                    (relationType == CrowdfundingRelationType.spouse || relationType == CrowdfundingRelationType.other) ) {
                for (int i = 0; i < crowdfundingInfoPayeeVo.getRelationVideos().size(); ++i) {
                    String videoUrls = crowdfundingInfoPayeeVo.getRelationVideos().get(i);
                    attachmentList.add(
                            new CrowdfundingAttachment(crowdfundingId, AttachmentTypeEnum.ATTACH_PAYEE_RELATION_VIDEO, videoUrls, i));
                }
            }
            //身份验证
            if (CollectionUtils.isNotEmpty(crowdfundingInfoPayeeVo.getAuthentication())){
                crowdfundingInfoPayeeVo.getAuthentication().stream().forEach(r->{
                    for (int i=0;i< r.getAttachments().size();i++){
                        String url = r.getAttachments().get(i);
                        if (StringUtils.isBlank(url)){
                            continue;
                        }
                        attachmentList.add(new CrowdfundingAttachment(crowdfundingId, AttachmentTypeEnum.getAttachmentTypeEnum(r.getRelType()), url, i));
                    }
                });
            }
            //关系证明
            if (CollectionUtils.isNotEmpty(crowdfundingInfoPayeeVo.getProves())){
                crowdfundingInfoPayeeVo.getProves().stream().forEach(r->{
                    for (int i=0;i< r.getAttachments().size();i++){
                        String url = r.getAttachments().get(i);
                        if (StringUtils.isBlank(url)){
                            continue;
                        }
                        attachmentList.add(new CrowdfundingAttachment(crowdfundingId, AttachmentTypeEnum.getAttachmentTypeEnum(r.getRelType()), url, i));
                    }
                });
            }

            if (StringUtils.isNotEmpty(crowdfundingInfoPayeeVo.getWeituoshuHezhao())){
                attachmentList.add(new CrowdfundingAttachment(crowdfundingId, AttachmentTypeEnum.ATTACH_PAYEE_WEITUOSHU_HEZHAO, crowdfundingInfoPayeeVo.getWeituoshuHezhao()));
            }

            if (StringUtils.isNotEmpty(crowdfundingInfoPayeeVo.getFaceVideo())){
                attachmentList.add(new CrowdfundingAttachment(crowdfundingId, AttachmentTypeEnum.ATTACH_PAYEE_FACE_ID, crowdfundingInfoPayeeVo.getFaceVideo()));
            }

            this.crowdfundingAttachmentBiz.add(attachmentList);
        }

        // 保存｜更新 户口勾选状态
        if (Objects.nonNull(crowdfundingInfoPayeeVo.getHukouOption())) {
            HuKouOption oldOption = cfHuKouService.selectHuKouOptionByCaseId(crowdfundingId);
            if (Objects.isNull(oldOption)) {
                cfHuKouService.saveHuKouOption(crowdfundingInfoPayeeVo.getHukouOption(), crowdfundingId);
            } else {
                cfHuKouService.updateHuKouOption(crowdfundingInfoPayeeVo.getHukouOption(), crowdfundingId);
            }
        }

        //更新案例为认证通过
        if (response.getErrorCode().equals(CfErrorCode.SUCCESS)) {
            crowdfundingInfoBiz.updateVerifyStatus(crowdfundingId, BankCardVerifyStatus.passed,
                    "认证通过", "认证通过");
        }
        // 是否显示提交对话框
        boolean showSubmit = this.checkShowSubmit(crowdfundingInfo);
        response = new CrowdfundingInfoResponse();
        response.setErrorCode(CfErrorCode.SUCCESS);
        response.setInfoUuid(crowdfundingInfo.getInfoId());
        response.setShowSubmit(showSubmit);
        return response;
    }

    /**
     * 提交审核（全部三项都保存之后才可以提交审核）
     *
     * @param crowdfundingInfo
     * @return
     */
    public Response<Void> submitForReview(CrowdfundingInfo crowdfundingInfo) {

        int crowdfundingId = crowdfundingInfo.getId();
        String infoUuid = crowdfundingInfo.getInfoId();
        int caseId = crowdfundingInfo.getId();

        // 修改资金材料补充状态
        infoCapitalCompensationService.onSubmit(crowdfundingId);

        //若当前案例处于已提交或者是审核通过状态  不让用户更新患者信息
        if (crowdfundingInfo.getStatus() == CrowdfundingStatus.SUBMITTED ||
                crowdfundingInfo.getStatus() == CrowdfundingStatus.CROWDFUNDING_STATED) {
            log.info("addOrUpdateBaseInfo info status is:{} infoUuid:{}", crowdfundingInfo.getStatus().value(), crowdfundingInfo.getInfoId());
            return NewResponseUtil.makeSuccess(null);
        }

        int type = crowdfundingInfo.getType();
        List<CrowdfundingInfoStatus> statusList = crowdfundingInfoStatusBiz.getByInfoUuid(infoUuid);
        List<Integer> dataTypeList = statusList.stream().map(CrowdfundingInfoStatus::getType)
                .collect(Collectors.toList());

        //获取这个案例需要校验的材料列表
        CfInfoExt cfInfoExt = cfInfoExtBiz.getByInfoUuid(infoUuid);
        List<CrowdfundingInfoDataStatusTypeEnum> requiredDataList = CrowdfundingUtil.getRequiredCaseList(cfInfoExt);

        if (checkDataType(dataTypeList, requiredDataList)) {
            // 检查是否所有提交的信息都没有被驳回
            if (statusList.stream().anyMatch(value -> {
                return value.getStatus() == null || CrowdfundingInfoStatusEnum.REJECTED.getCode() == value.getStatus();
            })) {
                log.info("submitForReview infoStatus : {}", JSON.toJSONString(statusList));
                return NewResponseUtil.makeError(CfErrorCode.CF_INFO_PARAM_ERROR_INFO_STATUS_REJECTED);
            }

            this.crowdfundingInfoBiz.updateDataStatus(crowdfundingInfo.getId(), CrowdfundingInfoDataStatusEnum.ALLDATA.getCode());

            Set<Integer> notPassDataType = statusList.stream()
                    .filter(val -> val.getStatus() != CrowdfundingInfoStatusEnum.PASSED.getCode())
                    .map(CrowdfundingInfoStatus::getType).collect(Collectors.toSet());
            // 标记所有信息为已提交状态
            this.crowdfundingInfoStatusBiz.updateByTypes(infoUuid, CrowdfundingInfoStatusEnum.SUBMITTED,
                    notPassDataType);
            log.info("notPassDataType:{}\tinfoUuid:{}", JSON.toJSONString(notPassDataType), infoUuid);
            //对于新案例自动审核 房产,车产信息
            if (requiredDataList.contains(CrowdfundingInfoDataStatusTypeEnum.CREDIT_SUPPLEMENT_INFO_SUBMIT)) {
                this.autoToCheckCreditSupplement(infoUuid, crowdfundingInfo);
            }
            // 记录提交审核操作
            this.recordSubmitOperation(infoUuid, caseId);
            commonOperationRecordClient.create()
                    .buildBasicUser(caseId, OperationActionTypeEnum.SUBMIT_INFO_APPROVE).save();
            // 审核状态机
            CrowdfundingStatus infoStatus = this.crowdfundingApproveService.checkDataUpdateCaseStatus(crowdfundingInfo, cfInfoExt);
            log.info("crowdFunding info status is :{}\t and  infoUuid:{}", infoStatus, infoUuid);
            if (type == CrowdfundingType.SERIOUS_ILLNESS.value()) {
                try {
                    if (CrowdfundingStatus.CROWDFUNDING_STATED != infoStatus) {
                        log.info("infoUuid:{} produce work order", infoUuid);
                        //创建材料审核工单
                        sendMsgWorkOrderCfApprove(infoUuid);
                    } else {
                        log.info("infoUuid:{} no produce work order", infoUuid);
                    }
                    //新版本提交审核后  清空草稿信息
                    cfCommonStoreService.deleteUserKeyCailiao(crowdfundingInfo.getUserId());

                    return NewResponseUtil.makeSuccess(null);
                } catch (Exception e) {
                    log.error("CrowdfundingInfoService addBaseInfo pushLaunchMsg Error!", e);
                }
            }

            return NewResponseUtil.makeSuccess(null);
        } else {
            for (CrowdfundingInfoDataStatusTypeEnum statusTypeEnum : requiredDataList) {
                if (!dataTypeList.contains(statusTypeEnum.getCode())) {
                    return NewResponseUtil.makeError(statusTypeEnum.getCfErrorCode());
                }
            }
        }

        return NewResponseUtil.makeError(CfErrorCode.SYSTEM_ERROR);
    }

    public AuditStatus pushAudit(CrowdfundingInfo crowdfundingInfo) {
        AuditStatus result = new AuditStatus();
        try {
            String workTime = cfRedisKvBiz.queryByKey("WORKING_TIME", true);
            String averageAuditTime = cfRedisKvBiz.queryByKey("AVERAGE_AUDIT_TIME", true);
            int waitTime = Integer.parseInt(averageAuditTime);
            long waitTimeMs = waitTime * 60 * 60 * 1000;
            String ymd = DateUtil.getCurrentDateStr();
            String beginWorkTime = ymd + " " + workTime.split("-")[0];
            String endWorkTime = ymd + " " + workTime.split("-")[1];
            String endOfDayTime = ymd + " 23:59:59";
            CrowdfundingOperation operation = crowdfundingOperationBiz.getByInfoId(crowdfundingInfo.getInfoId());
            if (operation == null) {
                return result;
            }
            Timestamp auditCommitTimestamp = operation.getAuditCommitTime();
            Date auditTimestamp = DateUtil.addHours(auditCommitTimestamp, waitTime);
            String auditCommitTime = DateUtil.getTimeStringFromTimestamp(auditCommitTimestamp);
            String auditTime = DateUtil.formatDateTime(auditTimestamp);
            Date now = new Date(System.currentTimeMillis());// 催审核时间
            Date beginWork = DateUtil.parseDateTime(beginWorkTime);// 今天工作开始时间
            Date tomorrowBeginWork = new Date(new DateTime(beginWork.getTime()).plusDays(1).getMillis());// 明天工作开始时间
            Date endWork = DateUtil.parseDateTime(endWorkTime);// 今天工作结束时间
            Date auditCommit = DateUtil.parseDateTime(auditCommitTime);// 提交审核时间
            Date audit = DateUtil.parseDateTime(auditTime);// 王磊的预计审核时间
            Date endOfDay = DateUtil.parseDateTime(endOfDayTime);
            Date judgeDate;
            if (beginWork.before(auditCommit) && endWork.after(auditCommit)) {
                if (audit.before(endWork)) {
                    judgeDate = audit;
                } else {
                    long l = waitTimeMs - (endWork.getTime() - auditCommit.getTime());
                    judgeDate = new DateTime(tomorrowBeginWork).plusMillis((int) l).toDate();
                }
            } else {
                if (auditCommit.after(endWork)) {
                    judgeDate = new DateTime(tomorrowBeginWork).plusHours(waitTime).toDate();
                } else {
                    judgeDate = new DateTime(beginWork).plusHours(waitTime).toDate();
                }
            }
            DateTimeFormatter ymdhmsfmt = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");
            String showTime = ymdhmsfmt.print(judgeDate.getTime()).split(" ")[1].substring(0, 5);
            String msg = null;
            if (now.after(judgeDate)) {
                msg = "我们已经开始为您审核，如您有其他需求，请拨打电话010-4006861179";
            } else {
                if (judgeDate.before(endOfDay)) {
                    showTime = "今天" + showTime + "前";
                    msg = "我们预计将在" + showTime + "为您进行审核，请您耐心等候";
                } else {
                    showTime = "明天" + showTime + "前";
                    msg = "我们预计将在" + showTime + "为您进行审核，请您耐心等候";
                }
            }
            result.setShowTime(showTime);
            result.setMsg(msg);
            result.setJudgeDate(judgeDate.getTime() + "");
            result.setStatus(crowdfundingInfo.getStatus().value() + "");
            return result;
        } catch (Exception e) {
            log.error("CrowdfundingService pushAudit error!", e);
            return result;
        }
    }

    private CfErrorCode verifyInfoId(CrowdfundingInfo crowdfundingInfo, long requestUserId) {
        if (crowdfundingInfo == null) {
            return CfErrorCode.CF_NOT_FOUND;
        }
        if (crowdfundingInfo.getStatus() != CrowdfundingStatus.APPROVE_PENDING
                && crowdfundingInfo.getStatus() != CrowdfundingStatus.APPROVE_DENIED) {
            return CfErrorCode.CF_CAN_NOT_EDIT;
        }

        if (crowdfundingInfo.getUserId() != requestUserId) {
            return CfErrorCode.CF_INFO_PARAM_ERROR_IDENTITY_INVALID;
        }
        return CfErrorCode.SUCCESS;
    }

    private void sendMsgWorkOrderCfApprove(String infoUuid) {
        log.info("sendMsgWorkOrderCfApprove infoUuid:{}", infoUuid);
        //加锁限制用户连续提交
        String identifier = "";
        String lockName = "sendMsgWorkOrderCfApprove" + infoUuid;
        try {
            identifier = cfRedissonHandler.tryLock(lockName, 30 * 1000L);
            if (StringUtils.isBlank(identifier)) {
                log.info(" 获取案例审核工单生成锁失败，identifier:{}", identifier);
                return;
                //throw new RuntimeException();
            }
            producer.send(
                    new Message<>(MQTopicCons.CF,
                            MQTagCons.CF_WORK_ORDER_APPROVE_LAUNCH,
                            UUID.randomUUID().toString(),
                            infoUuid,
                            DelayLevel.S10));
        } catch (Exception e) {
            log.error("error:", e);
        } finally {
            try {
                if (StringUtils.isNotBlank(identifier)) {
                    cfRedissonHandler.unLock(lockName, identifier);
                }
            } catch (Exception e) {
                log.info("", e);
            }
        }

    }

    /**
     * 检查是否需要显示提交,
     * 会对案例所需的全部数据进行核对，全部存在且没有一个拒绝时为true
     *
     * @param crowdfundingInfo
     * @return
     */
    public boolean checkShowSubmit(CrowdfundingInfo crowdfundingInfo) {
        if (crowdfundingInfo == null || StringUtils.isBlank(crowdfundingInfo.getInfoId())) {
            return false;
        }
        String infoUuid = crowdfundingInfo.getInfoId();
        List<CrowdfundingInfoStatus> infoStatuses = this.crowdfundingInfoStatusBiz.getByInfoUuid(infoUuid);
        if (CollectionUtils.isEmpty(infoStatuses) || infoStatuses.size() < 4) {
            return false;
        }
        Map<Integer, Integer> infoStatusMap = Maps.newHashMap();
        for (CrowdfundingInfoStatus infoStatus : infoStatuses) {
            infoStatusMap.put(infoStatus.getType(), infoStatus.getStatus());
        }
        if (infoStatusMap.size() < 4) {
            return false;
        }
        Integer status = infoStatusMap.get(CrowdfundingInfoDataStatusTypeEnum.BASE_INFO_SUBMIT.getCode());
        if (status == null || status.intValue() == CrowdfundingInfoStatusEnum.REJECTED.getCode()) {
            return false;
        }
        status = infoStatusMap.get(CrowdfundingInfoDataStatusTypeEnum.PATIENT_INFO_SUBMIT.getCode());
        if (status == null || status.intValue() == CrowdfundingInfoStatusEnum.REJECTED.getCode()) {
            return false;
        }
        status = infoStatusMap.get(CrowdfundingInfoDataStatusTypeEnum.PAYEE_INFO_SUBMIT.getCode());
        if (status == null || status.intValue() == CrowdfundingInfoStatusEnum.REJECTED.getCode()) {
            return false;
        }
        status = infoStatusMap.get(CrowdfundingInfoDataStatusTypeEnum.TREATMENT_INFO_SUBMIT.getCode());
        if (status == null || status.intValue() == CrowdfundingInfoStatusEnum.REJECTED.getCode()) {
            return false;
        }

        CfInfoExt currInfoExt = cfInfoExtBiz.getByInfoUuid(crowdfundingInfo.getInfoId());

        if (currInfoExt == null) {
            return true;
        }
        //区别新旧案例,只有新案例走房产、车产的审核
//        if (CrowdfundingUtil.decideIsOldCrowdFunding(currInfoExt)) {
//            //旧案例
//            return true;
//        }

        List<CrowdfundingInfoDataStatusTypeEnum> requiredDateTypes = CrowdfundingUtil.getRequiredCaseList(currInfoExt);
        if (requiredDateTypes.contains(CrowdfundingInfoDataStatusTypeEnum.CREDIT_SUPPLEMENT_INFO_SUBMIT)) {
            status = infoStatusMap.get(CrowdfundingInfoDataStatusTypeEnum.CREDIT_SUPPLEMENT_INFO_SUBMIT.getCode());
            if (status == null || status.intValue() == CrowdfundingInfoStatusEnum.REJECTED.getCode()) {
                return false;
            }
        }
        return true;
    }

    /**
     * 刷新案例的状态信息
     *
     * @param crowdfundingInfo
     * @return
     */
    public boolean refreshCaseMaterialStatus(CrowdfundingInfo crowdfundingInfo) {
        int crowdfundingId = crowdfundingInfo.getId();
        String infoUuid = crowdfundingInfo.getInfoId();
        boolean allFillIn = this.crowdfundingInfoStatusBiz.isAllFillIn(infoUuid);
        if (allFillIn) {
            this.crowdfundingInfoBiz.updateDataStatus(crowdfundingId, CrowdfundingInfoDataStatusEnum.ALLDATA.getCode());
            this.cfInfoTaskBiz.updateTimes(crowdfundingInfo.getInfoId(), CfTaskEnum.Rule.REPLENISH_MATERIAL);
        }
        return allFillIn;
    }

    public boolean checkDataType(List<Integer> dataTypeList, List<CrowdfundingInfoDataStatusTypeEnum> requiredList) {

        //校验材料必须不能为空
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(requiredList));

        List<Integer> allTypeList = requiredList.stream().map(CrowdfundingInfoDataStatusTypeEnum::getCode).collect(Collectors.toList());

        return dataTypeList.containsAll(allTypeList);
    }

    public void saveBaseInfoTemplateRecord(CfBaseInfoTemplateRecord cfBaseInfoTemplateRecord, String infoUuid) {
        cfBaseInfoTemplateRecord.setInfoUuid(infoUuid);
        if (StringUtils.isNotBlank(infoUuid) || cfBaseInfoTemplateRecord.getRelationship() != 0 ||
                cfBaseInfoTemplateRecord.getTitleId() != 0 || cfBaseInfoTemplateRecord.getContentId() != 0) {
            String relationStr = cfBaseInfoTemplateRecord.getRelationStr();
            BaseInfoTemplateConst.CfBaseInfoRelationshipEnum relationshipEnum =
                    BaseInfoTemplateConst.CfBaseInfoRelationshipEnum.getByStr(relationStr);
            cfBaseInfoTemplateRecord.setRelationship(relationshipEnum.getCode());
            cfBaseInfoTemplateRecordBiz.insertOne(cfBaseInfoTemplateRecord);
        }
    }

    /**
     * 单独的房产、车产增信接口
     *
     * @param requestUserId
     * @param creditSupplementList
     * @return
     */
    public CrowdfundingInfoResponse addOrUpdateCreditSupplementInfo(long requestUserId,
                                                                    List<CfCreditSupplement> creditSupplementList,
                                                                    String infoUuid) {
        CrowdfundingInfoResponse infoResponse = new CrowdfundingInfoResponse();
        if (StringUtils.isEmpty(infoUuid) && CollectionUtils.isNotEmpty(creditSupplementList)) {
            infoUuid = creditSupplementList.get(0).getInfoUuid();
        }
        log.info("addOrUpdateCreditSupplementInfo, infoUuid:{}", infoUuid);

        //判断是否更新task任务
        List<CfCreditSupplement> oldSupplementList = cfCreditSupplementBiz.selectByInfoUuid(infoUuid);
        boolean updateFlag = (CollectionUtils.isEmpty(oldSupplementList) || oldSupplementList.size() != 2);

        CrowdFundingInfoErrorCodeVo checkoutCodeVo = this.checkoutCreditSupplementInfo(creditSupplementList, infoUuid);
        if (!CfErrorCode.SUCCESS.equals(checkoutCodeVo.getCheckoutCode())) {
            infoResponse.setErrorCode(checkoutCodeVo.getCheckoutCode());
            return infoResponse;
        }
        CrowdfundingInfo crowdfundingInfo = checkoutCodeVo.getCrowdfundingInfo();
        CfErrorCode verifyResult = verifyInfoId(crowdfundingInfo, requestUserId);
        if (!verifyResult.equals(CfErrorCode.SUCCESS)) {
            infoResponse.setErrorCode(verifyResult);
            return infoResponse;
        }
        //在提交被驳回的患者信息时,如果增信为通过则不修改增信的数据
        CrowdfundingInfoStatus oldInfoStatus = crowdfundingInfoStatusBiz.getByInfoUuidAndType(infoUuid,
                CrowdfundingInfoDataStatusTypeEnum.CREDIT_SUPPLEMENT_INFO_SUBMIT.getCode());
        if (oldInfoStatus != null &&
                CrowdfundingInfoStatusEnum.PASSED.getCode() == oldInfoStatus.getStatus()) {
            boolean showSubmit = this.checkShowSubmit(crowdfundingInfo);
            infoResponse.setErrorCode(CfErrorCode.SUCCESS);
            infoResponse.setInfoUuid(crowdfundingInfo.getInfoId());
            infoResponse.setShowSubmit(showSubmit);
            return infoResponse;
        }
        this.compatibilityCreditSupplementInfo(creditSupplementList, infoUuid);
        // 新增案例id
        if (CollectionUtils.isNotEmpty(creditSupplementList) && crowdfundingInfo != null) {
            creditSupplementList.stream().forEach(v -> v.setCaseId(crowdfundingInfo.getId()));
        }
        // 增信补充材料入库,区分新旧案例
        if (CrowdfundingUtil.decideIsOldCrowdFunding(cfInfoExtBiz.getByInfoUuid(crowdfundingInfo.getInfoId()))) {
            //旧案例
            cfCreditSupplementBiz.deleteFromInfoUuid(infoUuid);
            cfCreditSupplementBiz.addList(creditSupplementList);
        } else {
            if (CollectionUtils.isEmpty(creditSupplementList) || creditSupplementList.size() != 2) {
                boolean showSubmit = this.checkShowSubmit(crowdfundingInfo);
                infoResponse.setErrorCode(CfErrorCode.SUCCESS);
                infoResponse.setInfoUuid(crowdfundingInfo.getInfoId());
                infoResponse.setShowSubmit(showSubmit);
                return infoResponse;
            }
            int count = cfCreditSupplementBiz.deleteFromInfoUuid(infoUuid);
            cfCreditSupplementBiz.addList(creditSupplementList);
            if (count != 0) {
                //更新时
                CrowdfundingInfoStatus infoStatus = crowdfundingInfoStatusBiz.getByInfoUuidAndType(infoUuid,
                        CrowdfundingInfoDataStatusTypeEnum.CREDIT_SUPPLEMENT_INFO_SUBMIT.getCode());
                if (infoStatus == null) {
                    try {
                        CrowdfundingInfoStatus crowdfundingInfoStatus = new CrowdfundingInfoStatus();
                        crowdfundingInfoStatus.setInfoUuid(infoUuid);
                        crowdfundingInfoStatus.setType(CrowdfundingInfoDataStatusTypeEnum.CREDIT_SUPPLEMENT_INFO_SUBMIT.getCode());
                        crowdfundingInfoStatus.setStatus(CrowdfundingInfoStatusEnum.UN_SUBMITTED.getCode());
                        // 新增案例id
                        crowdfundingInfoStatus.setCaseId(crowdfundingInfo.getId());
                        this.crowdfundingInfoStatusBiz.add(crowdfundingInfoStatus);
                    } catch (Exception e) {
                        log.error("add crowdfunding info status second is error", e);
                    }
                } else if (infoStatus.getStatus() != CrowdfundingInfoStatusEnum.UN_SUBMITTED.getCode()) {
                    crowdfundingInfoStatusBiz.updateByInfoId(infoUuid,
                            CrowdfundingInfoDataStatusTypeEnum.CREDIT_SUPPLEMENT_INFO_SUBMIT.getCode(),
                            CrowdfundingInfoStatusEnum.UN_SUBMITTED);
                }
            } else {
                //初次添加时
                try {
                    CrowdfundingInfoStatus crowdfundingInfoStatus = new CrowdfundingInfoStatus();
                    crowdfundingInfoStatus.setInfoUuid(infoUuid);
                    crowdfundingInfoStatus.setType(CrowdfundingInfoDataStatusTypeEnum.CREDIT_SUPPLEMENT_INFO_SUBMIT.getCode());
                    crowdfundingInfoStatus.setStatus(CrowdfundingInfoStatusEnum.UN_SUBMITTED.getCode());
                    // 新增案例id
                    crowdfundingInfoStatus.setCaseId(crowdfundingInfo.getId());
                    this.crowdfundingInfoStatusBiz.add(crowdfundingInfoStatus);
                } catch (Exception e) {
                    log.error("insert  crowdfunding info status second is error", e);
                    this.crowdfundingInfoStatusBiz.updateByInfoId(infoUuid,
                            CrowdfundingInfoDataStatusTypeEnum.CREDIT_SUPPLEMENT_INFO_SUBMIT.getCode(),
                            CrowdfundingInfoStatusEnum.UN_SUBMITTED);
                }
            }
        }
        //更新任务状态
        if (updateFlag) {
            refreshCaseMaterialStatus(crowdfundingInfo);
        }
        boolean showSubmit = this.checkShowSubmit(crowdfundingInfo);
        infoResponse.setErrorCode(CfErrorCode.SUCCESS);
        infoResponse.setInfoUuid(crowdfundingInfo.getInfoId());
        infoResponse.setShowSubmit(showSubmit);

        return infoResponse;
    }

    /**
     * 对案例状态与增信信息的核对(会对新旧案例作出区分)
     *
     * @param creditSupplementList
     * @param infoUuid
     * @return
     */
    private CrowdFundingInfoErrorCodeVo checkoutCreditSupplementInfo(List<CfCreditSupplement> creditSupplementList,
                                                                     String infoUuid) {
        CrowdFundingInfoErrorCodeVo checkoutCodeVo = new CrowdFundingInfoErrorCodeVo();
        if (StringUtils.isEmpty(infoUuid) && CollectionUtils.isNotEmpty(creditSupplementList)) {
            infoUuid = creditSupplementList.get(0).getInfoUuid();
        }
        //对案例信息状态的判断
        checkoutCodeVo = this.checkInfoStatus(infoUuid);
        if (!checkoutCodeVo.getCheckoutCode().equals(CfErrorCode.SUCCESS)) {
            return checkoutCodeVo;
        }
        //对新旧案例的区分
        boolean isOldCrowdFunding = CrowdfundingUtil.decideIsOldCrowdFunding(cfInfoExtBiz.getByInfoUuid(infoUuid));
        if (!isOldCrowdFunding && CollectionUtils.isEmpty(creditSupplementList)) {
            checkoutCodeVo.setCheckoutCode(CfErrorCode.USER_INFO_PATIENT_IS_EMPTY);
            return checkoutCodeVo;
        }
        if (isOldCrowdFunding) {
            //旧案例
            if (creditSupplementList.size() > 2) {
                checkoutCodeVo.setCheckoutCode(CfErrorCode.CF_SWITCHER_ERROR);
                return checkoutCodeVo;
            }
        } else {
            if (creditSupplementList.size() != 2) {
                checkoutCodeVo.setCheckoutCode(CfErrorCode.USER_INFO_PATIENT_IS_EMPTY);
                return checkoutCodeVo;
            }
        }
        checkoutCodeVo.setCheckoutCode(CfErrorCode.SUCCESS);
        return checkoutCodeVo;
    }

    /**
     * 增信部分数据的检验兼容
     *
     * @param creditSupplementList
     * @param infoUuid
     */
    public void compatibilityCreditSupplementInfo(List<CfCreditSupplement> creditSupplementList,
                                                  String infoUuid) {
        if (CollectionUtils.isEmpty(creditSupplementList)) {
            return;
        }
        if (StringUtils.isEmpty(infoUuid)) {
            infoUuid = creditSupplementList.get(0).getInfoUuid();
        }
        // 兼容用户侧提交的问题，@王磊，把已变卖但是为0的改为未变卖
        if (!CollectionUtils.isEmpty(creditSupplementList)) {
            for (CfCreditSupplement cfCreditSupplement : creditSupplementList) {
                if (cfCreditSupplement != null && cfCreditSupplement.getStatus() == 1
                        && cfCreditSupplement.getSellCount() == 0) {
                    cfCreditSupplement.setStatus(0);
                    log.info(
                            "CrowdfundingV4InfoUpdateController addOrUpdatePatientInfo change cfCreditSupplement with infoUuid={},"
                                    + "modify type={}, status={} to status={}",
                            infoUuid, cfCreditSupplement.getPropertyType(), 1, 0);
                }
            }
        }
    }

    /**
     * 患者信息新接口
     *
     * @param requestUserId
     * @param crowdfundingInfoTreatmentVo
     * @return
     */
    public CrowdfundingInfoResponse addOrUpdateTreatmentInfo(long requestUserId, String appVersions,
                                                             CrowdfundingInfoTreatmentVo crowdfundingInfoTreatmentVo) {
        CrowdfundingInfoResponse response = new CrowdfundingInfoResponse();
        CrowdFundingInfoErrorCodeVo checkoutCode = this.checkoutTreatmentInfo(crowdfundingInfoTreatmentVo, appVersions);
        if (!CfErrorCode.SUCCESS.equals(checkoutCode.getCheckoutCode())) {
            response.setErrorCode(checkoutCode.getCheckoutCode());
            return response;
        }
        CrowdfundingInfo crowdfundingInfo = checkoutCode.getCrowdfundingInfo();
        //检验资料填写人是否与发起人信息一致
        CfErrorCode verifyResult = verifyInfoId(crowdfundingInfo, requestUserId);
        if (!verifyResult.equals(CfErrorCode.SUCCESS)) {
            response.setErrorCode(verifyResult);
            return response;
        }

        int crowdfundingId = crowdfundingInfo.getId();
        String diseaseName = crowdfundingInfoTreatmentVo.getDiseaseName();
        String hospitalName = crowdfundingInfoTreatmentVo.getHospitalName();
        String diagnoseHospitalName = crowdfundingInfoTreatmentVo.getDiagnoseHospitalName();
        Integer hospitalId = crowdfundingInfoTreatmentVo.getHospitalId();
        Integer diagnoseHospitalId = crowdfundingInfoTreatmentVo.getDiagnoseHospitalId();
        Integer hospitalCityId = crowdfundingInfoTreatmentVo.getHospitalCityId();
        Integer diagnoseHospitalCityId = crowdfundingInfoTreatmentVo.getDiagnoseHospitalCityId();
        Integer subAttachmentType = crowdfundingInfoTreatmentVo.getSubAttachmentType();
        String verifyPhoto = crowdfundingInfoTreatmentVo.getVerifyPhoto();
        List<String> attachments = crowdfundingInfoTreatmentVo.getAttachments();
        CrowdfundingTreatment treatment = this.crowdfundingTreatmentBiz.get(crowdfundingId);

        String infoUuid = crowdfundingInfoTreatmentVo.getInfoUuid();

        // 医院信息若有修改则通知医院库保存记录
        onHospitalChanged(crowdfundingId, treatment, crowdfundingInfoTreatmentVo);

        if (treatment == null) {
            treatment = new CrowdfundingTreatment();
            treatment.setCrowdfundingId(crowdfundingId);
            treatment.setDiseaseName(StringUtils.trimToEmpty(diseaseName));
            treatment.setHospitalName(StringUtils.trimToEmpty(hospitalName));
            treatment.setDiagnoseHospitalName(StringUtils.trimToEmpty(diagnoseHospitalName));
            treatment.setHospitalId(hospitalId == null ? 0 : hospitalId);
            treatment.setDiagnoseHospitalId(diagnoseHospitalId == null ? 0 : diagnoseHospitalId);
            treatment.setHospitalCityId(hospitalCityId == null ? 0 : hospitalCityId);
            treatment.setDiagnoseHospitalCityId(diagnoseHospitalCityId == null ? 0 : diagnoseHospitalCityId);
            treatment.setSubAttachmentType(subAttachmentType);
            treatment.setHospitalCode(crowdfundingInfoTreatmentVo.getHospitalCode());
            this.crowdfundingTreatmentService.addCrowdfundingTreatment(treatment, infoUuid);
            // 提交病例信息状态
            CrowdfundingInfoStatus crowdfundingInfoStatus = new CrowdfundingInfoStatus();
            crowdfundingInfoStatus.setInfoUuid(infoUuid);
            crowdfundingInfoStatus.setType(CrowdfundingInfoDataStatusTypeEnum.TREATMENT_INFO_SUBMIT.getCode());
            crowdfundingInfoStatus.setStatus(CrowdfundingInfoStatusEnum.UN_SUBMITTED.getCode());
            // 新增caseId
            crowdfundingInfoStatus.setCaseId(crowdfundingId);
            this.crowdfundingInfoStatusBiz.add(crowdfundingInfoStatus);
            //与任务task有关
            refreshCaseMaterialStatus(crowdfundingInfo);
        } else {
            treatment.setDiseaseName(diseaseName);
            treatment.setHospitalName(hospitalName);
            treatment.setDiagnoseHospitalName(StringUtil.isBlank(diagnoseHospitalName) ? "" : diagnoseHospitalName);
            treatment.setHospitalId(hospitalId == null ? 0 : hospitalId);
            treatment.setDiagnoseHospitalId(diagnoseHospitalId == null ? 0 : diagnoseHospitalId);
            treatment.setHospitalCityId(hospitalCityId == null ? 0 : hospitalCityId);
            treatment.setDiagnoseHospitalCityId(diagnoseHospitalCityId == null ? 0 : diagnoseHospitalCityId);
            treatment.setSubAttachmentType(subAttachmentType);
            treatment.setHospitalCode(crowdfundingInfoTreatmentVo.getHospitalCode());
            this.crowdfundingTreatmentService.updateCrowdfundingTreatment(treatment);
            this.crowdfundingInfoStatusBiz.updateByInfoId(infoUuid,
                    CrowdfundingInfoDataStatusTypeEnum.TREATMENT_INFO_SUBMIT.getCode(),
                    CrowdfundingInfoStatusEnum.UN_SUBMITTED);
        }

        List<AttachmentTypeEnum> typeList = Lists.newArrayList(AttachmentTypeEnum.ATTACH_TREATMENT_VERIFY,
                AttachmentTypeEnum.ATTACH_TREATMENT, AttachmentTypeEnum.ATTACH_MEDICAL_RECORD_HOME,
                AttachmentTypeEnum.ATTACH_PASS_HOSPITAL, AttachmentTypeEnum.ATTACH_TREATMENT_NOTE,
                AttachmentTypeEnum.ATTACH_INSPECTION_REPORT,AttachmentTypeEnum.ATTACH_IN_HOSPITAL,
                AttachmentTypeEnum.ATTACH_LEAVE_HOSPITAL);
        this.crowdfundingAttachmentBiz.deleteByParentIdAndType(crowdfundingId, typeList);
        List<CrowdfundingAttachment> attachmentList = Lists.newArrayList();
        if (StringUtils.isNotBlank(verifyPhoto)) {
            attachmentList.add(new CrowdfundingAttachment(crowdfundingId, AttachmentTypeEnum.ATTACH_TREATMENT_VERIFY,
                    verifyPhoto));
        }
        if (CollectionUtils.isNotEmpty(attachments)) {
            for (int i = 0; i < attachments.size(); i++) {
                String url = attachments.get(i);
                attachmentList.add(new CrowdfundingAttachment(crowdfundingId, AttachmentTypeEnum.ATTACH_TREATMENT, url, i));
            }
        } else {
            List<CrowdfundingInfoTreatmentVo.ImageTypeUrl> newAttachments = crowdfundingInfoTreatmentVo.getNewAttachments();
            for (CrowdfundingInfoTreatmentVo.ImageTypeUrl imageTypeUrl : newAttachments) {
                int imageType = imageTypeUrl.getImageType();
                List<String> imageUrls = imageTypeUrl.getImageUrl();
                AttachmentTypeEnum itemEnum = AttachmentTypeEnum.getAttachmentTypeEnum(imageType);
                for (String imageUrl : imageUrls) {
                    int index = attachmentList.size();
                    attachmentList.add(new CrowdfundingAttachment(crowdfundingId, itemEnum, imageUrl, index));
                }
            }
            // 诊断图片相似判断的代码 先下线
//			producer.send(new Message<>(MQTopicCons.CF,
//					MQTagCons.CF_DIAGNOSIS_INFO_CHANGE_TAG, MQTagCons.CF_DIAGNOSIS_INFO_CHANGE_TAG + "-" + crowdfundingId
//					+ "-" + System.currentTimeMillis(), crowdfundingId, DelayLevel.S5));

        }
        if (CollectionUtils.isNotEmpty(attachmentList)) {
            this.crowdfundingAttachmentBiz.add(attachmentList);
        }

        boolean showSubmit = this.checkShowSubmit(crowdfundingInfo);
        response.setShowSubmit(showSubmit);
        response.setErrorCode(CfErrorCode.SUCCESS);
        response.setInfoUuid(crowdfundingInfo.getInfoId());

        return response;
    }

    private void onHospitalChanged(int crowdfundingId, CrowdfundingTreatment pre, CrowdfundingInfoTreatmentVo now) {
        if (pre == null) {
            saveHospitalChangeRecord(crowdfundingId, now, true, true);
            return ;
        }
        boolean isTreatmentChange = !StringUtils.equals(pre.getHospitalName(), now.getHospitalName()) ||
                pre.getHospitalCityId() != now.getHospitalCityId();
        boolean isDiagnoseChange = !StringUtils.equals(pre.getDiagnoseHospitalName(), now.getDiagnoseHospitalName()) ||
                pre.getDiagnoseHospitalCityId() != now.getDiagnoseHospitalCityId();
        saveHospitalChangeRecord(crowdfundingId, now, isTreatmentChange, isDiagnoseChange);
    }

    private void saveHospitalChangeRecord(int crowdfundingId, CrowdfundingInfoTreatmentVo now, boolean isTreatmentChange, boolean isDiagnoseChange) {
        if (now == null) {
            return;
        }
        CfUserSubmitHospitalVo hospitalUpdateVO = new CfUserSubmitHospitalVo();
        hospitalUpdateVO.setCaseId(crowdfundingId);

        if (isTreatmentChange && !StringUtils.isBlank(now.getHospitalName())) {
            CfUserSubmitHospitalVo.HospitalInfo info = new CfUserSubmitHospitalVo.HospitalInfo();
            info.setCityId(now.getHospitalCityId());
            info.setHospitalName(now.getHospitalName());
            hospitalUpdateVO.setTreatmentHospital(info);
        }
        if (isDiagnoseChange && !StringUtils.isBlank(now.getDiagnoseHospitalName())) {
            CfUserSubmitHospitalVo.HospitalInfo info = new CfUserSubmitHospitalVo.HospitalInfo();
            info.setCityId(now.getDiagnoseHospitalCityId());
            info.setHospitalName(now.getDiagnoseHospitalName());
            hospitalUpdateVO.setConfirmHospital(info);
        }

        Response<Boolean> updateHospitalRecordResp = cfHospitalNormalFeignClient.notifyGrowthUserAddHospital(hospitalUpdateVO);
        log.info("updateHospitalRecordResp {}, {}",hospitalUpdateVO,  updateHospitalRecordResp);
    }

    /**
     * 检验患者信息
     *
     * @param crowdfundingInfoTreatmentVo
     * @return
     */
    private CrowdFundingInfoErrorCodeVo checkoutTreatmentInfo(CrowdfundingInfoTreatmentVo crowdfundingInfoTreatmentVo,
                                                              String appVersions) {
        CrowdFundingInfoErrorCodeVo checkoutCode = new CrowdFundingInfoErrorCodeVo();
        if (crowdfundingInfoTreatmentVo == null) {
            checkoutCode.setCheckoutCode(CfErrorCode.SYSTEM_PARAM_ERROR);
            return checkoutCode;
        }
        String infoUuid = crowdfundingInfoTreatmentVo.getInfoUuid();
        if (StringUtils.isEmpty(infoUuid)) {
            checkoutCode.setCheckoutCode(CfErrorCode.SYSTEM_PARAM_ERROR);
            return checkoutCode;
        }
        if (StringUtils.isNotBlank(crowdfundingInfoTreatmentVo.getDiseaseName())
                && crowdfundingInfoTreatmentVo.getDiseaseName().length() > 50) {
            checkoutCode.setCheckoutCode(CfErrorCode.PARTNER_PARAM_DISEASE_NAME_TOLONG);
            return checkoutCode;
        }
        log.info("CrowdfundingV4InfoUpdateController addOrUpdateTreatmentInfo crowdfundingInfoTreatmentVo:{}",
                crowdfundingInfoTreatmentVo);
        String diseaseName = crowdfundingInfoTreatmentVo.getDiseaseName();
        String hospitalName = crowdfundingInfoTreatmentVo.getHospitalName();
        String diagnoseHospitalName = crowdfundingInfoTreatmentVo.getDiagnoseHospitalName();

        if (StringUtils.isEmpty(diseaseName) || StringUtils.isEmpty(hospitalName)) {
            checkoutCode.setCheckoutCode(CfErrorCode.CF_PARAM_ERROR_TREATMENT);
            return checkoutCode;
        }
        // 如果疾病或医院里面包含emoji，直接报错
        if (EmojiUtil.containsEmoji(hospitalName)) {
            checkoutCode.setCheckoutCode(CfErrorCode.CF_INFO_HOSPITAL_EMOJI);
            return checkoutCode;
        }
        //diagnoseHospitalName,对诊断医院
        if (EmojiUtil.containsEmoji(diagnoseHospitalName)) {
            checkoutCode.setCheckoutCode(CfErrorCode.CF_INFO_HOSPITAL_EMOJI);
            return checkoutCode;
        }
//		if (EmojiUtil.containsEmoji(diseaseName)) {
//			checkoutCode.setCheckoutCode(CfErrorCode.CF_INFO_DISEASE_EMOJI);
//			return checkoutCode;
//		}
        // String verifyPhoto =
        // crowdfundingInfoTreatmentVo.getVerifyPhoto();
        checkoutCode = this.checkInfoStatus(infoUuid);
        if (!checkoutCode.getCheckoutCode().equals(CfErrorCode.SUCCESS)) {
            return checkoutCode;
        }
        //新版本材审
        MaterialVersion materialVersion = materialStatusService.getMaterialsVersion(infoUuid);
        if (materialVersion.isMaterial100()) {
            List<CrowdfundingInfoTreatmentVo.ImageTypeUrl> newAttachments = crowdfundingInfoTreatmentVo.getNewAttachments();
            if (CollectionUtils.isEmpty(newAttachments)) {
                checkoutCode.setCheckoutCode(CfErrorCode.USER_NEW_TREATMENT);
                return checkoutCode;
            }

            newAttachments = newAttachments.stream().
                    filter(item -> null != item && CollectionUtils.isNotEmpty(item.getImageUrl()))
                    .collect(Collectors.toList());
            crowdfundingInfoTreatmentVo.setAttachments(Lists.newArrayList());
            crowdfundingInfoTreatmentVo.setNewAttachments(newAttachments);
            checkoutCode.setCheckoutCode(CfErrorCode.SUCCESS);
            return checkoutCode;
        }

        //对新旧案例的区分
        boolean isOldCrowdFunding = CrowdfundingUtil.decideIsOldCrowdFunding(cfInfoExtBiz.getByInfoUuid(infoUuid));
        //老案例
        if (isOldCrowdFunding) {
            List<String> attachments = crowdfundingInfoTreatmentVo.getAttachments();
            if (CollectionUtils.isEmpty(attachments)) {
                checkoutCode.setCheckoutCode(CfErrorCode.CF_PARAM_ERROR_MISS_TREATMENT_PICS);
                return checkoutCode;
            }
            //对老案例在新页面的兼容
            crowdfundingInfoTreatmentVo.setNewAttachments(Lists.newArrayList());
        } else {//新案例使用旧版的APP上传的数据无法审核，强制驳回
            List<CrowdfundingInfoTreatmentVo.ImageTypeUrl> newAttachments = crowdfundingInfoTreatmentVo.getNewAttachments();
            if (CollectionUtils.isEmpty(newAttachments)) {
                checkoutCode.setCheckoutCode(CfErrorCode.USER_NEW_TREATMENT);
                return checkoutCode;
            }
            //必须填写诊断证明+其他任意两项证明
            newAttachments = newAttachments.stream().
                    filter(item -> null != item && CollectionUtils.isNotEmpty(item.getImageUrl()))
                    .collect(Collectors.toList());
            List<Integer> imageTypeList = newAttachments.stream()
                    .filter(item -> null != item)
                    .map(CrowdfundingInfoTreatmentVo.ImageTypeUrl::getImageType)
                    .collect(Collectors.toList());
            if (newAttachments.size() < 3 || !imageTypeList.contains(AttachmentTypeEnum.ATTACH_TREATMENT.value())) {
                checkoutCode.setCheckoutCode(CfErrorCode.USER_NEW_TREATMENT_LESS_THREE);
                return checkoutCode;
            }
            crowdfundingInfoTreatmentVo.setAttachments(Lists.newArrayList());
            crowdfundingInfoTreatmentVo.setNewAttachments(newAttachments);
        }

        checkoutCode.setCheckoutCode(CfErrorCode.SUCCESS);
        return checkoutCode;
    }

    /**
     * 对案例状态判断
     *
     * @param infoUuid
     * @return
     */
    private CrowdFundingInfoErrorCodeVo checkInfoStatus(String infoUuid) {
        CrowdfundingInfo crowdfundingInfo = this.crowdfundingInfoBiz.getFundingInfo(infoUuid);
        return this.checkInfoStatusImpl(crowdfundingInfo);
    }

    @NotNull
    private CrowdFundingInfoErrorCodeVo checkInfoStatusImpl(CrowdfundingInfo crowdfundingInfo) {
        CrowdFundingInfoErrorCodeVo checkoutCode = new CrowdFundingInfoErrorCodeVo();
        if (null == crowdfundingInfo) {
            checkoutCode.setCheckoutCode(CfErrorCode.CF_INFO_ERROR_STATUS_INVALID);
            return checkoutCode;
        }
        checkoutCode.setCrowdfundingInfo(crowdfundingInfo);
        //若当前案例处于已提交或者是审核通过状态  不让用户更新患者信息
        if (crowdfundingInfo.getStatus() == CrowdfundingStatus.SUBMITTED
                || crowdfundingInfo.getStatus() == CrowdfundingStatus.CROWDFUNDING_STATED) {
            log.info("addOrUpdateBaseInfo info status is:{} infoUuid:{}", crowdfundingInfo.getStatus().value(),
                    crowdfundingInfo.getInfoId());
            checkoutCode.setCheckoutCode(CfErrorCode.CF_INFO_SUBMITTED);
            return checkoutCode;
        }
        //未提交审核但是已经结束
        if (new Date().after(crowdfundingInfo.getEndTime())) {
//			checkoutCode.setCheckoutCode(CfErrorCode.CF_END);
//			return checkoutCode;
            log.info("infoUuid:{} \t is end but update", crowdfundingInfo.getInfoId());
        }
        checkoutCode.setCheckoutCode(CfErrorCode.SUCCESS);
        return checkoutCode;
    }

    /**
     * 自动审核房产,车产
     *
     * @param infoUuid
     */
    private void autoToCheckCreditSupplement(String infoUuid, CrowdfundingInfo crowdfundingInfo) {
        try {
//        房产数量<=1，价值<=100万 且 车产数量<=1，价值<=15万的
            if (StringUtils.isEmpty(infoUuid)) {
                return;
            }
            List<CfCreditSupplement> cfCreditSupplementList = cfCreditSupplementBiz.selectByInfoUuid(infoUuid);
            if (CollectionUtils.isEmpty(cfCreditSupplementList) || cfCreditSupplementList.size() != 2) {
                //在提交之前的材料审核会把旧案例没有提交的车房置为提交
                CrowdfundingInfoStatus crowdfundingInfoStatus = this.crowdfundingInfoStatusBiz.getByInfoUuidAndType(
                        infoUuid, CrowdfundingInfoDataStatusTypeEnum.CREDIT_SUPPLEMENT_INFO_SUBMIT.getCode());
                if (CrowdfundingInfoStatusEnum.SUBMITTED.getCode() == crowdfundingInfoStatus.getStatus()) {
                    this.crowdfundingInfoStatusBiz.updateByInfoId(infoUuid,
                            CrowdfundingInfoDataStatusTypeEnum.CREDIT_SUPPLEMENT_INFO_SUBMIT.getCode(),
                            CrowdfundingInfoStatusEnum.UN_SUBMITTED);
                }
                return;
            }
            boolean houseFlag = false;
            boolean carFlag = false;
            for (CfCreditSupplement cfCreditSupplement : cfCreditSupplementList) {
                if (null == cfCreditSupplement) {
                    return;
                }

                //公约版的不走自动通过机制
                if (cfCreditSupplement.getConvention() == CfCreditSupplementVo.CONVENTION) {
                    return;
                }

                //房子
                if (1 == cfCreditSupplement.getPropertyType()
                        && cfCreditSupplement.getCount() <= 1
                        && cfCreditSupplement.getTotalValue() <= 1000000) {
                    houseFlag = true;
                    continue;
                }
                //车子
                if (2 == cfCreditSupplement.getPropertyType()
                        && cfCreditSupplement.getCount() <= 1
                        && cfCreditSupplement.getTotalValue() <= 150000) {
                    carFlag = true;
                    continue;
                }
            }
            if (houseFlag && carFlag) {
                CfOperatingRecord cfOperatingRecord = cfInfoMirrorService.before(crowdfundingInfo,
                        crowdfundingInfo.getUserId(), crowdfundingInfo.getPayeeName(),
                        CfOperatingRecordEnum.Type.PASS_CREDIT_SUPPLEMENT, CfOperatingRecordEnum.Role.SYSTEM, "c端系统自动审核通过");
                crowdfundingInfoStatusBiz.updateByInfoId(infoUuid,
                        CrowdfundingInfoDataStatusTypeEnum.CREDIT_SUPPLEMENT_INFO_SUBMIT.getCode(),
                        CrowdfundingInfoStatusEnum.PASSED);
                //删除拒绝理由
                log.info("auto pass zeng xin infoUuid:{}", crowdfundingInfo.getInfoId());
                cfRefuseReasonMsgBiz.deleteByInfoUuid(infoUuid);
                cfInfoMirrorService.after(cfOperatingRecord);
            }
        } catch (Exception e) {
            log.error("autoToCheckCreditSupplement", e);
        }
    }

    public boolean saveCfBaseInfo(CrowdfundingBaseInfoBackup info){
        return saveCfBaseInfo(info, false);
    }

    public boolean saveCfBaseInfo(CrowdfundingBaseInfoBackup info, boolean onlySaveBaseInfo) {
        if (info == null) {
            return false;
        }
        long userId = info.getUserId();

        CrowdfundingBaseInfoBackup backup = crowdfundingBaseInfoBackupDao.selectRecentlyCfByUserId(userId, 0);
        boolean success = false;
        if (backup == null) {
            success = crowdfundingBaseInfoBackupDao.insertV2(info) > 0;
        } else {
            info.setId(backup.getId());
            if (onlySaveBaseInfo) {
                success = crowdfundingBaseInfoBackupDao.updateBaseInfo(info) > 0;
            } else {
                success = crowdfundingBaseInfoBackupDao.update(info) > 0;
            }
        }
        log.debug("save userId:{}, success:{}, info:{}", userId, success, info);

        info.setCreateTime(new Date());

        // 新版本催前置消息
        if (info.getCfVersion() >= CfVersion.definition_20191231.getCode()) {
            saveDraftEventPublisher.publishSaveDraftEvent(userId, info.getPageFlag(), info.getCfVersion());
        } else {
            MessageResult sendR = producer.send(
                    new Message<>(MQTopicCons.CF,
                            MQTagCons.CF_SNAPSHOT_SAVE_SUCESS,
                            MQTagCons.CF_SNAPSHOT_SAVE_SUCESS + "_" + info.getId(),
                            info, applicationService.isProduction() ? DelayLevel.M10 : DelayLevel.M2));
            log.info("saveCfBaseInfo sendMQ:{}, result: {}", info, sendR);

            producer.send(
                    new Message<>(MQTopicCons.CF,
                            MQTagCons.CF_SNAPSHOT_SAVE_SUCESS_RIGHT_NOW,
                            MQTagCons.CF_SNAPSHOT_SAVE_SUCESS_RIGHT_NOW + "_" + info.getId(),
                            info));
        }

        //只有前置审核的，才需要发这种消息
        MessageResult send = producer.send(
                new Message<>(MQTopicCons.CF,
                        MQTagCons.CF_SNAPSHOT_SAVE_SUCESS_NOW,
                        MQTagCons.CF_SNAPSHOT_SAVE_SUCESS_NOW + "_" + info.getId(),
                        info, DelayLevel.M5));

        mqProdecer.sendUserLabelAlterMq(0, info.getUserId(), UserLabelAlterEnum.WRITE_DRAFT, DelayLevel.S1);
        log.info("saveCfBaseInfo sendMQ:CF_SNAPSHOT_SAVE_SUCESS_NOW, result: {}", send);

        // 提交第四页发送消息给小鲸鱼用于确权
        if (StringUtils.equals(RaiseCons.PageNameV2.ASSERT_INFO, info.getPageFlag())) {
            Message message = new Message<>(MQTopicCons.CF,
                    CfClientMQTagCons.CF_DRAFT_PAGE_SUBMIT,
                    CfClientMQTagCons.CF_DRAFT_PAGE_SUBMIT + "_" + userId,
                    info);
            MessageResult result = producer.send(message);
            log.info("saveCfBaseInfo send message:{}", result);
        }

        log.info("SUBMIT_BACKUP track {}", info.getUserId());
        BackupSubmit backupSubmit = new BackupSubmit();
        try {
            backupSubmit.setCase_title(info.getTitle());
            backupSubmit.setCase_content(info.getContent());
            backupSubmit.setTarget_amt(Long.valueOf(info.getTargetAmount()));
            backupSubmit.setIs_auto_article(String.valueOf(info.getUseTemplateRaise()));
            backupSubmit.setTemplate_id(0L);

//            if (info.getTemplateParam() != null && CollectionUtils.isNotEmpty(info.getTemplateParam().getContents())
//                                                && info.getTemplateParam().getContents().get(0) != null) {
//                backupSubmit.setTemplate_id(info.getTemplateParam().getContents().get(0).getKey());
//            } else {
//                backupSubmit.setTemplate_id(0l);
//            }
            if (StringUtils.isNoneEmpty(info.getPictureUrl())) {
                String[] imgs = info.getPictureUrl().split(",");
                backupSubmit.setImg_num(Long.valueOf(imgs.length));
            } else {
                backupSubmit.setImg_num(0L);
            }
            backupSubmit.setUser_tag(String.valueOf(info.getUserId()));
            backupSubmit.setUser_tag_type(UserTagTypeEnum.userid);
            analytics.track(backupSubmit);
            log.info("大数据打点上报,提交草稿:{}", JSONObject.toJSONString(backupSubmit));
        } catch (Exception e) {
            log.error("大数据打点上报异常,提交草稿:{}", JSONObject.toJSONString(backupSubmit), e);
        }


        return success;
    }

    /**
     * 图文信息兼容 v3字段设置到v2中
     * v2字段内容走草稿表存储
     */
    private void fillInfoFromV3(CrowdfundingBaseInfoBackup info) {
        CrowdfundingInfoBaseVo v3BaseInfoVO = info.getCrowdfundingInfoBaseVo();
        if (v3BaseInfoVO != null) {
            info.setChannel(StringUtils.trimToEmpty(v3BaseInfoVO.getChannel()));
            info.setTitle(v3BaseInfoVO.getTitle());
            info.setContent(v3BaseInfoVO.getContent());
            String url = Optional.ofNullable(v3BaseInfoVO.getAttachments())
                    .map(v -> StringUtils.join(v, ","))
                    .orElse("");
            info.setPictureUrl(url);
            info.setTargetAmount(Optional.ofNullable(v3BaseInfoVO.getTargetAmount()).orElse(0) * 100);
            info.setPreAuditImageUrl(StringUtils.trimToEmpty(v3BaseInfoVO.getPreAuditImageUrl()));
            info.setPovertyImageUrl(StringUtils.trimToEmpty(v3BaseInfoVO.getPovertyImageUrl()));
        }
    }

    /**
     * 新老草稿兼容
     * 获取草稿数据后 将v2表中数据复制到v3字段中
     */
    private void loadBaseInfoToV3(CrowdfundingBaseInfoBackup v2Data) {
        if (v2Data == null) {
            return;
        }
        CrowdfundingInfoBaseVo v3Data = v2Data.getCrowdfundingInfoBaseVo();
        if (StringUtils.isEmpty(v3Data.getTitle())) {
            v3Data.setTitle(v2Data.getTitle());
        }
        if (StringUtils.isEmpty(v3Data.getContent())) {
            v3Data.setContent(v2Data.getContent());
        }
        String pictureUrl = v2Data.getPictureUrl();
        if (StringUtils.isNotEmpty(pictureUrl)) {
            List<String> images = Arrays.stream(pictureUrl.split(","))
                    .collect(Collectors.toList());
            v3Data.setAttachments(images);
        }
        v3Data.setChannel(v2Data.getChannel());

        int targetAmount = v2Data.getTargetAmount();
        // 3100 存储的是元不需要转换
        int cfVersion = v2Data.getCfVersion();
        if (cfVersion < CfVersion.definition_3100.getCode()) {
            // 前端要求 元单位
            targetAmount = v2Data.getTargetAmount() / 100;
        }
        v3Data.setTargetAmount(targetAmount);
        // 大内容
        v3Data.setPreAuditImageUrl(v2Data.getPreAuditImageUrl());
        v3Data.setPovertyImageUrl(v2Data.getPovertyImageUrl());
    }

    private Map<String, String> obj2DataMap(CrowdfundingBaseInfoBackup info) {
        HashMap<String, String> dataMap = Maps.newHashMap();
        if (info == null) {
            return dataMap;
        }

        // 前置信息
        dataMap.putAll(jsonBundleUtils.obj2Map(info.getCrowdfundingInfoBaseVo(), null, FILTER_KEY_SET, SENSITIVE_KEY_SET));

        // 增信信息
        CfPropertyInsuranceVO creditInfo = Optional.ofNullable(info.getCrowdfundingInfoBaseVo())
                .map(CrowdfundingInfoBaseVo::getPropertyInsuranceParam)
                .orElse(null);
        dataMap.putAll(jsonBundleUtils.obj2Map(creditInfo, CREDIT_INFO_JSON_PREFIX, FILTER_KEY_SET, SENSITIVE_KEY_SET));

        return dataMap;
    }

    /**
     * 如果用户已经发起了案例 删除之前的草稿
     *
     * @param userId
     * @return
     */
    public int deleteSnapshot(long userId) {
        try {
            materialBundleClient.cleanDraft(userId);
        } catch (Exception e) {
            log.error("cleanDraft error userId:{}", userId, e);
        }
        return crowdfundingBaseInfoBackupDao.delete(userId);
    }

    public CrowdfundingBaseInfoBackup selectRecentlyCfByUserId(long userId, int type) {
        CrowdfundingBaseInfoBackup infoBackup = crowdfundingBaseInfoBackupDao.selectRecentlyCfByUserId(userId, type);
        infoBackup = buildDefaultDraft(infoBackup);
        // 填充v3数据
        CrowdfundingBaseInfoBackup crowdfundingBaseInfoBackup = fillDataMap2Obj(infoBackup);
        // 兼容新老草稿
        loadBaseInfoToV3(crowdfundingBaseInfoBackup);
        log.info("get draft userId:{}, type:{}, info:{}", userId, type, infoBackup);

        if (crowdfundingBaseInfoBackup != null) {
            crowdfundingBaseInfoBackup.setTemplateFromDetail();

            // 写入v4草稿
            Response<RaiseCaseParam> draftResponse = raiseDraftService.getDraft(userId);
            if (draftResponse != null && draftResponse.ok()) {
                crowdfundingBaseInfoBackup.setRaiseCaseParam(draftResponse.getData());
            }
        }
        return crowdfundingBaseInfoBackup;
    }

    private CrowdfundingBaseInfoBackup buildDefaultDraft(CrowdfundingBaseInfoBackup infoBackup) {
        if (infoBackup == null) {
            return null;
        }
        if (infoBackup.getCrowdfundingInfoBaseVo() == null) {
            infoBackup.setCrowdfundingInfoBaseVo(new CrowdfundingInfoBaseVo());
        }
        if (infoBackup.getPageFillInfoDetail() == null) {
            infoBackup.setPageFillInfoDetail(new CrowdfundingBaseInfoBackup.PageDetail());
        }
        return infoBackup;
    }

    private CrowdfundingBaseInfoBackup fillDataMap2Obj(CrowdfundingBaseInfoBackup infoBackup) {
        if (infoBackup == null) {
            return null;
        }

        MaterialBundleDO pageBundle = materialBundleClient.getLastByUserIdAndType(infoBackup.getUserId(), MaterialUsageTypeEnum.DRAFT_CONTROL);
        Map<String, String> pageDataMap = Optional.ofNullable(pageBundle)
                .map(MaterialBundleDO::getDataMap)
                .orElse(null);
        if (MapUtils.isNotEmpty(pageDataMap)) {
            // 解析页面浏览详情
            CrowdfundingBaseInfoBackup.PageDetail pageDetail = map2Obj(pageDataMap, CrowdfundingBaseInfoBackup.PageDetail.class,
                    PAGE_FILL_IN_DETAIL_PREFIX);
            infoBackup.setPageFillInfoDetail(pageDetail);
        }

        long materialBundleId = infoBackup.getMaterialBundleId();
        if (materialBundleId <= 0) {
            return infoBackup;
        }
        MaterialBundleDO bundle = materialBundleClient.getById(materialBundleId);
        Map<String, String> dataMap = Optional.ofNullable(bundle).map(MaterialBundleDO::getDataMap).orElse(null);
        if (MapUtils.isEmpty(dataMap)) {
            return infoBackup;
        }

        // 解析v3草稿数据
        CrowdfundingInfoBaseVo crowdfundingInfoBaseVo = map2Obj(dataMap, CrowdfundingInfoBaseVo.class, null);
        CfPropertyInsuranceVO creditInfo = map2Obj(dataMap, CfPropertyInsuranceVO.class, CREDIT_INFO_JSON_PREFIX);
        crowdfundingInfoBaseVo.setPropertyInsuranceParam(creditInfo);

        infoBackup.setCrowdfundingInfoBaseVo(crowdfundingInfoBaseVo);
        return infoBackup;
    }

    public <T> T map2Obj(Map<String, String> dataMap,
                                Class<T> clazz,
                                @Nullable String prefix) {
        return jsonBundleUtils.map2Obj(dataMap, clazz, prefix, SENSITIVE_KEY_SET);
    }

    public Response<Integer> saveCfBaseInfoV3Or2(CrowdfundingBaseInfoBackup baseInfoBackup, HitMomentEnum hitMomentEnum) {
        long userId = baseInfoBackup.getUserId();

        // 以前端传参为准
        if (baseInfoBackup.getCfVersion() <= 0) {
            log.info("前端没传cfVersion userId:{}, draft:{}", userId, baseInfoBackup);
            CfVersion cfVersion = userCfVersionService.getCfVersion(userId);
            baseInfoBackup.setCfVersion(cfVersion.getCode());
        }
        log.info("保存草稿时的cfVersion userId:{}, cfVersion:{}, draft:{}",
                userId, baseInfoBackup.getCfVersion(), baseInfoBackup);

        // 校验targetAmount
        CrowdfundingInfoBaseVo v3Info = baseInfoBackup.getCrowdfundingInfoBaseVo();
        if (v3Info != null && v3Info.getTargetAmount() == null) {
            log.error("save draft lose param userId:{}", userId);
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }

        // 根据版本走不同逻辑
        if (baseInfoBackup.getCfVersion() >= CfVersion.definition_20191231.getCode()){
            return saveDraftV3(baseInfoBackup, hitMomentEnum);
        } else {
            fillInfoFromV3(baseInfoBackup);
            saveCfBaseInfo(baseInfoBackup);
            return NewResponseUtil.makeSuccess(baseInfoBackup.getId());
        }
    }

    @NotNull
    private Response<Integer> saveDraftV3(CrowdfundingBaseInfoBackup info, HitMomentEnum hitMomentEnum) {
        long userId = info.getUserId();
        CrowdfundingInfoBaseVo baseInfoVO = info.getCrowdfundingInfoBaseVo();
        log.info("draft save userId:{}, info:{}", userId, info);

        if (baseInfoVO == null) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }

        baseInfoVO.setChannel(StringUtils.trimToEmpty(baseInfoVO.getChannel()));

//		- pageFlag传值 baseInfo(图文) firstApprove(前置) family(家庭经济状况) protection(患者保障状况)
        String pageFlag = info.getPageFlag();

        if (StringUtils.equals(RaiseCons.PageName.BASE_INFO, pageFlag)) {
            OpResult validateResult = crowdfundingInfoFacade.verifyBaseInfo(baseInfoVO, userId, hitMomentEnum, "");

            if (validateResult.isFail()) {
                log.info("draft valid userId:{}, info:{}, result:{}", userId, info, validateResult);
                return NewResponseUtil.makeError(validateResult.getErrorCode());
            }
        }

        if (StringUtils.equals(RaiseCons.PageName.FIRST_APPROVE, pageFlag)) {

            OpResult validateResult = crowdfundingInfoFacade.verifySubFirstApprove(baseInfoVO, userId);
            if (validateResult.isFail()) {
                log.info("draft valid userId:{}, info:{}, result:{}", userId, info, validateResult);
                return NewResponseUtil.makeError(validateResult.getErrorCode());
            }

            Response<Void> errorResponse = crowdfundingInfoFacade.validateMaterial(userId, baseInfoVO);
            if (errorResponse != null) {
                saveIdCardErrorMsg(errorResponse.getCode(),baseInfoVO.getCfVersion(),userId);
                log.info("draft valid userId:{}, info:{}, result:{}", userId, info, errorResponse.getCode());
                return NewResponseUtil.makeResponse(errorResponse.getCode(),errorResponse.getMsg(),null);
            }
        }

        if (StringUtils.equals(RaiseCons.PageName.FAMILY, pageFlag)) {
            CfPropertyInsuranceVO propertyInsuranceParam = baseInfoVO.getPropertyInsuranceParam();
            // 保存草稿时 不校验患者的个人保障情况
            propertyInsuranceParam.setIgnoreInsuranceValidate(true);
            CfErrorCode validate = propertyInsuranceParam.validate();
            if (validate.getCode() != 0) {
                log.info("draft valid userId:{}, info:{}, result:{}", userId, info, validate);
                return NewResponseUtil.makeError(validate);
            }
        }

        log.info("draft valid success userId:{}", userId);
        /**
         * 保存图文只存储图文数据 为了不覆盖其他数据
         * 保存图文时可能上传非全量草稿。不能覆盖只能替换，覆盖的话会有基本类型默认0等问题
         */
        boolean onlyBaseInfo = StringUtils.equals(RaiseCons.PageName.BASE_INFO, pageFlag);
        if (!onlyBaseInfo) {
            MaterialBundleDO bundle = materialBundleClient.saveDraft(userId, obj2DataMap(info));
            if (bundle == null){
                log.info("draft save bundle fail userId:{}", userId);
                return NewResponseUtil.makeError(CfErrorCode.STORAGE_ERROR);
            } else {
                info.setMaterialBundleId(bundle.getId());
                log.info("draft save bundle success bundle:{}", bundle);
            }
        }

        /*
         保存页面浏览情况
         保存时根据pageFlag记录pageFillInfoDetail 数据不能替换
         */
        MaterialBundleDO bundleDO = saveDraftControlOverlap(userId, pageDetail2DataMap(info));
        if (bundleDO == null) {
            return NewResponseUtil.makeError(CfErrorCode.STORAGE_ERROR);
        }

        // v3草稿图文在crowdfundingInfoBaseVo里面
        fillInfoFromV3(info);

        // 保存图文信息 并发mq
        saveCfBaseInfo(info, onlyBaseInfo);

        return NewResponseUtil.makeSuccess(info.getId());
    }

    /**
     * 保存发起时身份验证失败的信息
     * @param errorCode
     * @param cfVersion
     * @param userId
     */
    public void saveIdCardErrorMsg(int errorCode,Integer cfVersion,long userId){

        log.info("saveIdCardErrorMsg userid={},errorCode={}",userId,errorCode);
       try {
           if (cfVersion == null){
               cfVersion=0;
           }

           if (CfErrorCode.ADD_CROWDFUNDING_IDCARD_ERROR_SELF.getCode() == errorCode
                   ||CfErrorCode.ADD_CROWDFUNDING_IDCARD_ERROR_OTHER.getCode() == errorCode
                   ||CfErrorCode.ADD_CROWDFUNDING_IDCARD_QUOTA_RUNOUT.getCode() == errorCode){

               CrowdfundingBaseInfoBackup backup = crowdfundingBaseInfoBackupDao.selectRecentlyCfByUserId(userId, 0);
               if (backup == null){
                   log.info("saveIdCardErrorMsg backup=null userid={}",userId);
                   return;
               }
               int backupId = backup.getId();
               CfIdCardErrorMsg cfIdCardErrorMsg = new CfIdCardErrorMsg(backupId,errorCode,cfVersion);
               crowdfundingBaseInfoBackupDao.saveCfIdCardErrorMsg(cfIdCardErrorMsg);
           }
       }catch (Exception e){
            log.error("saveIdCardErrorMsg userid={}",userId,e);
       }
    }

    /**
     * 查询身份验证失败的信息
     * @param ids
     * @return
     */
    public List<CfIdCardErrorMsg> getListByBackupId(List<Long> ids){
       return crowdfundingBaseInfoBackupDao.getListByBackupId(ids);
    }

    /**
     * 保存草稿页面情况pageDetail
     * @param userId
     * @param dataMap
     * @return
     */
    public MaterialBundleDO saveDraftControlOverlap(long userId, Map<String, String> dataMap) {
        Response<MaterialBundleDO> resp = materialBundleFeignClient.getLastByUserIdAndType(userId,
                MaterialUsageTypeEnum.DRAFT_CONTROL.getValue());
        log.info("draft get bundle resp:{}", JSON.toJSON(resp));
        if (resp.notOk()) {
            log.info("draft get bundle fail");
            return null;
        }
        MaterialBundleDO bundle = resp.getData();
        if (bundle == null) {
            log.info("draft get bundle empty");
            return materialBundleClient.saveBundle(MaterialUsageTypeEnum.DRAFT_CONTROL, userId, 0, dataMap);
        }
        Map<String, String> preDataMap = bundle.getDataMap();
        if (MapUtils.isEmpty(preDataMap)) {
            bundle.setDataMap(dataMap);
        }else {
            preDataMap.putAll(dataMap);
            bundle.setDataMap(preDataMap);
        }
        return materialBundleClient.saveBundle(bundle);
    }

    private Map<String, String> pageDetail2DataMap(CrowdfundingBaseInfoBackup info) {
        HashMap<String, String> dataMap = Maps.newHashMap();
        if (info == null) {
            return dataMap;
        }
        // 当前页
        dataMap.put("page_flag", JSON.toJSONString(info.getPageFlag()));
        // 页填写情况
        dataMap.put(PAGE_FILL_IN_DETAIL_PREFIX + info.getPageFlag(), JSON.toJSONString(true));
        return dataMap;
    }

    /**月报案例信息*/
    public List<MonthReportSimpleCaseInfo> getListByCaseId(List<Integer> caseIds, long userId) {
        List<MonthReportSimpleCaseInfo> list = Lists.newArrayList();
        for(Integer caseId : caseIds){
            String caseKey = "month_report_case_info_"+caseId;
            MonthReportSimpleCaseInfo caseInfo = cfRedissonHandler.get(caseKey, MonthReportSimpleCaseInfo.class);
            if(caseInfo == null){
                CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getSimpleInfoById(caseId);
                CfInfoExt cfInfoExt = cfInfoExtBiz.getByInfoUuidFromSlave(crowdfundingInfo.getInfoId());
                if(crowdfundingInfo != null){
                    caseInfo = new MonthReportSimpleCaseInfo();
                    List<String> labels = labelService.getCaseLabels(crowdfundingInfo, userId,cfInfoExt);
                    caseInfo.setLabels(labels);
                    caseInfo.setTitle(crowdfundingInfo.getTitle());
                    caseInfo.setAmount(crowdfundingInfo.getAmount());
                    caseInfo.setCaseId(caseId);
                    long timeStatus = crowdfundingInfo.getEndTime().getTime() - System.currentTimeMillis();
                    caseInfo.setStatus(timeStatus > 0 ? 0 : 1);
                    cfRedissonHandler.setEX(caseKey,caseInfo,RedissonHandler.ONE_MINUTE);
                }
            }
            if(caseInfo != null){
                list.add(caseInfo);
            }
        }
        return list;
    }

    /**
     * 判断发起人/患者是否为新疆, 并返回鹰眼结果
     *
     * @param infoUuid  -
     * @return -
     */
    public Response<CrowdfundingBilingualVo> judgeBilingual(String infoUuid) {
        CfInfoSimpleModel fundingInfo = crowdfundingInfoSimpleBiz.getFundingInfo(infoUuid);
        if (Objects.isNull(fundingInfo)) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        int caseId = fundingInfo.getId();
        CrowdfundingBilingualVo crowdfundingBilingualVo = xJJudgeResult(caseId);
        return NewResponseUtil.makeSuccess(crowdfundingBilingualVo);
    }


    public CrowdfundingBilingualVo xJJudgeResult(int caseId) {
        CrowdfundingBilingualVo crowdfundingBilingualVo = new CrowdfundingBilingualVo();
        String idCard = judgeIdCard(caseId);

        if (StringUtils.isBlank(idCard)) {
            return crowdfundingBilingualVo;
        }
        IdcardInfoExtractor infoExtractor = new IdcardInfoExtractor(idCard);
        crowdfundingBilingualVo.setXj(StringUtils.equals(infoExtractor.getProvince(), "新疆"));
        String cityCode = idCard.substring(0, 4);
        JSONObject jsonObject = JSONObject.parseObject(doubleLanguageCityConfig);
        if (MapUtils.isNotEmpty(jsonObject)) {
            for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                String value = entry.getValue().toString();
                if (StringUtils.isBlank(value)) {
                    continue;
                }
                boolean contains = Splitter.on(",").splitToList(value).contains(cityCode);
                if (contains) {
                    crowdfundingBilingualVo.setDoubleLanguage(entry.getKey());
                    break;
                }
            }
        }
        return crowdfundingBilingualVo;
    }

    private String judgeIdCard(int caseId) {
        CrowdfundingAuthor crowdfundingAuthor = crowdfundingAuthorBiz.get(caseId);
        String idCard = Optional.ofNullable(crowdfundingAuthor).map(r -> {
            UserIdentityType idType = r.getIdType();
            if (idType == UserIdentityType.identity) {
                String raiseIdCard = r.getCryptoIdCard();
                if (StringUtils.isNotBlank(raiseIdCard)) {
                    return shuidiCipher.decrypt(raiseIdCard);
                }
            }
            return StringUtils.EMPTY;
        }).orElse(StringUtils.EMPTY);

        if (StringUtils.isBlank(idCard)) {
            CfFirsApproveMaterial cfFirsApproveMaterial = cfFirstApproveBiz.getByInfoId(caseId);
            idCard = Optional.ofNullable(cfFirsApproveMaterial).map(r -> {
                int patientIdType = r.getPatientIdType();
                if (UserIdentityType.identity.getCode() == patientIdType
                        && StringUtils.isNotBlank(r.getPatientCryptoIdcard())) {
                    return shuidiCipher.decrypt(r.getPatientCryptoIdcard());
                }
                String selfCryptoIdCard = r.getSelfCryptoIdcard();
                if (StringUtils.isNotBlank(selfCryptoIdCard)) {
                    return shuidiCipher.decrypt(selfCryptoIdCard);
                }
                return StringUtils.EMPTY;
            }).orElse(StringUtils.EMPTY);
        }
        return idCard;
    }


    public Response<List<CrowdfundingBarrageModel>> getBarrageCaseList() {
        List<CrowdfundingBarrageModel> barrageCaseCache = cfRedissonHandler.getList(CROWDFUNDING_CLEW_BARRAGE_CASE_KEY, CrowdfundingBarrageModel.class);
        if (CollectionUtils.isNotEmpty(barrageCaseCache)) {
            return NewResponseUtil.makeSuccess(barrageCaseCache);
        }
        final int RESULT_SIZE = 30;
        final int DAYS_INTERVAL = -10;
        final int MIN_AMOUNT = 10000000;
        Date currentDate = com.shuidihuzhu.common.util.DateUtil.getCurrentDate();
        Date startDate = DateUtil.addDay(currentDate, DAYS_INTERVAL);
        int limit = RESULT_SIZE;
        List<CrowdfundingBarrageModel> result = Lists.newArrayList();
        while (result.size() < RESULT_SIZE) {
            List<CrowdfundingInfo> crowdfundingInfoListByAmount = crowdfundingInfoBiz.getCrowdfundingInfoListByAmount(MIN_AMOUNT, null, startDate, currentDate, limit);
            List<CrowdfundingBarrageModel> crowdfundingBarrageModels = handleBarrageModelResult(crowdfundingInfoListByAmount);
            result.addAll(crowdfundingBarrageModels);
            // 在当前时间范围内不够在下一个时间范围进行补充
            if (crowdfundingBarrageModels.size() < limit) {
                limit = limit - crowdfundingBarrageModels.size();
            }
            currentDate = startDate;
            startDate = DateUtil.addDay(startDate, DAYS_INTERVAL);
            // 只获取最近一个月的案例，不够30条随机取
            if (startDate.getTime() < DateUtil.addDay(DateUtil.getCurrentDate(), -31).getTime() && result.size() < RESULT_SIZE) {
                currentDate = DateUtil.getCurrentDate();
                startDate = DateUtil.addDay(currentDate, DAYS_INTERVAL);
                List<CrowdfundingInfo> randomCase = crowdfundingInfoBiz.getCrowdfundingInfoListByAmount(0, MIN_AMOUNT, startDate, com.shuidihuzhu.common.util.DateUtil.getCurrentDate(), RESULT_SIZE - result.size());
                result.addAll(handleBarrageModelResult(randomCase));
                break;
            }
        }
        cfRedissonHandler.addListEX(CROWDFUNDING_CLEW_BARRAGE_CASE_KEY, result, TimeUnit.DAYS.toSeconds(1));
        return NewResponseUtil.makeSuccess(result);
    }

    private List<CrowdfundingBarrageModel> handleBarrageModelResult(List<CrowdfundingInfo> crowdfundingInfoList) {
        List<CrowdfundingBarrageModel> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(crowdfundingInfoList)) {
            return result;
        }
        List<Integer> caseIdList = crowdfundingInfoList.stream().map(CrowdfundingInfo::getId).collect(Collectors.toList());
        List<Long> userIdList = crowdfundingInfoList.stream().map(CrowdfundingInfo::getUserId).collect(Collectors.toList());
        List<UserInfoModel> userInfoModels = userInfoDelegate.getUserInfoByUserIdBatch(userIdList);
        Map<Integer, CfFirsApproveMaterial> mapByInfoIds = cfFirstApproveBiz.getMapByInfoIds(caseIdList);
        Map<Long, UserInfoModel> userInfoModelMap = userInfoModels.stream().collect(Collectors.toMap(UserInfoModel::getUserId, Function.identity(), (before, after) -> before));
        return crowdfundingInfoList.stream()
                .map(crowdfundingInfo -> createBarrageModel(crowdfundingInfo, userInfoModelMap, mapByInfoIds))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private CrowdfundingBarrageModel createBarrageModel(CrowdfundingInfo crowdfundingInfo, Map<Long, UserInfoModel> userInfoModelMap, Map<Integer, CfFirsApproveMaterial> mapByInfoIds) {
        UserInfoModel userInfoModel = userInfoModelMap.get(crowdfundingInfo.getUserId());

        if (userInfoModel == null || StringUtils.isBlank(userInfoModel.getHeadImgUrl())) {
            return null;
        }

        CfFirsApproveMaterial cfFirsApproveMaterial = mapByInfoIds.get(crowdfundingInfo.getId());
        if (cfFirsApproveMaterial == null) {
            return null;
        }

        CrowdfundingBarrageModel crowdfundingBarrageModel = new CrowdfundingBarrageModel();
        crowdfundingBarrageModel.setHeadImgUri(userInfoModel.getHeadImgUrl());
        crowdfundingBarrageModel.setAmount(crowdfundingInfo.getAmount() / 100);
        String authorProvince = crowdfundingInfoDetailService.getAuthorProvince(shuidiCipher.decrypt(cfFirsApproveMaterial.getPatientCryptoIdcard()));
        crowdfundingBarrageModel.setProvince(authorProvince);
        crowdfundingBarrageModel.setPatientName(encryptName(cfFirsApproveMaterial.getPatientRealName()));

        return crowdfundingBarrageModel;
    }

    private static String encryptName(String name) {
        if (StringUtils.isEmpty(name)) {
            return "";
        }

        switch (name.length()) {
            case 2:
                return name.charAt(0) + "*";
            case 3:
                return name.charAt(0) + "*" + "*";
            default:
                return name.charAt(0) + "**";
        }
    }
}
