package com.shuidihuzhu.cf.service.inventory.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.dao.inventory.CfInventoryDao;
import com.shuidihuzhu.cf.domain.UserDeviceInfo;
import com.shuidihuzhu.cf.enums.FirstInventoryEnum;
import com.shuidihuzhu.cf.enums.UserInventoryEnum;
import com.shuidihuzhu.cf.model.inventory.SecondInventoryDetail;
import com.shuidihuzhu.cf.model.inventory.SecondInventoryModel;
import com.shuidihuzhu.cf.service.inventory.AppInventoryService;
import com.shuidihuzhu.cf.service.inventory.InventoryRuleHandle;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

/**
 * @Description: 个人清单服务
 * @Author: panghairui
 * @Date: 2023/1/11 4:40 下午
 */
@Slf4j
@Service
public class AppInventoryServiceImpl implements AppInventoryService {

    @Resource
    private CfInventoryDao cfInventoryDao;

    private static final Map<String, InventoryRuleHandle> handlers = new HashMap<>();

    @Resource
    private NickNameRuleHandle nickNameRuleHandle;
    @Resource
    private HeadImgRuleHandle headImgRuleHandle;
    @Resource
    private UserNameRuleHandle userNameRuleHandle;
    @Resource
    private IdentityCardRuleHandle identityCardRuleHandle;
    @Resource
    private CreditCardRuleHandle creditCardRuleHandle;
    @Resource
    private OrderHistoryRuleHandle orderHistoryRuleHandle;
    @Resource
    private CashDrawRuleHandle cashDrawRuleHandle;
    @Resource
    private ThirdPartyRuleHandle thirdPartyRuleHandle;
    @Resource
    private LocationRuleHandle locationRuleHandle;
    @Resource
    private DeviceMaskRuleHandle deviceMaskRuleHandle;
    @Resource
    private DeviceRuleHandle deviceRuleHandle;
    /**
     * 初始化handler
     */
    @PostConstruct
    private void init() {
        handlers.put("nickNameRuleHandle", nickNameRuleHandle);
        handlers.put("headImgRuleHandle", headImgRuleHandle);
        handlers.put("userNameRuleHandle", userNameRuleHandle);
        handlers.put("identityCardRuleHandle", identityCardRuleHandle);
        handlers.put("creditCardRuleHandle", creditCardRuleHandle);
        handlers.put("orderHistoryRuleHandle", orderHistoryRuleHandle);
        handlers.put("cashDrawRuleHandle", cashDrawRuleHandle);
        handlers.put("thirdPartyRuleHandle", thirdPartyRuleHandle);
        handlers.put("locationRuleHandle", locationRuleHandle);
        handlers.put("deviceMaskRuleHandle", deviceMaskRuleHandle);
        handlers.put("deviceRuleHandle", deviceRuleHandle);
    }

    @Override
    public Boolean addDeviceInfo(long userId, String deviceInfo) {

        log.info("AppInventoryServiceImpl addDeviceInfo userId {}", userId);

        // 从json中解析所有设备信息
        List<UserDeviceInfo> userDeviceInfos = buildDeviceInfos(userId, deviceInfo);
        if (CollectionUtils.isEmpty(userDeviceInfos) || userDeviceInfos.size() == 0) {
            return false;
        }

        // 保存该用户所有设备数据
        int num = cfInventoryDao.insertDeviceInfoList(userDeviceInfos);

        return num != 0;
    }

    @Override
    public SecondInventoryModel getSecondInventoryModel(long userId, String firstType, String period) {

        log.info("AppInventoryServiceImpl getSecondInventoryModel userId {} firstType {} period {}", userId, firstType, period);

        // 先根据 firstType(一级清单标识) 取出二级清单信息
        FirstInventoryEnum firstInventoryEnum = FirstInventoryEnum.fromFirstType(firstType);
        if (Objects.isNull(firstInventoryEnum)) {
            return null;
        }
        List<SecondInventoryDetail> secondInventoryDetails = UserInventoryEnum.getSecondInventoryDetail(userId, firstInventoryEnum);
        if (CollectionUtils.isEmpty(secondInventoryDetails)) {
            return null;
        }

        // period -> 时间戳
        long time = caseToTimestamp(period);
        if (time == 0) {
            return null;
        }

        // 各自的二级清单根据各自的策略取数据
        for (SecondInventoryDetail secondInventoryDetail : secondInventoryDetails) {
            InventoryRuleHandle ruleHandler = handlers.get(secondInventoryDetail.getRule());
            if (Objects.isNull(ruleHandler)) {
                continue;
            }
            ruleHandler.handle(secondInventoryDetail, time);
            secondInventoryDetail.setUserId(-1L);
            log.info("AppInventoryServiceImpl getSecondInventoryModel secondInventoryDetail {}", JSONObject.toJSONString(secondInventoryDetail));
        }

        return SecondInventoryModel.builder()
                .firstType(firstType)
                .list(secondInventoryDetails)
                .build();
    }

    private static long caseToTimestamp(String period) {

        Calendar calendar = Calendar.getInstance();
        switch (period) {
            case "sevenDays":
                calendar.set(Calendar.DATE, calendar.get(Calendar.DATE) - 7);
                return calendar.getTimeInMillis();
            case "thirtyDays":
                calendar.set(Calendar.DATE, calendar.get(Calendar.DATE) - 30);
                return calendar.getTimeInMillis();
            case "oneYear":
                calendar.set(Calendar.DATE, calendar.get(Calendar.DATE) - 365);
                return calendar.getTimeInMillis();
        }

        return 0;
    }

    private List<UserDeviceInfo> buildDeviceInfos(long userId, String deviceInfo) {

        JSONObject deviceInfoObject = JSONObject.parseObject(deviceInfo);
        if (Objects.isNull(deviceInfoObject)) {
            return Lists.newArrayList();
        }

        List<UserDeviceInfo> userDeviceInfos = Lists.newArrayList();
        for (Map.Entry<String, Object> entry : deviceInfoObject.entrySet()) {

            String value = (String) entry.getValue();
            if (StringUtils.isEmpty(value)) {
                continue;
            }

            UserDeviceInfo userDeviceInfo = UserDeviceInfo.builder()
                    .userId(userId)
                    .identification(entry.getKey())
                    .content(value)
                    .build();
            userDeviceInfos.add(userDeviceInfo);
        }

        return userDeviceInfos;
    }
}
