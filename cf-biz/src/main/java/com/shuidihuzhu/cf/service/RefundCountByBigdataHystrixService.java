package com.shuidihuzhu.cf.service;

import brave.Tracing;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixProperty;
import com.shuidihuzhu.cf.client.stat.enums.StatDataEnum;
import com.shuidihuzhu.cf.client.stat.facade.Sos;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.dataservice.faceApi.v1.FaceApiClient;
import com.shuidihuzhu.client.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class RefundCountByBigdataHystrixService {

    @Autowired
    private FaceApiClient faceApiClient;

    @HystrixCommand(fallbackMethod = "getRefundCountFallback", commandProperties =
            {
                    @HystrixProperty(name = "execution.isolation.thread.timeoutInMilliseconds", value = "50")
            })
    public Integer getRefundCount(int infoId, String infoUuid) {
        StopWatch watch = StopWatch.createStarted();
        Response<Map<String, String>> resp = faceApiClient.caseQuery(infoUuid,
                Lists.newArrayList("cf_donator_refund_cnt"));
        if(resp == null || resp.getCode() != 0){
            log.warn("get refundCnt by bigData fail caseId:{}, resp:{}", infoId, resp);
            return null;
        }
        Map<String, String> data = resp.getData();

        String caseDonateUserCount = MapUtils.getString(data, "cf_donator_refund_cnt");

        if (StringUtils.isBlank(caseDonateUserCount)) {
            return null;
        }

        watch.stop();
        log.debug("getRefundCount time :{}",watch.getTime());
        return Integer.parseInt(caseDonateUserCount);
    }

    public Integer getRefundCountFallback(int infoId, String infoUuid) {
        return null;
    }

    public Integer getUserCaseQuery(int infoId, long userId, List<String> tags) {
        StopWatch watch = StopWatch.createStarted();
        Response<Map<String, String>> resp = faceApiClient.userCaseQuery(userId, infoId, tags);
        if (Objects.isNull(resp) || resp.getCode() != 0) {
            return 0;
        }

        Map<String, String> data = resp.getData();
        String tagUserCount = MapUtils.getString(data, "cf_user_case_visit_cnt");
        if (StringUtils.isBlank(tagUserCount)) {
            return 0;
        }

        watch.stop();
        log.info("RefundCountByBigdataHystrixService getUserCaseQuery time :{}",watch.getTime());
        return Integer.parseInt(tagUserCount);
    }

    public Integer getUserCaseQueryFallback(int infoId, long userId, List<String> tags) {
        return 0;
    }

    @HystrixCommand(fallbackMethod = "getUserCaseQueryMapFallback", commandProperties =
            {
                    @HystrixProperty(name = "execution.isolation.thread.timeoutInMilliseconds", value = "500")
            })
    public Map<String, String> getUserCaseQueryMap(int infoId, long userId, List<String> tags) {

        try {
            Response<Map<String, String>> resp = faceApiClient.query(userId, tags);
            return Optional.ofNullable(resp)
                    .map(Response::getData)
                    .orElse(Maps.newHashMap());
        } catch (Exception e) {
            log.warn("RefundCountByBigdataHystrixService getUserCaseQueryMap error, infoId:{}, userId:{}, tags:{}", infoId, userId, tags, e);
            return Maps.newHashMap();
        }
    }

    public Map<String, String> getUserCaseQueryMapFallback(int infoId, long userId, List<String> tags) {
        return Maps.newHashMap();
    }

}
