package com.shuidihuzhu.cf.service.combineorder;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.dao.crowdfunding.combine.CfCombineOrderManageDao;
import com.shuidihuzhu.cf.model.contribute.CfCombineOrderManage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class CfCombineOrderManageService {

    @Autowired
    private CfCombineOrderManageDao combineOrderManageDao;

    public int addCombineOrderList(List<CfCombineOrderManage> combineList) {
        if (CollectionUtils.isEmpty(combineList)) {
            return 0;
        }

        return combineOrderManageDao.addCombineOrderList(combineList);
    }

    public int updateThirdPayUid(List<CfCombineOrderManage> combineList) {

        if (CollectionUtils.isEmpty(combineList)) {
            return 0;
        }
        int cnt = 0;
        for (CfCombineOrderManage orderManage : combineList) {
            cnt += combineOrderManageDao.updatePayStatus(orderManage.getParentThirdPayUid(),
                    orderManage.getSubThirdPayUid(),
                    orderManage.getSubPayUid());
        }
        return cnt;
    }

    public List<CfCombineOrderManage> selectByParentPayUid(String parentPayUid) {
        if (StringUtils.isBlank(parentPayUid)) {
            return Lists.newArrayList();
        }

        return combineOrderManageDao.selectByParentUids(Lists.newArrayList(parentPayUid));
    }

    public List<CfCombineOrderManage> selectBySubPayUid(List<String> subPayUids) {
        if (CollectionUtils.isEmpty(subPayUids)) {
            return Lists.newArrayList();
        }

        return combineOrderManageDao.selectBySubUids(subPayUids);
    }

    public CfCombineOrderManage getByParentThirdPayUidAndOrderType(String parentThirdPayUid, int orderType) {
        if (StringUtils.isBlank(parentThirdPayUid) || orderType <= 0) {
            return null;
        }
        return combineOrderManageDao.getByParentThirdPayUidAndOrderType(parentThirdPayUid, orderType);
    }

    public List<CfCombineOrderManage> selectBySubThirdPayUid(List<String> subThirdPayUids) {
        if (CollectionUtils.isEmpty(subThirdPayUids)) {
            return Collections.emptyList();
        }
        return combineOrderManageDao.selectBySubThirdPayUid(subThirdPayUids);
    }

    public CfCombineOrderManage getByOrderId(long orderId) {
        return combineOrderManageDao.getByOrderId(orderId);
    }
}
