package com.shuidihuzhu.cf.service.activity;

import com.shuidihuzhu.cf.domain.activity.ActivityDonatorRedPocketDO;
import com.shuidihuzhu.cf.domain.activity.ActivityDonatorRedPocketHistory;
import com.shuidihuzhu.cf.response.OpResult;

import java.util.List;

/**
 * Created by sven on 2020/5/6.
 *
 * <AUTHOR>
 */
public interface IActivityDonatorRedPocketService {

    void create(int caseId);

    int getPocketValue(List<ActivityDonatorRedPocketDO> list);

    OpResult<Void> add(int caseId, int shareDv, long userId, long activityId);

    List<ActivityDonatorRedPocketDO> getByCaseId(int caseId);

    List<ActivityDonatorRedPocketHistory> getLastThree(int caseId);

    boolean canAttendPocket(int caseId, long userId);

    boolean getTodayAttend(long userId);

    void setTodayAttend(long userId);

    void cleanUserSubsidyBackdoor(long activityId, int caseId, long userId);
}
