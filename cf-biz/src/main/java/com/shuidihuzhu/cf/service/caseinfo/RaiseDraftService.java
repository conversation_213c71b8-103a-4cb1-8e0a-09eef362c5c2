package com.shuidihuzhu.cf.service.caseinfo;

import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.client.ugc.model.domain.risk.RiskControlWordCategoryDO;
import com.shuidihuzhu.cf.client.ugc.model.enums.HitLocationEnum;
import com.shuidihuzhu.cf.client.ugc.model.enums.HitMomentEnum;
import com.shuidihuzhu.cf.client.ugc.model.view.RiskWordResultV2;
import com.shuidihuzhu.cf.constants.ModuleKeyCons;
import com.shuidihuzhu.cf.constants.crowdfunding.RaiseCons;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.delegate.verifycode.IVerifyCodeDelegate;
import com.shuidihuzhu.cf.enhancer.utils.HelpResponseUtils;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.facade.CrowdfundingInfoFacadeImpl;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingBaseInfoBackup;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.param.SendHitNoticeToAlarmParam;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoBaseVo;
import com.shuidihuzhu.cf.param.raise.RaiseBasicInfoParam;
import com.shuidihuzhu.cf.param.raise.RaiseCaseParam;
import com.shuidihuzhu.cf.response.OpResult;
import com.shuidihuzhu.cf.service.TracerService;
import com.shuidihuzhu.cf.service.crowdfunding.CrowdfundingInfoService;
import com.shuidihuzhu.cf.service.risk.word.RiskControlWordHelpService;
import com.shuidihuzhu.cf.service.user.UserMobileService;
import com.shuidihuzhu.cf.util.raise.RaiseParamConvert;
import com.shuidihuzhu.cf.vo.initialaudit.CfPropertyInsuranceVO;
import com.shuidihuzhu.client.cf.risk.client.CfUgcWhileListClient;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.frame.client.api.platform.NewUserClient;
import com.shuidihuzhu.frame.client.model.platform.ModuleNewUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 存啥取啥
 * 每次保存得写老草稿表
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class RaiseDraftService {

    @Autowired
    private CrowdfundingInfoService crowdfundingInfoService;

    @Autowired
    private UserMobileService userMobileService;

    @Autowired
    private CrowdfundingInfoFacadeImpl crowdfundingInfoFacade;

    @Autowired
    private DraftBundleStorageService draftBundleJsonService;

    @Resource
    private RiskControlWordHelpService riskControlWordHelpService;

    @Resource
    private UserInfoDelegate userInfoDelegate;
    @Resource
    private CfUgcWhileListClient cfUgcWhileListClient;
    @Autowired
    private TracerService tracerService;

    @Resource
    private NewUserClient newUserClient;

    @Resource
    private IVerifyCodeDelegate verifyCodeDelegate;

    /**
     * 处理手机号(可能有修改)
     * 检查此页参数合法
     * 保存此页草稿
     * 查询完整草稿
     * 转换为老版草稿model并保存到原草稿表
     * @param raiseCaseParam
     * @return
     */
    public Response<Integer> save(RaiseCaseParam raiseCaseParam, HitMomentEnum hitMomentEnum) {

        // 若是第一页检查是否有修改手机号
        Response<Void> mobileResponse = promoteMobile(raiseCaseParam);
        if (mobileResponse.notOk()) {
            return HelpResponseUtils.makeRelayError(mobileResponse);
        }

        // 检查此页参数合法
        Response<Void> validateResult = checkParam(raiseCaseParam, hitMomentEnum);
        if (validateResult.notOk()){
            return HelpResponseUtils.makeRelayError(validateResult);
        }

        // 保存此页草稿
        Response<RaiseCaseParam> saveByPage = draftBundleJsonService.saveByPage(raiseCaseParam);
        if (saveByPage.notOk()) {
            return HelpResponseUtils.makeRelayError(saveByPage);
        }

        // 转换为老版草稿model并保存到原草稿表
        RaiseCaseParam data = saveByPage.getData();
        CrowdfundingBaseInfoBackup draft = RaiseParamConvert.convertDraft(data);
        crowdfundingInfoService.saveCfBaseInfo(draft, true);

        return NewResponseUtil.makeSuccess(draft.getId());
    }

    private boolean userInWhileList(long userId) {
        boolean userInWhileList = false;
        Response<Boolean> inWhileList = cfUgcWhileListClient.inWhileList(userId);
        if (inWhileList.ok()) {
            userInWhileList = Optional.ofNullable(inWhileList.getData()).orElse(false);
        }
        return userInWhileList;
    }


    /**
     * 检查草稿参数合法
     */
    private Response<Void> checkParam(RaiseCaseParam raiseCaseParam, HitMomentEnum hitMomentEnum){
        long userId = raiseCaseParam.getUserId();
        String pageFlag = raiseCaseParam.getPageFlag();
        CrowdfundingInfoBaseVo baseInfoVO = RaiseParamConvert.convertRaise(raiseCaseParam, null);

        RaiseBasicInfoParam basicInfoParam = raiseCaseParam.getBasicInfoParam();
        boolean userInWhileList = userInWhileList(userId);
        String diseaseName = Optional.ofNullable(basicInfoParam)
                .map(RaiseBasicInfoParam::getDiseaseName)
                .orElse("");
        if (StringUtils.equals(RaiseCons.PageNameV2.BASIC_INFO, pageFlag)) {
            OpResult<CrowdfundingInfo> verifyRaiseBasicInfoOPResult = crowdfundingInfoFacade.verifyRaiseBasicInfo(baseInfoVO, userId);
            if (verifyRaiseBasicInfoOPResult.isFail()) {
                log.info("draft verifyRaiseBasicInfo userId:{}, info:{}, result:{}", userId, baseInfoVO, verifyRaiseBasicInfoOPResult);
                return NewResponseUtil.makeError(verifyRaiseBasicInfoOPResult.getErrorCode());
            }
        }
        if (Objects.nonNull(basicInfoParam) && StringUtils.isNotEmpty(basicInfoParam.getDiseaseName()) && !userInWhileList) {
            RiskWordResultV2 diseaseNameResult = riskControlWordHelpService.isHitRaiseBanneredV1(diseaseName, RiskControlWordCategoryDO.RiskWordUseScene.CASE_RAISE_DISEASE,
                    hitMomentEnum.getCode(), HitLocationEnum.DISEASE_NAME.getCode(), tracerService.getSpanId(), userId, "", "", diseaseName);

            if (!diseaseNameResult.isPassed()) {
                UserInfoModel userInfoModel = userInfoDelegate.getUserInfoByUserId(userId);
                SendHitNoticeToAlarmParam sendHitNoticeToAlarmParam = SendHitNoticeToAlarmParam.builder()
                        .userInfoModel(userInfoModel)
                        .hitResult(diseaseNameResult)
                        .diseaseName(diseaseName)
                        .desc(hitMomentEnum.getEnumDesc(hitMomentEnum.getCode()))
                        .location(HitLocationEnum.DISEASE_NAME.getEnumDesc(HitLocationEnum.DISEASE_NAME.getCode()))
                        .build();
                crowdfundingInfoFacade.sendHitNoticeToAlarm(sendHitNoticeToAlarmParam);
                return NewResponseUtil.makeError(CfErrorCode.CF_INFO_HIT_SENSTIVE_WORDS);
            }
        }

        if (StringUtils.equals(RaiseCons.PageNameV2.TITLE_IMAGE, pageFlag)) {
            OpResult validateResult = crowdfundingInfoFacade.verifyBaseInfo(baseInfoVO, userId, hitMomentEnum, diseaseName);

            if (validateResult.isFail()) {
                log.info("draft valid userId:{}, info:{}, result:{}", userId, baseInfoVO, validateResult);
                return NewResponseUtil.makeError(validateResult.getErrorCode());
            }
        }

        if (StringUtils.equals(RaiseCons.PageNameV2.FIRST_APPROVE, pageFlag)) {

            OpResult validateResult = crowdfundingInfoFacade.verifySubFirstApprove(baseInfoVO, userId);
            if (validateResult.isFail()) {
                log.info("draft valid userId:{}, info:{}, result:{}", userId, baseInfoVO, validateResult);
                return NewResponseUtil.makeError(validateResult.getErrorCode());
            }

            Response<Void> errorResponse = crowdfundingInfoFacade.validateMaterial(userId, baseInfoVO);
            if (errorResponse != null) {
                crowdfundingInfoService.saveIdCardErrorMsg(errorResponse.getCode(),baseInfoVO.getCfVersion(),userId);
                log.info("draft valid userId:{}, info:{}, result:{}", userId, baseInfoVO, errorResponse.getCode());
                return NewResponseUtil.makeResponse(errorResponse.getCode(),errorResponse.getMsg(),null);
            }
        }

        if (StringUtils.equals(RaiseCons.PageNameV2.ASSERT_INFO, pageFlag)) {
            CfPropertyInsuranceVO propertyInsuranceParam = baseInfoVO.getPropertyInsuranceParam();
            // 保存草稿时 不校验患者的个人保障情况
            propertyInsuranceParam.setIgnoreInsuranceValidate(true);
            CfErrorCode validate = propertyInsuranceParam.validate();
            if (validate.getCode() != 0) {
                log.info("draft valid userId:{}, info:{}, result:{}", userId, baseInfoVO, validate);
                return NewResponseUtil.makeError(validate);
            }
        }
        return NewResponseUtil.makeSuccess(null);
    }


//    public void saveRiskControlRecord()
    private Response<Void> promoteMobile(RaiseCaseParam raiseCaseParam) {
        String pageFlag = raiseCaseParam.getPageFlag();
        if (!StringUtils.equals(pageFlag, RaiseCons.PageNameV2.BASIC_INFO)) {
            return NewResponseUtil.makeSuccess(null);
        }
        RaiseBasicInfoParam basicInfoParam = raiseCaseParam.getBasicInfoParam();
        String selfMobile = basicInfoParam.getSelfMobile();
        String verifyCode = basicInfoParam.getVerifyCode();

        //必须有手机号
        if (StringUtils.isBlank(selfMobile)) {
            return NewResponseUtil.makeFail("您的手机号未填写！");
        }

        String mobileFromCf = userMobileService.getMobileByUserIdWithInitFromAccountMobile(raiseCaseParam.getUserId());
        if (StringUtils.equals(selfMobile, mobileFromCf)) {
            return NewResponseUtil.makeSuccess(null);
        }
        //手机号不一致，需要校验验证码
        if (StringUtils.isEmpty(verifyCode)) {
            return NewResponseUtil.makeFail("验证码未填写，请先获取验证码");
        }

        return userMobileService.checkVerifyCodeAndSaveMobile(
                raiseCaseParam.getUserId(),
                basicInfoParam.getSelfMobile(),
                raiseCaseParam.getClientIp(),
                basicInfoParam.getVerifyCode(),
                basicInfoParam.getKey());
    }

    public Response<RaiseCaseParam> getDraft(long userId) {
        return draftBundleJsonService.getDraft(userId);
    }

    public Response<RaiseCaseParam> getDraft(long userId, List<String> pageNames) {
        return draftBundleJsonService.getDraft(userId, pageNames);
    }

    public Response<Void> cleanAllDraft(long userId) {
        crowdfundingInfoService.deleteSnapshot(userId);
        Response<ModuleNewUser> response = newUserClient.get(ModuleKeyCons.FIRST_COPY,userId);
        if(Objects.isNull(response)){
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_ERROR);
        }
        ModuleNewUser moduleNewUser = response.getData();
        if(Objects.isNull(moduleNewUser)){
            return NewResponseUtil.makeSuccess(null);
        }
        newUserClient.updateModuleKey(ModuleKeyCons.FIRST_COPY+"-DELETE-"+moduleNewUser.getId(),userId,moduleNewUser.getId());
        return NewResponseUtil.makeSuccess(null);
    }

}
