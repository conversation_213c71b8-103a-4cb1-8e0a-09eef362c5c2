package com.shuidihuzhu.cf.service.inventory.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingOrderShardingCrowdfundingIdDao;
import com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingOrderShardingUserIdDao;
import com.shuidihuzhu.cf.model.inventory.SecondInventoryDetail;
import com.shuidihuzhu.cf.service.inventory.InventoryRuleHandle;
import com.shuidihuzhu.cf.util.DesensitizationUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description: 订单记录
 * @Author: panghairui
 * @Date: 2023/1/12 1:59 下午
 */
@Slf4j
@Service("orderHistoryRuleHandle")
public class OrderHistoryRuleHandle implements InventoryRuleHandle {

    @Resource
    private CrowdfundingOrderShardingUserIdDao crowdfundingOrderShardingUserIdDao;

    @Resource
    private CrowdfundingOrderShardingCrowdfundingIdDao crowdfundingOrderShardingCrowdfundingIdDao;

    @Override
    public void handle(SecondInventoryDetail secondInventoryDetail, long time) {

        String url = URLEncoder.encode("https://www.shuidichou.com/mine/donation", StandardCharsets.UTF_8);
        secondInventoryDetail.setRouteUrl("sdchou://universal/page/web?url=" + url);
        secondInventoryDetail.setContent("查看详情");

//        int orderResultCount = crowdfundingOrderSlaveDao.getOrderCountByUserId(secondInventoryDetail.getUserId(), new Timestamp(time));
//        if (orderResultCount <= 0) {
//            return;
//        }
        secondInventoryDetail.setSituation("已收集");


    }
}
