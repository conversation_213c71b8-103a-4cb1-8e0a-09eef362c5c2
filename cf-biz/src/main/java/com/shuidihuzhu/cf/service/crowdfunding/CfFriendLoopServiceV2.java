package com.shuidihuzhu.cf.service.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.CfInfoShareRecordBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoSimpleBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingOrderBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.verification.service.cache.QueryVerifyUserService;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.domain.risk.RiskUgcVerifyDO;
import com.shuidihuzhu.cf.enums.crowdfunding.FriendsBizTypeEnum;
import com.shuidihuzhu.cf.enums.risk.UgcTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoShareRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSimpleModel;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfFriendLoopInfoVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdFundingLoopVerificationVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdFundingVerificationVo;
import com.shuidihuzhu.cf.service.crowdfunding.bigdata.DSFriendsService;
import com.shuidihuzhu.cf.service.label.CfLabelService;
import com.shuidihuzhu.cf.service.risk.verify.RiskUgcVerifyService;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RefreshScope
public class CfFriendLoopServiceV2 {

    @Autowired
    private CrowdfundingOrderBiz crowdfundingOrderBiz;
    @Autowired
    private CfInfoShareRecordBiz cfInfoShareRecordBiz;
    @Autowired
    private UserInfoDelegate userInfoDelegate;
    @Autowired
    private QueryVerifyUserService queryVerifyUserService;
    @Autowired
    private DSFriendsService dsFriendsService;
    @Autowired
    private RiskUgcVerifyService riskUgcVerifyService;
    @Autowired
    private CrowdfundingInfoSimpleBiz crowdfundingInfoSimpleBiz;
    @Autowired
    private CfLabelService cfLabelService;
    @Autowired
    private BlackListService blackListService;
    @Autowired
    private ShuidiCipher shuidiCipher;
    @Resource(name = "cfRedissonHandler")
    private RedissonHandler cfRedissonHandler;
    private final long HOUR_TIMES = 1L * 60 * 60;
    private final long ONE_DAY_TIMES = 24L * 60 * 60 * 1000;
    /**
     * 转发并捐款
     */
    public final int ORDER_SHARED = 0;
    /**
     * 订单
     */
    public final int ORDER = 1;
    /**
     * 转发
     */
    public final int SHARED = 2;
    /**
     * 纯好友关系
     */
    public final int DEFAULT = 3;

    /**
     * 得到轮播数据
     *
     * @param friendUserIdList
     * @param crowdFundingId
     * @return
     */
    public List<CfFriendLoopInfoVo> getFriendLoopResult(List<Long> friendUserIdList, int crowdFundingId, long userId) {
        if (CollectionUtils.isEmpty(friendUserIdList) || crowdFundingId < 0) {
            this.setRedissonValue(userId, crowdFundingId, Lists.newArrayList());
            return Lists.newArrayList();
        }

        //筛除没有nickname的用户
        Map<Long, CfFriendLoopInfoVo> cfFriendLoopInfoVoMap = this.getUserInfoMap(friendUserIdList);
        log.debug("userId:{},有昵称的好友数量为: count{}", userId, cfFriendLoopInfoVoMap.size());
        if (cfFriendLoopInfoVoMap.isEmpty()) {
            this.setRedissonValue(userId, crowdFundingId, Lists.newArrayList());
            return Lists.newArrayList();
        }
        //一度好友的捐款与转发数据
        this.getFriendOrderAndShared(cfFriendLoopInfoVoMap, crowdFundingId);

        //好友轮播展示数据
        List<CfFriendLoopInfoVo> cfFriendLoopInfoVoList = Lists.newArrayList();
        try {
            cfFriendLoopInfoVoList = this.getFriendLoopResult(cfFriendLoopInfoVoMap);
        } catch (Exception e) {
            log.error("对数据的组织出现错误", e);
        } finally {
            this.setRedissonValue(userId, crowdFundingId, cfFriendLoopInfoVoList);
        }
        return cfFriendLoopInfoVoList;
    }

    /**
     * 获得用户的订单与分享数据
     *
     * @param cfFriendLoopInfoVoMap
     * @param infoId
     * @return
     */
    private void getFriendOrderAndShared(Map<Long, CfFriendLoopInfoVo> cfFriendLoopInfoVoMap,
                                         int infoId) {

        Set<Long> userIdSet = cfFriendLoopInfoVoMap.keySet();
        List<CrowdfundingOrder> crowdFundingOrderList = crowdfundingOrderBiz.getByUserId(userIdSet, infoId);
        if (!CollectionUtils.isEmpty(crowdFundingOrderList)) {
            for (CrowdfundingOrder crowdfundingOrder : crowdFundingOrderList) {
                try {
                    //过滤掉匿名捐款的
                    if (crowdfundingOrder == null || crowdfundingOrder.isAnonymous() || crowdfundingOrder.getAmount() <= 0) {
                        continue;
                    }
                    int amountFen = crowdfundingOrder.getAmount();
                    if (amountFen == 0) {
                        continue;
                    }
                    CfFriendLoopInfoVo cfFriendLoopInfoVo = cfFriendLoopInfoVoMap.get(crowdfundingOrder.getUserId());
                    this.setMaxDate(crowdfundingOrder.getPayTime(), cfFriendLoopInfoVo, true);
                    cfFriendLoopInfoVo.setAmountFen(cfFriendLoopInfoVo.getAmountFen() + amountFen);
                    //捐款次数+1
                    cfFriendLoopInfoVo.setAmountCount(cfFriendLoopInfoVo.getAmountCount() + 1);
                } catch (Exception e) {
                    log.error("getFriendOrderAndShared查询订单crowdFundingId:{}时,异常", infoId, e);
                }
            }
        }

        Map<Long, Long> userIdShareCountMap =
                cfInfoShareRecordBiz.countShareByInfoIdAndUserIds(infoId, userIdSet);
        if (null == userIdShareCountMap) {
            userIdShareCountMap = Maps.newHashMap();
        }

        List<CfInfoShareRecord> cfInfoShareRecordList = cfInfoShareRecordBiz.findLastByUserIdListAndInfoId(Lists.newArrayList(userIdSet), infoId);
        Map<Long, CfInfoShareRecord> shareRecordMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(cfInfoShareRecordList)) {
            shareRecordMap = cfInfoShareRecordList.stream().filter(item -> null != item)
                    .collect(Collectors.toMap(CfInfoShareRecord::getUserId, Function.identity(), (a, b) -> a));
        }
        for (long userId : userIdSet) {
            try {
                Long count = 0L;
                if (userIdShareCountMap.containsKey(userId)) {
                    count = userIdShareCountMap.get(userId);
                }

                CfFriendLoopInfoVo cfFriendLoopInfoVo = cfFriendLoopInfoVoMap.get(userId);
                CfInfoShareRecord cfInfoShareRecord = shareRecordMap.get(userId);
                if (null != cfInfoShareRecord) {
                    this.setMaxDate(cfInfoShareRecord.getDateCreated(), cfFriendLoopInfoVo, false);
                }

                int amountFen = cfFriendLoopInfoVo.getAmountFen();
                //既无捐款又无转发
                if (count < 1 && amountFen <= 0) {
                    cfFriendLoopInfoVo.setType(DEFAULT);
                    continue;
                }
                //有捐款但是无转发
                if (count < 1 && amountFen > 0) {
                    cfFriendLoopInfoVo.setType(ORDER);
                    continue;
                }
                //有转发但是无捐款
                if (count > 0 && amountFen <= 0) {
                    cfFriendLoopInfoVo.setType(SHARED);
                    cfFriendLoopInfoVo.setShared(count.intValue());
                    continue;
                }
                //有捐款与转发
                cfFriendLoopInfoVo.setShared(count.intValue());
                cfFriendLoopInfoVo.setType(ORDER_SHARED);

            } catch (Exception e) {
                log.error("getFriendOrderAndShared查询分享userId:{},infoId:{}时,异常", userId, infoId, e);
            }
        }
    }

    /**
     * 得到用户信息(昵称,头像)
     *
     * @param friendUserIdList
     * @return
     */
    private Map<Long, CfFriendLoopInfoVo> getUserInfoMap(List<Long> friendUserIdList) {
        if (CollectionUtils.isEmpty(friendUserIdList)) {
            return Maps.newHashMap();
        }
        List<List<Long>> friendUserIdPartitionList = Lists.partition(friendUserIdList, 30);
        Map<Long, CfFriendLoopInfoVo> resultMap = Maps.newHashMap();
        for (List<Long> itemList : friendUserIdPartitionList) {
            List<UserInfoModel> userInfoModelList = userInfoDelegate.getUserInfoByUserIdBatch(itemList);
            if (CollectionUtils.isEmpty(userInfoModelList)) {
                continue;
            }
            userInfoModelList.stream().filter(item -> item != null && !StringUtils.isEmpty(item.getNickname())).
                    forEach(item -> {
                        CfFriendLoopInfoVo cfFriendLoopInfoVo = new CfFriendLoopInfoVo();
                        cfFriendLoopInfoVo.setNickname(item.getNickname());
                        cfFriendLoopInfoVo.setHeadImgUrl(item.getHeadImgUrl());
                        //默认为3,无转发捐款
                        cfFriendLoopInfoVo.setType(DEFAULT);
                        resultMap.put(item.getUserId(), cfFriendLoopInfoVo);
                    });
        }
        return resultMap;
    }

    /**
     * 得到好友的轮播数据信息
     *
     * @param cfFriendLoopInfoVoMap
     * @return
     */
    private List<CfFriendLoopInfoVo> getFriendLoopResult(Map<Long, CfFriendLoopInfoVo> cfFriendLoopInfoVoMap) {
        if (null == cfFriendLoopInfoVoMap || cfFriendLoopInfoVoMap.isEmpty()) {
            return Lists.newArrayList();
        }

        Collection<CfFriendLoopInfoVo> cfFriendLoopInfoVoCollection = cfFriendLoopInfoVoMap.values();
        //时间降序排列
        List<CfFriendLoopInfoVo> cfFriendLoopInfoVoList = Lists.newArrayList();
        try {
            cfFriendLoopInfoVoList = cfFriendLoopInfoVoCollection.parallelStream().
                    filter(item -> null != item && DEFAULT != item.getType())
                    .sorted(Comparator.comparing(CfFriendLoopInfoVo::getUpdateTime).reversed())
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("getFriendLoopResult排序出错", e);
        }
        //只要前三十个
        if (cfFriendLoopInfoVoList.size() > 30) {
            cfFriendLoopInfoVoList = cfFriendLoopInfoVoList.subList(0, 30);
        }
        return cfFriendLoopInfoVoList;
    }

    /**
     * 得到 分享者的转发,捐助,用户信息
     * 加一个缓存
     *
     * @param userSourceId
     * @param infoId
     * @return
     */
    public CfFriendLoopInfoVo getSourceUserInfo(long userSourceId, int infoId) {
        if (userSourceId < 0) {
            return null;
        }
        CfFriendLoopInfoVo cfFriendLoopInfoVo = this.getShareFriendFromCash(userSourceId, infoId);
        if (null != cfFriendLoopInfoVo) {
            return cfFriendLoopInfoVo;
        }
        UserInfoModel baseUserInfoByUserId = userInfoDelegate.getUserInfoByUserId(userSourceId);

        cfFriendLoopInfoVo = new CfFriendLoopInfoVo();
        if (baseUserInfoByUserId == null) {
            return null;
        }
        String nickname = baseUserInfoByUserId.getNickname();
        //名字为空直接跳过
        if (StringUtils.isEmpty(nickname)) {
            return null;
        }

        int count = cfInfoShareRecordBiz.countShareByUserIdAndInfoId(infoId, userSourceId);
        if (count < 1) {
            return null;
        }
        CfInfoShareRecord cfInfoShareRecord = cfInfoShareRecordBiz.findLastByUserIdAndInfoId(userSourceId, infoId);
        if (null != cfInfoShareRecord) {
            this.setMaxDate(cfInfoShareRecord.getDateCreated(), cfFriendLoopInfoVo, false);
        }

        List<CrowdfundingOrder> crowdFundingOrderList = crowdfundingOrderBiz.getListByUserId(infoId, userSourceId, 1000);
        int payAmountFen = 0;
        if (!CollectionUtils.isEmpty(crowdFundingOrderList)) {
            for (CrowdfundingOrder crowdfundingOrder : crowdFundingOrderList) {
                if (crowdfundingOrder == null || crowdfundingOrder.isAnonymous()) {
                    continue;
                }
                payAmountFen += crowdfundingOrder.getAmount();
                this.setMaxDate(crowdfundingOrder.getPayTime(), cfFriendLoopInfoVo, true);
                cfFriendLoopInfoVo.setAmountCount(cfFriendLoopInfoVo.getAmountCount() + 1);
            }
        }

        String headImgUrl = baseUserInfoByUserId.getHeadImgUrl();

        cfFriendLoopInfoVo.setNickname(nickname);
        cfFriendLoopInfoVo.setHeadImgUrl(headImgUrl);
        cfFriendLoopInfoVo.setShared(count);
        cfFriendLoopInfoVo.setAmountFen(payAmountFen);
        cfFriendLoopInfoVo.setType(SHARED);

        if (payAmountFen > 0) {
            cfFriendLoopInfoVo.setType(ORDER);
        }

        this.setShareFriendCash(userSourceId, infoId, cfFriendLoopInfoVo);
        return cfFriendLoopInfoVo;
    }

    private void setRedissonValue(long userId, int crowdFundingId, List<CfFriendLoopInfoVo> friendResultList) {
        String key = this.getRedissonKey(userId + "_" + crowdFundingId);
        cfRedissonHandler.addListEX(key, friendResultList, HOUR_TIMES);
        this.setRedissonFlag(userId, crowdFundingId);
        log.debug("userId:{},crowdFundingId:{},缓存loop数据,设置标志位为true", userId, crowdFundingId);
    }

    /**
     * 通过缓存获取好友捐款与分享数据
     *
     * @param userId
     * @param crowdFundingId
     * @return
     */
    public List<CfFriendLoopInfoVo> getRedissonResultList(long userId, int crowdFundingId) {
        String flag = this.getRedissonFlag(userId, crowdFundingId);
        if ("true".equals(flag)) {
            String key = this.getRedissonKey(userId + "_" + crowdFundingId);
            log.debug("userId:{},crowdFundingId:{},存在缓存的loop数据,标志位为true", userId, crowdFundingId);
            return cfRedissonHandler.getList(key, CfFriendLoopInfoVo.class);
        } else {
            log.debug("userId:{},crowdFundingId:{},不存在缓存的loop数据,标志位为空", userId, crowdFundingId);
            return null;
        }
    }

    /**
     * 因为redis的getList在key不存在时,也会返回一个空对象,所以设置一个标志位来确定是否有数据缓存
     *
     * @param userId
     * @param crowdFundingId
     */
    private void setRedissonFlag(long userId, int crowdFundingId) {
        String key = this.getRedissonKey(userId + "_" + crowdFundingId + "_boolean");
        cfRedissonHandler.setEX(key, "true", HOUR_TIMES * 1000);
    }

    private String getRedissonFlag(long userId, int crowdFundingId) {
        String key = this.getRedissonKey(userId + "_" + crowdFundingId + "_boolean");
        return cfRedissonHandler.get(key, String.class);
    }

    private String getRedissonKey(String key) {
        return key + "_friend_v3";
    }

    /**
     * 设置最新的更新时间,
     * v2 有捐款记录的忽略分享时间
     *
     * @param date
     * @return
     */
    private void setMaxDate(Date date, CfFriendLoopInfoVo cfFriendLoopInfoVo, boolean isOrder) {
        if (null == cfFriendLoopInfoVo) {
            return;
        }
        if (null == cfFriendLoopInfoVo.getUpdateTime()) {
            cfFriendLoopInfoVo.setUpdateTime(date);
            return;
        }
        if (null == date) {
            return;
        }
        if (cfFriendLoopInfoVo.getAmountCount() > 0 && !isOrder) {
            //存在捐款记录且是分享时间的直接忽略
            log.debug("忽略时间 date:{}", JSON.toJSONString(date));
            return;
        }

        if (cfFriendLoopInfoVo.getAmountCount() == 0 && isOrder) {
            //不在捐款记录且是捐款时间的直接设置
            log.debug("直接设置时间 date:{}", JSON.toJSONString(date));
            cfFriendLoopInfoVo.setUpdateTime(date);
            return;
        }
        Date voDate = cfFriendLoopInfoVo.getUpdateTime();
        if (voDate.before(date)) {
            cfFriendLoopInfoVo.setUpdateTime(date);
        }
    }


    public CfFriendLoopInfoVo getShareFriendFromCash(long userId, int crowdFundingId) {
        String key = this.getRedissonKey(userId + "_" + crowdFundingId + "_" + "share");
        return cfRedissonHandler.get(key, CfFriendLoopInfoVo.class);
    }


    public void setShareFriendCash(long userId, int crowdFundingId, CfFriendLoopInfoVo friendLoopInfoVo) {
        String key = this.getRedissonKey(userId + "_" + crowdFundingId + "_" + "share");
        cfRedissonHandler.setEX(key, friendLoopInfoVo, ONE_DAY_TIMES);
    }

    @Async
    public void delShareFriendCash(long userId, int crowdFundingId) {
        String key = this.getRedissonKey(userId + "_" + crowdFundingId + "_" + "share");
        cfRedissonHandler.del(key);
    }

    private List<CfFriendLoopInfoVo> getResultList(List<CfFriendLoopInfoVo> cfFriendLoopInfoVoList) {
        if (CollectionUtils.isEmpty(cfFriendLoopInfoVoList)) {
            return Lists.newArrayList();
        }
        List<CfFriendLoopInfoVo> result = Lists.newArrayList();
        try {
            cfFriendLoopInfoVoList.stream().forEach(item -> {
                if (null != item && null == item.getUpdateTime()) {
                    item.setUpdateTime(new Date(System.currentTimeMillis() - 5L * 60 * 1000));
                }
            });
            result = cfFriendLoopInfoVoList.parallelStream().
                    filter(item -> null != item && this.DEFAULT != item.getType())
                    .sorted(Comparator.comparing(CfFriendLoopInfoVo::getUpdateTime).reversed())
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("filterResult", e);
        }
        if (result.size() > 30) {
            result = result.subList(0, 30);
        }
        return result;
    }

    /**
     *
     * @param userSourceId  当前链接的转发人
     * @param contextUserId 当前用户
     * @param infoUuid
     * @return
     */
    public List<CfFriendLoopInfoVo> getFriendLoopInfo(String userSourceId, long contextUserId, String infoUuid) {
        if (contextUserId <= 0L) {
            return null;
        }
        CfInfoSimpleModel simpleModel = crowdfundingInfoSimpleBiz.getFundingInfo(infoUuid);

        if (simpleModel == null) {
            log.info("infoUuid:{},解析失败", infoUuid);
            return null;
        }
        long sourceUserId = shuidiCipher.decryptUserId(userSourceId);
        if (sourceUserId == 0 || sourceUserId == simpleModel.getUserId()) {
            sourceUserId = -1L;
            log.info("userSourceId:{},解析失败或者分享人是案例发起者,contextUserId:{}", userSourceId, contextUserId);
        }
        log.debug("sourceUserId{}", sourceUserId);

        try {
            List<CfFriendLoopInfoVo> resultList = this.getRedissonResultList(contextUserId, simpleModel.getId());
            if (null != resultList) {
                log.debug("page friend redis, contextUserId:{},crowdfundingId:{}", contextUserId, simpleModel.getId());
                resultList = this.getResultList(resultList);
                return resultList;
            }
            log.debug("缓存没有loop friend, contextUserId:{},crowdfundingId:{}", contextUserId, simpleModel.getId());
            //得到一度好友列表
            Set<Long> friendUserIdSet = dsFriendsService.getFriendsUserId(contextUserId, 200,
                    FriendsBizTypeEnum.FRIEND_LOOP);
            if (CollectionUtils.isEmpty(friendUserIdSet)) {
                log.debug("contextUserId{},未发现一度好友-page", contextUserId);
                friendUserIdSet = Sets.newHashSet();
            }
            //案例发起人不在范围内
            friendUserIdSet.remove(simpleModel.getUserId());
            if (contextUserId == sourceUserId) {
                friendUserIdSet.remove(contextUserId);
            } else {
                friendUserIdSet.add(sourceUserId);
            }
            List<Long> userIdList = Lists.newArrayList();
            userIdList.addAll(friendUserIdSet);
            resultList = this.getFriendLoopResult(userIdList, simpleModel.getId(), contextUserId);
            resultList = this.getResultList(resultList);
            return resultList;
        } catch (Exception e) {
            log.error("page friend", e);
        }
        return null;
    }
}
