package com.shuidihuzhu.cf.service.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.ctrip.framework.apollo.model.ConfigChange;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.account.model.UserThirdModel;
import com.shuidihuzhu.cf.biz.crowdfunding.CfPaySplitFlowRecordBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.WxMpConfigCacheBiz;
import com.shuidihuzhu.cf.biz.datautilapi.DataUtilApiBiz;
import com.shuidihuzhu.cf.biz.wx.CfWxMpCommonBiz;
import com.shuidihuzhu.cf.constants.WxConstants;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.delegate.UserThirdDelegate;
import com.shuidihuzhu.cf.enums.SplitFlowPay;
import com.shuidihuzhu.cf.enums.crowdfunding.CfMulitMpEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingType;
import com.shuidihuzhu.cf.facade.eagle.EagleFacadeImpl;
import com.shuidihuzhu.cf.model.crowdfunding.CfPaySplitFlowRecord;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.datautilapi.DataAddressByIp;
import com.shuidihuzhu.cf.util.UserAgentUtil;
import com.shuidihuzhu.cf.vo.WxMpConfigVo;
import com.shuidihuzhu.cf.vo.crowdfunding.PaySplitFlowResultVO;
import com.shuidihuzhu.client.dataservice.faceApi.v1.FaceApiClient;
import com.shuidihuzhu.common.web.util.IdGenUtil;
import com.shuidihuzhu.wx.enums.AccountThirdTypeEnum;
import com.shuidihuzhu.wx.grpc.model.WxMpSubscribeModel;
import com.shuidihuzhu.wx.model.WxMpConfig;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RefreshScope
@Service
public class CfMulitMpService {

	/**
	 * 公众号与人群包映射列表
	 */
	@Value("${apollo.official-accounts.portrait-id.mapping:}")
	private String accountPortraitIdMappingList;

	@Autowired
	private UserThirdDelegate userThirdDelegate;
	@Autowired
	private UserInfoDelegate userInfoDelegate;
	@Autowired
	private CfPaySplitFlowRecordBiz cfPaySplitFlowRecordBiz;
	@Autowired
	private WxMpConfigCacheBiz wxMpConfigCacheBiz;
	@Resource
	private FaceApiClient faceApiClient;
	@Autowired
	protected CfWxMpCommonBiz cfWxMpCommonBiz;

	@Autowired
	private DataUtilApiBiz dataUtilApiBiz;
	/**
	 * 保存公众号-人群包的关联结构  有序
	 */
	private List<APModelGroup> apModelGroupList;

	@Resource
	private EagleFacadeImpl eagleFacade;

	private static final String INTERESTED_KEY ="apollo.official-accounts.portrait-id.mapping";


	@PostConstruct
	private void init() {
		initApModelGroupList(accountPortraitIdMappingList);
		Config config = ConfigService.getAppConfig();
		config.addChangeListener(changeEvent -> {
			Set<String> keys = changeEvent.changedKeys();
			if (CollectionUtils.isEmpty(keys)) {
				return;
			}
			for (String key : changeEvent.changedKeys()) {
				if (!INTERESTED_KEY.equals(key)) {
					continue;
				}

				ConfigChange change = changeEvent.getChange(key);
				log.info("Found change - key: [{}], oldValue: [{}], newValue: [{}], changeType: [{}]", change.getPropertyName(), change.getOldValue(), change.getNewValue(), change.getChangeType());
				initApModelGroupList(change.getNewValue());
			}
		}, Sets.newHashSet(INTERESTED_KEY));
	}

	private void initApModelGroupList(String param) {
	    String tmpParam = param;
		// 初始化时将配置的公众号人群包映射列表转换为本地结构
		if (StringUtils.isEmpty(param)) {
			tmpParam = "[{\"priority\":999,\"portraitId\":null,\"models\":[{\"account\":54,\"specAccount\":null,\"percentage\":100}]}]";
		}
		apModelGroupList = JSON.parseArray(tmpParam, APModelGroup.class);
		if (apModelGroupList == null) {
			apModelGroupList = Lists.newArrayList();
		}
		apModelGroupList.sort(Comparator.comparingInt(APModelGroup::getPriority));
		log.info("newShunt apModelGroupList={}", apModelGroupList);
	}


	public PaySplitFlowResultVO shuntForPay(CrowdfundingInfo crowdfundingInfo, long userId,String userAgent,String clientIp) {
		String mobileType = "";
		String province = "";
		String city = "";
		try {
			mobileType = UserAgentUtil.getInstance().getMobileType(userAgent);
			DataAddressByIp dataAddressByIp = dataUtilApiBiz.queryAddressByIp(clientIp);
			dataAddressByIp = Optional.ofNullable(dataAddressByIp).orElse(new DataAddressByIp());
			province = StringUtils.isEmpty(dataAddressByIp.getProvince()) ? "unkown" : dataAddressByIp.getProvince();
			city = StringUtils.isEmpty(dataAddressByIp.getCity()) ? "unkown" : dataAddressByIp.getCity();
		} catch (Exception e) {
			log.warn("", e);
		}

		//分流
		WxMpConfigVo wxMpConfigVo = getWxMpConfig(crowdfundingInfo, userId);
		WxMpConfig wxMpConfig = wxMpConfigVo.getWxMpConfig();
		CfMulitMpEnum.AuthType authTypeEnum = check(wxMpConfig, userId);
		String splitFlowUuid = IdGenUtil.tradeNo();

		//记录分流记录
		CfPaySplitFlowRecord cfPaySplitFlowRecord = new CfPaySplitFlowRecord(splitFlowUuid, userId,
				crowdfundingInfo.getInfoId(), authTypeEnum.getCode(), wxMpConfig.getAppId(), wxMpConfig.getThirdType(),
				wxMpConfigVo.getRule(), mobileType, province, city);
		this.cfPaySplitFlowRecordSave(cfPaySplitFlowRecord);

		//下发分流结果
		PaySplitFlowResultVO vo = new PaySplitFlowResultVO();
		vo.setSplitFlowUuid(splitFlowUuid);
		vo.setAuthType(authTypeEnum.getCode());
		vo.setAppId(wxMpConfig.getAppId());
		vo.setUserThirdType(wxMpConfig.getThirdType());
		return vo;
	}

	public PaySplitFlowResultVO shuntForPayDefault() {
		WxMpConfig wxMpConfig = wxMpConfigCacheBiz.getWxMpConfigFromCache(AccountThirdTypeEnum.WX_CF_SERIOUS_ILLNESS.getCode());

		PaySplitFlowResultVO vo = new PaySplitFlowResultVO();
		vo.setSplitFlowUuid("");
		vo.setAuthType(0);
		vo.setAppId(wxMpConfig.getAppId());
		vo.setUserThirdType(wxMpConfig.getThirdType());
		return vo;
	}

	private CfMulitMpEnum.AuthType check(WxMpConfig wxMpConfig, long userId) {
		UserInfoModel baseUserInfo = null;
		try {
			baseUserInfo = this.userInfoDelegate.getUserInfoByUserId(userId);
		} catch (Exception e) {
			log.error("userInfoGrpcClient.getUserInfoByUserId failed! userId={}", userId, e);
		}
		if (baseUserInfo == null || StringUtils.isBlank(baseUserInfo.getHeadImgUrl())
				|| StringUtils.isBlank(baseUserInfo.getNickname())) {
			return CfMulitMpEnum.AuthType.EXPLICIT;
		}
		UserThirdModel userThirdModel = null;
		try {
			userThirdModel = this.userThirdDelegate.getThirdModelWithUserId(userId, wxMpConfig.getThirdType());
		} catch (Exception e) {
			log.error("userThirdGrpcClient.getThirdModelWithUserId failed! userId={},wxMpConfig={}", userId, wxMpConfig, e);
		}
		if (userThirdModel == null || StringUtils.isBlank(userThirdModel.getOpenId())) {
			return CfMulitMpEnum.AuthType.IMPLICIT;
		}
		return CfMulitMpEnum.AuthType.DEFAULT;
	}

	private WxMpConfigVo getWxMpConfig(CrowdfundingInfo crowdfundingInfo, long userId) {
		CrowdfundingType cfType = CrowdfundingType.fromValue(crowdfundingInfo.getType());
		switch (cfType) {
			case SERIOUS_ILLNESS: //仅对大病患者进行分流
				return newShunt(userId);
			case DREAM:
			case GOODS:
				return getDreamRule(SplitFlowPay.Rule.DEFAULT);
		}
		return getHealthSecurityRule(SplitFlowPay.Rule.DEFAULT);
	}

	private WxMpConfigVo getHealthSecurityRule(SplitFlowPay.Rule rule) {
		WxMpConfig wxMpConfig = wxMpConfigCacheBiz.getWxMpConfigFromCache(AccountThirdTypeEnum.WX_CF_SERIOUS_ILLNESS.getCode());
		return new WxMpConfigVo(wxMpConfig, rule);
	}

	private WxMpConfigVo getDreamRule(SplitFlowPay.Rule rule) {
		WxMpConfig wxMpConfig = wxMpConfigCacheBiz.getWxMpConfigFromCache(AccountThirdTypeEnum.WX_CF_DREAM.getCode());
		return new WxMpConfigVo(wxMpConfig, rule);
	}


	private WxMpConfigVo getDefaultForNewShunt(int defaultThirdType) {
		WxMpConfig wxMpConfig = wxMpConfigCacheBiz.getWxMpConfigFromCache(defaultThirdType);
		return new WxMpConfigVo(wxMpConfig, SplitFlowPay.Rule.DEFAULT);
	}

	private int cfPaySplitFlowRecordSave(CfPaySplitFlowRecord cfPaySplitFlowRecord) {
		if (cfPaySplitFlowRecord == null) {
			return 0;
		}
		try {
			this.cfPaySplitFlowRecordBiz.save(cfPaySplitFlowRecord);
		} catch (Exception e) {
			log.error("cfPaySplitFlowRecordDao save failed! cfPaySplitFlowRecord={}", cfPaySplitFlowRecord, e);
		}
		return 0;
	}

	/**
	 * 增加新实验，新版公众号切号逻辑：https://wiki.shuiditech.com/pages/viewpage.action?pageId=*********
	 *
	 * @param userId
	 * @return
	 */
	public WxMpConfigVo newShunt(long userId) {
		// 获取兜底方案的配置
		WxMpConfigVo defaultWxMpConfigVo = this.getDefaultForNewShunt(AccountThirdTypeEnum.SD_AI_XIN_BAO_GZH.getCode());

		// 查询当前用户在公众号的关注情况，获取已关注或已取关的 thirdType 列表
		List<WxMpSubscribeModel> subscribed = cfWxMpCommonBiz.listUserSubscribeOfMajor(userId);
		subscribed = subscribed.stream()
				.filter(wxMpSubscribeModel -> wxMpSubscribeModel.getThirdType() != WxConstants.FUNDRAISER_THIRD_TYPE)
				.collect(Collectors.toList());

		// 没有关注任何公众号，走默认
		if (CollectionUtils.isEmpty(subscribed)) {
			log.info("newShunt subscribed is null {}", userId);
			return defaultWxMpConfigVo;
		}

		// 鹰眼对照组走默认
		final String hitEagleFacade = eagleFacade.getFromKey(userId, "paywaytest2", "0");
		if (StringUtils.isEmpty(hitEagleFacade) || "0".equals(hitEagleFacade)) {
			log.info("newShunt hitEagleFacade is 0 {}", userId);
			return defaultWxMpConfigVo;
		}

		// 用户有关注且命中鹰眼实验组，则走任意关注公众号
		Random random = new Random();
		final int randomIndex = random.nextInt(subscribed.size());
		WxMpSubscribeModel wxMpSubscribeModel = subscribed.get(randomIndex);
		WxMpConfig byThirdType = cfWxMpCommonBiz.getByThirdType(wxMpSubscribeModel.getThirdType());
		if (Objects.isNull(byThirdType)) {
			log.info("newShunt byThirdType is null {}", userId);
			return defaultWxMpConfigVo;
		}
		return new WxMpConfigVo(byThirdType, SplitFlowPay.Rule.USER_LEVEL);
	}

	/**
	 * 公众号-人群包模型
	 */
	@Data
	public static class AccountPortraitModel {
		private Integer account;
		private Integer specAccount;
		private Integer percentage;
	}

	@Data
	public static class APModelGroup {
		private Integer priority;
		private String portraitId;
		private List<AccountPortraitModel> models = Lists.newArrayList();
	}
}
