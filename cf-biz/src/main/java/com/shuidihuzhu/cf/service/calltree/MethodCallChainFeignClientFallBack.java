package com.shuidihuzhu.cf.service.calltree;

import com.shuidihuzhu.cf.model.calltree.MethodCallChain;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import org.springframework.stereotype.Component;

/**
 * @author: cuikexiang
 */
@Component
public class MethodCallChainFeignClientFallBack implements MethodCallChainFeignClient {
    @Override
    public Response<MethodCallChain> findMethodCallChain(String projectName, String url) {
        return NewResponseUtil.makeFail(9999, "fall back", null);
    }
}
