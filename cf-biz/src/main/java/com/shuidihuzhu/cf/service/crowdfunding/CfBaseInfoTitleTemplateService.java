package com.shuidihuzhu.cf.service.crowdfunding;

import com.google.common.base.Strings;
import com.shuidihuzhu.cf.biz.crowdfunding.CfBaseInfoTitleTemplateBiz;
import com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTitleTemplate;
import com.shuidihuzhu.cf.util.ListUtil;
import com.shuidihuzhu.common.web.util.cache.ICache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * Created by Ahrievil on 2017/12/6
 * 筹款标题模板本地缓存
 */
@Service
public class CfBaseInfoTitleTemplateService extends AbstractCfCache<String, List<CfBaseInfoTitleTemplate>>
        implements ICache<String, List<CfBaseInfoTitleTemplate>> {
    private static final Logger LOGGER = LoggerFactory.getLogger(CfBaseInfoTitleTemplateService.class);

    @Autowired
    private CfBaseInfoTitleTemplateBiz cfBaseInfoTitleTemplateBiz;

    @Override
    protected List<CfBaseInfoTitleTemplate> queryData(String key) {
        if (Strings.isNullOrEmpty(key)) {
            return Collections.emptyList();
        }
        return ListUtil.getList(3000, cfBaseInfoTitleTemplateBiz::selectLimit);
    }

    @Override
    public List<CfBaseInfoTitleTemplate> get(String key) {
        if (Strings.isNullOrEmpty(key)) {
            return Collections.emptyList();
        }
        try {
            return getValue(key);
        } catch (ExecutionException e) {
            LOGGER.error("getCfBaseInfoTitleTemplate from cache error,key={}",key, e);
        }

        return Collections.emptyList();
    }
}
