package com.shuidihuzhu.cf.service.visitconfig;

import com.shuidihuzhu.cf.domain.visitconfig.VisitConfigLogDO;
import com.shuidihuzhu.cf.enums.visitconfig.VisitConfigLogActionInfoEnum;
import com.shuidihuzhu.cf.enums.visitconfig.VisitConfigLogActionTypeEnum;
import com.shuidihuzhu.cf.enums.visitconfig.VisitConfigSourceEnum;
import com.shuidihuzhu.cf.response.OpResult;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-09-19  20:53
 */
public interface VisitConfigLogService {

    /**
     * 保存操作记录
     *
     * @param caseId
     * @param actionInfoEnumList
     * @param operatorId
     * @return
     */
    @Deprecated
    OpResult save(int caseId, List<VisitConfigLogActionInfoEnum> actionInfoEnumList, int operatorId);

    @Deprecated
    OpResult save(int caseId, VisitConfigLogActionInfoEnum actionInfoEnum, int operatorId);

    /**
     * 系统操作不需要操作人id
     *
     * @param caseId
     * @param actionInfoEnumList
     * @return
     */
    @Deprecated
    OpResult save(int caseId, List<VisitConfigLogActionInfoEnum> actionInfoEnumList);

    @Deprecated
    OpResult save(int caseId, VisitConfigLogActionInfoEnum actionInfoEnum);

    /**
     * 增加来源区分
     *
     * @param caseId
     * @param actionInfoEnumList
     * @param sourceEnum
     * @param operatorId
     * @return
     */
    OpResult pin(int caseId,
                 VisitConfigSourceEnum sourceEnum,
                 List<VisitConfigLogActionInfoEnum> actionInfoEnumList,
                 int operatorId);

    /**
     * 获取操作记录列表
     *
     * @param infoUuid
     * @return
     */
    List<VisitConfigLogDO> listOperatorLog(String infoUuid);

    List<VisitConfigLogDO> listByCondition(String infoUuid,
                                           List<VisitConfigLogActionTypeEnum> actionTypeEnums,
                                           List<VisitConfigSourceEnum> sourceEnums);

    /**
     * 获取操作记录列表
     *
     * @param infoUuid
     * @return
     */
    List<VisitConfigLogDO> list(String infoUuid);
}
