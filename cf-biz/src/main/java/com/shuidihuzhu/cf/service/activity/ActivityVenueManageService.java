package com.shuidihuzhu.cf.service.activity;

import com.shuidihuzhu.cf.enums.activity.Activity111CaseTypeEnum;
import com.shuidihuzhu.cf.model.activity.ActivityVenueCaseModel;

/**
 * <AUTHOR>
 */
public interface ActivityVenueManageService {

    boolean makeInvalidByCaseId(int caseId, String comment, Activity111CaseTypeEnum caseTypeEnum);

    ActivityVenueCaseModel getbyCaseId(long activityId, int caseId);
}
