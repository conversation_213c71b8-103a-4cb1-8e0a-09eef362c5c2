package com.shuidihuzhu.cf.service.crowdfunding.casematerial;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.biz.crowdfunding.material.CfPropertyInsuranceAuditService;
import com.shuidihuzhu.cf.biz.crowdfunding.material.IMaterialCenterService;
import com.shuidihuzhu.cf.client.base.result.RpcResult;
import com.shuidihuzhu.cf.client.material.feign.CfPreSubmitMaterialClient;
import com.shuidihuzhu.cf.client.material.model.PropertyInsuranceCaseType;
import com.shuidihuzhu.cf.client.material.model.preSubmit.CfMaterialPreSubmit;
import com.shuidihuzhu.cf.domain.caseinfo.CaseInfoApproveStageDO;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.facade.caseinfo.InitialAuditSubmitService;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoExt;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSimpleModel;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoBaseVo;
import com.shuidihuzhu.cf.param.raise.RaiseBasicInfoParam;
import com.shuidihuzhu.cf.service.caseinfo.CaseInfoApproveStageService;
import com.shuidihuzhu.cipher.ShuidiCipher;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class CfMaterialPreSubmitService {

    @Autowired
    private InitialAuditSubmitService auditSubmitService;
    @Autowired
    private CfPreSubmitMaterialClient preSumbitClient;
    @Autowired
    private IMaterialCenterService materialCenterService;
    @Autowired
    private CfPropertyInsuranceAuditService insuranceAuditService;
    @Autowired
    private CfFirstApproveBiz firstApproveBiz;
    @Autowired
    private ShuidiCipher shuidiCipher;
    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private CaseInfoApproveStageService caseInfoApproveStageService;
    @Autowired
    private CrowdfundingAttachmentBiz crowdfundingAttachmentBiz;
    @Autowired
    private CrowdfundingInfoSimpleBiz simpleBiz;
    @Autowired
    private CrowdfundingInitialAuditInfoBiz crowdfundingInitialAuditInfoBiz;
    @Autowired
    private CfInfoExtBiz cfInfoExtBiz;


    public void saveInitialPreModify(int caseId, CrowdfundingInfoBaseVo material) {

        if (material == null) {
            throw new RuntimeException("材料为空");
        }

        RpcResult<String> result = preSumbitClient.savePreSubmitInitialMaterial(caseId, material);
        if (result == null || result.isFail()) {
            throw new RuntimeException("初审材料修改保存失败");
        }

        log.info("保存初审材料的预修改 caseId:{} material:{} result:{}", caseId, material, result);
    }

    public CrowdfundingInfoBaseVo selectInitialMaterial(int caseId, CfMaterialPreSubmit.ModifySource source,
                                                        String modifyId) {

        RpcResult<CrowdfundingInfoBaseVo> baseVoResult = preSumbitClient.selectPreSubmitInitialMaterial(caseId,
                source, StringUtils.trimToEmpty(modifyId));

        Map<Integer, List<String>> baseRejectDetail = null;
        // 如果是初审 并且初审未通过 覆盖老图文驳回详情
        boolean onInitialAudit = crowdfundingInitialAuditInfoBiz.isOnInitialAudit(caseId);
        CfInfoExt ext = cfInfoExtBiz.getByCaseId(caseId);
        if (ext != null) {
            FirstApproveStatusEnum firstApproveStatusEnum = FirstApproveStatusEnum.parse(ext.getFirstApproveStatus());
            if (onInitialAudit && FirstApproveStatusEnum.isNotPassed(firstApproveStatusEnum)) {
                baseRejectDetail = crowdfundingInitialAuditInfoBiz.getBaseRejectDetail(caseId);
            }
        }

        //从材料获取疾病名称
        RaiseBasicInfoParam basicInfoParam = materialCenterService.selectRaiseBasicInfo(caseId);
        log.debug("从材料获取疾病名称:{}", JSON.toJSONString(basicInfoParam));

        // 图文
        CrowdfundingInfo info = crowdfundingInfoBiz.getFundingInfoById(caseId);
        // 能找到bd的提交数据
        if (baseVoResult != null && baseVoResult.getData() != null) {
            // 是bd的修改
            baseVoResult.getData().setInitialModifyType(CfMaterialPreSubmit.ModifySource.BD_MODIFY_INITIAL.getCode());
            baseVoResult.getData().setFirstApproveIdcardVerifyStatusEnum(null);
            CfInfoSimpleModel simpleModel = simpleBiz.getFundingInfoById(caseId);

            if (simpleModel != null) {
                baseVoResult.getData().setInfoUuid(simpleModel.getInfoId());
            }
            baseVoResult.getData().setRejectDetails(baseRejectDetail);
            baseVoResult.getData().setCfVersion(cfInfoExtBiz.getCaseVersion(caseId));
            log.debug("通过查询selectPreSubmitInitialMaterial去查询,结果:{}", JSON.toJSONString(baseVoResult));
            baseVoResult.getData().setRaiseBasicInfoParam(basicInfoParam);
            if (info != null) {
                baseVoResult.getData().setTargetAmount(info.getTargetAmount() / 100);
            }
            baseVoResult.getData().setPlanId(info.getMaterialPlanId());
            return baseVoResult.getData();
        }

        CrowdfundingInfoBaseVo infoBaseVo = new CrowdfundingInfoBaseVo();
        infoBaseVo.setFirstApproveIdcardVerifyStatusEnum(null);
        if (info == null) {
            log.warn("案例不存在 caseId:{}", caseId);
            return null;
        }
        infoBaseVo.setInfoUuid(info.getInfoId());
        infoBaseVo.setRaiseBasicInfoParam(basicInfoParam);

        fillBaseInfo(infoBaseVo, info);
        // 前置
        fillFirstMaterial(infoBaseVo, caseId);
        // 增信
        fillInsurance(infoBaseVo, caseId);
        // 低保数据
        fillLivingGuard(infoBaseVo, caseId);

        infoBaseVo.setRejectDetails(baseRejectDetail);
        infoBaseVo.setInitialModifyType(CfMaterialPreSubmit.ModifySource.USER_FOR_C.getCode());
        infoBaseVo.setCfVersion(cfInfoExtBiz.getCaseVersion(caseId));
        infoBaseVo.setPlanId(info.getMaterialPlanId());
        log.debug("通过案例去查询:{}", infoBaseVo);
        return infoBaseVo;
    }

    private void fillBaseInfo(CrowdfundingInfoBaseVo infoBaseVo, CrowdfundingInfo info) {
        infoBaseVo.setTargetAmount(info.getTargetAmount() / 100);
        // 获取暂存区内容
        CaseInfoApproveStageDO stageInfo = caseInfoApproveStageService.getByCaseId(info.getId());
        if (stageInfo != null) {
            infoBaseVo.setTitle(stageInfo.getTitle());
            infoBaseVo.setContent(stageInfo.getContent());
            List<String> attachments = Lists.newArrayList(StringUtils.split(StringUtils.trimToEmpty(stageInfo.getImages()), ","));
            infoBaseVo.setAttachments(attachments);
        } else {
            infoBaseVo.setTitle(info.getTitle());
            infoBaseVo.setContent(info.getContent());

            // 图片
            List<CrowdfundingAttachmentVo> attachmentVoList = this.crowdfundingAttachmentBiz
                    .getAttachmentsByType(info.getId(), AttachmentTypeEnum.ATTACH_CF);
            List<String> attachments = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(attachmentVoList)) {
                for (CrowdfundingAttachmentVo vo : attachmentVoList) {
                    attachments.add(vo.getUrl());
                }
            }

            infoBaseVo.setAttachments(attachments);
        }
    }

    private void fillFirstMaterial(CrowdfundingInfoBaseVo infoBaseVo, int caseId) {
        CfFirsApproveMaterial idCardMaterial = firstApproveBiz.getByInfoId(caseId);
        if (idCardMaterial != null) {
            infoBaseVo.setSelfRealName(idCardMaterial.getSelfRealName());
            infoBaseVo.setSelfIdCard(shuidiCipher.decrypt(idCardMaterial.getSelfCryptoIdcard()));

            infoBaseVo.setPatientRealName(idCardMaterial.getPatientRealName());
            infoBaseVo.setPatientIdCard(shuidiCipher.decrypt(idCardMaterial.getPatientCryptoIdcard()));
            infoBaseVo.setPatientBornCard(idCardMaterial.getPatientBornCard());
            infoBaseVo.setPatientIdType(idCardMaterial.getPatientIdType());
            infoBaseVo.setRelType(UserRelTypeEnum.getUserRelTypeEnum(idCardMaterial.getUserRelationType()));
            infoBaseVo.setUserRelationTypeForC(idCardMaterial.getUserRelationTypeForC());
            infoBaseVo.setImageUrlType(idCardMaterial.getImageUrlType());

            infoBaseVo.setPreAuditImageUrl(idCardMaterial.getImageUrl());
        }
    }

    private void fillInsurance(CrowdfundingInfoBaseVo infoBaseVo, int caseId) {

        if (insuranceAuditService.selectPropertyCaseType(caseId) ==
                PropertyInsuranceCaseType.INITIAL_WITH_INSURANCE.getCode()) {

            infoBaseVo.setPropertyInsuranceParam(materialCenterService.selectPropertyInsurance(caseId));
        }
    }

    private void fillLivingGuard(CrowdfundingInfoBaseVo infoBaseVo, int caseId) {
        infoBaseVo.setLivingGuardParam(materialCenterService.selectLivingGuard(caseId));
    }

}