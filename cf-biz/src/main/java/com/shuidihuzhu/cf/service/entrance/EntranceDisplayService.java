package com.shuidihuzhu.cf.service.entrance;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.CfInfoExtBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.finance.client.feign.CfCapitalAccountFeignClient;
import com.shuidihuzhu.cf.finance.client.response.model.FeignResponse;
import com.shuidihuzhu.cf.model.crowdfunding.CfCapitalAccount;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.util.CaseUtils;
import com.shuidihuzhu.client.cf.admin.client.CfEntranceDisplayFeignClient;
import com.shuidihuzhu.client.cf.admin.model.AdminEntranceStatus;
import com.shuidihuzhu.client.cf.admin.model.UserCaseStatusVO;
import com.shuidihuzhu.client.cf.workorder.CfWorkOrderClient;
import com.shuidihuzhu.client.cf.workorder.model.WorkOrderVO;
import com.shuidihuzhu.client.cf.workorder.model.enums.HandleResultEnum;
import com.shuidihuzhu.client.cf.workorder.model.enums.WorkOrderType;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class EntranceDisplayService {

    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBizImpl;

    @Autowired
    private CfInfoExtBiz cfInfoExtBiz;

    @Resource
    private CfEntranceDisplayFeignClient cfEntranceDisplayFeignClient;

    @Resource
    private CfCapitalAccountFeignClient cfCapitalAccountFeignClient;

    private static final Integer REQUEST_WITHDRAW= 1;

    public Response<UserCaseStatusVO> userCaseStatus(long userId, int channel) {

        List<CrowdfundingInfo> crowdfundingInfoList = crowdfundingInfoBizImpl.getCrowdfundingInfoListByUserId(userId);
        if (CollectionUtils.isEmpty(crowdfundingInfoList)) {
            return NewResponseUtil.makeError(CfErrorCode.CF_NOT_FOUND);
        }

        UserCaseStatusVO result = new UserCaseStatusVO();

        int caseCount = crowdfundingInfoList.size();
        result.setCaseCount(caseCount);

        // 未结束案例
        List<CrowdfundingInfo> notEndCaseList = crowdfundingInfoList.stream().filter(v -> !CaseUtils.hasEnd(v)).collect(Collectors.toList());
        int caseNotEndCount = notEndCaseList.size();
        result.setCaseNotEndCount(caseNotEndCount);

        int caseHaveSurplusAmountCount = 0;
        List<CrowdfundingInfo> haveSurplusAccentCaseList = Lists.newArrayList();
        // 如果用户点击申请提现 计算可提现余额>0案例
        if(channel == REQUEST_WITHDRAW) {
            caseHaveSurplusAmountCount = getCaseHaveSurplusAmountCount(crowdfundingInfoList, haveSurplusAccentCaseList);
            result.setCaseHaveSurplusAmountCount(caseHaveSurplusAmountCount);
        }

        CrowdfundingInfo crowdfundingInfo = null;
        crowdfundingInfo = getCrowdfundingInfo(crowdfundingInfoList, caseCount, notEndCaseList, caseNotEndCount, caseHaveSurplusAmountCount, haveSurplusAccentCaseList, crowdfundingInfo);

        if(crowdfundingInfo == null){
            return NewResponseUtil.makeSuccess(result);
        }

        result.setInfoUuid(crowdfundingInfo.getInfoId());

        Response<List<AdminEntranceStatus>>response = cfEntranceDisplayFeignClient.twEntranceIsDisplay(Lists.newArrayList(crowdfundingInfo.getInfoId()));
        if (Objects.isNull(response) || response.notOk() || response.getData() == null) {
            return NewResponseUtil.makeSuccess(result);
        }
        List<AdminEntranceStatus> contentInfos = response.getData();
        if(CollectionUtils.isEmpty(contentInfos)){
            return NewResponseUtil.makeSuccess(result);
        }
        UserCaseStatusVO caseStatusVO = contentInfos.get(0).getCaseStatusVO();
        result.setFirstApproveStatus(caseStatusVO.getFirstApproveStatus());
        result.setCrowdfundingStatus(caseStatusVO.getCrowdfundingStatus());
        result.setContentWorkOrderStatus(caseStatusVO.getContentWorkOrderStatus());
        result.setTwEntranceDisplay(caseStatusVO.isTwEntranceDisplay());

        return NewResponseUtil.makeSuccess(result);
    }

    private int getCaseHaveSurplusAmountCount(List<CrowdfundingInfo> crowdfundingInfoList, List<CrowdfundingInfo> haveSurplusAccentCaseList) {
        int caseHaveSurplusAmountCount;
        for (CrowdfundingInfo info : crowdfundingInfoList) {
            FeignResponse<CfCapitalAccount> feignResponse = cfCapitalAccountFeignClient.getAccountByInfoUuid(info.getInfoId());
            CfCapitalAccount cfCapitalAccount = Optional.ofNullable(feignResponse).filter(FeignResponse::ok).map(FeignResponse::getData).orElse(null);
            if (Objects.nonNull(cfCapitalAccount) && cfCapitalAccount.getSurplusAmount() > 0L) {
                haveSurplusAccentCaseList.add(info);
            }
        }

        caseHaveSurplusAmountCount = haveSurplusAccentCaseList.size();
        return caseHaveSurplusAmountCount;
    }

    private CrowdfundingInfo getCrowdfundingInfo(List<CrowdfundingInfo> crowdfundingInfoList, int caseCount,
                                                 List<CrowdfundingInfo> notEndCaseList, int caseNotEndCount,
                                                 int caseHaveSurplusAmountCount,
                                                 List<CrowdfundingInfo> haveSurplusAccentCaseList,
                                                 CrowdfundingInfo crowdfundingInfo) {
        // 如果未结束案例只有一个，获取该案例，继续走逻辑
        if(caseNotEndCount == 1 ){
            crowdfundingInfo = notEndCaseList.get(0);
        }
        // 如果可提现余额>0的案例只有一个，获取该案例，继续走逻辑
        if(caseHaveSurplusAmountCount == 1 ){
            crowdfundingInfo = haveSurplusAccentCaseList.get(0);
        }

        if(caseCount == 1){
            crowdfundingInfo = crowdfundingInfoList.get(0);
        }
        return crowdfundingInfo;
    }
}
