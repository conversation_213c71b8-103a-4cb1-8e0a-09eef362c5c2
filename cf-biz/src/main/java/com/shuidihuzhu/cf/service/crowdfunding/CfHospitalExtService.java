package com.shuidihuzhu.cf.service.crowdfunding;

import com.shuidihuzhu.cf.biz.crowdfunding.CfHospitalExtBiz;
import com.shuidihuzhu.cf.model.crowdfunding.CfHospitalExt;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.ResponseUtil;
import jodd.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by ahrievil on 2017/4/13.
 */
@Service
public class CfHospitalExtService {
    @Autowired
    private CfHospitalExtBiz cfHospitalExtBiz;

    public Response insertIntoHospitalExt(CfHospitalExt cfHospitalExt) {
        if (cfHospitalExt == null) {
            return ResponseUtil.makeSuccess(null);
        }
        Integer province = cfHospitalExt.getProvinceId();
        if (province == null) {
            return ResponseUtil.makeSuccess(null);
        }
        Integer city = cfHospitalExt.getCityId();
        if (city == null) {
            return ResponseUtil.makeSuccess(null);
        }
        String userInput = cfHospitalExt.getUserInput();
        if (StringUtil.isBlank(userInput)) {
            return ResponseUtil.makeSuccess(null);
        }
        cfHospitalExtBiz.insertOne(cfHospitalExt);
        return ResponseUtil.makeSuccess(null);
    }
}
