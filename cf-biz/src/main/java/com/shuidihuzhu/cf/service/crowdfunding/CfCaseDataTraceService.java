package com.shuidihuzhu.cf.service.crowdfunding;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.account.verify.client.menu.UserRelTypeEnum;
import com.shuidihuzhu.cf.biz.crowdfunding.CfFirstApproveBiz;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationActionTypeEnum;
import com.shuidihuzhu.cf.client.ugc.model.subject.operation.OperationRecordDTO;
import com.shuidihuzhu.cf.client.ugc.service.CommonOperationRecordClient;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.common.web.enums.Platform;
import com.shuidihuzhu.common.web.model.IdcardInfoExtractor;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.data.analytics.javasdk.core.Analytics;
import com.shuidihuzhu.data.analytics.javasdk.core.UserTagTypeEnum;
import com.shuidihuzhu.data.servicelog.meta.cf.CaseBegin;
import com.shuidihuzhu.data.servicelog.meta.cf.CaseStart;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
public class CfCaseDataTraceService {

    @Autowired
    private Analytics analytics;
    @Autowired
    private CfFirstApproveBiz cfFirstApproveBiz;
    @Resource
    private UserInfoDelegate userInfoDelegate;
    @Resource
    private CommonOperationRecordClient commonOperationRecordClient;
    @Autowired
    private CfDiseaseNormService cfDiseaseNormService;


    public void cfCaseCreateTrace(CrowdfundingInfo cfCase, String clientIp, Integer platform, String deviceId, String uniqueId, String diseaseName, String patientIdCard) {
        long userId = cfCase.getUserId();
        UserInfoModel crowdfundingUserInfo = this.userInfoDelegate.getUserInfoByUserId(userId);
        boolean isFromApp = false;
        if(platform != null && (StringUtils.equals(String.valueOf(platform),
                Platform.IOS.getCode()) || StringUtils.equals(String.valueOf(platform), Platform.ANDROID.getCode()))) {
            isFromApp = true;
        }

        CaseStart caseStart = new CaseStart();
        try {
            int age = 0;
            if (StringUtils.isNotEmpty(patientIdCard)) {
                IdcardInfoExtractor idcardInfoExtractor = new IdcardInfoExtractor(patientIdCard);

                LocalDate localDate = LocalDate.now();
                age = localDate.getYear() - idcardInfoExtractor.getYear();
            }

            caseStart.setCase_launch_time(DateUtil.formatDateTime(cfCase.getCreateTime()));
            caseStart.setTarget_amt(Long.valueOf(cfCase.getTargetAmount()));
            caseStart.setIs_app_launch(String.valueOf(isFromApp));
            caseStart.setIp(clientIp);
            caseStart.setInfo_id(Long.valueOf(cfCase.getId()));
            caseStart.setCase_id(StringUtils.trimToEmpty(cfCase.getInfoId()));
            caseStart.setCase_title(cfCase.getTitle());
            caseStart.setUser_encrypt_mobile(Optional.ofNullable(crowdfundingUserInfo).map(UserInfoModel::getCryptoMobile).orElse(""));
            caseStart.setDisease_name(this.getDiseaseNorm(diseaseName));
            caseStart.setAge((long) age);
            caseStart.setUser_tag(String.valueOf(cfCase.getUserId()));
            caseStart.setUser_tag_type(UserTagTypeEnum.userid);

            analytics.track(caseStart);
            log.info("大数据打点上报,案例发起:{}", JSONObject.toJSONString(caseStart));
        } catch (Exception e) {
            log.error("创建筹款案例 case:{} 上报异常", JSONObject.toJSONString(caseStart), e);
            return;
        }
    }

    public void cfCaseBeginFundingTrace(CrowdfundingInfo cfCase) {

        CfFirsApproveMaterial material = cfFirstApproveBiz.getByInfoId(cfCase.getId());
        String authorName = Objects.nonNull(material) ? material.getPatientRealName() : "";
        String authorIdentity = Objects.nonNull(material) ? material.getPatientCryptoIdcard() : "";
        int userType = Objects.nonNull(material) ? material.getUserRelationType() : 0;
        if(userType == UserRelTypeEnum.SELF.getValue() && (StringUtils.isEmpty(authorName) || StringUtils.isEmpty(authorIdentity))) {
            authorName = Objects.nonNull(material) ? material.getSelfRealName() : "";
            authorIdentity = Objects.nonNull(material) ? material.getSelfCryptoIdcard() : "";
        }

        String diseaseName = "";
        List<OperationRecordDTO> operationRecordDTOList = commonOperationRecordClient.listByCaseIdAndActionTypes(cfCase.getId(),
                Lists.newArrayList(OperationActionTypeEnum.TARGET_AMOUNT_CHECK_DISEASE_CLASSIFY.getValue()));
        if(CollectionUtils.isNotEmpty(operationRecordDTOList) && operationRecordDTOList.get(0).getExtMap() != null) {
            diseaseName = getDiseaseNorm(operationRecordDTOList.get(0).getExtMap().get("ocrDiseaseNames"));
        }

        CaseBegin caseBegin = null;
        try {
            caseBegin = new CaseBegin();
            caseBegin.setPatient_name(StringUtils.trimToEmpty(authorName));
            caseBegin.setDonate_begin_time(DateUtil.formatDateTime(cfCase.getBeginTime()));
            caseBegin.setTarget_amt(Long.valueOf(cfCase.getTargetAmount()));
            caseBegin.setPatient_encrypt_idcard(authorIdentity);
            caseBegin.setCase_id(cfCase.getInfoId());
            caseBegin.setInfo_id(Long.valueOf(cfCase.getId()));
            caseBegin.setDisease_name(diseaseName);
            caseBegin.setUser_tag(String.valueOf(cfCase.getUserId()));
            caseBegin.setUser_tag_type(UserTagTypeEnum.userid);
            analytics.track(caseBegin);
            log.info("大数据打点上报,案例开始筹款:{}", JSONObject.toJSONString(caseBegin));
        } catch (Exception e) {
            log.error("筹款开始案例 case:{} 上报异常", JSONObject.toJSONString(caseBegin), e);
            return;
        }
    }


    private String getDiseaseNorm(String diseaseName) {
        if(StringUtils.isBlank(diseaseName)) {
            return "";
        }
        return cfDiseaseNormService.diseaseNormNew(diseaseName);
    }
}
