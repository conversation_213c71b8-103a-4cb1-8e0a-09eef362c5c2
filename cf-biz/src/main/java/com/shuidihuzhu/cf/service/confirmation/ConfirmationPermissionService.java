package com.shuidihuzhu.cf.service.confirmation;

import com.shuidihuzhu.client.cf.clewtrack.model.UserConfirmationCheckModel;
import com.shuidihuzhu.client.cf.clewtrack.model.UserScanCodeCheckModel;

/**
 * @Description: 确权相关操作
 * @Author: pan<PERSON><PERSON><PERSON>
 * @Date: 2023/8/3 7:32 PM
 */
public interface ConfirmationPermissionService {

    UserConfirmationCheckModel judgeSkipConfirmation(Long userId);

    UserScanCodeCheckModel judgeEnterAuthenticity(Long userId);

}
