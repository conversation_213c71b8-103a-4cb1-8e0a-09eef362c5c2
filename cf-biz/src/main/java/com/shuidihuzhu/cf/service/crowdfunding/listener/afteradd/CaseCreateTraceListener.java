package com.shuidihuzhu.cf.service.crowdfunding.listener.afteradd;

import com.google.common.base.Strings;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoBaseVo;
import com.shuidihuzhu.cf.service.crowdfunding.CfCaseDataTraceService;
import com.shuidihuzhu.cf.service.crowdfunding.event.CrowdFundingAddEvent;
import com.shuidihuzhu.cf.service.crowdfunding.event.AddListenerOrder;
import com.shuidihuzhu.cf.service.crowdfunding.listener.base.AbstractCrowdFundingAddEventListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.event.SmartApplicationListener;
import org.springframework.stereotype.Service;

/**
 * 筹款案例上报数据
 */
@Slf4j
@Service
public class CaseCreateTraceListener extends AbstractCrowdFundingAddEventListener implements SmartApplicationListener {

    @Autowired
    private CfCaseDataTraceService cfCaseDataTraceService;

    @Override
    public void onApplicationEvent(ApplicationEvent _event) {

        CrowdFundingAddEvent crowdFundingAddEvent = (CrowdFundingAddEvent) _event;
        CrowdfundingInfo cfCase = crowdFundingAddEvent.getCrowdfundingInfo();

        CrowdfundingInfoBaseVo baseVo = crowdFundingAddEvent.getCrowdfundingInfoBaseVo();

        String clientIp = crowdFundingAddEvent.getClientIp();
        if(cfCase != null && !Strings.isNullOrEmpty(clientIp)) {
            try {
                String diseaseName = "";
                if(baseVo.getRaiseBasicInfoParam() != null) {
                    diseaseName = baseVo.getRaiseBasicInfoParam().getDiseaseName();
                }
                cfCaseDataTraceService.cfCaseCreateTrace(cfCase, clientIp, baseVo.getPlatform(),
                        baseVo.getDeviceId(), baseVo.getUniqueId(), diseaseName, baseVo.getPatientIdCard());
            } catch (Exception e) {
                log.error("cfCaseDataTraceService.cfCaseCreateTrace error:", e);
            }
        }


    }

    @Override
    public int getOrder() {
        return AddListenerOrder.CaseCreateTrace.getValue();
    }

}
