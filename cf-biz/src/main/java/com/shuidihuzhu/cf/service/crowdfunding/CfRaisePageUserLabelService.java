package com.shuidihuzhu.cf.service.crowdfunding;

import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.ICfUserCaseLabelService;
import com.shuidihuzhu.cf.biz.crowdfunding.ICfUserLabelService;
import com.shuidihuzhu.cf.dao.crowdfunding.CfFirstApproveMaterialDao;
import com.shuidihuzhu.cf.dao.crowdfunding.CfInfoExtDao;
import com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingBaseInfoBackupDao;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.CaseRaiseChannelEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.ClewtrackPrimaryChannelEnum;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.cf.clewtrack.client.CfClewtrackApiClient;
import com.shuidihuzhu.client.cf.clewtrack.model.CfClewBaseInfoDO;
import com.shuidihuzhu.client.dataservice.ds.v1.DsApiClient;
import com.shuidihuzhu.client.model.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @time 2019/3/21 下午7:57
 * @desc https://wiki.shuiditech.com/pages/viewpage.action?pageId=164201711
 */
@Slf4j
@Service
public class CfRaisePageUserLabelService {
    private final static int FIRST_APPROVE_UGC_ORDER_CONTENT = 7;
    private final static int FIRST_APPROVE_REJECT = 15;

    @Autowired
    private DsApiClient dsApiClient;

    @Autowired
    private CfInfoExtDao cfInfoExtDao;

    @Autowired
    private UserInfoDelegate userInfoDelegate;

    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;

    @Autowired
    private ICfUserLabelService cfUserLabelService;

    @Autowired
    private CfClewtrackApiClient cfClewtrackApiClient;

    @Autowired
    private ICfUserCaseLabelService cfUserCaseLabelService;

    @Autowired
    private CfFirstApproveMaterialDao cfFirstApproveMaterialDao;

    @Autowired
    private CrowdfundingBaseInfoBackupDao crowdfundingBaseInfoBackupDao;
    @Autowired
    private ShuidiCipher shuidiCipher;

    //如果筹款用户此时没标签信息并且有案例，则新建用户标签信息
    public CfUserLabelInfo createUserLabel(long userId, CfUserLabelDO cfUserLabelDO, List<CfUserCaseLabelDO> cfUserCaseLabels){
        CrowdfundingBaseInfoBackup backup = null;
        ClewtrackPrimaryChannelEnum channelEnum = queryToufangChannel(userId);
        if(null == cfUserLabelDO || CollectionUtils.isEmpty(cfUserCaseLabels)){
            backup = crowdfundingBaseInfoBackupDao.selectRecentlyCfByUserId(userId, 0);
        }

        int caseNum = null != cfUserLabelDO ? cfUserLabelDO.getCaseNum() : 0;
        boolean draft = null != cfUserLabelDO ? cfUserLabelDO.getDraft() : null != backup ? true : false;
        int accessRaisePageNum = queryAccessPaisePageNum(userId);
        int toufangChannel = channelEnum.getCode();

        List<CrowdfundingInfo> cfInfos = crowdfundingInfoBiz.selectByUserId(userId);

        List<CfUserCaseLabelDO> userCaseLabelDOS = Lists.newArrayList();
        if((null == cfUserLabelDO || CollectionUtils.isEmpty(cfUserCaseLabels)) && CollectionUtils.isNotEmpty(cfInfos)){
            caseNum = cfInfos.size();

            CfUserLabelDO userLabelDO = new CfUserLabelDO();
            userLabelDO.setUserId(userId);
            userLabelDO.setCaseNum(caseNum);
            userLabelDO.setDraft(draft);
            userLabelDO.setChannelStr(channelEnum.getEnglish());
            userLabelDO.setToufangChannel(toufangChannel);
            cfUserLabelService.insertLabel(userLabelDO);

            List<String> infoUuids = Lists.newArrayList();
            cfInfos.forEach(cfInfo -> {
                infoUuids.add(cfInfo.getInfoId());
            });

            List<CfInfoExt> infoExts = cfInfoExtDao.getListByUuids(infoUuids);
            Map<String, CfInfoExt> infoExtMap = infoExts.stream().collect(Collectors.toMap(CfInfoExt::getInfoUuid, Function.identity()));

            for (CrowdfundingInfo info : cfInfos){
                CfInfoExt ext = infoExtMap.get(info.getInfoId());
                boolean firstApprove = null != ext && ext.getFirstApproveStatus() != FirstApproveStatusEnum.DEFAULT.getCode() ? true : false;
                CaseRaiseChannelEnum raiseChannelEnum = buildRaiseChannel(info.getChannel());
                int rejectCount = cfFirstApproveMaterialDao.countWorkOrderByCaseIdAndApproveStatus(info.getId(), FIRST_APPROVE_UGC_ORDER_CONTENT, FIRST_APPROVE_REJECT);

                CfUserCaseLabelDO labelDO = new CfUserCaseLabelDO();
                labelDO.setUserId(userId);
                labelDO.setCaseId(info.getId());
                labelDO.setEnd(new Date().after(info.getEndTime()));
                labelDO.setRaiseTime(info.getCreateTime());
                labelDO.setEndTime(info.getEndTime());
                labelDO.setChannel(StringUtils.isNotEmpty(info.getChannel()) ? info.getChannel() : "");
                labelDO.setRaiseChannel(raiseChannelEnum.getCode());
                labelDO.setFirstApprove(firstApprove);
                labelDO.setApproveStatus(null != ext ? ext.getFirstApproveStatus() : 0);
                labelDO.setApproveTime(null != ext ? ext.getFirstApproveTime() : new Date());
                labelDO.setRejectCount(rejectCount);
                userCaseLabelDOS.add(labelDO);
            }

            cfUserCaseLabelService.batchInsertCaseLabel(userCaseLabelDOS);
        }

        CfUserLabelInfo userLabelInfo = new CfUserLabelInfo();
        userLabelInfo.setUserId(userId);
        userLabelInfo.setCaseNum(caseNum);
        userLabelInfo.setDraft(draft);
        userLabelInfo.setAccessRaisePageNum(accessRaisePageNum);
        userLabelInfo.setToufangChannel(toufangChannel);
        userLabelInfo.setCaseDetailList(buildCaseDetail(userCaseLabelDOS,cfUserCaseLabels));

        return userLabelInfo;
    }

    //不走前置审核或者前置审核未通过，前置审核通过时间置为null
    //不想这么写，产品太矫情
    private List<CfUserCaseLabelDO> buildCaseDetail(List<CfUserCaseLabelDO> userCaseLabelDOS, List<CfUserCaseLabelDO> cfUserCaseLabels){
        List<CfUserCaseLabelDO> caseDetails = CollectionUtils.isNotEmpty(userCaseLabelDOS) ? userCaseLabelDOS : cfUserCaseLabels;
        caseDetails.forEach(caseDetail -> {
            if(!caseDetail.getFirstApprove() || FirstApproveStatusEnum.APPLY_SUCCESS.getCode() != caseDetail.getApproveStatus()){
                caseDetail.setApproveTime(null);
            }
        });

        return caseDetails;
    }

    private ClewtrackPrimaryChannelEnum queryToufangChannel(long userId){
        try {
            UserInfoModel userInfo = this.userInfoDelegate.getUserInfoByUserId(userId);

            if(null == userInfo || StringUtils.isEmpty(userInfo.getCryptoMobile())){
                return ClewtrackPrimaryChannelEnum.DEFAULT;
            }

            com.shuidihuzhu.common.web.model.Response<CfClewBaseInfoDO> cfClewBase =
                    cfClewtrackApiClient.getClewbaseByMobile(shuidiCipher.decrypt(userInfo.getCryptoMobile()));
            log.info("raisepage variable clewtrack userId:{},result:{}", userId, null != cfClewBase && null != cfClewBase.getData() ? cfClewBase.getData().getPrimaryChannel() : null);

            if(null == cfClewBase || cfClewBase.notOk() || null == cfClewBase.getData()){
                return ClewtrackPrimaryChannelEnum.DEFAULT;
            }

            return ClewtrackPrimaryChannelEnum.parse(cfClewBase.getData().getPrimaryChannel());
        } catch (Exception e) {
            log.info("aisepage variable toufangChannel ERROR", e);
            return ClewtrackPrimaryChannelEnum.DEFAULT;
        }
    }


    private int queryAccessPaisePageNum(long userId){
        int accessRaisePageNum = 0;

        try {
            Response<Map<String, Long>> dsResult =  dsApiClient.queryUserCount(userId);
            log.info("raisepage variable dsApiClient SUCCESS userId:{}, result:{}", userId, null != dsResult ? dsResult.getData() : null);
            if(null == dsResult || 0 != dsResult.getCode() || MapUtils.isEmpty(dsResult.getData())){
                return accessRaisePageNum;
            }

            Map<String, Long> map = (Map<String, Long>) dsResult.getData();
            if(!map.containsKey(String.valueOf(userId))){
                return accessRaisePageNum;
            }

            Long accessNum = map.get(String.valueOf(userId));

            if(null == accessNum || accessNum <= 0){
                return accessRaisePageNum;
            }

            accessRaisePageNum = Integer.valueOf(String.valueOf(accessNum));

            return accessRaisePageNum;
        } catch (Exception e){
            log.error("raisepage variable dsApiClient ERROR,userId:{}", userId, e);
            return 0;
        }
    }

    public CaseRaiseChannelEnum buildRaiseChannel(String channel){
        CaseRaiseChannelEnum caseRaiseChannelEnum = CaseRaiseChannelEnum.default_channel;

        if(StringUtils.isEmpty(channel)){
            channel = StringUtils.EMPTY;
            caseRaiseChannelEnum = CaseRaiseChannelEnum.default_channel;
        }

        if(channel.startsWith("alxq_")){
            caseRaiseChannelEnum = CaseRaiseChannelEnum.alxq;
        }

        List<String> gzh_zh = Lists.newArrayList("wx_3_ckrMenu_fqck","wx_17_ckrMenu_fqck","wx_115_ckrMenu_fqck","wx_116_ckrMenu_fqck","wx_61_ckrMenu_fqck");
        if(gzh_zh.contains(channel)){
            caseRaiseChannelEnum = CaseRaiseChannelEnum.gzh_zhmenu;
        }

        if(channel.startsWith("gzh_") || channel.startsWith("wx_gzh_")){
            caseRaiseChannelEnum = CaseRaiseChannelEnum.gzh_wwgzh;
        }

        if(channel.contains("tuiwen")){
            caseRaiseChannelEnum = CaseRaiseChannelEnum.gzh_tuiwen;
        }

        if(channel.contains("welcome_message")){
            caseRaiseChannelEnum = CaseRaiseChannelEnum.gzh_welcome;
        }

        if(channel.startsWith("wx_null_msg_")){
            caseRaiseChannelEnum = CaseRaiseChannelEnum.gzh_cfqmsg;
        }

        List<String> zh_znkf = Lists.newArrayList("wx_null_askHelper_raiseLeaveToYes", "wx_null_askHelper_raiseLeaveToNo");
        if(zh_znkf.contains(channel)){
            caseRaiseChannelEnum = CaseRaiseChannelEnum.zh_znkf;
        }

        if("wx_null_Auto_raiseLeaveToAuto".equals(channel)){
            caseRaiseChannelEnum = CaseRaiseChannelEnum.zh_znfq;
        }

        List<String> zh_wjdy = Lists.newArrayList("wx_null_wenjuan_raiseLeaveToHand","wx_null_wenjuan_raiseLeaveToAuto","wx_null_wenjuan_raiseLeaveToSuccess");
        if(zh_wjdy.contains(channel)){
            caseRaiseChannelEnum = CaseRaiseChannelEnum.zh_wjdy;
        }

        if(!gzh_zh.contains(channel) && !channel.startsWith("wx_gzh_") && !channel.contains("tuiwen") &&
                !channel.contains("welcome_message") && !channel.startsWith("wx_null_msg_") &&
                !zh_znkf.contains(channel) && !"wx_null_Auto_raiseLeaveToAuto".equals(channel) &&
                !zh_wjdy.contains(channel) && channel.startsWith("wx_")){
            caseRaiseChannelEnum = CaseRaiseChannelEnum.gzh_gf;
        }

        if(channel.startsWith("app_andriod_")){
            caseRaiseChannelEnum = CaseRaiseChannelEnum.app_android;
        }

        if(channel.startsWith("app_ios_")){
            caseRaiseChannelEnum = CaseRaiseChannelEnum.app_ios;
        }

        if(channel.contains("miniapp")){
            caseRaiseChannelEnum = CaseRaiseChannelEnum.wechat;
        }

        if(channel.startsWith("sem_") || "ad_search_sem".equals(channel)){
            caseRaiseChannelEnum = CaseRaiseChannelEnum.sem;
        }

        if(channel.startsWith("guanwang_")){
            caseRaiseChannelEnum = CaseRaiseChannelEnum.seo;
        }

        if("ad_search_feed".equals(channel) || channel.startsWith("feed_") || channel.contains("ad_search_toutiao")){
            caseRaiseChannelEnum = CaseRaiseChannelEnum.feed_flow;
        }

        List<String> bd = Lists.newArrayList("ad_search_bd","cf_volunteer","cf_volunteer_v2","ad_search_toutiao20","ad_search_toutiao19");
        if(channel.startsWith("BD_") || channel.startsWith("bd_") || bd.contains(channel) || channel.contains("fuwu_zhanzhang")){
            caseRaiseChannelEnum = CaseRaiseChannelEnum.bd;
        }

        if(channel.startsWith("outer_")){
            caseRaiseChannelEnum = CaseRaiseChannelEnum.outer_enter;
        }

        if(channel.contains("invite")){
            caseRaiseChannelEnum = CaseRaiseChannelEnum.tuijian;
        }

        return caseRaiseChannelEnum;
    }
}
