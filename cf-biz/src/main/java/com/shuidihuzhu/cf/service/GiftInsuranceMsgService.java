package com.shuidihuzhu.cf.service;

import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.client.baseservice.msg.v2.MsgClientV2;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import com.shuidihuzhu.msg.vo.MessageFeedBack;
import com.shuidihuzhu.msg.vo.MsgResponse;
import com.shuidihuzhu.msg.vo.rpc.MsgRecordBatch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2020/2/18
 */
@Slf4j
@Service
public class GiftInsuranceMsgService {

    @Autowired
    private MsgClientV2 msgClientV2;

    @Autowired(required = false)
    private Producer producer;

    public Response<Void> handleMsg(List<MsgRecordBatch> msgRecordBatches, Integer delayMinutes) {

        if (delayMinutes != null && delayMinutes > 0) {
            String keys = MQTagCons.CF_GIFT_INSURANCE_DELAY_MSG + "_" + System.currentTimeMillis();
            long scheduleTimeStamp = (System.currentTimeMillis() + TimeUnit.MINUTES.toMillis(delayMinutes)) / 1000;
            Message msg = Message.ofSchedule(MQTopicCons.CF, MQTagCons.CF_GIFT_INSURANCE_DELAY_MSG, keys, msgRecordBatches, scheduleTimeStamp);
            MessageResult result = producer.send(msg);
            log.info("GiftInsuranceMsgService sendMq response:{}", result);
            return NewResponseUtil.makeSuccess(null);
        }

        MsgResponse<List<MessageFeedBack>> msgResponse = sendMsg(msgRecordBatches);
        return NewResponseUtil.makeResponse(msgResponse.getCode(), msgResponse.getMsg(), null);
    }

    public MsgResponse<List<MessageFeedBack>> sendMsg(List<MsgRecordBatch> msgRecordBatches) {
        MsgResponse<List<MessageFeedBack>> msgResponse = null;
        if (msgRecordBatches.size() == 1) {
            msgResponse = msgClientV2.saveBatchV2(msgRecordBatches.get(0));
        } else {
            for (MsgRecordBatch msgRecordBatch : msgRecordBatches) {
                msgResponse = msgClientV2.saveBatchV2(msgRecordBatch);
            }
        }
        log.info("GiftInsuranceMsgService sendMsg response:{}", msgResponse);
        return  msgResponse;
    }
}
