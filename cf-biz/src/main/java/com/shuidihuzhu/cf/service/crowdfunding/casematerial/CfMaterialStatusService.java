package com.shuidihuzhu.cf.service.crowdfunding.casematerial;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.biz.crowdfunding.*;
import com.shuidihuzhu.cf.biz.crowdfunding.casematerial.CfQuestionsAnswersBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.verification.CrowdFundingVerificationBiz;
import com.shuidihuzhu.cf.client.material.model.MaterialPlanVersion;
import com.shuidihuzhu.cf.delegate.finance.IFinanceDrawCashFeignDelegate;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.*;
import com.shuidihuzhu.cf.finance.model.vo.CfDrawCashApplyVo;
import com.shuidihuzhu.cf.model.crowdfunding.*;
import com.shuidihuzhu.cf.model.crowdfunding.casematerial.CfCaseVerifyStatus;
import com.shuidihuzhu.cf.model.crowdfunding.casematerial.CfQuestionsAnswers;
import com.shuidihuzhu.cf.service.crowdfunding.CfCommonStoreService;
import com.shuidihuzhu.cf.service.crowdfunding.casematerial.caseAuditListPage.ICfMaterialRejectListService;
import com.shuidihuzhu.cf.service.crowdfunding.casematerial.caseAuditListPage.impl.*;
import com.shuidihuzhu.cf.util.crowdfunding.CrowdfundingUtil;
import com.shuidihuzhu.cf.vo.v5.*;
import com.shuidihuzhu.client.cf.api.model.MaterialVersion;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * @Author: lianghongchao
 * @Date: 2018/11/05 19:21
 */
@Slf4j
@Service
public class CfMaterialStatusService {
    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private CrowdfundingInfoStatusBiz crowdfundingInfoStatusBiz;
    @Autowired
    private CfInfoExtBiz cfInfoExtBiz;
    @Autowired
    private CfRefuseReasonMsgBiz cfRefuseReasonMsgBiz;
    @Autowired
    private CfRedisKvBiz cfRedisKvBiz;
    @Autowired
    private CfQuestionsAnswersBiz cfQuestionsAnswersBiz;
    @Autowired
    private IFinanceDrawCashFeignDelegate financeDrawCashFeignDelegate;
    @Autowired
    private CrowdfundingRefuseReasonItemBiz reasonItemBiz;
    @Autowired
    private CrowdFundingVerificationBiz verificationBiz;
    @Autowired
    private CfCaseMaterialListService cfCaseMaterialListService;
    @Autowired
    private CfPayeeRejectListService payeeListService;
    @Autowired
    private CfPatientRejectListService patientListService;
    @Autowired
    private CfTitleContentRejectListService titleContentListService;
    @Autowired
    private CfTreatmentRejectListService treatmentListService;
    @Autowired
    private CfFundUseRejectListService fundUseRejectListService;
    @Autowired
    private CfCommonStoreService cfCommonStoreService;
    @Autowired
    private CrowdfundingInfoSimpleBiz crowdfundingInfoSimpleBiz;


    public MaterialVersion getMaterialsVersion(String infoUuid){
        CrowdfundingInfo c = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        return getMaterialsVersion(c);
    }

    /**
     * 统一查询入口   后期材料版本问题  在这统一兼容
     *
     * @param c
     * @return
     */
    public MaterialVersion getMaterialsVersion(CrowdfundingInfo c){
        MaterialVersion mv = new MaterialVersion();
        mv.setVersion(MaterialPlanVersion.PLAN_0.getCode());
        if (c == null){
            return mv;
        }
        mv.setVersion(c.getMaterialPlanId());
        return mv;
    }

    public boolean is0906MaterialAudit(String infoUuid) {
        return is0906MaterialAudit(crowdfundingInfoBiz.getFundingInfo(infoUuid));
    }

    public boolean is0906MaterialAudit(CrowdfundingInfo cfCase) {
        return cfCase != null ? MaterialPlanVersion.is0906MaterialAudit(cfCase.getMaterialPlanId()) : false;
    }

    /**
     * 检测驳回时  是否提交了
     * @param infoUuid
     * @param typeEnum
     * @param rejectCodes
     * @return
     */
    public boolean checkReject4Submit(String infoUuid,
                                      long userId,
                                      CrowdfundingInfoDataStatusTypeEnum typeEnum,
                                      List<Integer> rejectCodes){
        log.info("checkReject4Submit infoUuid={} typeEnum={} rejectCodes={}",infoUuid,typeEnum,rejectCodes);
        if (CollectionUtils.isEmpty(rejectCodes)){
            return true;
        }

        CfInfoSimpleModel simpleModel = crowdfundingInfoSimpleBiz.getFundingInfo(infoUuid);
        Map<Integer, Integer> dataTypeStatus = crowdfundingInfoStatusBiz.getMapByInfoUuid(simpleModel.getInfoId());

        List<CfMaterialAuditListView.AuditModifyEntry> list =  cfCaseMaterialListService.getAllAuditEntry(simpleModel,Lists.newArrayList(typeEnum),dataTypeStatus);

//        Pair<Map<Integer, Set<String>>, List<CfMaterialAuditListView.ModifySuggest>> pair = cfCaseMaterialListService.getRejectDetails(infoUuid,typeEnum);
//
//        CfMaterialParam param = new CfMaterialParam();
//        param.setInfoUuid(infoUuid);
//        param.setDataType(typeEnum);
//        param.setMaterialAuditStatus(CrowdfundingInfoStatusEnum.REJECTED.getCode());
//        param.setRejectDetails(pair.getLeft());
//        param.setSuggestViews(pair.getRight());
//
//        List<CfMaterialAuditListView.AuditModifyEntry> list = getServiceRouter(typeEnum).getRejectModifyEntry(param);

        if (CollectionUtils.isEmpty(list)){
            log.info("当前案例没有驳回入口.caseId:{} type:{}", simpleModel.getId(), typeEnum);
           return false;
        }
        Map<Integer,List<CfMaterialAuditListView.AuditModifyEntry>> map = list.stream().collect(Collectors.groupingBy(CfMaterialAuditListView.AuditModifyEntry::getDataType));

        List<CfMaterialAuditListView.AuditModifyEntry> result =  map.get(typeEnum.getCode());

        List<Integer> codes = result.stream().map(CfMaterialAuditListView.AuditModifyEntry::getEntryCode).collect(Collectors.toList());

        List<String> keys = codes.stream().map(code->CfCaseMaterialListService.getEntryKey(infoUuid,code)).collect(Collectors.toList());

        List<CfCommonStoreModel> storeModels = cfCommonStoreService.getByKeys(userId,keys,infoUuid);

        if (CollectionUtils.isEmpty(storeModels)){
            log.info("storeModels =null infoUuid={},keys={}",infoUuid,keys);
            return false;
        }

        Set<String> set = storeModels.stream().map(CfCommonStoreModel::getStoreKey).collect(Collectors.toSet());
        Optional optional = keys.stream().filter(s->!set.contains(s)).findAny();
        boolean r = !optional.isPresent();
        log.info("reject check infoUuid={} codes={} set={} r={}",infoUuid,codes,set,r);
        return r;
    }

    private ICfMaterialRejectListService getServiceRouter( CrowdfundingInfoDataStatusTypeEnum typeEnum){

        switch (typeEnum){
            case BASE_INFO_SUBMIT:
                return titleContentListService;
            case PATIENT_INFO_SUBMIT:
                return patientListService;
            case PAYEE_INFO_SUBMIT:
                return payeeListService;
            case TREATMENT_INFO_SUBMIT:
                return treatmentListService;
            case FUND_USE_SUBMIT:
                return fundUseRejectListService;
        }
        return null;
    }


    public Response<List<CfOneDataStatusVo>> getMaterialsStatus(String infoUuid, long userId) {

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (crowdfundingInfo == null || crowdfundingInfo.getUserId() != userId) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }

        List<CfOneDataStatusVo> result = Lists.newArrayList();

        Map<Integer, CrowdfundingInfoStatus> crowdfundingInfoStatusMap = getInfoStatusMap(crowdfundingInfo.getInfoId());
        //收款信息
        CrowdfundingInfoStatus payee = crowdfundingInfoStatusMap.get(CrowdfundingInfoDataStatusTypeEnum.PAYEE_INFO_SUBMIT.getCode());
        CfOneDataStatusVo vo = new CfOneDataStatusVo();
        vo.setDataTypeName(CrowdfundingInfoDataStatusTypeEnum.PAYEE_INFO_SUBMIT_100.name());
        vo.setDataType(CrowdfundingInfoDataStatusTypeEnum.PAYEE_INFO_SUBMIT_100.getCode());
        vo.setDataStatus(CrowdfundingInfoStatusEnum.UN_SAVE.name());
        if (payee != null){
            vo.setDataStatus(CrowdfundingInfoStatusEnum.getByCode(payee.getStatus()).name());
        }
        result.add(vo);

        //医疗证明
        CrowdfundingInfoStatus patient = crowdfundingInfoStatusMap.get(CrowdfundingInfoDataStatusTypeEnum.PATIENT_INFO_SUBMIT.getCode());
        CrowdfundingInfoStatus treatment = crowdfundingInfoStatusMap.get(CrowdfundingInfoDataStatusTypeEnum.TREATMENT_INFO_SUBMIT.getCode());
        CrowdfundingInfoStatus fund = crowdfundingInfoStatusMap.get(CrowdfundingInfoDataStatusTypeEnum.FUND_USE_SUBMIT.getCode());

        CfOneDataStatusVo v1 = new CfOneDataStatusVo();
        v1.setDataTypeName(CrowdfundingInfoDataStatusTypeEnum.TREATMENT_INFO_SUBMIT_100.name());
        v1.setDataType(CrowdfundingInfoDataStatusTypeEnum.TREATMENT_INFO_SUBMIT_100.getCode());
        v1.setDataStatus(CrowdfundingInfoStatusEnum.UN_SAVE.name());
        //有任何一个没填写   就是继续填写
        if (patient != null || treatment != null || fund != null){
            v1.setDataStatus(CrowdfundingInfoStatusEnum.UN_SUBMITTED.name());
        }
        if (patient != null && patient.getStatus() == CrowdfundingInfoStatusEnum.PASSED.getCode()
                && treatment != null && treatment.getStatus() == CrowdfundingInfoStatusEnum.PASSED.getCode()
                && fund != null && fund.getStatus() == CrowdfundingInfoStatusEnum.PASSED.getCode() ){
            v1.setDataStatus(CrowdfundingInfoStatusEnum.PASSED.name());
        }

        result.add(v1);

        return NewResponseUtil.makeSuccess(result);
    }
    /**
     * 获取案例材料状态与tags
     *
     * @param infoUuid
     * @param userId
     * @return
     */
    public Response<CfAllDataStatusVo> getCfInfoStatusAndTags(String infoUuid, long userId) {
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (crowdfundingInfo == null || crowdfundingInfo.getUserId() != userId) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        CfAllDataStatusVo cfAllDataStatusVo = new CfAllDataStatusVo();
        cfAllDataStatusVo.setInfoStatus(crowdfundingInfo.getStatus().name());
        cfAllDataStatusVo.setCaseEndTime(crowdfundingInfo.getEndTime());
        cfAllDataStatusVo.setCaseAmount(crowdfundingInfo.getAmount());
        //是否可以申请提现

        cfAllDataStatusVo.setApplyDrawCash(crowdfundingInfo.getStatus().equals(CrowdfundingStatus.CROWDFUNDING_STATED));
        cfAllDataStatusVo.setInfoUuid(crowdfundingInfo.getInfoId());
        //提现状态
        Response<CfDrawCashApplyVo>  cashApplyVoFeignResponse = financeDrawCashFeignDelegate.getApplyInfo(crowdfundingInfo.getId());
        log.debug("cashApplyVoFeignResponse:{} ", JSON.toJSONString(cashApplyVoFeignResponse));
        CfDrawCashApplyVo cfDrawCashApplyVo = cashApplyVoFeignResponse.getData();
        if (null == cfDrawCashApplyVo) {
            cfAllDataStatusVo.setWithdrawApplyStatus(CfDrawCashConstant.ApplyStatus.EMPTY_VALUE.name());
        } else {
            cfAllDataStatusVo.setWithdrawApplyStatus(CfDrawCashConstant.ApplyStatus.getByCode(cfDrawCashApplyVo.getApplyStatus()).name());
        }

        CfInfoExt cfInfoExt = cfInfoExtBiz.getByInfoUuid(infoUuid);
        //案例结束状态
        cfAllDataStatusVo.setCaseFinishStatus(CfFinishStatus.getByValue(cfInfoExt.getFinishStatus()).name());

        //保存材料项与tags列表
//        cfAllDataStatusVo.setDataList(this.getCfOneDataStatusVoList(cfInfoExt, crowdfundingInfo));
        cfAllDataStatusVo.setDataList(this.getDataStatusList(crowdfundingInfo, cfInfoExt));

        return NewResponseUtil.makeSuccess(cfAllDataStatusVo);
    }

    private List<CfOneDataStatusVo> getDataStatusList(CrowdfundingInfo crowdfundingInfo, CfInfoExt cfInfoExt) {
        if (cfInfoExt == null || crowdfundingInfo == null) {
            return Lists.newArrayList();
        }

        List<CrowdfundingInfoDataStatusTypeEnum> requiredCaseList = CrowdfundingUtil.getRequiredCaseList(cfInfoExt);
        if (CollectionUtils.isEmpty(requiredCaseList)) {
            return Lists.newArrayList();
        }

        Map<Integer, CrowdfundingInfoStatus> crowdfundingInfoStatusMap = getInfoStatusMap(crowdfundingInfo.getInfoId());

        Map<Integer, List<CfOneDataStatusVo.RejectPositionContentForUser>> contentForUserMap = reasonItemBiz.buildContentForUserMap(
                requiredCaseList.stream().map(CrowdfundingInfoDataStatusTypeEnum::getCode).collect(Collectors.toList()));

        List<CfOneDataStatusVo> resultList = Lists.newArrayList();
        requiredCaseList.sort(Comparator.comparing(CrowdfundingInfoDataStatusTypeEnum::getSort));
        for (CrowdfundingInfoDataStatusTypeEnum dataStatusTypeEnum : requiredCaseList) {
            CrowdfundingInfoStatus infoStatus = crowdfundingInfoStatusMap.get(dataStatusTypeEnum.getCode());
            if (Objects.isNull(infoStatus)) {
                continue;
            }
            // 图文材料只有在驳回时，才在资料认证中显示。图文的infoStatus不会为空
            if (dataStatusTypeEnum.equals(CrowdfundingInfoDataStatusTypeEnum.BASE_INFO_SUBMIT)
                    && infoStatus.getStatus() != CrowdfundingInfoStatusEnum.REJECTED.getCode()) {
                continue;
            }

            CfOneDataStatusVo cfOneDataStatusVo = buildDataStatusVo(infoStatus, dataStatusTypeEnum,
                    contentForUserMap.get(dataStatusTypeEnum.getCode()),
                    cfInfoExt.getCfVersion() >= CfVersion.definition_20181131.getCode());
            resultList.add(cfOneDataStatusVo);
        }

        return resultList;
    }

    private Map<Integer, CrowdfundingInfoStatus> getInfoStatusMap(String infoId) {
        Map<Integer, CrowdfundingInfoStatus> crowdfundingInfoStatusMap = Maps.newHashMap();
        List<CrowdfundingInfoStatus> crowdfundingInfoStatusList = this.crowdfundingInfoStatusBiz.getByInfoUuid(infoId);

        if (CollectionUtils.isNotEmpty(crowdfundingInfoStatusList)) {
            crowdfundingInfoStatusMap = crowdfundingInfoStatusList.stream()
                    .collect(Collectors.toMap(CrowdfundingInfoStatus::getType, Function.identity(), (a, b) -> b));
        }

        return crowdfundingInfoStatusMap;
    }


    private CfOneDataStatusVo buildDataStatusVo(CrowdfundingInfoStatus infoStatus, 
                                                CrowdfundingInfoDataStatusTypeEnum dataStatusTypeEnum,
                                                List<CfOneDataStatusVo.RejectPositionContentForUser> contentForUsers,
                                                boolean definition20181131) {
        CfOneDataStatusVo cfOneDataStatusVo = new CfOneDataStatusVo();
        cfOneDataStatusVo.setDataTypeName(dataStatusTypeEnum.name());
        cfOneDataStatusVo.setDataType(dataStatusTypeEnum.getCode());

        CrowdfundingInfoStatusEnum materialStatus = CrowdfundingInfoStatusEnum.getByCode(
                infoStatus == null ? CrowdfundingInfoStatusEnum.UN_SAVE.getCode() : infoStatus.getStatus());
        cfOneDataStatusVo.setDataStatus(materialStatus.name());
        cfOneDataStatusVo.setEntityList(buildEntityList(infoStatus, dataStatusTypeEnum, materialStatus, contentForUsers, definition20181131));

        return cfOneDataStatusVo;
    }

    private List<CfOneDataStatusVo.EntityItem> buildEntityList(CrowdfundingInfoStatus infoStatus,
                                                               CrowdfundingInfoDataStatusTypeEnum dataStatusTypeEnum,
                                                               CrowdfundingInfoStatusEnum materialStatus,
                                                               List<CfOneDataStatusVo.RejectPositionContentForUser> contentForUsers,
                                                               boolean definition20181131) {
        
        boolean newVersionCredit = isNewVersionCredit(dataStatusTypeEnum, definition20181131);

        List<CfOneDataStatusVo.EntityItem> itemList = generateEntityItemList(contentForUsers, newVersionCredit);

        if (!newVersionCredit && materialStatus == CrowdfundingInfoStatusEnum.REJECTED) {
            fillItemStatusByReject(itemList, infoStatus);
        } else {
            fillItemStatus(itemList, materialStatus);
        }
        return itemList;
    }

    private void fillItemStatusByReject(List<CfOneDataStatusVo.EntityItem> itemList,
                                CrowdfundingInfoStatus infoStatus) {
        CfRefuseReasonMsg reasonMsg = cfRefuseReasonMsgBiz.selectByInfoUuidAndType(infoStatus.getInfoUuid(), infoStatus.getType());
        if (reasonMsg == null || StringUtils.isBlank(reasonMsg.getItemIds())) {
            log.warn("当前案例不能找到驳回详情 infoStatus:{}", infoStatus);
            fillItemStatus(itemList, CrowdfundingInfoStatusEnum.SUBMITTED);
            return;
        }

        // 先将所有的驳回位置置为通过、  用驳回的详情reasonMsg 将具体的驳回位置置为驳回
        fillItemStatus(itemList, CrowdfundingInfoStatusEnum.PASSED);

        List<Integer> rejectItemIds = Lists.transform(Splitter.on(",")
                .splitToList(reasonMsg.getItemIds()), Integer::parseInt);
        for (Integer itemId : rejectItemIds) {
            for (CfOneDataStatusVo.EntityItem entityItem : itemList) {
                if (entityItem.getItemIds().contains(itemId)) {
                    entityItem.setItemStatus(CrowdfundingInfoStatusEnum.REJECTED.name());
                    break;
                }
            }
        }
    }

    private List<CfOneDataStatusVo.EntityItem> generateEntityItemList(List<CfOneDataStatusVo.RejectPositionContentForUser> contentForUsers,
                                                                      boolean newVersionCredit) {
        List<CfOneDataStatusVo.EntityItem> entityList = Lists.newArrayList();

        // 新版、增信材料只需要提交房车。 因此只显示一句给定的话术 （线上新版发起的案例数极少）
        if (newVersionCredit) {
            CfOneDataStatusVo.EntityItem entityItem = new CfOneDataStatusVo.EntityItem();
            entityItem.setContent("患者家庭房屋、车辆财产状况");
            entityList.add(entityItem);
            return entityList;
        }

        for (CfOneDataStatusVo.RejectPositionContentForUser contentForUser : contentForUsers) {
            CfOneDataStatusVo.EntityItem entityItem = new CfOneDataStatusVo.EntityItem();
            entityItem.setContent(contentForUser.getContentForUser());
            entityItem.setItemIds(contentForUser.getItemIds());
            entityList.add(entityItem);
        }

        return entityList;
    }

    private boolean isNewVersionCredit(CrowdfundingInfoDataStatusTypeEnum dataStatusTypeEnum,
                                       boolean definition20181131) {

        return !definition20181131 &&
                dataStatusTypeEnum == CrowdfundingInfoDataStatusTypeEnum.CREDIT_SUPPLEMENT_INFO_SUBMIT ;
    }

    private void fillItemStatus(List<CfOneDataStatusVo.EntityItem> itemList, CrowdfundingInfoStatusEnum materialStatus) {
        for (CfOneDataStatusVo.EntityItem item : itemList) {
            item.setItemStatus(materialStatus.name());
        }
    }

    /**
     * 获取某个类型的驳回信息
     *
     * @param infoUuid
     * @param dataStatusTypeEnum
     * @return
     */

    public CfTreatmentMaterialView.TreatmentRejectDetail getRejectDetailsByType(String infoUuid, CrowdfundingInfoDataStatusTypeEnum dataStatusTypeEnum) {

        CfTreatmentMaterialView.TreatmentRejectDetail treatmentRejectDetail = new CfTreatmentMaterialView
                .TreatmentRejectDetail();

        if (StringUtils.isEmpty(infoUuid) || dataStatusTypeEnum == null) {
            return treatmentRejectDetail;
        }

        CfRefuseReasonMsg cfRefuseReasonMsg = cfRefuseReasonMsgBiz.selectByInfoUuidAndType(infoUuid, dataStatusTypeEnum.getCode());
        if (cfRefuseReasonMsg == null) {
            return treatmentRejectDetail;
        }

        Map<Integer, Set<String>> rejectDetails = buildRejectReasonMap(cfRefuseReasonMsg.getItemReason(), infoUuid);

        treatmentRejectDetail.setRejectDetailsV1(rejectDetails);
        treatmentRejectDetail.setDiseaseAndHospitalCommonReasons(getCommonRejectReason(rejectDetails));
        removeCommonReasons(treatmentRejectDetail);
        return treatmentRejectDetail;
    }

    private Map<Integer, Set<String>> buildRejectReasonMap(String itemReasons, String infoUuid) {
        Map<Integer, Set<String>> rejectDetails = Maps.newHashMap();

        List<ItemReason> itemReasonList = Lists.newArrayList();
        try {
            itemReasonList = JSON.parseArray(itemReasons, ItemReason.class);
        } catch (Exception e) {
            log.error("驳回理由解析错误 infoUuid:{}", infoUuid, e);
        }

        if (CollectionUtils.isEmpty(itemReasonList)) {
            return rejectDetails;
        }

        for (ItemReason reason : itemReasonList) {
            Set<String> rejectReasons = rejectDetails.get(reason.getItemIds());
            if (rejectReasons == null) {
                rejectReasons = Sets.newHashSet();
            }
            if (reason.getReason() != null) {
                rejectReasons.addAll(reason.getReason().values());
            }
            rejectDetails.put(reason.getItemIds(), rejectReasons);
        }

        return  rejectDetails;
    }

    private Set<String> getCommonRejectReason(Map<Integer, Set<String>> rejectDetails) {
        Set<String> commonReasons = Sets.newHashSet();

        if (MapUtils.isEmpty(rejectDetails)) {
            return commonReasons;
        }

        // 疾病名称 和 确诊医院相关的 驳回理由去重
        if (!containDiseaseAndHospitalReason(rejectDetails)) {
            return commonReasons;
        }

        Set<String> hospitalReasons = getHospitalRejectReasons(rejectDetails);
        for (String reason : rejectDetails.get(CfTreatmentMaterialView.DISEASE_NAME_REJECT_POSITION)) {
            if (hospitalReasons.contains(reason)) {
                commonReasons.add(reason);
            }
        }

        return commonReasons;
    }

    private Set<String> getHospitalRejectReasons(Map<Integer, Set<String>> rejectDetails) {
        Set<String> hospitalReasons = Sets.newHashSet();

        hospitalReasons.addAll(getEmptyIfNull(rejectDetails.get(CfTreatmentMaterialView.HOSPITAL_REJECT_POSITION)));
        hospitalReasons.addAll(getEmptyIfNull(rejectDetails.get(CfTreatmentMaterialView.IF_DIAGNOSIS_REJECT_POSITION)));
        hospitalReasons.addAll(getEmptyIfNull(rejectDetails.get(CfTreatmentMaterialView.DIAGNOSIS_HOSPITAL_REJECT_POSITION)));

        return hospitalReasons;
    }

    private Set<String> getEmptyIfNull(Set<String> reasons) {
        return reasons == null ? Sets.newHashSet() : reasons;
    }

    private void removeCommonReasons(CfTreatmentMaterialView.TreatmentRejectDetail treatmentRejectDetail) {

        if (treatmentRejectDetail == null
                || CollectionUtils.isEmpty(treatmentRejectDetail.getDiseaseAndHospitalCommonReasons())) {
            return;
        }
        Map<Integer, Set<String>> rejectDetails = treatmentRejectDetail.getRejectDetailsV1();
        for (Integer rejectPosition : CfTreatmentMaterialView.FILTER_COMMON_REJECT_REASON_POSITIONS) {
            if (CollectionUtils.isEmpty(rejectDetails.get(rejectPosition))) {
                continue;
            }
            rejectDetails.get(rejectPosition).removeAll(treatmentRejectDetail.getDiseaseAndHospitalCommonReasons());
        }
    }

    private boolean containDiseaseAndHospitalReason(Map<Integer, Set<String>> rejectDetails) {

        return rejectDetails.containsKey(CfTreatmentMaterialView.DISEASE_NAME_REJECT_POSITION)
                && (rejectDetails.containsKey(CfTreatmentMaterialView.HOSPITAL_REJECT_POSITION)
                    || rejectDetails.containsKey(CfTreatmentMaterialView.IF_DIAGNOSIS_REJECT_POSITION)
                    || rejectDetails.containsKey(CfTreatmentMaterialView.DIAGNOSIS_HOSPITAL_REJECT_POSITION));
    }

    /**
     * @param dataQuestionsId
     * @return
     */
    public List<CfQuestionsAnswers> getQuestionsAnswers(String dataQuestionsId) {
        if (StringUtils.isEmpty(dataQuestionsId)) {
            return Lists.newArrayList();
        }
        String idStr = cfRedisKvBiz.queryByKey(dataQuestionsId, true);
        if (StringUtils.isEmpty(idStr)) {
            return Lists.newArrayList();
        }
        Set<Integer> idSet = Sets.newHashSet();
        for (String str : idStr.split(",")) {
            try {
                idSet.add(Integer.parseInt(str));
            } catch (Exception e) {
                log.error("", e);
            }
        }

        List<CfQuestionsAnswers> questionsAnswersList = cfQuestionsAnswersBiz.getByIds(idSet);
        if (CollectionUtils.isEmpty(questionsAnswersList)) {
            return Lists.newArrayList();
        }

        //按照数据给的的顺序给前端显示
        questionsAnswersList.sort(Comparator.comparing(CfQuestionsAnswers::getSort));

        return questionsAnswersList;
    }

    @Data
    public static class ItemReason {
        private int itemIds;
        private Map<Integer, String> reason;
    }

    @Data
    public static class ItemEntityReason {
        private int itemIds;
        private int entityId;
        private String reason;
    }

    public CfCaseVerifyStatus getVerifyStatus(String infoUuid) {

        CrowdfundingInfo caseInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (caseInfo == null) {
            return null;
        }
        CfInfoExt infoExt = cfInfoExtBiz.getByInfoUuid(infoUuid);
        if (infoExt == null) {
            return null;
        }

        CfCaseVerifyStatus verify = new CfCaseVerifyStatus();
        verify.setAmount(caseInfo.getAmount() / 100);
        verify.setTargetAmount(caseInfo.getTargetAmount() / 100);
        verify.setVerificationCount(verificationBiz.countCrowdFundingVerificationByInfoUuid(infoUuid));

        verify.setMaterialPass(caseInfo.getStatus() == CrowdfundingStatus.CROWDFUNDING_STATED);

        verify.setCaseEnd(caseInfo.getEndTime() != null && caseInfo.getEndTime().before(new Date()));
        verify.setCfVersion(infoExt.getCfVersion());

        return verify;
    }
}
