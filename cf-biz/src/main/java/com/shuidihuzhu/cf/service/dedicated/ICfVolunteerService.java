package com.shuidihuzhu.cf.service.dedicated;


import com.shuidihuzhu.cf.domain.dedicated.CfUserVolunteerRelationDO;
import com.shuidihuzhu.cf.domain.dedicated.CrowdfundingVolunteerInviteUserRecordDO;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.client.cf.growthtool.model.CfVolunteerMaterialDO;
import com.shuidihuzhu.client.cf.growthtool.model.CfVolunteerModelDto;
import com.shuidihuzhu.client.cf.growthtool.model.CrowdfundingVolunteer;

/**
 * @author: wanghui
 * @create: 2019/1/14 8:07 PM
 */
public interface ICfVolunteerService {
    CrowdfundingVolunteer getByUniqueCode(String uniqueCode);

    CfUserVolunteerRelationDO getAccountUserAndVolunteerRelationByPhone(String phone);

    CfVolunteerMaterialDO getVolunteerMateri(String uniqueCode);

    CrowdfundingVolunteer getVolunteer(CrowdfundingInfo crowdfundingInfo);
}
