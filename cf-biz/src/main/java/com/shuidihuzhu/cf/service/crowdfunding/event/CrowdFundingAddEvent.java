package com.shuidihuzhu.cf.service.crowdfunding.event;

import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.account.model.UserThirdModel;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoBaseVo;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import lombok.Data;
import org.springframework.context.ApplicationEvent;

/**
 * 新建筹款事件
 */
public class CrowdFundingAddEvent extends ApplicationEvent {

    private static final long serialVersionUID = 1L;
    private CrowdfundingInfoBaseVo crowdfundingInfoBaseVo;
    private CrowdfundingInfo crowdfundingInfo;
    private String clientIp;
    private Message message;
    private UserThirdModel userThirdModel;
    private UserInfoModel userInfoModel;

    public CrowdfundingInfoBaseVo getCrowdfundingInfoBaseVo() {
        return crowdfundingInfoBaseVo;
    }

    public void setCrowdfundingInfoBaseVo(CrowdfundingInfoBaseVo crowdfundingInfoBaseVo) {
        this.crowdfundingInfoBaseVo = crowdfundingInfoBaseVo;
    }

    public CrowdfundingInfo getCrowdfundingInfo() {
        return crowdfundingInfo;
    }

    public void setCrowdfundingInfo(CrowdfundingInfo crowdfundingInfo) {
        this.crowdfundingInfo = crowdfundingInfo;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public Message getMessage() {
        return message;
    }

    public void setMessage(Message message) {
        this.message = message;
    }

    public UserThirdModel getUserThirdModel() {
        return userThirdModel;
    }

    public UserInfoModel getUserInfoModel() {
        return userInfoModel;
    }

    public void setUserThirdModel(UserThirdModel userThirdModel) {
        this.userThirdModel = userThirdModel;
    }

    public CrowdFundingAddEvent(Object source, CrowdfundingInfoBaseVo crowdfundingInfoBaseVo,
                                CrowdfundingInfo crowdfundingInfo, String clientIp,
                                 UserThirdModel userThirdModel, Message message, UserInfoModel userInfoModel) {
        super(source);
        this.crowdfundingInfoBaseVo = crowdfundingInfoBaseVo;
        this.crowdfundingInfo = crowdfundingInfo;
        this.clientIp = clientIp;
        this.userThirdModel = userThirdModel;
        this.message = message;
        this.userInfoModel = userInfoModel;
    }
}
