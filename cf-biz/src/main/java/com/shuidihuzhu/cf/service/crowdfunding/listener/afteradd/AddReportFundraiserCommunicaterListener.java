package com.shuidihuzhu.cf.service.crowdfunding.listener.afteradd;

import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.crowdfunding.event.AddListenerOrder;
import com.shuidihuzhu.cf.service.crowdfunding.event.CrowdFundingAddEvent;
import com.shuidihuzhu.cf.service.crowdfunding.listener.base.AbstractCrowdFundingAddEventListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.event.SmartApplicationListener;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @time 2019/12/17 下午2:17
 * @desc
 */
@Slf4j
@Service
public class AddReportFundraiserCommunicaterListener extends AbstractCrowdFundingAddEventListener implements SmartApplicationListener {
    @Autowired(required = false)
    private Producer producer;

    @Override
    public void onApplicationEvent(ApplicationEvent event) {
        CrowdFundingAddEvent crowdFundingAddEvent = (CrowdFundingAddEvent) event;
        CrowdfundingInfo cfCase = crowdFundingAddEvent.getCrowdfundingInfo();
        producer.send(new Message(MQTopicCons.CF, MQTagCons.ADD_REPORT_FUNDRAISER_COMMUNICATER, cfCase.getInfoId(), cfCase));
    }

    @Override
    public int getOrder() {
        return AddListenerOrder.ReportFundraiserCommunicater.getValue();
    }
}
