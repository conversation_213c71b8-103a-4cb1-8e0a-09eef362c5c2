package com.shuidihuzhu.cf.service.crowdfunding;

import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.shuidihuzhu.cf.dao.crowdfunding.CrowdfundingIdCaseDao;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingIdCaseStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingIdCase;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.msg.MsgClientV2Service;
import com.shuidihuzhu.cf.util.crowdfunding.CFPushRecordFactory;
import com.shuidihuzhu.cipher.OldShuidiCipher;
import com.shuidihuzhu.cipher.ShuidiCipher;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by sven on 18/8/16.
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class CrowdfundingIdCaseService {

    @Resource
    private CrowdfundingIdCaseDao crowdundingIdCaseDao;
    @Autowired
    private OldShuidiCipher oldShuidiCipher;
    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private MsgClientV2Service msgClientV2Service;


    private Gson gson = new GsonBuilder().disableHtmlEscaping().create();

    public CrowdfundingIdCase getByInfoId(int caseId){
        CrowdfundingIdCase idCase = crowdundingIdCaseDao.getByInfoId(caseId);
        if(idCase!= null) {
            putIdentifyVo(idCase);
        }
        return idCase;
    }

    private void putIdentifyVo(CrowdfundingIdCase idCase) {
        String cryptoIdcard = idCase.getCryptoIdCard();
        String identify = shuidiCipher.decrypt(cryptoIdcard);
        idCase.setIdCardVo(identify);
    }

    public boolean insert(CrowdfundingIdCase crowdfundingIdCase){
        return crowdundingIdCaseDao.insert(crowdfundingIdCase) > 0;
    }

    public boolean updateStatus(int caseId, CrowdfundingIdCaseStatusEnum crowdfundingIdCaseStatusEnum){
        return crowdundingIdCaseDao.updateStatus(caseId, crowdfundingIdCaseStatusEnum.getCode()) > 0;
    }

    public boolean updateIdCase(int caseId, String name, String identify){
        String cryptoIdCard = oldShuidiCipher.aesEncrypt(identify);
        return crowdundingIdCaseDao.updateIdCase(caseId, name, cryptoIdCard) > 0;
    }

    @Async
    public void sendIdCaseReqeustMsg(CrowdfundingInfo crowdfundingInfo){
        //发送app推送
        this.sendIdCaseAppPush(crowdfundingInfo);
    }

    @Async
    public void sendIdCaseFailMsg(CrowdfundingInfo crowdfundingInfo){
        //发送app推送
        this.sendIdCaseFailAppPush(crowdfundingInfo);
    }

    private void sendIdCaseAppPush(CrowdfundingInfo crowdfundingInfo) {
        msgClientV2Service.sendAppMsg("TYY8320", Lists.newArrayList(crowdfundingInfo.getUserId()));
    }

    private void sendIdCaseFailAppPush(CrowdfundingInfo crowdfundingInfo) {
        msgClientV2Service.sendAppMsg("AMV0124", Lists.newArrayList(crowdfundingInfo.getUserId()));
    }


    public List<CrowdfundingIdCase> getByCryptoIdCard(String cryptoIdCard) {
        if (StringUtils.isBlank(cryptoIdCard)) {
            return Lists.newArrayList();
        }
        return crowdundingIdCaseDao.getByCryptoIdCard(cryptoIdCard);
    }

}
