package com.shuidihuzhu.cf.service.crowdfunding.listener.afteradd;

import com.shuidihuzhu.cf.service.crowdfunding.listener.base.AbstractCrowdFundingAddEventListener;
import org.apache.commons.lang3.StringUtils;
import com.shuidihuzhu.cf.facade.risk.CfCaseRiskFacade;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoBaseVo;
import com.shuidihuzhu.cf.service.crowdfunding.event.CrowdFundingAddEvent;
import com.shuidihuzhu.cf.service.crowdfunding.event.AddListenerOrder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.event.SmartApplicationListener;
import org.springframework.stereotype.Service;

/**
 * 案例可以转发的时候
 */
@Slf4j
@Service
public class CaseCouldShareAndDonateListener extends AbstractCrowdFundingAddEventListener implements SmartApplicationListener {

    @Autowired
    private CfCaseRiskFacade cfCaseRiskFacade;

    @Override
    public void onApplicationEvent(ApplicationEvent _event) {


        CrowdFundingAddEvent crowdFundingAddEvent = (CrowdFundingAddEvent) _event;
        CrowdfundingInfo cfCase = crowdFundingAddEvent.getCrowdfundingInfo();
        CrowdfundingInfoBaseVo infoBaseVo = crowdFundingAddEvent.getCrowdfundingInfoBaseVo();
        if (infoBaseVo != null) {
            if (!StringUtils.isEmpty(infoBaseVo.getSelfRealName()) ||
                    !StringUtils.isEmpty(infoBaseVo.getPatientRealName())) {
            }else {
                try {
                    cfCaseRiskFacade.onCaseCouldShareAndDonate(cfCase.getInfoId());
                } catch (Exception e) {
                    log.error("CaseCouldShareAndDonateListener error:", e);
                }
            }
        }

    }

    @Override
    public int getOrder() {
        return AddListenerOrder.CaseCouldShareAndDonate.getValue();
    }
}
