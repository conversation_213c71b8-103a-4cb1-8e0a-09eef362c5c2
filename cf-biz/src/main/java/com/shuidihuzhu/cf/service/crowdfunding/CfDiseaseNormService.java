package com.shuidihuzhu.cf.service.crowdfunding;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shuidihuzhu.cf.dao.crowdfunding.CfDiseaseNormDao;
import com.shuidihuzhu.cf.delegate.DiseaseNormDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.DiseaseLabel;
import com.shuidihuzhu.cf.model.disease.DiseaseNorm;
import com.shuidihuzhu.data.analytics.javasdk.core.Analytics;
import com.shuidihuzhu.data.analytics.javasdk.core.UserTagTypeEnum;
import com.shuidihuzhu.data.servicelog.meta.cf.CommercialForwarding;
import com.shuidihuzhu.data.servicelog.meta.cf.DiseaseNormDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2021/2/10  4:15 下午
 */
@Service
@Slf4j
@RefreshScope
public class CfDiseaseNormService {
    @Resource
    private CfDiseaseNormDao cfDiseaseNormDao;
    @Resource
    private DiseaseNormDelegate diseaseNormDelegate;
    @Resource
    private Analytics analytics;

    @Value("#{'${apollo.new.case.info.check.aml.disease.name:}'.split(',')}")
    private List<String> AMLList;

    @Value("#{'${apollo.new.case.info.check.all.disease.name:}'.split(',')}")
    private List<String> ALLList;

    public String diseaseNormNew(String diseaseName) {
        Map<String, Set<String>> map = diseaseNormDelegate.agent(Lists.newArrayList(diseaseName));
        if (MapUtils.isEmpty(map) || CollectionUtils.isEmpty(map.get(diseaseName))) {
            return StringUtils.EMPTY;
        }

        return Joiner.on(",").join(Lists.newArrayList(map.get(diseaseName)));
    }

    public Map<String, Set<String>> diseaseNormMap(String diseaseName) {
        if (StringUtils.isBlank(diseaseName)) {
            return Maps.newHashMap();
        }
        List<String> diseaseNames = List.of(Objects.requireNonNull(StringUtils.split(diseaseName, ",，")));
        if (CollectionUtils.isEmpty(diseaseNames)) {
            return Maps.newHashMap();
        }
        Map<String, Set<String>> map = diseaseNormDelegate.agent(diseaseNames);
        if (MapUtils.isEmpty(map)) {
            return Maps.newHashMap();
        }
        return map;
    }

    public DiseaseLabel selectByCaseId(int caseId) {
        return cfDiseaseNormDao.selectByCaseId(caseId);
    }

    public DiseaseNorm getByCaseId(int caseId) {
        return cfDiseaseNormDao.getByCaseId(caseId);
    }

    public void insert(int caseId, String infoUuid, String diseaseName) {
        Map<String, Set<String>> diseaseNormMap = diseaseNormMap(diseaseName);
        String diseaseNorm = getDiseaseNorm(diseaseNormMap);
        String diseaseNormJson = MapUtils.isNotEmpty(diseaseNormMap) ? JSONObject.toJSONString(diseaseNormMap) : StringUtils.EMPTY;

        int count = cfDiseaseNormDao.selectCount(caseId);
        log.info("获取到的疾病归一结果insert caseId:{} diseaseName:{} diseaseNorm:{} count:{}", caseId, diseaseName, diseaseNorm, count);
        if (count == 0) {
            cfDiseaseNormDao.insert(caseId, infoUuid, diseaseName, diseaseNorm, diseaseNormJson);
        }
    }

    public void update(int caseId, String diseaseName) {
        Map<String, Set<String>> diseaseNormMap = diseaseNormMap(diseaseName);
        String diseaseNorm = getDiseaseNorm(diseaseNormMap);
        String diseaseNormJson = MapUtils.isNotEmpty(diseaseNormMap) ? JSONObject.toJSONString(diseaseNormMap) : StringUtils.EMPTY;

        int count = cfDiseaseNormDao.selectCount(caseId);
        log.info("获取到的疾病归一结果update caseId:{} diseaseName:{} diseaseNorm:{} count:{}", caseId, diseaseName, diseaseNorm, count);
        if (count == 1) {
            cfDiseaseNormDao.update(caseId, diseaseName, diseaseNorm, diseaseNormJson);
        }
    }

    private static String getDiseaseNorm(Map<String, Set<String>> diseaseNormMap) {
        if (MapUtils.isEmpty(diseaseNormMap)) {
            return StringUtils.EMPTY;
        }

        Set<String> diseaseNormSets = Sets.newHashSet();
        for (Map.Entry<String, Set<String>> entry : diseaseNormMap.entrySet()) {
            Set<String> diseaseNormSet = entry.getValue();
            if (CollectionUtils.isEmpty(diseaseNormSet)) {
                continue;
            }
            diseaseNormSets.addAll(diseaseNormSet);
        }

        return Joiner.on(",").join(diseaseNormSets);
    }

    public List<DiseaseNorm> selectByCaseIds(List<Integer> caseIds) {
        if (CollectionUtils.isEmpty(caseIds)) {
            return Lists.newArrayList();
        }
        return cfDiseaseNormDao.selectByCaseIds(caseIds);
    }

    public void getDiseaseNormByCheckAMLAndALL(DiseaseNorm diseaseNorm, String infoUuid, long userId) {
        List<String> diseaseNormList = Splitter.on(",").splitToList(diseaseNorm.getDiseaseNorm());
        boolean haveDisease = diseaseNormList.contains("急性白血病");
        if (!haveDisease) {
            return;
        }

        String disease = StringUtils.EMPTY;

        for (String AML : AMLList) {
            if (diseaseNorm.getDiseaseName().contains(AML)) {
                disease = "急性髓系白血病";
            }
        }

        for (String ALL : ALLList) {
            if (diseaseNorm.getDiseaseName().contains(ALL)) {
                disease = "急性淋巴细胞白血病";
            }
        }

        if (StringUtils.isEmpty(disease)) {
            return;
        }

        List<String> diseaseList = Lists.newArrayList();
        diseaseList.addAll(diseaseNormList);
        diseaseList.add(disease);
        diseaseList.remove("急性白血病");

        diseaseNorm.setDiseaseNorm(Joiner.on(",").join(diseaseList));
        track(userId, infoUuid, diseaseNorm.getDiseaseNorm());

    }

    private void track(long userId, String infoUuid, String diseaseNorm) {
        if (userId <= 0) {
            return;
        }

        DiseaseNormDetail diseaseNormDetail = new DiseaseNormDetail();

        try {
            diseaseNormDetail.setCase_id(infoUuid);
            diseaseNormDetail.setNorm_disease_name(diseaseNorm);

            diseaseNormDetail.setUser_tag(String.valueOf(userId));
            diseaseNormDetail.setUser_tag_type(UserTagTypeEnum.userid);

            analytics.track(diseaseNormDetail);
            log.info("急性白血病细化记录:{}", JSONObject.toJSONString(diseaseNormDetail));
        } catch (Exception e) {
            log.error("急性白血病细化记录异常:{}", JSONObject.toJSONString(diseaseNormDetail));
        }
    }
}
