package com.shuidihuzhu.cf.service.crowdfunding.casematerial;

import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingTreatmentBiz;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingTreatment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CrowdfundingTreatmentService {
    @Autowired
    private CrowdfundingTreatmentBiz crowdfundingTreatmentBiz;

    public void addCrowdfundingTreatment(CrowdfundingTreatment crowdfundingTreatment, String infoUuid) {
        crowdfundingTreatmentBiz.add(crowdfundingTreatment, infoUuid);
    }

    public int updateCrowdfundingTreatment(CrowdfundingTreatment crowdfundingTreatment) {
        return crowdfundingTreatmentBiz.update(crowdfundingTreatment);
    }
}
