package com.shuidihuzhu.cf.service.crowdfunding.event;

import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.account.model.UserThirdModel;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoBaseVo;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

/**
 * 新建筹款事件发布
 */
@Service
public class CrowdFundingAddPublisher {

    @Autowired
    ApplicationContext applicationContext;

    public void addCrowdFunding(CrowdfundingInfoBaseVo crowdfundingInfoBaseVo,
                                CrowdfundingInfo crowdfundingInfo, String clientIp, UserThirdModel userThirdModel,
                                Message message, UserInfoModel userInfoModel) {
        // 1.构造一个事件
        CrowdFundingAddEvent event = new CrowdFundingAddEvent(
                this, crowdfundingInfoBaseVo, crowdfundingInfo, clientIp,userThirdModel,message, userInfoModel);
        // 2.触发事件
        applicationContext.publishEvent(event);


    }
}
