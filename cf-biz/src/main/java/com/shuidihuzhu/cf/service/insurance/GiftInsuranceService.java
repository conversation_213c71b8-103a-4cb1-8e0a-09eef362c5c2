package com.shuidihuzhu.cf.service.insurance;

import com.shuidihuzhu.cf.dao.insurance.CfReceiveGiftInsuranceRecordDao;
import com.shuidihuzhu.cf.model.insurance.CfReceiveGiftInsuranceRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/3/11
 */

@Slf4j
@Service
public class GiftInsuranceService {

    @Resource
    private CfReceiveGiftInsuranceRecordDao cfReceiveGiftInsuranceRecordDao;

    public boolean hasReceivedGiftInsurance(long userId) {
        try {
            int recordCount = cfReceiveGiftInsuranceRecordDao.selectRecordCount(userId);
            return recordCount > 0;
        } catch (Exception e) {
            log.error("selectRecordCount error. userId:{}", userId, e);
        }
        return false;
    }

    public boolean receiveGiftInsurance(long userId) {

        try {
            boolean hasReceived = hasReceivedGiftInsurance(userId);
            if (!hasReceived) {
                cfReceiveGiftInsuranceRecordDao.insert(userId);
            }
            return true;
        } catch (Exception e) {
            log.error("receiveGiftInsurance error. userId:{}", userId, e);
        }
        return false;
    }
}
