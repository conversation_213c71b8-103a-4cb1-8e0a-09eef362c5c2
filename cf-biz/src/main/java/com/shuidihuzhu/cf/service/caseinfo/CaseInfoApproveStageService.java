package com.shuidihuzhu.cf.service.caseinfo;

import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingAttachmentBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.dao.caseinfo.CaseInfoApproveStageDAO;
import com.shuidihuzhu.cf.domain.caseinfo.CaseInfoApproveStageDO;
import com.shuidihuzhu.cf.enums.crowdfunding.AttachmentTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingAttachmentVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CaseInfoApproveStageService {

    @Autowired
    private CaseInfoApproveStageDAO caseInfoApproveStageDAO;

    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;

    @Autowired
    private CrowdfundingAttachmentBiz crowdfundingAttachmentBiz;

    public int insert(int caseId, String title, String content, String images) {
        CaseInfoApproveStageDO v = new CaseInfoApproveStageDO();
        v.setCaseId(caseId);
        v.setTitle(title);
        v.setContent(content);
        v.setImages(images);
        crowdfundingAttachmentBiz.saveAttachmentByImages(caseId, images, AttachmentTypeEnum.ATTACH_USER_STAGE_CF);
        log.info("案例图文数据入暂存表.caseId:{} title:{} content:{} images:{}", caseId, title, content, images);
        return caseInfoApproveStageDAO.insert(v);
    }

    public int update(int caseId, String title, String content, List<String> images) {
        return update(caseId, title, content, processImages(images));
    }

    public int update(int caseId, String title, String content, String images) {
        CaseInfoApproveStageDO v = caseInfoApproveStageDAO.getByCaseId(caseId);

        log.info("案例图文数据更新暂存表.caseId:{} title:{} content:{} images:{}", caseId, title, content, images);

        if (v != null) {
            v.setTitle(title);
            v.setContent(content);
            v.setImages(images);
            crowdfundingAttachmentBiz.saveAttachmentByImages(caseId, images, AttachmentTypeEnum.ATTACH_USER_STAGE_CF);
            return caseInfoApproveStageDAO.update(v);
        }

        // 老数据没有记录
        CrowdfundingInfo fundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        if (title == null) {
            title = fundingInfo.getTitle();
        }
        if (content == null) {
            content = fundingInfo.getContent();
        }
        if (images == null) {
            images = crowdfundingAttachmentBiz
                    .getAttachmentsByType(caseId, AttachmentTypeEnum.ATTACH_CF)
                    .stream()
                    .map(CrowdfundingAttachmentVo::getUrl)
                    .collect(Collectors.joining(","));
        }
        crowdfundingAttachmentBiz.saveAttachmentByImages(caseId, images, AttachmentTypeEnum.ATTACH_USER_STAGE_CF);
        return insert(caseId, title, content, images);
    }

    private String processImages(List<String> images) {
        if (images == null) {
            return null;
        }
        return StringUtils.join(images, ',');
    }

    @Nullable
    public CaseInfoApproveStageDO getByCaseId(int caseId) {
        return caseInfoApproveStageDAO.getByCaseId(caseId);
    }

    public List<CaseInfoApproveStageDO> getByCaseIdBatch(List<Integer> caseIdList) {
        return caseInfoApproveStageDAO.getByCaseIdBatch(caseIdList);
    }

}
