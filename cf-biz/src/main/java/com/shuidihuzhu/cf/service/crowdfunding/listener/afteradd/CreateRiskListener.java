package com.shuidihuzhu.cf.service.crowdfunding.listener.afteradd;

import com.shuidihuzhu.cf.facade.risk.CfCaseRiskFacade;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.crowdfunding.event.CrowdFundingAddEvent;
import com.shuidihuzhu.cf.service.crowdfunding.event.AddListenerOrder;
import com.shuidihuzhu.cf.service.crowdfunding.listener.base.AbstractCrowdFundingAddEventListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.event.SmartApplicationListener;
import org.springframework.stereotype.Service;

/**
 * 创建风险对应记录
 */
@Slf4j
@Service
public class CreateRiskListener extends AbstractCrowdFundingAddEventListener implements SmartApplicationListener {

    @Autowired
    private CfCaseRiskFacade cfCaseRiskFacade;

    @Override
    public void onApplicationEvent(ApplicationEvent _event) {

        CrowdFundingAddEvent crowdFundingAddEvent = (CrowdFundingAddEvent) _event;
        CrowdfundingInfo cfCase = crowdFundingAddEvent.getCrowdfundingInfo();
        if (cfCase != null) {
            try {
                //风险上报
                cfCaseRiskFacade.onRaise(cfCase.getInfoId());

            } catch (Exception e) {
                log.error("cfCaseRiskFacade.onRaise error:", e);
            }
        }

    }

    @Override
    public int getOrder() {
        return AddListenerOrder.CreateRisk.getValue();
    }
}
