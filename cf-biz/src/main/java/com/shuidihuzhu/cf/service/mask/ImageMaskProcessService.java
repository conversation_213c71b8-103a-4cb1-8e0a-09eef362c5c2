package com.shuidihuzhu.cf.service.mask;

import com.shuidihuzhu.alps.feign.ocean.OceanApiMQResponse;
import com.shuidihuzhu.client.model.ImageMaskInform;
import com.shuidihuzhu.client.model.MaskAttachmentVo;

import java.util.List;

/**
 * @Description: 图片掩码处理服务
 * @Author: panghairui
 * @Date: 2022/9/8 3:18 下午
 */
public interface ImageMaskProcessService {

    /**
     * 调用AI接口处理图片
     */
    void processImageToAIMask(ImageMaskInform imageMaskInform);

    /**
     * 保存AI掩码后的图片
     */
    void submitAiMaskImage(OceanApiMQResponse apiMQResponse);

    /**
     * 根据业务id查询掩码图片信息
     */
    List<MaskAttachmentVo> queryMaskImageByBizId(List<Long> bizIds, List<Integer> bizType);

    /**
     * 根据业务id删除掩码图片
     */
    Integer deleteMaskImageByBizId(List<Long> bizIds, List<Integer> bizType);

    Integer deleteMaskProgressImage(List<Long> bizIds, List<Integer> bizType, String imageUrl);

}
