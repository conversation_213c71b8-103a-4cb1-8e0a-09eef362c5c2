package com.shuidihuzhu.cf.service.combineorder;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.combine.CfCombineOrderManageBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.dao.crowdfunding.combine.CfContributeOrderDao;
import com.shuidihuzhu.cf.enums.combine.CombineCommonEnum;
import com.shuidihuzhu.cf.model.contribute.CfCombineOrderManage;
import com.shuidihuzhu.cf.model.contribute.CfContributeMsgBody;
import com.shuidihuzhu.cf.model.contribute.CfContributeOrder;
import com.shuidihuzhu.cf.model.contribute.CfContributeOrderListParam;
import com.shuidihuzhu.cf.model.contribute.CfContributeTotalInfo;
import com.shuidihuzhu.cf.model.contribute.CfPayInnerCallBack;
import com.shuidihuzhu.cf.response.NewPageVo;
import com.shuidihuzhu.common.web.util.DateUtil;
import com.shuidihuzhu.common.web.util.MoneyUtil;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageQueueSelector;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageQueue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CfContributeOrderService {

    @Autowired
    private CfContributeOrderDao contributeOrderDao;
    @Autowired
    private Producer producer;
    @Autowired
    private CfCombineOrderManageBiz cfCombineOrderManageBiz;


    public int addOrder(CfContributeOrder contributeOrder) {
        if (contributeOrder == null) {
            return 0;
        }

        return contributeOrderDao.addContributeOrder(contributeOrder);
    }

    public int updatePaySuccess(CfPayInnerCallBack callBack) {
        int fee = MoneyUtil.multiply("" + callBack.getAmountInFen(), "0.006", 0, RoundingMode.HALF_UP).intValue();
        return contributeOrderDao.updatePaySuccess(
                callBack.getOrderId(),
                callBack.getPayUid(),
                CfContributeOrder.OrderStatus.PAY_SUCCESS.getCode(),
                new Date(callBack.getBusinessTime().getTime()),
                callBack.getAmountInFen(),
                fee
        );
    }

    public void sendContributeActionMsg(String payUid, CfContributeMsgBody.OrderAction action) {
        if (StringUtils.isBlank(payUid) || action == null) {
            return;
        }

        // 发消息
        Message msg = new Message(MQTopicCons.CF, MQTagCons.CF_CONTRIBUTE_ORDER_TAG, payUid,
                CfContributeMsgBody.builder().payUid(payUid).action(action.getCode()).build());

        MessageResult result = producer.send(msg, new MessageQueueSelector() {
            @Override
            public MessageQueue select(List<MessageQueue> messageQueues, Message message) {
                int hashCode = ((CfContributeMsgBody) message.getPayload()).getPayUid().hashCode();
                return messageQueues.get(Math.abs(hashCode) % messageQueues.size());
            }
        });

        log.info("捐赠消息发送msg:{} result:{}", msg, result);
    }

    public CfContributeOrder selectByPayUid(String payUid) {
        if (StringUtils.isBlank(payUid)) {
            return null;
        }
        List<CfContributeOrder> list = selectByPayUids(Lists.newArrayList(payUid));

        return CollectionUtils.isNotEmpty(list) ? list.get(0) : null;
    }

    public List<CfContributeOrder> selectByPayUids(List<String> payUids) {
        if (CollectionUtils.isEmpty(payUids)) {
            return Lists.newArrayList();
        }

        return contributeOrderDao.selectByPayUids(payUids);
    }

    public CfContributeOrder selectByThirdPayUid(String thirdPayUid) {
        if (StringUtils.isBlank(thirdPayUid)) {
            return null;
        }

        List<CfContributeOrder> list = selectByThirdPayUids(Lists.newArrayList(thirdPayUid));

        return CollectionUtils.isNotEmpty(list) ? list.get(0) : null;
    }


    public List<CfContributeOrder> selectByThirdPayUids(List<String> thirdPayUids) {
        if (CollectionUtils.isEmpty(thirdPayUids)) {
            return Lists.newArrayList();
        }

        return contributeOrderDao.selectByThirdPayUids(thirdPayUids);
    }

    public List<CfContributeOrder> selectByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Lists.newArrayList();
        }

        return contributeOrderDao.selectByIds(ids);
    }

    public int updateOrderStatus(String payUid, int orderStatus) {

        return contributeOrderDao.updateOrderStatus(payUid, orderStatus);
    }

    public List<CfContributeOrder> selectByUserIdLimit(long userId, long anchorId, int limit) {
        return contributeOrderDao.selectByUserIdLimit(userId, anchorId, limit);
    }


    public List<CfContributeOrder> selectByUserIds(List<Long> userIds, List<Integer> statusList) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Lists.newArrayList();
        }

        List<CfContributeOrder> resultList = Lists.newArrayList();
        Lists.partition(userIds, 50).forEach(ids -> {
                    resultList.addAll(contributeOrderDao.selectByUserIds(ids, statusList));
                }
        );


        return resultList;
    }

    // 去掉案例为0的数据
    public List<CfContributeOrder> selectByCaseId(int caseId) {

        if (caseId == 0) {
            return Lists.newArrayList();
        }

        return contributeOrderDao.selectByCaseId(caseId);
    }


//    public static int getFee(long amountInFen) {
//        if (amountInFen <= 0) {
//            return 0;
//        }
//        return MoneyUtil.multiply("" + amountInFen, "0.006", 0, RoundingMode.HALF_UP).intValue();
//    }

    public NewPageVo<CfContributeOrder> getList(CfContributeOrderListParam param) {
        log.info("资助订单查询参数: {}", JSON.toJSONString(param));
        List<CfContributeOrder> result = new ArrayList<>();
        NewPageVo<CfContributeOrder> pageVo = NewPageVo.<CfContributeOrder>builder().pageList(result).build();
        if (Objects.isNull(param)) {
            return pageVo;
        }
        // 参数校验
        Date callbackStart = null;
        if (StringUtils.isNotBlank(param.getCallbackStartTime())) {
            callbackStart = DateUtil.getDateFromLongString(param.getCallbackStartTime());
        }
        Date callbackEnd = null;
        if (StringUtils.isNotBlank(param.getCallbackEndTime())) {
            callbackEnd = DateUtil.getDateFromLongString(param.getCallbackEndTime());
        }
        int caseId = Optional.ofNullable(param.getCaseId()).orElse(-1);
        if (caseId < -1) {
            return pageVo;
        }
        long donateOrderId = Optional.ofNullable(param.getDonateOrderId()).orElse(-1L);
        if (donateOrderId < -1) {
            return pageVo;
        }
        String payAmountStr = param.getPayAmountStr();
        long payAmount = StringUtils.isNotBlank(payAmountStr) ? MoneyUtil.changeYuanStrToFen(payAmountStr, RoundingMode.HALF_UP) : -1L;
        if (payAmount < -1) {
            return pageVo;
        }
        int contributeType = Optional.ofNullable(param.getContributeType()).orElse(-1);
        int orderStatus = Optional.ofNullable(param.getOrderStatus()).orElse(-1);
        String pageType = Optional.ofNullable(param.getPageType()).orElse(StringUtils.EMPTY);
        long id = Optional.ofNullable(param.getId()).orElse(0L);
        int pageSize = Optional.ofNullable(param.getPageSize()).orElse(10);
        // 资助来源
        CombineCommonEnum.CombineOrderType contributeSource = CombineCommonEnum.CombineOrderType.valueOfCode(contributeType);
        String sourceName = Optional.ofNullable(contributeSource).map(CombineCommonEnum.CombineOrderType::name).orElse("");
        List<CfContributeOrder> list = contributeOrderDao.getList(callbackStart, callbackEnd, sourceName, orderStatus, caseId, donateOrderId,
                payAmount, pageType, id, pageSize, param.getThirdPayUid());
        if (CollectionUtils.isNotEmpty(list)) {
            pageVo.setHasNext(list.size() >= pageSize);
            if ("previous".equals(pageType)) {
                list = list.stream().sorted(Comparator.comparingLong(r -> -r.getId())).collect(Collectors.toList());
                ;
            }
            pageVo.setHeadId(list.get(0).getId());
            pageVo.setTailId(list.get(list.size() - 1).getId());
        } else {
            pageVo.setHasNext(false);
        }
        pageVo.setPageList(list);
        return pageVo;
    }


    public List<CfContributeOrder> selectByCaseIdTimeRange(int caseId,
                                                           long payBeginMills,
                                                           long payEndMills) {

        if (caseId == 0 || payBeginMills >= payEndMills) {
            return null;
        }

        return contributeOrderDao.selectByCaseIdTimeRange(caseId,
                new Date(payBeginMills),
                new Date(payEndMills));
    }

    public CfContributeTotalInfo selectTotalInfoByCaseIdTimeRange(int caseId,
                                                                  long payBeginMills,
                                                                  long payEndMills) {

        if (caseId == 0 || payBeginMills >= payEndMills) {
            return null;
        }

        return contributeOrderDao.selectTotalInfoByCaseIdTimeRange(caseId,
                new Date(payBeginMills),
                new Date(payEndMills));
    }


    public List<CfContributeOrder> selectBySourceChannel(String sourceChannel, List<String> sourceIds) {

        if (StringUtils.isBlank(sourceChannel) || CollectionUtils.isEmpty(sourceIds)) {
            return Lists.newArrayList();
        }

        return contributeOrderDao.selectBySourceChannel(sourceChannel, sourceIds);
    }


    public int updateUserByIds(List<Long> ids, long toUserId) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }

        return contributeOrderDao.updateUserByIds(ids, toUserId);
    }

    public List<Long> selectContributeUserId(List<Long> excludeUserIds, int limit) {
        if (limit <= 0) {
            return Lists.newArrayList();
        }

        return contributeOrderDao.selectContributeUserId(excludeUserIds, CfContributeOrder.OrderStatus.PAY_SUCCESS.getCode(), limit);
    }

    public CfContributeOrder getByParentThirdPayUid(String parentThirdPayUid) {
        if (StringUtils.isBlank(parentThirdPayUid)) {
            return null;
        }
        CfCombineOrderManage cfCombineOrderManage = cfCombineOrderManageBiz.getByParentThirdPayUidAndOrderType(parentThirdPayUid, CombineCommonEnum.CombineOrderType.CONTRIBUTE);
        if (Objects.isNull(cfCombineOrderManage)) {
            return null;
        }
        String subPayUid = cfCombineOrderManage.getSubPayUid();
        List<CfContributeOrder> contributeOrders = contributeOrderDao.selectByPayUids(Lists.newArrayList(subPayUid));
        if (CollectionUtils.isEmpty(contributeOrders)) {
            return null;
        }
        return contributeOrders.get(0);
    }
}
