package com.shuidihuzhu.cf.service.crowdfunding.listener.afteradd;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.crowdfunding.event.AddListenerOrder;
import com.shuidihuzhu.cf.service.crowdfunding.event.CrowdFundingAddEvent;
import com.shuidihuzhu.cf.service.crowdfunding.listener.base.AbstractCrowdFundingAddEventListener;
import com.shuidihuzhu.cf.service.task.CfInfoTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.event.SmartApplicationListener;
import org.springframework.stereotype.Service;

/**
 * 扩展信息:创建筹款任务
 */
@Slf4j
@Service
public class CfInfoTaskListener extends AbstractCrowdFundingAddEventListener implements SmartApplicationListener {

    @Autowired
    private CfInfoTaskService cfInfoTaskService;

    @Override
    public void onApplicationEvent(ApplicationEvent _event) {

        CrowdFundingAddEvent crowdFundingAddEvent = (CrowdFundingAddEvent) _event;
        CrowdfundingInfo cfCase = crowdFundingAddEvent.getCrowdfundingInfo();
        if (cfCase != null) {
            try {
                cfInfoTaskService.buildAllTask(cfCase);
            } catch (Exception e) {
                log.error("CfInfoTaskListener error:", e);
            }
        }
    }


    @Override
    public int getOrder() {
        return AddListenerOrder.CfInfoTask.getValue();
    }
}
