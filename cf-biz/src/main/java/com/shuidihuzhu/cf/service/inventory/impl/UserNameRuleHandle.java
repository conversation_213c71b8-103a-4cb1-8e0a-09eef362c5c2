package com.shuidihuzhu.cf.service.inventory.impl;

import com.alibaba.fastjson.JSONObject;
import com.shuidihuzhu.cf.biz.crowdfunding.impl.CfFirstApproveBizImpl;
import com.shuidihuzhu.cf.biz.crowdfunding.impl.CrowdfundingAuthorBizImpl;
import com.shuidihuzhu.cf.biz.crowdfunding.impl.CrowdfundingInfoBizImpl;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.inventory.SecondInventoryDetail;
import com.shuidihuzhu.cf.service.inventory.InventoryRuleHandle;
import com.shuidihuzhu.cf.util.DesensitizationUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Description: 真实姓名
 * @Author: panghairui
 * @Date: 2023/1/12 10:39 上午
 */
@Slf4j
@Service("userNameRuleHandle")
public class UserNameRuleHandle implements InventoryRuleHandle {

    @Resource
    private CfFirstApproveBizImpl cfFirstApproveBiz;

    @Resource
    private CrowdfundingInfoBizImpl crowdfundingInfoBiz;

    @Resource
    private CrowdfundingAuthorBizImpl crowdfundingAuthorBiz;

    @Override
    public void handle(SecondInventoryDetail secondInventoryDetail, long time) {

        // 取该userId最近的案例
        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getLastByUserId(secondInventoryDetail.getUserId());
        if (Objects.isNull(crowdfundingInfo)) {
            return;
        }

        // 取材料审核中患者姓名
        CrowdfundingAuthor crowdfundingAuthor = crowdfundingAuthorBiz.get(crowdfundingInfo.getId());
        log.info("UserNameRuleHandle crowdfundingAuthor {}", JSONObject.toJSONString(crowdfundingAuthor));
        if (crowdfundingAuthor != null && crowdfundingInfo.getCreateTime().getTime() > time) {
            secondInventoryDetail.setContent(DesensitizationUtil.desensitizationRealName(crowdfundingAuthor.getName()));
            secondInventoryDetail.setSituation("已收集1条");
            return ;
        }

        // 取前置信息中患者姓名
        CfFirsApproveMaterial firstApproveInfo = cfFirstApproveBiz.getByInfoId(crowdfundingInfo.getId());
        log.info("UserNameRuleHandle firstApproveInfo {}", JSONObject.toJSONString(firstApproveInfo));
        if (firstApproveInfo != null && Objects.nonNull(crowdfundingInfo.getCreateTime()) && crowdfundingInfo.getCreateTime().getTime() > time) {
            secondInventoryDetail.setContent(DesensitizationUtil.desensitizationRealName(firstApproveInfo.getPatientRealName()));
            secondInventoryDetail.setSituation("已收集1条");
        }

    }

}
