package com.shuidihuzhu.cf.service.crowdfunding.listener;

import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.clinet.event.center.enums.BizParamEnum;
import com.shuidihuzhu.cf.clinet.event.center.enums.UserOperationTypeEnum;
import com.shuidihuzhu.cf.clinet.event.center.model.CfUserEvent;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.constants.crowdfunding.RaiseCons;
import com.shuidihuzhu.cf.mq.payload.SaveDraftPayload;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.cf.service.crowdfunding.event.SaveDraftEvent;
import com.shuidihuzhu.cf.service.crowdfunding.event.SaveDraftEventPublisher;
import com.shuidihuzhu.infra.starter.rocketmq.core.DelayLevel;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.event.SmartApplicationListener;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2019/12/16
 *
 * 保存草稿后续处理
 */

@Slf4j
@Service
public class SaveDraftEventListener implements SmartApplicationListener {

    @Autowired
    private Producer producer;

    @Autowired
    private ApplicationService applicationService;

    private static final String USER_EVENT_TAG = com.shuidihuzhu.cf.clinet.event.center.constants.MQTagCons.USER_EVENT_FOR_EVENT_CENTER;
    private static final String USER_EVENT_TOPIC = com.shuidihuzhu.cf.clinet.event.center.constants.MQTopicCons.CF;

    private static final Map<String, Integer> pageFlag2DelayMinutesFor3000 = Maps.newHashMap();
    private static final Map<String, Pair<Integer, String>> pageFlagInfoFor3000AppNew = Maps.newHashMap();

    private static final Map<String, Pair<Integer, String>> pageFlagInfoFor3000TomorrowAppNew = Maps.newHashMap();


    private static final Map<String, Integer> pageFlag2DelayMinutesFor3100 = Maps.newHashMap();

    private static final Map<String, Pair<Integer, String>> pageFlagInfoFor3100AppNew = Maps.newHashMap();

    private static final Map<String, Pair<Integer, String>> pageFlagInfoFor3100TomorrowAPPNew = Maps.newHashMap();

    static {
        pageFlag2DelayMinutesFor3000.put(RaiseCons.PageName.BASE_INFO, 15);
        pageFlag2DelayMinutesFor3000.put(RaiseCons.PageName.FIRST_APPROVE, 30);
        pageFlag2DelayMinutesFor3000.put(RaiseCons.PageName.FAMILY, 10);

        pageFlagInfoFor3000AppNew.put(RaiseCons.PageName.BASE_INFO, Pair.of(2273, "RKU1302"));
        pageFlagInfoFor3000AppNew.put(RaiseCons.PageName.FIRST_APPROVE, Pair.of(2274, "ATM2014"));
        pageFlagInfoFor3000AppNew.put(RaiseCons.PageName.FAMILY, Pair.of(2275, "HWV4366"));

        pageFlagInfoFor3000TomorrowAppNew.put(RaiseCons.PageName.BASE_INFO, Pair.of(2276, "KFV0583"));
        pageFlagInfoFor3000TomorrowAppNew.put(RaiseCons.PageName.FIRST_APPROVE, Pair.of(2277, "QQN2732"));
        pageFlagInfoFor3000TomorrowAppNew.put(RaiseCons.PageName.FAMILY, Pair.of(2278, "RVT7350"));

        pageFlag2DelayMinutesFor3100.put("basicInfo", 15);
        pageFlag2DelayMinutesFor3100.put("titleImage", 15);
        pageFlag2DelayMinutesFor3100.put("firstApprove", 30);

        pageFlagInfoFor3100AppNew.put("basicInfo", Pair.of(2551, "YKW4820"));
        pageFlagInfoFor3100AppNew.put("titleImage", Pair.of(2552, "XYO0632"));
        pageFlagInfoFor3100AppNew.put("firstApprove", Pair.of(2556, "VDV5425"));

        pageFlagInfoFor3100TomorrowAPPNew.put("basicInfo", Pair.of(2554, "HYC3066"));
        pageFlagInfoFor3100TomorrowAPPNew.put("titleImage", Pair.of(2555, "SAQ3022"));
        pageFlagInfoFor3100TomorrowAPPNew.put("firstApprove", Pair.of(2557, "IOE2777"));
    }

    @Override
    public boolean supportsSourceType(Class<?> sourceType) {
        return sourceType == SaveDraftEventPublisher.class;
    }

    @Override
    public boolean supportsEventType(Class<? extends ApplicationEvent> eventType) {
        return SaveDraftEvent.class.isAssignableFrom(eventType);
    }

    @Override
    public void onApplicationEvent(ApplicationEvent applicationEvent) {
        SaveDraftEvent saveDraftEvent = (SaveDraftEvent) applicationEvent;
        if (saveDraftEvent == null) {
            return;
        }

        String pageFlag = saveDraftEvent.getPageFlag();
        if (StringUtils.isBlank(pageFlag)) {
            return;
        }

        // 1.发事件中心mq
        sendMqForEventCenter(saveDraftEvent);

        // 2.发处理appPush mq

        // 2.1 延迟分钟级别
        sendDelayMqForAppPushNew(saveDraftEvent);

        // 2.2 延迟到第二天10点
        sendTomorrowMqForAppPushNew(saveDraftEvent);
    }

    private void sendMqForEventCenter(SaveDraftEvent event) {
        try {
            Map<String, String> dataMap = Maps.newHashMap();
            dataMap.put(BizParamEnum.DRAFT_PAGE_FLAG.getDesc(), event.getPageFlag());
            dataMap.put(BizParamEnum.CF_VERSION.getDesc(), String.valueOf(event.getCfVersion()));

            CfUserEvent cfUserEvent = new CfUserEvent();
            cfUserEvent.setUserId(event.getUserId());
            cfUserEvent.setType(UserOperationTypeEnum.SAVE_CASE_INITIATION_DRAFT.getCode());
            cfUserEvent.setDataMap(dataMap);

            String key = USER_EVENT_TAG + "_" + event.getUserId() + System.currentTimeMillis();
            producer.send(new Message<>(USER_EVENT_TOPIC, USER_EVENT_TAG, key, cfUserEvent));
        } catch (Exception e) {
           log.error("sendMqForEventCenter error. event:{}.", event, e);
        }
    }

    private void sendDelayMqForAppPushNew(SaveDraftEvent event) {

        try {
            Pair<Integer, String> subBizTypeAndTemplate = event.getCfVersion() >= 3100 ? pageFlagInfoFor3100AppNew.get(event.getPageFlag()) : pageFlagInfoFor3000AppNew.get(event.getPageFlag());
            if (subBizTypeAndTemplate == null) {
                return;
            }
            SaveDraftPayload payload = convert(event);
            payload.setSubBizType(subBizTypeAndTemplate.getLeft());
            payload.setTemplateId(subBizTypeAndTemplate.getRight());

            long todayDelayTime = getTodayDelayTime(event.getCfVersion(), event.getPageFlag());
            String delayKey = event.getUserId() + "-" + event.getPageFlag() + "-" + todayDelayTime;
            Message delayMsg = Message.ofSchedule(MQTopicCons.CF, MQTagCons.CF_SAVE_DRAFT_DELAY, delayKey, payload, todayDelayTime);
            MessageResult delayMsgResult = producer.send(delayMsg);
            log.info("sendDelayMqForSaveDraft payload:{}, delayMsgResult:{}", payload, delayMsgResult);
        } catch (Exception e) {
            log.error("sendDelayMqForAppPushNew error. event:{}.", event, e);
        }
    }

    private void sendTomorrowMqForAppPushNew(SaveDraftEvent event) {
        try {
            Pair<Integer, String> subBizTypeAndTemplate = event.getCfVersion() >= 3100 ? pageFlagInfoFor3100TomorrowAPPNew.get(event.getPageFlag()) : pageFlagInfoFor3000TomorrowAppNew.get(event.getPageFlag());
            if (subBizTypeAndTemplate == null) {
                return;
            }
            SaveDraftPayload payload = convert(event);
            payload.setSubBizType(subBizTypeAndTemplate.getLeft());
            payload.setTemplateId(subBizTypeAndTemplate.getRight());

            long tomorrowTime = getTomorrowTime();
            String tomorrowKey = event.getUserId() + "-" + event.getPageFlag() + "-" + tomorrowTime;
            Message tomorrowMsg = Message.ofSchedule(MQTopicCons.CF, MQTagCons.CF_SAVE_DRAFT_DELAY, tomorrowKey, payload, tomorrowTime);
            MessageResult tomorrowMsgResult = producer.send(tomorrowMsg);
            log.info("sendTomorrowMqForSaveDraft payload:{}, delayMsgResult:{}", payload, tomorrowMsgResult);
        } catch (Exception e) {
            log.error("sendTomorrowMqForAppPush error. event:{}.", event, e);
        }
    }

    private SaveDraftPayload convert(SaveDraftEvent event) {
        SaveDraftPayload payload = new SaveDraftPayload();
        payload.setUserId(event.getUserId());
        payload.setPageFlag(event.getPageFlag());
        return payload;
    }

    private long getTodayDelayTime(int cfVersion, String pageFlag) {
        if (applicationService.isDevelopment()) {
            return (System.currentTimeMillis() + TimeUnit.MINUTES.toMillis(2)) / 1000;
        }

        int minutes = cfVersion >= 3100 ? pageFlag2DelayMinutesFor3100.get(pageFlag) : pageFlag2DelayMinutesFor3000.get(pageFlag);
        return DateUtils.addMinutes(new Date(), minutes).getTime() / 1000;
    }

    private long getTomorrowTime() {

        if (applicationService.isDevelopment()) {
            return (System.currentTimeMillis() + TimeUnit.MINUTES.toMillis(5)) / 1000;
        }

        // 明天10点
        Date now = new Date();
        Date today = DateUtils.truncate(now, Calendar.DATE);
        Date tomorrow = DateUtils.addDays(today, 1);
        return DateUtils.addHours(tomorrow, 10).getTime() / 1000;
    }
}
