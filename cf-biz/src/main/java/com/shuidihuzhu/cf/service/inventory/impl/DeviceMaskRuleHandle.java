package com.shuidihuzhu.cf.service.inventory.impl;

import com.shuidihuzhu.cf.dao.inventory.CfInventoryDao;
import com.shuidihuzhu.cf.domain.UserDeviceInfo;
import com.shuidihuzhu.cf.model.inventory.SecondInventoryDetail;
import com.shuidihuzhu.cf.service.inventory.InventoryRuleHandle;
import com.shuidihuzhu.cf.util.DesensitizationUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Description: 脱敏设备信息
 * @Author: panghairui
 * @Date: 2023/1/13 2:14 下午
 */
@Slf4j
@Service("deviceMaskRuleHandle")
public class DeviceMaskRuleHandle implements InventoryRuleHandle {

    @Resource
    private CfInventoryDao cfInventoryDao;

    @Override
    public void handle(SecondInventoryDetail secondInventoryDetail, long time) {

        UserDeviceInfo userDeviceInfo = cfInventoryDao.getOneDeviceInfoByUserId(secondInventoryDetail.getUserId(), secondInventoryDetail.getIdentification());
        if (Objects.isNull(userDeviceInfo)) {
            return;
        }

        if (userDeviceInfo.getCreateTime().getTime() < time) {
            return;
        }

        secondInventoryDetail.setSituation("已收集1条");
        secondInventoryDetail.setContent(DesensitizationUtil.desensitizationDeviceInfo(userDeviceInfo.getContent()));

    }

}
