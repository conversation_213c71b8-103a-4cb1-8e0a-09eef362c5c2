package com.shuidihuzhu.cf.service.crowdfunding.order;

import com.shuidihuzhu.cf.dto.OrderSearchDto;
import com.shuidihuzhu.cf.vo.UserFriendOrderVo;

/**
 * @Author：liuchangjun
 * @Date：2021/7/15
 */
public interface CrowdfundingOrderService {


    UserFriendOrderVo getByFriendsUserId(OrderSearchDto orderSearchDto);

    UserFriendOrderVo getByVerificatioUserId(long cfId,String infoUuid,int size);
}
