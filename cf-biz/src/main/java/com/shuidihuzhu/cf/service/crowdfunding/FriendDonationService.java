package com.shuidihuzhu.cf.service.crowdfunding;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoSimpleBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingOrderBiz;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.FriendsBizTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfInfoSimpleModel;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.model.crowdfunding.vo.FriendDonationVo;
import com.shuidihuzhu.cf.service.crowdfunding.bigdata.DSFriendsService;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.dataservice.faceApi.v1.FaceApiClient;
import com.shuidihuzhu.client.model.Response;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.msg.enums.ErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/24  11:56
 */
@Service
@Slf4j
public class FriendDonationService {


    @Autowired
    private ShuidiCipher shuidiCipher;

    @Autowired
    private CrowdfundingOrderBiz crowdfundingOrderBiz;

    @Autowired
    private CrowdfundingInfoSimpleBiz crowdfundingInfoSimpleBiz;

    @Autowired
    private UserInfoDelegate userInfoDelegate;

    @Autowired
    private DSFriendsService dsFriendsService;

    @Autowired
    private FaceApiClient faceApiClient;

    @Resource(name = "redis-cf-api-new")
    private RedissonHandler cfRedissonHandler;

    public FriendDonationVo.FriendDonationMsg test1(long userId, int caseId, String userSourceId) {

        //1. 通过该好友链接进来的，若该好友已捐款，使用该好友信息显示

        //解密分享人userId
        long shareUserId = shuidiCipher.decryptUserId(userSourceId);

        //分享人和当前用户相同，不展示
        if (userId == shareUserId) {
            return null;
        }

        //获取缓存
        FriendDonationVo.FriendDonationMsg vo = cfRedissonHandler.get(getRedisKeyTest1(caseId, userId, shareUserId), FriendDonationVo.FriendDonationMsg.class);
        if (vo != null) {
            if (StringUtils.isEmpty(vo.getNickname())) {
                return null;
            }
            return vo;
        }

        List<CrowdfundingOrder> orders = crowdfundingOrderBiz.getByUserId(Sets.newHashSet(shareUserId), caseId);
        if (CollectionUtils.isNotEmpty(orders)) {
            UserInfoModel userInfoModel = getHasHeadImgUrlAndNicknameUser(shareUserId);
            if (Objects.isNull(userInfoModel)) {
                setRedissonTest1(new FriendDonationVo.FriendDonationMsg(), caseId, userId, shareUserId);
                return null;
            }

            int sum = orders.stream().mapToInt(CrowdfundingOrder::getAmount).sum();
            FriendDonationVo.FriendDonationMsg friendDonationMsg = new FriendDonationVo.FriendDonationMsg(userInfoModel.getNickname(), userInfoModel.getHeadImgUrl(), sum);
            setRedissonTest1(friendDonationMsg, caseId, userId, shareUserId);
            return friendDonationMsg;
        }

        //2. 通过该好友链接进来的，若该好友未捐款，则使用其他好友信息显示

        //- 获取有头像有昵称的一度好友的用户信息
        List<UserInfoModel> userInfoModelList = getHasHeadImgUrlAndNicknameUser(userId, 0);
        if (CollectionUtils.isEmpty(userInfoModelList)) {
            setRedissonTest1(new FriendDonationVo.FriendDonationMsg(), caseId, userId, shareUserId);
            return null;
        }

        //获取有头像有昵称的一度好友的捐款信息
        List<CrowdfundingOrder> orderList = getOrderList(caseId, userInfoModelList);
        if (CollectionUtils.isEmpty(orderList)) {
            setRedissonTest1(new FriendDonationVo.FriendDonationMsg(), caseId, userId, shareUserId);
            return null;
        }

        //低捐款金额画像
        boolean isLowDonation = isLowDonationAmountPortrait(userId);

        long userIdResult = 0;
        if (isLowDonation) {
            //- 非低捐款金额画像用户选择最近一次捐款时间的好友（该捐款人的一度好友）
            userIdResult = orderList.stream().sorted(Comparator.comparing(CrowdfundingOrder::getPayTime).reversed()).map(CrowdfundingOrder::getUserId).findFirst().get();

        } else {
            //- 非低捐款金额画像用户选择最高捐款金额的好友（该捐款人的一度好友）

            //获取一个orderList中金额总和最大的userId
            userIdResult = orderList.stream().collect(Collectors.groupingBy(CrowdfundingOrder::getUserId, Collectors.summingInt(CrowdfundingOrder::getAmount)))
                    .entrySet().stream().max(Comparator.comparing(Map.Entry::getValue)).get().getKey();

        }

        long finalUserIdResult = userIdResult;
        int sum = orderList.stream().filter(v -> v.getUserId() == finalUserIdResult).mapToInt(CrowdfundingOrder::getAmount).sum();

        UserInfoModel userInfoModel = userInfoModelList.stream().filter(v -> finalUserIdResult == v.getUserId()).findFirst().get();

        FriendDonationVo.FriendDonationMsg friendDonationMsg = new FriendDonationVo.FriendDonationMsg(userInfoModel.getNickname(), userInfoModel.getHeadImgUrl(), sum);
        setRedissonTest1(friendDonationMsg, caseId, userId, shareUserId);
        return friendDonationMsg;
    }

    public FriendDonationVo.FriendDonationMsg test2(long userId, int caseId, String userSourceId) {

        //解密分享人userId
        long shareUserId = shuidiCipher.decryptUserId(userSourceId);

        //分享人和当前用户相同，不展示
        if (userId == shareUserId) {
            return null;
        }

        //获取缓存
        FriendDonationVo.FriendDonationMsg vo = cfRedissonHandler.get(getRedisKeyTest2(caseId, shareUserId), FriendDonationVo.FriendDonationMsg.class);
        if (vo != null) {
            if (StringUtils.isEmpty(vo.getNickname())) {
                return null;
            }
            return vo;
        }

        List<CrowdfundingOrder> orders = crowdfundingOrderBiz.getByUserId(Sets.newHashSet(shareUserId), caseId);
        //通过该好友链接进来的，若该好友未捐款，则整个条目不显示，照常显示蓝条或其他（根据其他内容的优先级显示）
        if (CollectionUtils.isEmpty(orders)) {
            setRedissonTest2(new FriendDonationVo.FriendDonationMsg(), caseId, shareUserId);
            return null;
        }

        //通过该好友链接进来的，若该好友已捐款，使用该好友信息显示
        UserInfoModel userInfoModel = getHasHeadImgUrlAndNicknameUser(shareUserId);
        if (Objects.isNull(userInfoModel)) {
            setRedissonTest2(new FriendDonationVo.FriendDonationMsg(), caseId, shareUserId);
            return null;
        }

        int sum = orders.stream().mapToInt(CrowdfundingOrder::getAmount).sum();
        FriendDonationVo.FriendDonationMsg friendDonationMsg = new FriendDonationVo.FriendDonationMsg(userInfoModel.getNickname(), userInfoModel.getHeadImgUrl(), sum);
        setRedissonTest2(friendDonationMsg, caseId, shareUserId);
        return friendDonationMsg;

    }

    public FriendDonationVo test3(long userId, int caseId) {

        //  1. 多个用户头像，用户昵称，已有xx名好友捐献了xx笔
        //    1. 多个好友头像，排序按照捐款时间倒序，最多3个
        //    2. xx名好友：该捐款人1度好友里捐款人数（剔除完全退款）
        //    3. xx比：该捐款人1度好友里捐款的次数之和（剔除完全退款）

        //获取缓存
        FriendDonationVo friendDonationVo = cfRedissonHandler.get(getRedisKeyTest3(caseId, userId), FriendDonationVo.class);
        if (friendDonationVo != null) {
            if (friendDonationVo.getFriendCount() == 0L) {
                return null;
            }
            return friendDonationVo;
        }

        //- 获取有头像有昵称的一度好友的用户信息
        List<UserInfoModel> userInfoModelList = getHasHeadImgUrlAndNicknameUser(userId, 0);
        if (CollectionUtils.isEmpty(userInfoModelList)) {
            setRedissonTest3(new FriendDonationVo(), caseId, userId);
            return null;
        }

        //获取有头像有昵称的一度好友的捐款信息
        List<CrowdfundingOrder> orderList = getOrderList(caseId, userInfoModelList);
        if (CollectionUtils.isEmpty(orderList)) {
            setRedissonTest3(new FriendDonationVo(), caseId, userId);
            return null;
        }

        Map<Long, UserInfoModel> userInfoModelMap = userInfoModelList.stream().collect(Collectors.toMap(UserInfoModel::getUserId, Function.identity()));

        List<FriendDonationVo.FriendDonationMsg> friendDonationMsgList = orderList.stream().sorted(Comparator.comparing(CrowdfundingOrder::getPayTime).reversed())
                .map(CrowdfundingOrder::getUserId).distinct().limit(3).map(v -> {
                    UserInfoModel userInfoModel = userInfoModelMap.get(v);
                    return new FriendDonationVo.FriendDonationMsg(userInfoModel.getHeadImgUrl());
                }).collect(Collectors.toList());

        FriendDonationVo vo = new FriendDonationVo();
        vo.setFriendDonationMsgList(friendDonationMsgList);
        vo.setFriendCount(orderList.size());
        vo.setFriendCountDistinct(orderList.stream().map(CrowdfundingOrder::getUserId).distinct().count());

        setRedissonTest3(vo, caseId, userId);
        return vo;
    }

    //获取有头像有昵称的用户
    private UserInfoModel getHasHeadImgUrlAndNicknameUser(long userId) {
        UserInfoModel userInfoModel = userInfoDelegate.getUserInfoByUserId(userId);
        if (Objects.isNull(userInfoModel) || StringUtils.isAnyEmpty(userInfoModel.getHeadImgUrl(), userInfoModel.getNickname())) {
            return null;
        }
        //- 出现无法取到用户昵称和头像的场景下，则不将取不到的用户按照好友处理，进行剔除，剔除后优先展示可以取到昵称和头像的用户
        return userInfoModel;
    }

    //获取有头像有昵称的用户
    private List<UserInfoModel> getHasHeadImgUrlAndNicknameUser(long userId, long shareUserId) {

        //获取访问人的一度好友
        Set<Long> friendUserIdSet = dsFriendsService.getFriendsUserId(userId, 100, FriendsBizTypeEnum.FRIEND_ALL);
        if (CollectionUtils.isEmpty(friendUserIdSet)) {
            friendUserIdSet = Sets.newHashSet();
        }

        if (shareUserId > 0) {
            //将分享人userId加入到一度好友列表中
            friendUserIdSet.add(shareUserId);
        }

        List<UserInfoModel> userInfoModelList = userInfoDelegate.getUserInfoByUserIdBatch(Lists.newArrayList(friendUserIdSet));
        if (CollectionUtils.isEmpty(userInfoModelList)) {
            return null;
        }
        //- 出现无法取到用户昵称和头像的场景下，则不将取不到的用户按照好友处理，进行剔除，剔除后优先展示可以取到昵称和头像的用户
        return userInfoModelList.stream().filter(v -> StringUtils.isNotEmpty(v.getNickname()) && StringUtils.isNotEmpty(v.getHeadImgUrl())).collect(Collectors.toList());
    }

    private boolean isLowDonationAmountPortrait(long userId) {
        //获取访问人的用户累计捐款金额和用户平均捐款金额
        Response<Map<String, String>> response = faceApiClient.query(userId, Lists.newArrayList("cf_average_donate_amount", "cf_donate_amount"));
        Map<String, String> map = Optional.ofNullable(response).filter(v -> v.getCode() == ErrorCode.SUCCESS.getCode()).map(Response::getData).orElse(Maps.newHashMap());

        int cfDonateAmount = 0;
        int cfAverageDonateAmount = 0;
        try {
            //用户累计捐款金额
            cfDonateAmount = StringUtils.isEmpty(map.get("cf_donate_amount")) ? 0 : new BigDecimal(map.get("cf_donate_amount")).intValue();
            //用户平均捐款金额
            cfAverageDonateAmount = StringUtils.isEmpty(map.get("cf_average_donate_amount")) ? 0 : new BigDecimal(map.get("cf_average_donate_amount")).intValue();
        } catch (Exception e) {
            log.error("获取用户捐款金额异常", e);
        }

        //- 低捐款金额画像判断标准：累计筹款金额<20元或平均款金额<10元
        return cfDonateAmount < 20 || cfAverageDonateAmount < 10;
    }

    //获取捐款信息
    private List<CrowdfundingOrder> getOrderList(int caseId, List<UserInfoModel> userInfoModelList) {
        Set<Long> friendUserIds = userInfoModelList.stream().map(UserInfoModel::getUserId).collect(Collectors.toSet());

        return crowdfundingOrderBiz.getByUserId(friendUserIds, caseId);
    }


    private List<FriendDonationVo.FriendDonationMsg> buildFriendDonationMsgList(List<CrowdfundingOrder> orderList, Map<Long, UserInfoModel> userInfoModelMap) {
        return orderList.stream().map(
                v -> new FriendDonationVo.FriendDonationMsg(userInfoModelMap.get(v.getUserId()).getNickname(), userInfoModelMap.get(v.getUserId()).getHeadImgUrl(), v.getAmount())
        ).collect(Collectors.toList());
    }

    private void setRedisson(FriendDonationVo friendDonationVo, String infoUuid, long userId, long shareUserId) {
        try {
            String key = getRedisKey(infoUuid, userId, shareUserId);
            cfRedissonHandler.setEX(key, friendDonationVo, TimeUnit.MINUTES.toMillis(3));
        } catch (Exception e) {
            log.error("setRedisson", e);
        }
    }

    private String getRedisKey(String infoUuid, long userId, long shareUserId) {
        return "friend_donation_drive" + "-" + infoUuid + "-" + userId + "-" + shareUserId;
    }

    private void setRedissonTest1(FriendDonationVo.FriendDonationMsg friendDonationMsg, int caseId, long userId, long shareUserId) {
        try {
            String key = getRedisKeyTest1(caseId, userId, shareUserId);
            cfRedissonHandler.setEX(key, friendDonationMsg, TimeUnit.MINUTES.toMillis(3));
        } catch (Exception e) {
            log.error("setRedisson", e);
        }
    }

    private String getRedisKeyTest1(int caseId, long userId, long shareUserId) {
        return "get-info-test1" + "-" + caseId + "-" + userId + "-" + shareUserId;
    }


    private void setRedissonTest2(FriendDonationVo.FriendDonationMsg friendDonationMsg, int caseId, long shareUserId) {
        try {
            String key = getRedisKeyTest2(caseId, shareUserId);
            cfRedissonHandler.setEX(key, friendDonationMsg, TimeUnit.MINUTES.toMillis(3));
        } catch (Exception e) {
            log.error("setRedisson", e);
        }
    }

    private String getRedisKeyTest2(int caseId, long shareUserId) {
        return "get-info-test2" + "-" + caseId + "-" + shareUserId;
    }


    private void setRedissonTest3(FriendDonationVo friendDonationVo, int caseId, long userId) {
        try {
            String key = getRedisKeyTest3(caseId, userId);
            cfRedissonHandler.setEX(key, friendDonationVo, TimeUnit.MINUTES.toMillis(3));
        } catch (Exception e) {
            log.error("setRedisson", e);
        }
    }

    private String getRedisKeyTest3(int caseId, long userId) {
        return "get-info-test3" + "-" + caseId + "-" + userId;
    }

}
