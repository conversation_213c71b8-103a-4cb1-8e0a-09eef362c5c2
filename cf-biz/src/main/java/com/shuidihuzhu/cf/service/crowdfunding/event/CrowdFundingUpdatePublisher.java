package com.shuidihuzhu.cf.service.crowdfunding.event;

import com.shuidihuzhu.account.model.UserThirdModel;
import com.shuidihuzhu.cf.enums.crowdfunding.FirstApproveStatusEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CrowdfundingInfoBaseVo;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

/**
 * 新建筹款事件发布
 */
@Service
public class CrowdFundingUpdatePublisher {

    @Autowired
    ApplicationContext applicationContext;

    public void updateCrowdFunding(CrowdfundingInfoBaseVo crowdfundingInfoBaseVo,
                                   CrowdfundingInfo crowdfundingInfo, Message message) {
        // 1.构造一个事件
        CrowdFundingUpdateEvent event = new CrowdFundingUpdateEvent(
                this, crowdfundingInfoBaseVo, crowdfundingInfo,message);
        // 2.触发事件
        applicationContext.publishEvent(event);


    }
}
