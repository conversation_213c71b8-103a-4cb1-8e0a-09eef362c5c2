package com.shuidihuzhu.cf.service.crowdfunding;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.client.apipure.model.crowdfunding.CfCaseUpdateAmountRecordParam;
import com.shuidihuzhu.cf.dao.crowdfunding.ICfCaseUpdateAmountDAO;
import com.shuidihuzhu.cf.model.crowdfunding.CfCaseUpdateAmountRecord;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class CfCaseUpdateAmountRecordService {

    @Resource
    private ICfCaseUpdateAmountDAO cfCaseUpdateAmountDAO;

    @Async
    public void insert(int caseId,long userId, long targetAmount){
        CfCaseUpdateAmountRecord record = CfCaseUpdateAmountRecord.builder().caseId(caseId).targetAmount(targetAmount).userId(userId).build();
        cfCaseUpdateAmountDAO.insert(record);
    }

    @Async
    public void insert(CfCaseUpdateAmountRecord cfCaseUpdateAmountRecord){
        cfCaseUpdateAmountDAO.insert(cfCaseUpdateAmountRecord);
    }

    public Optional<CfCaseUpdateAmountRecord> getByCaseId(int caseId){
        CfCaseUpdateAmountRecord cfCaseUpdateAmountRecord = cfCaseUpdateAmountDAO.getLatestByCaseId(caseId);
        return Optional.ofNullable(cfCaseUpdateAmountRecord);
    }

    public List<CfCaseUpdateAmountRecordParam> getListByCaseId(int caseId){
        return cfCaseUpdateAmountDAO.getListByCaseId(caseId);
    }

    public List<CfCaseUpdateAmountRecordParam> getFixTargetAmountRecordByCaseIds(List<Integer> caseIds){
        if (CollectionUtils.isEmpty(caseIds)) {
            return Lists.newArrayList();
        }
        return cfCaseUpdateAmountDAO.getFixTargetAmountRecordByCaseIds(caseIds);
    }
}
