package com.shuidihuzhu.cf.service.crowdfunding.listener.addorupdate;

import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.service.crowdfunding.event.*;
import com.shuidihuzhu.cf.service.crowdfunding.listener.base.AbstractCrowdFundingAddOrUpdateEventListener;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.MessageResult;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.event.SmartApplicationListener;
import org.springframework.stereotype.Service;

/**
 * 新建案例 发送消息 用于重复二次案例的判断
 */
@Slf4j
@Service
public class CaseSendMQListener extends AbstractCrowdFundingAddOrUpdateEventListener implements SmartApplicationListener {

    @Autowired(required = false)
    private Producer producer;


    @Override
    public void onApplicationEvent(ApplicationEvent _event) {

        String event = "";
        CrowdfundingInfo crowdfundingInfo = null;
        Message message = null;
        if (_event.getSource() instanceof CrowdFundingAddPublisher) {
            event = "CrowdFundingAddEvent";
            CrowdFundingAddEvent crowdFundingAddEvent = (CrowdFundingAddEvent) _event;
            crowdfundingInfo = crowdFundingAddEvent.getCrowdfundingInfo();
            message = crowdFundingAddEvent.getMessage();
        } else {
            event = "CrowdFundingUpdateEvent";
            CrowdFundingUpdateEvent crowdFundingUpdateEvent = (CrowdFundingUpdateEvent) _event;
            crowdfundingInfo = crowdFundingUpdateEvent.getCrowdfundingInfo();
            message = crowdFundingUpdateEvent.getMessage();
        }
        if (crowdfundingInfo != null && message != null) {

            try {
                // 新建案例 发送消息 用于重复二次案例的判断
                if (producer != null) {
                    MessageResult messageResult = producer.send(message);
                    log.info("caseSendMQListener messageResult:{}",messageResult);
                }
            } catch (Exception e) {
                log.error("caseSendMQListener {} error:", event, e);
            }
        }

    }


    @Override
    public int getOrder() {
        return AddListenerOrder.CaseSendMQ.getValue();
    }
}