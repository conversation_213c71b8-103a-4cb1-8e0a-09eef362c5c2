package com.shuidihuzhu.cf.service.crowdfunding.dailysign;

import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.dailysign.DailySignBiz;
import com.shuidihuzhu.cf.biz.dailysign.DailySignScoreRecordBiz;
import com.shuidihuzhu.cf.constants.MQTagCons;
import com.shuidihuzhu.cf.constants.MQTopicCons;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.enums.crowdfunding.BonusPointAction;
import com.shuidihuzhu.cf.model.dailysign.DailySign;
import com.shuidihuzhu.cf.model.dailysign.DailySignScoreRecord;
import com.shuidihuzhu.cf.model.dailysign.DailySignUserStat;
import com.shuidihuzhu.cf.model.dailysign.vo.DailySignRecordVO;
import com.shuidihuzhu.cf.model.dailysign.vo.DailySignUserStatVo;
import com.shuidihuzhu.cf.model.dailysign.vo.DailySignVo;
import com.shuidihuzhu.client.common.grpc.BizType;
import com.shuidihuzhu.client.feign.UserPointsFeignClient;
import com.shuidihuzhu.client.model.userpoints.UserPointsResultVO;
import com.shuidihuzhu.client.model.userpoints.UserPointsVO;
import com.shuidihuzhu.client.model.userpoints.req.UserPointReq;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.infra.starter.rocketmq.core.Message;
import com.shuidihuzhu.infra.starter.rocketmq.core.Producer;
import lombok.extern.slf4j.Slf4j;
import net.devh.springboot.autoconfigure.grpc.client.GrpcStub;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by wangsf on 18/4/27.
 */
@Service
@Slf4j
@RefreshScope
public class DailySignService {

	@Autowired
	private DailySignBiz dailySignBiz;

	@Autowired
	private DailySignScoreRecordBiz dailySignScoreRecordBiz;

	@Autowired(required = false)
	private Producer producer;

	@Value("${activity.dailysign.points:0}")
	private int dailySignPoints;

	@Autowired
	private UserInfoDelegate userInfoDelegate;

	@Resource
	private UserPointsFeignClient userPointsFeignClient;

	/**
	 * 打卡，返回此次打卡获得的积分
	 * @param userId
	 * @return -1 则表示已经打卡; -2 表示插入记录失败
	 */
	public int signin(long userId, String channel, int userThirdType) {
		log.info("signin|{}|{}", userId, this.dailySignPoints);

		String dayKey = DateFormatUtils.format(Calendar.getInstance().getTime(), "yyyyMMdd");

		DailySign dailySign = this.dailySignBiz.getByUserIdAndDayKey(userId, dayKey);
		//如果已经打卡，则不再打
		if(dailySign != null) {
			return -1;
		}

		//记录打卡信息
		dailySign = new DailySign();
		dailySign.setUserId(userId);
		dailySign.setDayKey(dayKey);
		dailySign.setPoints(this.dailySignPoints);
		int count = this.dailySignBiz.insertUpdate(dailySign);

		//如果插入失败，则返回-1
		if(count == 0) {
			return -2;
		}

		//增加用户的打卡次数和得分
		if(count > 0) {
			//TODO: 需要保证一致性(当前抛异常没有处理)
			this.dailySignBiz.updateStat(userId, this.dailySignPoints);
		}

		//发送一个打卡成功消息，去记录爱心值
		DailySignScoreRecord record = new DailySignScoreRecord();
		record.setSignId(dailySign.getId());
		record.setUserId(dailySign.getUserId());
		record.setUserThirdType(userThirdType);
		record.setChannel(channel);
		String outTradeNo = UUID.randomUUID().toString().replaceAll("-", "");
		record.setOutTradeNo(outTradeNo);
		record.setTradeNo("");
		record.setAction(BonusPointAction.DAILY_SIGN.getCode());
		record.setRemark("签到 " + DateFormatUtils.format(Calendar.getInstance().getTime(), "yyyy-MM-dd"));
		record.setPoints(BonusPointAction.DAILY_SIGN.getScore());

		producer.send(new Message(MQTopicCons.CF, MQTagCons.CF_DAILY_SIGNIN_SUCCESS,
					MQTagCons.CF_DAILY_SIGNIN_SUCCESS + "_" + record.getId(), record));

		//返回分值
		return this.dailySignPoints;
	}

	/**
	 * 当天是否已经签到
	 * @param userId
	 * @return
	 */
	public boolean hasSignedIn(long userId) {
		String dayKey = DateFormatUtils.format(Calendar.getInstance().getTime(), "yyyyMMdd");
		DailySign dailySign = this.dailySignBiz.getByUserIdAndDayKey(userId, dayKey);
		return dailySign != null;
	}

	/**
	 * 分页获取签到记录
	 * @param userId
	 * @param anchorId
	 * @param limit
	 * @return
	 */
	public DailySignRecordVO getListByAnchorIdAndLimit(long userId, long anchorId, int limit) {

		List<DailySign> dailySigns = this.dailySignBiz.getByUserIdAndAnchorId(userId, anchorId, limit + 1);

		DailySignRecordVO result = new DailySignRecordVO();
		if(CollectionUtils.isEmpty(dailySigns)) {
			result.setAnchorId(0L);
			result.setHasMore(false);
			result.setHistory(new ArrayList<>());
		}

		boolean hasMore = false;
		long nextAnchorId = 0;
		if(dailySigns.size() > limit) {
			nextAnchorId = dailySigns.remove(limit).getId();
			hasMore = true;
		}

		List<DailySignVo> dailySignVos = dailySigns.stream()
				.map(dailySign -> new DailySignVo(dailySign))
				.collect(Collectors.toList());


		result.setAnchorId(nextAnchorId);
		result.setHasMore(hasMore);
		result.setHistory(dailySignVos);

		return result;
	}

	/**
	 * 获取用户截止当前的积分和签到情况
	 *
	 * @return
	 */
	public DailySignUserStatVo getByUserId(long userId) {

		UserInfoModel userInfoModel = this.userInfoDelegate.getUserInfoByUserId(userId);
		if(userInfoModel == null) {
			return null;
		}

		DailySignUserStat stat = this.dailySignBiz.getByUserId(userId);

		DailySignUserStatVo dailySignUserStatVo = new DailySignUserStatVo();
		if(stat != null) {
			dailySignUserStatVo.setSignCount(stat.getSignCount());
		} else {
			dailySignUserStatVo.setSignCount(0);
		}

		Response<UserPointsVO> response = this.userPointsFeignClient.getUserPointsStat(userId);
		UserPointsVO getUserPointsStatVO = response.getData();
		if (getUserPointsStatVO != null) {
			dailySignUserStatVo.setPoints(getUserPointsStatVO.getTotalPoints());
		}
		dailySignUserStatVo.setHeadImgUrl(userInfoModel.getHeadImgUrl());
		dailySignUserStatVo.setNickname(userInfoModel.getNickname());
		return dailySignUserStatVo;
	}

	/**
	 * 增加积分
	 * @param dailySignScoreRecord
	 * @return
	 */
	public int increasePoints(DailySignScoreRecord dailySignScoreRecord) {

		DailySignScoreRecord record = this.dailySignScoreRecordBiz.getBySignId(dailySignScoreRecord.getSignId());
		if(record == null) {
			int count = this.dailySignScoreRecordBiz.insert(dailySignScoreRecord);

			if(count <= 0) {
				return 0;
			}

			record = dailySignScoreRecord;
		}

		if(record.isConfirmed()) {
			return record.getPoints();
		}
		final DailySignScoreRecord tmp = record;
		//grpc 增加积分
		UserPointReq bonusPointsReq = UserPointReq
				.builder()
				.action(record.getAction())
				.bizType(BizType.CF.getNumber())
				.channel(record.getChannel())
				.userThirdType(record.getUserThirdType())
				.outTradeNo(record.getOutTradeNo() == null ? "" : record.getOutTradeNo())
				.points(record.getPoints())
				.remark(StringUtils.isEmpty(record.getRemark()) ? "" : record.getRemark())
				.userId(record.getUserId())
				.build();

		try {
			Response<UserPointsResultVO> response = userPointsFeignClient.bounsPoints(bonusPointsReq);
			return Optional.of(response)
							.filter(Response::ok)
							.filter(r -> r.getData() != null)
							.map(r -> {
								UserPointsResultVO bonusPointsVO = r.getData();
								if(bonusPointsVO.getPoints() == tmp.getPoints()) {
									//更新状态
									String tradeNo = bonusPointsVO.getTradeNo();
									this.dailySignScoreRecordBiz.updateConfirm(tmp.getId(), tradeNo == null ? "" : tradeNo);
								}
								return bonusPointsVO.getPoints();
							}).orElse(0);

		} catch (Exception e) {
			log.error("", e);
		}
		return 0;
	}
}
