package com.shuidihuzhu.cf.service.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.biz.crowdfunding.CfBaseInfoTemplatizeBiz;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.crowdfunding.AuthorFamilyMemberEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.AuthorOccupationEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.BaseInfoTemplateConst;
import com.shuidihuzhu.cf.enums.crowdfunding.TemplatizeBaseInfoRelationship;
import com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplatize;
import com.shuidihuzhu.cf.model.crowdfunding.CfTemplateBaseInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CfTemplateBaseInfoConvert;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfBaseInfoTemplateVo;
import com.shuidihuzhu.cf.model.questionnaire.RaiseQuestionnaire;
import com.shuidihuzhu.cf.service.ApplicationService;
import com.shuidihuzhu.cf.util.crowdfunding.EmojiUtil;
import com.shuidihuzhu.cf.vo.questionnaire.TemplateTitleAndContentVo;
import com.shuidihuzhu.cf.vo.templatize.RaisingHelpInstructionVo;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.common.web.util.NewResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by Ahrievil on 2017/12/8
 */
@Slf4j
@Service
public class CfBaseInfoTemplateService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CfBaseInfoTemplateService.class);
    private static final String REDIS_KEY = "cf-base-info-templatize-%d";
    @Autowired
    private CfBaseInfoTemplatizeBiz cfBaseInfoTemplatizeBiz;
    @Autowired
    private CfContentPlaceHolderHandleService cfContentPlaceHolderHandleService;
    @Autowired
    private AiSensWordsFilter aiSensWordsFilterService;

    @Resource(name = "cfRedissonHandler")
    private RedissonHandler redissonHandler;

    @Autowired
    private ApplicationService applicationService;

    private int limit = 500;
    private List<CfBaseInfoTemplatize> getAllListByUseScene(int useScene) {

        return cfBaseInfoTemplatizeBiz.getAllBaseByUseScene(useScene);
    }

    private List<CfBaseInfoTemplatize> setCache(int useScene) {
        LOGGER.info("CfBaseInfoTemplateService setCache");
        List<CfBaseInfoTemplatize> allList = this.getAllListByUseScene(useScene);
        try {
            long leaseTimeSeconds = 10 * 60L;
            if (!applicationService.isProduction()) {
                leaseTimeSeconds = 60L;
            }
            redissonHandler.addListEX(getRedisKey(useScene), allList, leaseTimeSeconds);
        } catch (Exception e) {
            LOGGER.error("", e);
        }
        return allList;
    }

    private String getRedisKey(int useScene) {
        return String.format(REDIS_KEY, useScene);
    }

    public Map<Integer, Map<Integer, List<CfBaseInfoTemplatize>>> getMap(int useScene) {

        List<CfBaseInfoTemplatize> list = redissonHandler.getList(getRedisKey(useScene), CfBaseInfoTemplatize.class);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream().collect(Collectors.groupingBy(CfBaseInfoTemplatize::getRelationType,
                    Collectors.groupingBy(CfBaseInfoTemplatize::getContentType)));
        } else {
            LOGGER.info("CfBaseInfoTemplateService setCache");
            List<CfBaseInfoTemplatize> cfBaseInfoTemplatizeList = setCache(useScene);
            return cfBaseInfoTemplatizeList.stream().collect(Collectors.groupingBy(CfBaseInfoTemplatize::getRelationType,
                    Collectors.groupingBy(CfBaseInfoTemplatize::getContentType)));
        }
    }

    public List<CfBaseInfoTemplatize> getContents(List<CfBaseInfoTemplatize> contentList, CfTemplateBaseInfo cfTemplateBaseInfo) {
        return cfContentPlaceHolderHandleService.getContents(contentList, cfTemplateBaseInfo);
    }

    public List<CfBaseInfoTemplatize> getContents4Section(List<CfBaseInfoTemplatize> contentList, CfTemplateBaseInfo cfTemplateBaseInfo){
        return cfContentPlaceHolderHandleService.getContents4Section(contentList, cfTemplateBaseInfo);
    }

    public Response<TemplateTitleAndContentVo> getTemplateBaseInfo(CfTemplateBaseInfoConvert baseInfoConvert) {
        if (null == baseInfoConvert) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        TemplateTitleAndContentVo vo=new TemplateTitleAndContentVo();
        String family = StringUtils.isNotEmpty(baseInfoConvert.getFamilyMember()) ? baseInfoConvert.getFamilyMember() : String.valueOf(0);
        List<Integer> familyMember = Arrays.stream(family.split(",")).map(Integer::valueOf).collect(Collectors.toList());
        int authorOccupation = baseInfoConvert.getAuthorOccupation();
        String medicalArrears = baseInfoConvert.getMedicalArrears() > 0 ? String.valueOf(Math.round(baseInfoConvert.getMedicalArrears())) : StringUtils.EMPTY;

        Map<String, String> familyMemberMap = Maps.newConcurrentMap();
        for (Integer member : familyMember){
            for (AuthorFamilyMemberEnum memberEnum : AuthorFamilyMemberEnum.values()){
                if(member == memberEnum.getCode()){
                    familyMemberMap.put(memberEnum.getEnglish(), memberEnum.getChinese());
                }
            }
        }

        Map<String, String> authorOccupationMap = Maps.newConcurrentMap();
        for (AuthorOccupationEnum occupationEnum : AuthorOccupationEnum.values()){
            if(authorOccupation == occupationEnum.getCode()){
                authorOccupationMap.put(occupationEnum.getEnglish(), occupationEnum.getChinese());
            }
        }

        CfTemplateBaseInfo cfTemplateBaseInfo = CfTemplateBaseInfoConvert.convertToBaseInfo(baseInfoConvert);
        cfTemplateBaseInfo.setFamilyMemberMap(familyMemberMap);
        cfTemplateBaseInfo.setAuthorOccupationMap(authorOccupationMap);
        cfTemplateBaseInfo.setMedicalArrears(medicalArrears);

        int relationship = cfTemplateBaseInfo.getRelationship();
        String name = cfTemplateBaseInfo.getName();
        String diseaseName = cfTemplateBaseInfo.getDiseaseName();
        String hospitalName = cfTemplateBaseInfo.getHospitalName();
        String authorName = cfTemplateBaseInfo.getAuthorName();

        if (relationship == 0 || StringUtils.isBlank(diseaseName)) {
            return NewResponseUtil.makeError(CfErrorCode.SYSTEM_PARAM_ERROR);
        }

        if (containEmoji(name) || containEmoji(hospitalName) || containEmoji(authorName)) {
            return NewResponseUtil.makeError(CfErrorCode.PROMOTION_NO_EMOJI_ALLOWED);
        }

        if (baseInfoConvert.getSource() == RaiseQuestionnaire.source_0
            && StringUtils.isNotBlank(authorName) && StringUtils.isNotBlank(diseaseName)){

            // 敏感词过滤 diseaseName   authorName
            AiSensWordsFilter.AiFilterResponse filterResponse = aiSensWordsFilterService
                    .patientAndDiseaseNameFilter(authorName, diseaseName);
            if (filterResponse.getCode() != AiSensWordsFilter.SUCCESS) {
                log.info("用户智能发起被过滤  authorName:{} diseaseName:{} errMsg:{}" , authorName, diseaseName, filterResponse.getMsg());
                return NewResponseUtil.makeResponse(filterResponse.getCode(), filterResponse.getMsg(), null);
            }

        }

        Map<Integer, List<CfBaseInfoTemplatize>> contentTypeMap = getRelationTemplatize(baseInfoConvert.getUseScene(), relationship);
        log.debug("getRelationTemplatize result is useScene:{}, relationShip:{}, contentTypeMap:{}", baseInfoConvert.getUseScene(), relationship, JSON.toJSONString(contentTypeMap));

        // 选择了模板只生成对应模版的文章
        boolean isSelectTemplate;
        isSelectTemplate = baseInfoConvert.getTemplateId() != null && baseInfoConvert.getTemplateId() > 0;
        if (isSelectTemplate) {
            List<CfBaseInfoTemplatize> contentTemplateList = contentTypeMap.get(BaseInfoTemplateConst.ContentType.CONTENT.getCode());
            contentTemplateList = contentTemplateList.stream().filter(item -> item.getId() == baseInfoConvert.getTemplateId()).collect(Collectors.toList());
            contentTypeMap.put(BaseInfoTemplateConst.ContentType.CONTENT.getCode(), contentTemplateList);
        }

        boolean finalIsSelectTemplate = isSelectTemplate;
        contentTypeMap.forEach((key, list) -> {
            // 未主动选择模版时，匹配年龄限制
            if (!finalIsSelectTemplate) {
                list = filterAgeLimit(list, cfTemplateBaseInfo);
                log.debug("filterAgeLimit list:{}", JSON.toJSONString(list));
            }

            // 未主动选择模版时且使用位置是1v1时，文章类型匹配疾病限制
            if (!finalIsSelectTemplate && baseInfoConvert.getUseScene() == 1
                    && key == BaseInfoTemplateConst.ContentType.CONTENT.getCode()) {
                List<CfBaseInfoTemplatize> diseaseLimit = filterDiseaseLimit(list, cfTemplateBaseInfo);
                list.addAll(diseaseLimit);
                log.debug("filterDiseaseLimit list:{}", JSON.toJSONString(list));
            }

            //单独处理含段落的内容
            list = getContents4Section(list, cfTemplateBaseInfo);
            //生成对应的title和内容
            List<CfBaseInfoTemplatize> result = getContents(list, cfTemplateBaseInfo);
            Collections.shuffle(result);

            if (key == BaseInfoTemplateConst.ContentType.TITLE.getCode()) {
                vo.setTitles(result.stream().filter(val -> val.getContent().length() <= 25)
                        .map(val -> new CfBaseInfoTemplateVo(val.getId(), val.getContent()))
                        .collect(Collectors.toList()));
            } else if (key == BaseInfoTemplateConst.ContentType.CONTENT.getCode()) {
                vo.setContents(result.stream().map(val -> new CfBaseInfoTemplateVo(val.getId(), val.getContent()))
                        .collect(Collectors.toList()));
            }
        });
        return NewResponseUtil.makeSuccess(vo);
    }

    private List<CfBaseInfoTemplatize> filterDiseaseLimit(List<CfBaseInfoTemplatize> templateList, CfTemplateBaseInfo cfTemplateBaseInfo) {
        if (StringUtils.isNotBlank(cfTemplateBaseInfo.getDiseaseName())) {
            List<CfBaseInfoTemplatize> diseaseTemplateList = templateList.stream()
                    .filter(item -> item.getApplicableDiseases().contains(cfTemplateBaseInfo.getDiseaseName())
                            || StringUtils.isBlank(item.getApplicableDiseases()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(diseaseTemplateList)) {
                return templateList;
            }
            return diseaseTemplateList;
        }
        return templateList;
    }

    private List<CfBaseInfoTemplatize> filterAgeLimit(List<CfBaseInfoTemplatize> templatizes,
                                                  CfTemplateBaseInfo cfTemplateBaseInfo) {

        List<CfBaseInfoTemplatize> ageTemplateList = Lists.newArrayList();
        if (cfTemplateBaseInfo.getAge() != null) {
            ageTemplateList = templatizes.stream()
                    .filter(cfBaseInfoTemplatize -> (cfBaseInfoTemplatize.getMinAge() <= cfTemplateBaseInfo.getAge() && cfTemplateBaseInfo.getAge() <= cfBaseInfoTemplatize.getMaxAge()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(ageTemplateList)) {
                return ageTemplateList;
            }
            // 未匹配到年龄限制的模板时，返回所有模板
            return templatizes;
        }

        BaseInfoTemplateConst.CfBaseInfoRelationshipEnum relation = BaseInfoTemplateConst.CfBaseInfoRelationshipEnum
                .getByCode(cfTemplateBaseInfo.getRelationship());

        int minAge = 0, maxAge = 0;
        switch (relation) {
            case SELF:
            case SON:
            case DAUGHTER:
                minAge = 0;
                maxAge = 1000;
                break;
            case HUSBAND:
            case WIFE:
            case FATHER:
                minAge = 18;
                maxAge = 10000;
                break;
            case MATHER:
                minAge = 20;
                maxAge = 10000;
                break;
            default:
                minAge = 0;
                maxAge = 10000;
                break;
        }

        Integer finalMinAge = minAge;
        Integer finalMaxAge = maxAge;
        return templatizes.stream().filter(
                cfBaseInfoTemplatize -> (finalMinAge.equals(cfBaseInfoTemplatize.getMinAge()) && finalMaxAge.equals(cfBaseInfoTemplatize.getMaxAge())))
                .collect(Collectors.toList());
    }



    private Map<Integer, List<CfBaseInfoTemplatize>> getRelationTemplatize(int useScene, int relationship) {

        BaseInfoTemplateConst.CfBaseInfoRelationshipEnum templatizeEnum = BaseInfoTemplateConst.CfBaseInfoRelationshipEnum
                .getByCode(relationship);
        Map<Integer, Map<Integer, List<CfBaseInfoTemplatize>>> map = getMap(useScene);
        Map<Integer, List<CfBaseInfoTemplatize>> contentTypeMap = map.get(templatizeEnum.getCode());

        if (MapUtils.isNotEmpty(contentTypeMap)) {
            return contentTypeMap;
        }

        // 如果不能找到RELATIONS 就用OTHER
        if (templatizeEnum == BaseInfoTemplateConst.CfBaseInfoRelationshipEnum.RELATIONS) {
            return map.get(BaseInfoTemplateConst.CfBaseInfoRelationshipEnum.OTHER.getCode());
        }

        return contentTypeMap;
    }


    private boolean containEmoji(String str) {
        return EmojiUtil.containsEmoji(str);
    }

    public static void main(String[] args) {
        String test = "iloveyou";
        List<String> x = Splitter.on("x").splitToList(test);
        x.forEach(System.out::println);
        System.out.println(x.size());
        System.out.println(test.length());
        System.out.println(test.lastIndexOf("u"));
        System.out.println(test.substring(0, 7));
        System.out.println(test.indexOf("i"));
        List<String> handleList = Lists.newArrayList("nihao", "hello", "fuck");
        String s = handleList.get(2);
        s = "heheda";
        handleList.set(2, s);
        handleList.forEach(System.out::println);
    }

    public RaisingHelpInstructionVo getRaisingHelpInstruction(TemplatizeBaseInfoRelationship templatizeBaseInfoRelationship) {
        List<CfBaseInfoTemplatize> baseInfoTemplates = cfBaseInfoTemplatizeBiz.selectByRelationAndContentType(templatizeBaseInfoRelationship.getCode(), 2, 0, 100);
        baseInfoTemplates = baseInfoTemplates.stream().filter(item -> 1 == item.getChannelType()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(baseInfoTemplates)) {
            return new RaisingHelpInstructionVo();
        }
        Collections.shuffle(baseInfoTemplates);
        String replace = "XXX";
        for (CfBaseInfoTemplatize cfBaseInfoTemplatize : baseInfoTemplates) {
            String text = cfBaseInfoTemplatize.getContent();
            List<String> contents = Lists.newArrayList(text.split("\\$\\$\\$"));
            for (String sectionPlaceHolder : BaseInfoTemplateConst.getSectionPlaceHolder()) {
                List<String> newContents = cfContentPlaceHolderHandleService.handleContent4Section(sectionPlaceHolder, contents, "");
                text = StringUtils.join(newContents, "");
            }
            for (String placeHolder : BaseInfoTemplateConst.getPlaceHolder()) {
                text = cfContentPlaceHolderHandleService.handleContent(placeHolder, text, replace);
            }
            String[] splitResult = text.split("\n", 4);
            if (splitResult.length == 4) {
                RaisingHelpInstructionVo raisingHelpInstructionVo = new RaisingHelpInstructionVo();
                raisingHelpInstructionVo.setId(cfBaseInfoTemplatize.getId());
                raisingHelpInstructionVo.setPersonalInfo(splitResult[0]);
                raisingHelpInstructionVo.setPatientInfo(splitResult[1]);
                raisingHelpInstructionVo.setFamilyInfo(splitResult[2]);
                raisingHelpInstructionVo.setServivalHope(splitResult[3]);
                return raisingHelpInstructionVo;
            }
        }
        return new RaisingHelpInstructionVo();
    }
}
