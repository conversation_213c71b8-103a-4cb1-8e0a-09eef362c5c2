package com.shuidihuzhu.cf.service.visitconfig;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.constants.AdminUserIDConstants;
import com.shuidihuzhu.cf.dao.crowdfunding.visitconfig.VisitConfigLogDAO;
import com.shuidihuzhu.cf.domain.visitconfig.VisitConfigLogDO;
import com.shuidihuzhu.cf.enums.CfErrorCode;
import com.shuidihuzhu.cf.enums.visitconfig.VisitConfigLogActionInfoEnum;
import com.shuidihuzhu.cf.enums.visitconfig.VisitConfigLogActionTypeEnum;
import com.shuidihuzhu.cf.enums.visitconfig.VisitConfigSourceEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.response.OpResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018-09-19  20:53
 */
@Slf4j
@Service
public class VisitConfigLogServiceImpl implements VisitConfigLogService {

    @Resource
    private VisitConfigLogDAO visitConfigLogDAO;

    @Resource
    private CrowdfundingInfoBiz crowdfundingInfoBiz;

    @Override
    public OpResult save(int caseId, List<VisitConfigLogActionInfoEnum> actionInfoEnumList, int operatorId) {
        return pin(caseId, null, actionInfoEnumList, operatorId);
    }

    @Override
    public OpResult save(int caseId, VisitConfigLogActionInfoEnum actionInfoEnum, int operatorId) {
        return save(caseId, Lists.newArrayList(actionInfoEnum), operatorId);
    }

    @Override
    public OpResult save(int caseId, List<VisitConfigLogActionInfoEnum> actionInfoEnumList) {
        return save(caseId, actionInfoEnumList, 0);
    }

    @Override
    public OpResult save(int caseId, VisitConfigLogActionInfoEnum actionInfoEnum) {
        return save(caseId, Lists.newArrayList(actionInfoEnum), 0);
    }

    @Override
    public OpResult pin(int caseId,
                        VisitConfigSourceEnum sourceEnum,
                        List<VisitConfigLogActionInfoEnum> actionInfoEnumList,
                        int operatorId) {
        log.info("save {}, {}, {}", caseId, actionInfoEnumList, operatorId);
        if (CollectionUtils.isEmpty(actionInfoEnumList)) {
            return OpResult.createFailResult(CfErrorCode.SYSTEM_PARAM_ERROR);
        }
        if (sourceEnum == null) {
            sourceEnum = VisitConfigSourceEnum.DEFAULT;
        }

        CrowdfundingInfo fundingInfo = crowdfundingInfoBiz.getFundingInfoById(caseId);
        String infoUuid = fundingInfo.getInfoId();

        int actionType = getActionType(operatorId).getValue();
        String actionInfo = actionInfoEnumList.stream()
                .map(VisitConfigLogActionInfoEnum::getValue)
                .collect(Collectors.joining(","));

        VisitConfigLogDO v = new VisitConfigLogDO();
        v.setInfoUuid(infoUuid);
        v.setActionType(actionType);
        v.setActionInfo(actionInfo);
        v.setSource(sourceEnum.getValue());
        v.setOperatorId(operatorId);
        visitConfigLogDAO.insert(v);
        return OpResult.createSucResult();
    }

    private VisitConfigLogActionTypeEnum getActionType(int operatorId) {
        if (operatorId == AdminUserIDConstants.SYSTEM) {
            return VisitConfigLogActionTypeEnum.SYSTEM;
        }
        return operatorId > 0 ?
                VisitConfigLogActionTypeEnum.OPERATOR :
                VisitConfigLogActionTypeEnum.SYSTEM;
    }

    @Override
    public List<VisitConfigLogDO> listOperatorLog(String infoUuid) {
        return visitConfigLogDAO
                .listByInfoUuidAndActionType(infoUuid, VisitConfigLogActionTypeEnum.OPERATOR.getValue());
    }

    @Override
    public List<VisitConfigLogDO> listByCondition(String infoUuid,
                                                  List<VisitConfigLogActionTypeEnum> actionTypeEnums,
                                                  List<VisitConfigSourceEnum> sourceEnums) {
        List<Integer> sources = null;
        if (sourceEnums != null) {
            sources = sourceEnums.stream()
                    .map(VisitConfigSourceEnum::getValue)
                    .collect(Collectors.toList());
        }
        List<Integer> actionTypes = null;
        if (actionTypeEnums != null) {
            actionTypes = actionTypeEnums.stream()
                    .map(VisitConfigLogActionTypeEnum::getValue)
                    .collect(Collectors.toList());
        }
        return visitConfigLogDAO.listByCondition(infoUuid, actionTypes, sources);
    }
    @Override
    public List<VisitConfigLogDO> list(String infoUuid) {
        return visitConfigLogDAO
                .listByInfoUuidAndActionType(infoUuid, null);
    }
}
