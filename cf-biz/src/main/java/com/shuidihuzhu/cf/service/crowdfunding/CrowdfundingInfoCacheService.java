package com.shuidihuzhu.cf.service.crowdfunding;

import com.shuidihuzhu.cf.biz.crowdfunding.CfRedisKvBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.constants.crowdfunding.CfRedisKvKey;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.common.web.util.cache.ICache;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * 缓存CrowdfundingInfo
 * 没有使用，废弃
 * zhangzhi 20190320.
 */
@Deprecated
@Service
public class CrowdfundingInfoCacheService extends AbstractCfCache<String, CrowdfundingInfo>
		implements ICache<String, CrowdfundingInfo> {

	private static final Logger LOGGER = LoggerFactory.getLogger(CrowdfundingInfoCacheService.class);

	@Autowired
	private CrowdfundingInfoBiz crowdfundingInfoBiz;

	@Autowired
	private CfRedisKvBiz cfRedisKvBiz;

	@Override
	protected CrowdfundingInfo queryData(String key) {
//
//		return null;
		if(StringUtils.isBlank(key)) {
			return null;
		}

		LOGGER.info("CrowdfundingInfoCacheService getFromDB key={}", key);

		return this.crowdfundingInfoBiz.getFundingInfo(key);
	}

	@Override
	public CrowdfundingInfo get(String key) {

		if(StringUtils.isBlank(key)) {
			return null;
		}

		try {
			CrowdfundingInfo crowdfundingInfo = getValue(key);
			if(crowdfundingInfo != null) {
				LOGGER.debug("CrowdfundingInfoCacheService getFromCache GOAL!");
			}
			return crowdfundingInfo;
		} catch (Exception e) {
			return null;
		}
	}

	@Override
	protected int getInitialCapacity() {
		int initialCapacity = this.cfRedisKvBiz.queryIntByKey(CfRedisKvKey.CF_INFO_LOCAL_CACHE_CAPACITY, true);
		if(initialCapacity <= 0) {
			return super.getInitialCapacity();
		}

		return initialCapacity;
//		return super.getInitialCapacity();
	}

	@Override
	protected int getMaximumSize() {
//		return super.getMaximumSize();
		int maximumSize = this.cfRedisKvBiz.queryIntByKey(CfRedisKvKey.CF_INFO_LOCAL_CACHE_MAX_SIZE, true);
		if(maximumSize <= 0) {
			return super.getMaximumSize();
		}
		return maximumSize;
	}

	@Override
	protected int getExpireTimeInSeconds() {
		int expire = this.cfRedisKvBiz.queryIntByKey(CfRedisKvKey.CF_INFO_LOCAL_CACHE_EXPIRE, true);
		if(expire <= 0) {
			return 60;
		}
		return expire;
	}

	@Override
	protected int getRefreshTimeAfterWrite() {
		int refresh = this.cfRedisKvBiz.queryIntByKey(CfRedisKvKey.CF_INFO_LOCAL_CACHE_REFRESH, true);
		if(refresh <= 0) {
			return 300;
		}
		return refresh;
	}
}
