package com.shuidihuzhu.cf.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.shuidihuzhu.cf.enums.crowdfunding.CaseRaiseRiskLevelEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CfBaseInfoTemplateRecord;
import com.shuidihuzhu.cf.response.OpResult;

/**
 * <AUTHOR>
 * @date 2018-08-16  15:33

 */
public interface CaseRaiseRiskDataService {

    OpResult<CaseRaiseRiskLevelEnum> analyseRisk(JsonNode jsonNode);

    OpResult<JsonNode> getRiskData(long caseId,
                                   long userId,
                                   String title,
                                   String content,
                                   boolean isTemplate,
                                   CfBaseInfoTemplateRecord templateRecord);
}
