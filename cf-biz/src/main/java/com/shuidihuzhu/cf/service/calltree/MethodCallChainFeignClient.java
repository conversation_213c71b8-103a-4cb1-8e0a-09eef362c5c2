package com.shuidihuzhu.cf.service.calltree;

import com.shuidihuzhu.cf.model.calltree.MethodCallChain;
import com.shuidihuzhu.common.web.model.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @author: cuikexiang
 */
@FeignClient(name = "itest-javaparser", url = "https://cloud-api.shuiditech.com", fallback = MethodCallChainFeignClientFallBack.class)
public interface MethodCallChainFeignClient {

    @GetMapping("/api/itest-javaparser/call-chain/method-call-chain")
    Response<MethodCallChain> findMethodCallChain(@RequestParam(name = "projectName") String projectName,
                                                  @RequestParam(name = "url") String url);
}
