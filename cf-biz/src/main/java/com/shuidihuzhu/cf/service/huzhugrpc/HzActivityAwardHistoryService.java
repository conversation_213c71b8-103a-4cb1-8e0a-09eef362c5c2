package com.shuidihuzhu.cf.service.huzhugrpc;

import com.google.common.collect.Lists;

import com.shuidihuzhu.cf.model.awardactivity.ActivityAwardHistory;
import com.shuidihuzhu.common.web.model.Response;
import com.shuidihuzhu.hz.client.hz.award.model.ActivityAwardHistoryDto;
import com.shuidihuzhu.hz.client.hz.award.service.ActivityAwardHistoryService;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import lombok.extern.slf4j.Slf4j;

/**
 * Created by chao on 16/9/12.
 */
@Service
@Slf4j
public class HzActivityAwardHistoryService {
    @Autowired
    private ActivityAwardHistoryService historyService;

    public void add(ActivityAwardHistory activityAwardHistory) {
        log.info("add activityAwardHistory,{}", activityAwardHistory);
        historyService.add(this.initActivityAwardHistoryDto(activityAwardHistory));
    }

    public int updateStatus(Integer id, Integer status) {
        Response<Integer> response = historyService.updateStatus(id, status);
        if (null == response || 0 != response.getCode()) {
            log.info("getLotteryCount response is null");
            return 0;
        }
        return response.getData();
    }

    public void delete(Integer id) {
        historyService.delete(id);
    }

    public List<ActivityAwardHistory> getLastAwardHistory(int count, int activityId, List<Integer> awardIdList) {
        if (count > 100) {
            count = 100;
        }
        Response<List<ActivityAwardHistoryDto>> response =
                historyService.getLastAwardHistoryByAwardIdListAndActivityId(awardIdList, activityId, count);

        if (null == response || 0 != response.getCode() || CollectionUtils.isEmpty(response.getData())) {
            log.info("getLastAwardHistory response is null");
            return Lists.newArrayList();
        }
        List<ActivityAwardHistory> resultList = Lists.newLinkedList();
        for (ActivityAwardHistoryDto historyDto : response.getData()) {
            if (null == historyDto) {
                continue;
            }
            resultList.add(this.initActivityAwardHistory(historyDto));
        }
        return resultList;
    }

    private ActivityAwardHistoryDto initActivityAwardHistoryDto(ActivityAwardHistory activityAwardHistory) {
        ActivityAwardHistoryDto activityAwardHistoryDto = new ActivityAwardHistoryDto();
        activityAwardHistoryDto.setId(activityAwardHistory.getId());
        activityAwardHistoryDto.setUserId(activityAwardHistory.getUserId());
        activityAwardHistoryDto.setAwardId(activityAwardHistory.getAwardId());
        activityAwardHistoryDto.setActivityId(activityAwardHistory.getActivityId());
        activityAwardHistoryDto.setAddTime(activityAwardHistory.getAddTime());
        activityAwardHistoryDto.setContent(activityAwardHistory.getContent());
        activityAwardHistoryDto.setStatus(activityAwardHistory.getStatus());
        activityAwardHistoryDto.setTradeNo(activityAwardHistory.getTradeNo());
        activityAwardHistoryDto.setPayType(activityAwardHistory.getPayType());

        return activityAwardHistoryDto;
    }


    private ActivityAwardHistory initActivityAwardHistory(ActivityAwardHistoryDto awardHistoryDto) {
        ActivityAwardHistory awardHistory = new ActivityAwardHistory();
        awardHistory.setId(awardHistoryDto.getId());
        awardHistory.setUserId(awardHistoryDto.getUserId());
        awardHistory.setAwardId(awardHistoryDto.getAwardId());
        awardHistory.setActivityId(awardHistoryDto.getActivityId());
        awardHistory.setAddTime(awardHistoryDto.getAddTime());
        awardHistory.setContent(awardHistoryDto.getContent());
        awardHistory.setStatus(awardHistoryDto.getStatus());
        awardHistory.setTradeNo(awardHistoryDto.getTradeNo());
        awardHistory.setPayType(awardHistoryDto.getPayType());
        return awardHistory;
    }
}
