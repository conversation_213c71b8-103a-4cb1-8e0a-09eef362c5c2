package com.shuidihuzhu.cf.service.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRedisKvBiz;
import com.shuidihuzhu.cf.constants.crowdfunding.CfRedisKvKey;
import com.shuidihuzhu.cf.enums.crowdfunding.CfOrderListTypeEnum;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingUserView;
import com.shuidihuzhu.cf.vo.AnchorPageV2VO;
import com.shuidihuzhu.cf.vo.AnchorPageVo;
import com.shuidihuzhu.common.util.redisson.RedissonHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.xerial.snappy.Snappy;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

import static com.shuidihuzhu.cf.service.crowdfunding.CrowdfundingInfoDetailService.REDIS_PAGE_NUM;

@Slf4j
@Service
public class CrowdfundingInfoDetailRedisService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CrowdfundingInfoDetailRedisService.class);

    private static final long REDIS_DETAIL_SECONDS = 3 * 60L;
    private static final String REDIS_DETAIL_LIST_NAME = "redis-detail-list-v3-";
    private static final String REDIS_DETAIL_PAGE_NAME = "redis-detail-page-v3-";

    @Autowired
    private CfRedisKvBiz cfRedisKvBiz;

    @Resource(name = "cfInfoDetailRedissonHandler")
    private RedissonHandler cfRedissonHandler;


    public AnchorPageV2VO<CrowdfundingUserView> getFriendsOrderFromRedis(int pageNum, int crowdfundingId, long contextUserId) {
        OrderRedisKey redisKey = getKey(pageNum, crowdfundingId, contextUserId, CfOrderListTypeEnum.FRIENDS);
        return doGetFromRedis(redisKey.pageKeyName, redisKey.listKeyName);
    }

    public AnchorPageV2VO<CrowdfundingUserView> getSelfOrderFromRedis(int pageNum, int crowdfundingId, long contextUserId) {
        OrderRedisKey orderRedisKey = getKey(pageNum, crowdfundingId, contextUserId, CfOrderListTypeEnum.SELF);
        return doGetFromRedis(orderRedisKey.pageKeyName, orderRedisKey.listKeyName);
    }

    public AnchorPageV2VO<CrowdfundingUserView> getOthersOrderFromRedis(int pageNum, int crowdfundingId, boolean isYestoday) {
        OrderRedisKey orderRedisKey;
        if (!isYestoday) {
            orderRedisKey = getKey(pageNum, crowdfundingId, 0, CfOrderListTypeEnum.OTHER);
        } else {
            orderRedisKey = getKey(pageNum, crowdfundingId, 0, CfOrderListTypeEnum.YESTODAY);
        }
        return doGetFromRedis(orderRedisKey.pageKeyName, orderRedisKey.listKeyName);
    }

    public void updateSelfOrderToRedis(List<CrowdfundingUserView> crowdfundingUserViews, int pageNum, int crowdfundingId,
                                       long contextUserId, int size, boolean hasNext, Long anchorId) {
        OrderRedisKey orderRedisKey = getKey(pageNum, crowdfundingId, contextUserId, CfOrderListTypeEnum.SELF);
        doSaveToRedis(orderRedisKey.pageKeyName, orderRedisKey.listKeyName, crowdfundingUserViews, size, hasNext, anchorId);
    }

    public void updateOtherToRedis(List<CrowdfundingUserView> crowdfundingUserViews, int pageNum, int crowdfundingId,
                                   int size, boolean hasNext, Long anchorId, boolean isYestoday) {
        OrderRedisKey orderRedisKey;
        if (!isYestoday) {
            orderRedisKey = getKey(pageNum, crowdfundingId, 0, CfOrderListTypeEnum.OTHER);
        } else {
            orderRedisKey = getKey(pageNum, crowdfundingId, 0, CfOrderListTypeEnum.YESTODAY);
        }

        doSaveToRedis(orderRedisKey.pageKeyName, orderRedisKey.listKeyName, crowdfundingUserViews, size, hasNext, anchorId);
    }

    public void updateFriendsToRedis(List<CrowdfundingUserView> crowdfundingUserViews, int pageNum, int crowdfundingId,
                                     long contextUserId, int size, boolean hasNext, Long anchorId) {
        OrderRedisKey orderRedisKey = getKey(pageNum, crowdfundingId, contextUserId, CfOrderListTypeEnum.FRIENDS);
        doSaveToRedis(orderRedisKey.pageKeyName, orderRedisKey.listKeyName,
                crowdfundingUserViews, size, hasNext, anchorId);
    }

    private OrderRedisKey getKey(int pageNum, int crowdfundingId, long contextUserId, CfOrderListTypeEnum typeEnum) {
        String pageKeyName = getPagekey(pageNum, crowdfundingId, contextUserId, typeEnum);
        String listKeyName = getListKey(pageNum, crowdfundingId, contextUserId, typeEnum);
        return new OrderRedisKey(pageKeyName, listKeyName);
    }

    private class OrderRedisKey {
        public String pageKeyName;
        public String listKeyName;

        public OrderRedisKey(String pageKeyName, String listKeyName) {
            this.pageKeyName = pageKeyName;
            this.listKeyName = listKeyName;
        }
    }

    /**
     * 把好友的捐款记录置为无效.
     *
     * @param crowdfundingId
     * @param userId
     */
    @Async
    public void clearOrderRedis(int crowdfundingId, long userId) {
        try {
            for (int pageNum = 1; pageNum <= REDIS_PAGE_NUM; pageNum++) {
                OrderRedisKey friendOrderKey = getKey(pageNum, crowdfundingId, userId, CfOrderListTypeEnum.FRIENDS);
                OrderRedisKey otherOrderKey = getKey(pageNum, crowdfundingId, 0, CfOrderListTypeEnum.OTHER);
                cfRedissonHandler.del(friendOrderKey.pageKeyName);
                cfRedissonHandler.del(friendOrderKey.listKeyName);
                cfRedissonHandler.del(otherOrderKey.pageKeyName);
                cfRedissonHandler.del(otherOrderKey.listKeyName);
            }
        } catch (Exception e) {
            LOGGER.error("", e);
        }
    }

    /**
     * 把自己的捐款记录置为无效。
     *
     * @param crowdfundingId
     * @param userId
     */
    @Async
    public void clearSelfDetailRedis(int crowdfundingId, long userId) {
        try {
            for (int pageNum = 1; pageNum <= REDIS_PAGE_NUM; pageNum++) {
                OrderRedisKey key = getKey(pageNum, crowdfundingId, userId, CfOrderListTypeEnum.SELF);
                cfRedissonHandler.del(key.pageKeyName);
                cfRedissonHandler.del(key.listKeyName);
            }
        } catch (Exception e) {
            LOGGER.error("", e);
        }
    }

    private String getPagekey(int pageNum, int crowdfundingId, long userId, CfOrderListTypeEnum type) {
        return getKeyName(REDIS_DETAIL_PAGE_NAME, pageNum, crowdfundingId, userId, type);
    }

    private String getListKey(int pageNum, int crowdfundingId, long userId, CfOrderListTypeEnum type) {
        return getKeyName(REDIS_DETAIL_LIST_NAME, pageNum, crowdfundingId, userId, type);
    }

    private String getKeyName(String prefix, int pageNum, int crowdfundingId, long userId, CfOrderListTypeEnum type) {
        StringBuilder key = new StringBuilder()
                .append(prefix)
                .append(crowdfundingId).append("-")
                .append(pageNum).append("-")
                .append(type).append("-");

        if (type == CfOrderListTypeEnum.SELF ||
                type == CfOrderListTypeEnum.FRIENDS) {
            key.append(userId).append("-");
        }
        return key.toString();
    }

    private void doSaveToRedis(String pageKeyName, String listKeyName,
                               List<CrowdfundingUserView> crowdfundingUserViews,
                               int size, boolean hasNext,
                               Long anchorId) {
        try {
            LOGGER.debug("updateRedis,pageKeyName:{},listKeyName:{}", pageKeyName, listKeyName);
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            AnchorPageVo anchorPageVo = new AnchorPageVo();
            anchorPageVo.setAnchorId(anchorId);
            anchorPageVo.setHasNext(hasNext);
            anchorPageVo.setSize(size);
            cfRedissonHandler.setEX(pageKeyName, anchorPageVo, REDIS_DETAIL_SECONDS * 1000L);
            LOGGER.debug("updateRedis pageKeyName:{} anchorPageVo:{}", pageKeyName, anchorPageVo);
            String value= compress(crowdfundingUserViews);
            cfRedissonHandler.setEX(listKeyName, value, REDIS_DETAIL_SECONDS*1000L);
            LOGGER.debug("updateRedis listKeyName:{} crowdfundingUserViews:{}", listKeyName, crowdfundingUserViews);
            stopWatch.stop();
            LOGGER.debug("updateRedis pageKeyName:{} updateDetail-total-cost:{}", pageKeyName, stopWatch.getTime());
        } catch (Exception e) {
            LOGGER.error("", e);
        }
    }

    private AnchorPageV2VO<CrowdfundingUserView> doGetFromRedis(String pageKeyName, String listKeyName) {
        try {
            LOGGER.debug("getFromRedis,pageKeyName:{},listKeyName:{}", pageKeyName, listKeyName);
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            AnchorPageVo anchorPageVo = cfRedissonHandler.get(pageKeyName, AnchorPageVo.class);
            LOGGER.debug("getRedis pageKeyName:{} anchorPageVo:{}", pageKeyName, anchorPageVo);
            String value=cfRedissonHandler.get(listKeyName,String.class);
            List<CrowdfundingUserView> crowdfundingUserViews = uncompress(value);
            String anonymousHead = this.cfRedisKvBiz.queryByKeyWithLocalCache(CfRedisKvKey.KEY_ANONYMOUS_DEFAULT_HEAD);

            LOGGER.debug("getRedis listKeyName:{} crowdfundingUserViews:{}", listKeyName, crowdfundingUserViews);

            if (CollectionUtils.isNotEmpty(crowdfundingUserViews)) {
                crowdfundingUserViews.forEach(item ->
                {
                    item.setShare(false);
                    //之前图片是http的，导致安全性有问题
                    if (StringUtils.equals(item.getHeadImgUrl(), "http://alioss.shuidichou.com/imgs/common/anonymous-avatar.png")) {
                        item.setHeadImgUrl(anonymousHead);
                    }
                });
            } else {
                crowdfundingUserViews = Lists.newArrayList();
            }

            AnchorPageV2VO<CrowdfundingUserView> result = null;
            if (anchorPageVo != null) {

                result = new AnchorPageV2VO<>();
                result.setAnchorId(anchorPageVo.getAnchorId());
                result.setSize(anchorPageVo.getSize());
                result.setHasNext(anchorPageVo.isHasNext());
                result.setList(crowdfundingUserViews);
            }
            stopWatch.stop();
            LOGGER.debug("getFromRedis pageKeyName:{} getDetail-total-cost:{}", pageKeyName, stopWatch.getTime());

            return result;
        } catch (Exception e) {
            LOGGER.error("", e);
            return null;
        }
    }
    private String compress(List<CrowdfundingUserView> crowdfundingUserViews) {
        String result = "";
        if (CollectionUtils.isEmpty(crowdfundingUserViews)) {
            return result;
        }
        long start = System.currentTimeMillis();
        String json="";
        try {
            json = JSON.toJSONString(crowdfundingUserViews);
            byte[] bytes = Snappy.compress(json);
            result = Base64.getEncoder().encodeToString(bytes);
        } catch (Exception e) {
            log.error("压缩异常:", e);
        }
        long end = System.currentTimeMillis();
        log.debug("压缩前长度:{},压缩后长度:{},耗时:{}", json.length(),result.length(),(end - start));
        return result;
    }

    private List<CrowdfundingUserView> uncompress(String value) {
        List<CrowdfundingUserView> crowdfundingUserViews = new ArrayList<>();
        if(Strings.isNullOrEmpty(value)){
          return   crowdfundingUserViews;
        }
        long start = System.currentTimeMillis();
        try {
            byte[] bytes = Base64.getDecoder().decode(value);
            String result = Snappy.uncompressString(bytes);
            crowdfundingUserViews = JSON.parseArray(result, CrowdfundingUserView.class);
        } catch (Exception e) {
            log.error("解压异常:", e);
        }
        if(CollectionUtils.isEmpty(crowdfundingUserViews)){
            log.error("解压异常,数据空");
        }
        long end = System.currentTimeMillis();
        log.debug("解压耗时:{}", (end - start));
        return crowdfundingUserViews;
    }
}
