package com.shuidihuzhu.cf.service.crowdfunding.casematerial.caseAuditListPage.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoStatusEnum;
import com.shuidihuzhu.cf.service.crowdfunding.casematerial.caseAuditListPage.ICfMaterialRejectListService;
import com.shuidihuzhu.cf.vo.v5.CfMaterialAuditListView;
import com.shuidihuzhu.cf.vo.v5.CfMaterialFieldName;
import com.shuidihuzhu.cf.vo.v5.CfMaterialParam;
import com.shuidihuzhu.cf.vo.v5.MaterialAuditEntry;
import com.shuidihuzhu.cf.vo.v5.MaterialRejectPositionType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
@Service
public class CfFundUseRejectListService implements ICfMaterialRejectListService {

    @Override
    public List<CfMaterialAuditListView.AuditModifyEntry> getRejectModifyEntry(CfMaterialParam param) {

        List<CfMaterialAuditListView.AuditModifyEntry> allEntry = Lists.newArrayList();

        int entryStatus = getEntryStatus(param.getMaterialAuditStatus());
        CfMaterialAuditListView.AuditModifyEntry modifyEntry = new CfMaterialAuditListView.AuditModifyEntry(
                MaterialAuditEntry.FUND_USE, entryStatus);

        Map<Integer, Set<String>> rejectDetails = param.getRejectDetails();
        if (MapUtils.isEmpty(rejectDetails)) {
            return allEntry;
        }

        List<CfMaterialAuditListView.FieldItemStatus> allFields = Lists.newArrayList();
        if (rejectDetails.containsKey(MaterialRejectPositionType.TREATMENT_COSTS.getCode())) {
            allFields.add(new CfMaterialAuditListView.FieldItemStatus(CfMaterialFieldName.hasCost, entryStatus));
        }
        if (rejectDetails.containsKey(MaterialRejectPositionType.MONEY_USE_FOR.getCode())) {
            allFields.add(new CfMaterialAuditListView.FieldItemStatus(CfMaterialFieldName.healthCare, entryStatus));
        }
        if (rejectDetails.containsKey(MaterialRejectPositionType.GOV_MEDICAL_HELP.getCode())) {
            allFields.add(new CfMaterialAuditListView.FieldItemStatus(CfMaterialFieldName.govMedical, entryStatus));
        }
        if (rejectDetails.containsKey(MaterialRejectPositionType.FUTURE_WILL_COST.getCode())) {
            allFields.add(new CfMaterialAuditListView.FieldItemStatus(CfMaterialFieldName.willCost, entryStatus));
        }

        modifyEntry.setAllFields(allFields);
        allEntry.add(modifyEntry);

        return allEntry;
    }
}
