package com.shuidihuzhu.cf.service.crowdfunding;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.CfInfoShareRecordBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingOrderBiz;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfFriendLoopInfoVo;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class CfFriendLoopService {

    @Autowired
    private CrowdfundingOrderBiz crowdfundingOrderBiz;
    @Autowired
    private CfInfoShareRecordBiz cfInfoShareRecordBiz;
    @Autowired
    private UserInfoDelegate userInfoDelegate;
    @Autowired
    private CfFriendLoopRedisService cfFriendLoopRedisService;

    public final int ORDER_SHARED = 0;
    public final int ONLY_ORDER = 1;
    public final int ONLY_SHARED = 2;
    public final int DEFAULT = 3;

    /**
     * 得到轮播数据
     *
     * @param friendUserIdList
     * @param crowdFundingId
     * @return
     */
    public List<CfFriendLoopInfoVo> getFriendLoopResult(List<Long> friendUserIdList, int crowdFundingId, long userId) {
        if (CollectionUtils.isEmpty(friendUserIdList) || crowdFundingId < 0) {
            cfFriendLoopRedisService.setRedissonValue(userId, crowdFundingId, Lists.newArrayList());
            return Lists.newArrayList();
        }

        //筛除没有nickname的用户
        Map<Long, CfFriendLoopInfoVo> cfFriendLoopInfoVoMap = this.getUserInfoMap(friendUserIdList);
        log.debug("userId:{},有昵称的好友数量为: count{}", userId, cfFriendLoopInfoVoMap.size());
        if (cfFriendLoopInfoVoMap.isEmpty()) {
            cfFriendLoopRedisService.setRedissonValue(userId, crowdFundingId, Lists.newArrayList());
            return Lists.newArrayList();
        }
        //一度好友的捐款转发数据
        this.getFriendOrderAndShared(cfFriendLoopInfoVoMap, crowdFundingId);

        //好有轮播展示数据,展示前五条
        List<CfFriendLoopInfoVo> cfFriendLoopInfoVoList = Lists.newArrayList();
        try {
            cfFriendLoopInfoVoList = this.getFriendLoopResult(cfFriendLoopInfoVoMap);
        } catch (Exception e) {
            log.error("对数据的组织出现错误", e);
        } finally {
            cfFriendLoopRedisService.setRedissonValue(userId, crowdFundingId, cfFriendLoopInfoVoList);
        }
        return cfFriendLoopInfoVoList;
    }

    /**
     * 获得用户的订单与分享数据
     *
     * @param cfFriendLoopInfoVoMap
     * @param crowdFundingId
     * @return
     */
    private void getFriendOrderAndShared(Map<Long, CfFriendLoopInfoVo> cfFriendLoopInfoVoMap,
                                         int crowdFundingId) {

        Set<Long> userIdSet = cfFriendLoopInfoVoMap.keySet();
        List<CrowdfundingOrder> crowdFundingOrderList = crowdfundingOrderBiz.getByUserId(userIdSet, crowdFundingId);
        if (!CollectionUtils.isEmpty(crowdFundingOrderList)) {
            for (CrowdfundingOrder crowdfundingOrder : crowdFundingOrderList) {
                try {
                    //过滤掉匿名捐款的
                    if (crowdfundingOrder == null || crowdfundingOrder.isAnonymous() || crowdfundingOrder.getAmount() <= 0) {
                        continue;
                    }
                    int amountFen = crowdfundingOrder.getAmount();
                    if (amountFen == 0) {
                        continue;
                    }
                    CfFriendLoopInfoVo cfFriendLoopInfoVo = cfFriendLoopInfoVoMap.get(crowdfundingOrder.getUserId());
                    cfFriendLoopInfoVo.setAmountFen(cfFriendLoopInfoVo.getAmountFen() + amountFen);
                } catch (Exception e) {
                    log.error("getFriendOrderAndShared查询订单crowdFundingId:{}时,异常", crowdFundingId, e);
                }
            }
        }

        Map<Long, Long> userIdShareCountMap =
                cfInfoShareRecordBiz.countShareByInfoIdAndUserIds(crowdFundingId, userIdSet);
        for (long userId : userIdSet) {
            try {
                Long count = 0L;
                if (userIdShareCountMap != null && userIdShareCountMap.containsKey(userId)) {
                    count = userIdShareCountMap.get(userId);
                }
                CfFriendLoopInfoVo cfFriendLoopInfoVo = cfFriendLoopInfoVoMap.get(userId);
                int payAmountFen = cfFriendLoopInfoVo.getAmountFen();
                //既无捐款又无转发
                if (count < 1 && payAmountFen <= 0) {
                    cfFriendLoopInfoVo.setType(DEFAULT);
                    continue;
                }
                //有捐款但是无转发
                if (count < 1 && payAmountFen > 0) {
                    cfFriendLoopInfoVo.setType(ONLY_ORDER);
                    continue;
                }
                //有转发但是无捐款
                if (count > 0 && payAmountFen <= 0) {
                    cfFriendLoopInfoVo.setType(ONLY_SHARED);
                    cfFriendLoopInfoVo.setShared(count.intValue());
                    continue;
                }
                //有捐款与转发
                cfFriendLoopInfoVo.setShared(count.intValue());
                cfFriendLoopInfoVo.setType(ORDER_SHARED);
            } catch (Exception e) {
                log.error("getFriendOrderAndShared查询分享userId:{},crowdFundingId:{}时,异常", userId, crowdFundingId, e);
            }
        }
    }

    /**
     * 得到用户信息(昵称,头像)
     *
     * @param friendUserIdList
     * @return
     */
    private Map<Long, CfFriendLoopInfoVo> getUserInfoMap(List<Long> friendUserIdList) {
        if (CollectionUtils.isEmpty(friendUserIdList)) {
            return Maps.newHashMap();
        }
        List<List<Long>> friendUserIdPartitionList = Lists.partition(friendUserIdList, 30);
        Map<Long, CfFriendLoopInfoVo> resultMap = Maps.newHashMap();
        for (List<Long> itemList : friendUserIdPartitionList) {
            List<UserInfoModel> userInfoModelList = userInfoDelegate.getUserInfoByUserIdBatch(itemList);
            if (CollectionUtils.isEmpty(userInfoModelList)) {
                continue;
            }
            userInfoModelList.stream().filter(item -> item != null && !StringUtils.isEmpty(item.getNickname())).
                    forEach(item -> {
                        CfFriendLoopInfoVo cfFriendLoopInfoVo = new CfFriendLoopInfoVo();
                        cfFriendLoopInfoVo.setNickname(item.getNickname());
                        cfFriendLoopInfoVo.setHeadImgUrl(item.getHeadImgUrl());
                        //默认为3,无转发捐款
                        cfFriendLoopInfoVo.setType(DEFAULT);
                        resultMap.put(item.getUserId(), cfFriendLoopInfoVo);
                    });
        }
        return resultMap;
    }

    /**
     * 得到好友的轮播数据信息
     *
     * @param cfFriendLoopInfoVoMap
     * @return
     */
    private List<CfFriendLoopInfoVo> getFriendLoopResult(Map<Long, CfFriendLoopInfoVo> cfFriendLoopInfoVoMap) {
        if (null == cfFriendLoopInfoVoMap || cfFriendLoopInfoVoMap.isEmpty()) {
            return Lists.newArrayList();
        }

        Collection<CfFriendLoopInfoVo> cfFriendLoopInfoVoCollection = cfFriendLoopInfoVoMap.values();
        //降序排列
        List<CfFriendLoopInfoVo> cfFriendLoopInfoVoList = Lists.newArrayList();
        try {
            //排序规则1.转发且捐款>大于捐款>转发(按金额与转发同级比较)
            cfFriendLoopInfoVoList = cfFriendLoopInfoVoCollection.parallelStream().
                    filter(item -> null != item && DEFAULT != item.getType()).
                    sorted(Comparator.comparing(CfFriendLoopInfoVo::getType).
                            thenComparing(item -> -(item.getAmountFen())).
                            thenComparing(item -> -(item.getShared()))
                    ).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("getFriendLoopResult排序出错", e);
        }
        //只要前五个
        if (cfFriendLoopInfoVoList.size() > 5) {
            cfFriendLoopInfoVoList = cfFriendLoopInfoVoList.subList(0, 5);
        }
        return cfFriendLoopInfoVoList;
    }

    public List<CfFriendLoopInfoVo> getRedissonResultList(long userId, int crowdFundingId) {
        return cfFriendLoopRedisService.getRedissonResultList( userId,  crowdFundingId);
    }

    public List<CfFriendLoopInfoVo> filterResult(List<CfFriendLoopInfoVo> cfFriendLoopInfoVoList) {
        if (CollectionUtils.isEmpty(cfFriendLoopInfoVoList)) {
            return Lists.newArrayList();
        }
        List<CfFriendLoopInfoVo> result = Lists.newArrayList();
        try {
            //排序规则1.转发且捐款>大于捐款>转发(按金额与转发同级比较)
            result = cfFriendLoopInfoVoList.parallelStream().
                    filter(item -> null != item && DEFAULT != item.getType()).
                    sorted(Comparator.comparing(CfFriendLoopInfoVo::getType).
                            thenComparing(item -> -(item.getAmount())).
                            thenComparing(item -> -(item.getShared()))
                    ).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("filterResult", e);
        }
        //只要前五个
        if (result.size() > 5) {
            result = result.subList(0, 5);
        }
        //只要有捐款记录的
        result = result.stream().filter(item ->
                null != item
                        && item.getType() != ONLY_SHARED
                        && item.getAmount() >= 10)
                .collect(Collectors.toList());
        return result;
    }


}
