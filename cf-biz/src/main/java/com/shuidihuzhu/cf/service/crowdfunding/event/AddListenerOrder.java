package com.shuidihuzhu.cf.service.crowdfunding.event;

/**
 * 事件监听者顺序枚举
 */
public enum AddListenerOrder {

    /**
     * 筹款案例上报数据
     */
    CaseCreateTrace(0),
    CreateRisk(1),
    UserTagClient(2),
    CfInfoStat(3),
    CfInfoTask(4),
    TouchSetCannotShareOrDonate(5),
    Pin(6),
    CaseCouldShareAndDonate(7),
    SendAddTagMQ(8),
    CaseBeginFundingTrace(9),
    PromoteRisk(10),
    /**
     * 异步推断来源
     */
    SaveOriginInfoForCase(12),
    CaseSendMQ(13),
    AddBaseAfter(14),
    ServiceLog(15),

    //用户标签中有案例信息，用户新发起案例需要更新标签信息
    UserLableAlter(16),
    //发起人重复但患者不重复时，埋点上报
    RepeatRaiserTrace(17),
    UserLabel(18),
    //增加筹款方沟通列表信息，在举报筹款方沟通列表页使用
    ReportFundraiserCommunicater(19),
    ;
    
    AddListenerOrder(int value)
    {
        this.value=value;
    }
    public int getValue() {
        return value;
    }

    private int value;
}
