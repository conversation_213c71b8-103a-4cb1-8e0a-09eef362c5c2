
```sql

CREATE TABLE `shuidi_crowdfunding`.`simple_blacklist` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',

  `case_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '案例id',
  `action_type` int(11) NOT NULL DEFAULT '0' COMMENT '黑名单类型',

  `create_time` Timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` Timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_delete` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否逻辑删除',
	PRIMARY KEY (`id`),
  KEY `idx_case_id` (`case_id`),
	KEY `idx_create_time` (`create_time`),
	KEY `idx_update_time` (`update_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='简单黑名单';

```