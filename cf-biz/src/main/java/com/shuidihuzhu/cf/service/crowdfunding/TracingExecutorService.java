package com.shuidihuzhu.cf.service.crowdfunding;

import brave.Tracing;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.concurrent.*;

/**
 * 线程池包装,防止丢traceId
 */
@Service
public class TracingExecutorService {

    @Autowired
    private Tracing tracing;
    private static ScheduledExecutorService scheduledExecutorService = Executors.newScheduledThreadPool(2);
    private static ExecutorService executorService = Executors.newFixedThreadPool(8);

    public ScheduledFuture<?> schedule(Runnable command,
                                       long delay, TimeUnit unit) {
        Runnable runnable = tracing.currentTraceContext().wrap(command);
        ScheduledFuture<?> future = scheduledExecutorService.schedule(runnable, delay, unit);
        return future;
    }

    public void execute(Runnable command) {
        Runnable runnable = tracing.currentTraceContext().wrap(command);
        executorService.execute(runnable);
    }
}