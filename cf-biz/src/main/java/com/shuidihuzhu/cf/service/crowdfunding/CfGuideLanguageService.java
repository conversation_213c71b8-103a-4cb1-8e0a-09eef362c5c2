package com.shuidihuzhu.cf.service.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.account.model.UserInfoModel;
import com.shuidihuzhu.cf.biz.crowdfunding.CfFirstApproveBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfInfoShareRecordBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingOrderBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.verification.CrowdFundingVerificationBiz;
import com.shuidihuzhu.cf.delegate.UserInfoDelegate;
import com.shuidihuzhu.cf.enums.partner.Gender;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdFundingVerification;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.service.crowdfunding.bigdata.DsUserMessageService;
import com.shuidihuzhu.cf.vo.CfDonationGuideLanguageVo;
import com.shuidihuzhu.cf.vo.DsDonationDataVo;
import com.shuidihuzhu.cipher.ShuidiCipher;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: lianghongchao
 * @Date: 2018/8/31 12:38
 */
@Service
@Slf4j
public class CfGuideLanguageService {
    @Autowired
    private DsUserMessageService dsUserMessageService;
    @Autowired
    private CfInfoShareRecordBiz infoShareRecordBiz;
    @Autowired
    private CrowdfundingInfoBiz crowdfundingInfoBiz;
    @Autowired
    private UserInfoDelegate userInfoDelegate;
    @Autowired
    private CrowdFundingVerificationBiz verificationBiz;
    @Autowired
    private CrowdfundingOrderBiz crowdfundingOrderBiz;

    @Autowired
    private ShuidiCipher shuidiCipher;
    @Resource
    private CfFirstApproveBiz cfFirstApproveBiz;

    public CfDonationGuideLanguageVo getDonationGuideLanguageVo(long userId, String infoUuid, String source,
                                                                String userSourceId, int sharedDV) {
        CfDonationGuideLanguageVo cfDonationGuideLanguageVo = null;
        //二度好友时
        if (1 == sharedDV) {
            cfDonationGuideLanguageVo = this.secondDVDetail(userId, infoUuid, source, userSourceId);
        } else {
            cfDonationGuideLanguageVo = this.dataDetail(userId, infoUuid);
        }

        int gender = getGenderByCase(infoUuid);
        cfDonationGuideLanguageVo.setAuthorGender(gender);

        return cfDonationGuideLanguageVo;
    }

    private int getGenderByCase(String infoUuid) {

        CfFirsApproveMaterial cfFirsApproveMaterial = cfFirstApproveBiz.getByInfoUuid(infoUuid);

        if(cfFirsApproveMaterial == null){
            //unkown
            return 0;
        }

        String idCardNo = shuidiCipher.decrypt(cfFirsApproveMaterial.getPatientCryptoIdcard());
        if(StringUtils.isBlank(idCardNo)){
            //unkown
            return 0;
        }

        char a;
        if(idCardNo.length() <= 15) {
            //15位身份证取第15位
            a = idCardNo.charAt(14);
        } else {
            //18位身份证取第17位
            a = idCardNo.charAt(16);
        }

        int value = a - '0';

        if(value%2 == 0){
            return Gender.FEMALE.getValue();
        } else {
            return Gender.MALE.getValue();
        }
    }

    /**
     * 二度好友的处理
     *
     * @param userId
     * @param infoUuid
     * @param source
     * @param userSourceId
     * @return
     */
    private CfDonationGuideLanguageVo secondDVDetail(long userId, String infoUuid, String source, String userSourceId) {
        CfDonationGuideLanguageVo guideLanguageVo = new CfDonationGuideLanguageVo();

        if (userId <= 0 || StringUtils.isEmpty(infoUuid) || StringUtils.isEmpty(source) || StringUtils.isEmpty(userSourceId)) {
            guideLanguageVo.setGuideType(-1);
            guideLanguageVo.setErrorMsg("参数信息不合法");
            log.info("secondDVDetail param is error userId:{}\t infoUuid:{}\t source:{} \tuserSourceId:{}",
                    userId, infoUuid, source, userSourceId);
            return guideLanguageVo;
        }
        //证实的处理
        guideLanguageVo = verificationDetail(infoUuid, userId);
        log.debug("CfDonationGuideLanguageVo guideLanguageVo:{}", JSON.toJSONString(guideLanguageVo));
        if (null != guideLanguageVo) {
            return guideLanguageVo;
        }
        long userSourceIdInt = 0;
        guideLanguageVo = new CfDonationGuideLanguageVo();
        try {
            userSourceIdInt = shuidiCipher.decryptUserId(userSourceId.trim());
        } catch (Exception e) {
            log.error("secondDVDetail", e);
            guideLanguageVo.setGuideType(-1);
            guideLanguageVo.setErrorMsg("解密分享人userId异常");
            log.info("secondDVDetail 解密分享人userId异常 userId:{}\t infoUuid:{}\t source:{} \tuserSourceId:{}",
                    userId, infoUuid, source, userSourceId);
            return guideLanguageVo;
        }

        UserInfoModel userInfoModel = userInfoDelegate.getUserInfoByUserId(userSourceIdInt <= 0 ? 0 : userSourceIdInt);
        guideLanguageVo = new CfDonationGuideLanguageVo();
        if (null == userInfoModel) {
            guideLanguageVo.setFirstDVName("");
            guideLanguageVo.setFirstDVUrl("");
        } else {
            guideLanguageVo.setFirstDVName(StringUtils.trimToEmpty(userInfoModel.getNickname()));
            guideLanguageVo.setFirstDVUrl(StringUtils.trimToEmpty(userInfoModel.getHeadImgUrl()));
        }
        guideLanguageVo.setGuideType(DonationGuideTypeEnum.SECOND_DV_FRIEND.getType());
        return guideLanguageVo;
    }

    /**
     * 非二度好友的处理
     *
     * @param userId
     * @param infoUuid
     * @return
     */
    private CfDonationGuideLanguageVo dataDetail(long userId, String infoUuid) {
        CfDonationGuideLanguageVo guideLanguageVo = new CfDonationGuideLanguageVo();
        if (userId <= 0 || StringUtils.isEmpty(infoUuid)) {
            guideLanguageVo.setGuideType(-1);
            guideLanguageVo.setErrorMsg("参数信息不合法");
            log.info("dataDetail param is error userId:{}\t infoUuid:{}", userId, infoUuid);
            return guideLanguageVo;
        }

        CrowdfundingInfo crowdfundingInfo = crowdfundingInfoBiz.getFundingInfo(infoUuid);
        if (null == crowdfundingInfo) {
            guideLanguageVo.setGuideType(-1);
            guideLanguageVo.setErrorMsg("案例查询不到");
            log.info("dataDetail param is error userId:{}\t infoUuid:{}", userId, infoUuid);
            return guideLanguageVo;
        }

        CrowdfundingOrder lastetOrder = crowdfundingOrderBiz.getLatestOrderOfUserId(userId);
        double lastDonationNum = 0;
        long lastOrderId = -1;
        if (null != lastetOrder) {
            lastDonationNum = lastetOrder.getAmountInYuanDouble();
            lastOrderId = lastetOrder.getId();
        }
        int sharedCount = infoShareRecordBiz.countShareByUserIdAndInfoId(crowdfundingInfo.getId(), userId);
        log.info("分享的数据 sharedCount:{}", sharedCount);

        /**
         * 是否对该案例有过分享(0-是  1-否)
         */
        int isShared = 1;
        if (sharedCount > 0) {
            isShared = 0;
        }
        try {
            DsDonationDataVo donationDataVo = dsUserMessageService.getDonate(userId, crowdfundingInfo.getUserId(),
                    crowdfundingInfo.getId(), lastOrderId, lastDonationNum, isShared);
            if (null == donationDataVo) {
                guideLanguageVo.setGuideType(-1);
                guideLanguageVo.setErrorMsg("大数据处理异常");
                log.info("dataDetail param is error userId:{}\t infoUuid:{}", userId, infoUuid);
                return guideLanguageVo;
            }

            //一度好友的处理
            if (donationDataVo.getIsOnceFriend() == 0) {
                guideLanguageVo = firstDVDetail(crowdfundingInfo.getUserId());
                if (null != guideLanguageVo) {
                    return guideLanguageVo;
                }
            }

            //证实的处理
            guideLanguageVo = verificationDetail(infoUuid, userId);
            if (null != guideLanguageVo) {
                return guideLanguageVo;
            }

            //对捐款记录的综合处理
            guideLanguageVo = donationRecordDetail(donationDataVo, userId, crowdfundingInfo, sharedCount);
            if (null != guideLanguageVo) {
                return guideLanguageVo;
            }
        } catch (Exception e) {
            log.error("分类异常", e);
        }
        guideLanguageVo = new CfDonationGuideLanguageVo();
        guideLanguageVo.setGuideType(-1);
        guideLanguageVo.setErrorMsg("没发现对应类型");
        log.info("dataDetail param is error userId:{}\t infoUuid:{} \t ", userId, infoUuid);
        return guideLanguageVo;
    }


    /**
     * @param fundingUserId 筹款人id
     * @return
     */
    private CfDonationGuideLanguageVo firstDVDetail(long fundingUserId) {

        if (fundingUserId <= 0) {
            return null;
        }

        CfDonationGuideLanguageVo guideLanguageVo = new CfDonationGuideLanguageVo();
        UserInfoModel userInfoModel = userInfoDelegate.getUserInfoByUserId(fundingUserId);
        if (null == userInfoModel) {
            log.warn("Fundraiser is  null fundingUserId:{}", fundingUserId);
            return null;
        }
        guideLanguageVo.setGuideType(DonationGuideTypeEnum.FIRST_DV_FRIEND.getType());
        guideLanguageVo.setFundraiserName(StringUtils.trimToEmpty(userInfoModel.getNickname()));
        guideLanguageVo.setFundraiserUrl(StringUtils.trimToEmpty(userInfoModel.getHeadImgUrl()));
        return guideLanguageVo;
    }

    /**
     * crowd_funding_verification证实
     *
     * @param infoUuid
     * @param userId
     * @return
     */
    private CfDonationGuideLanguageVo verificationDetail(String infoUuid, long userId) {

        if (null == infoUuid || userId <= 0) {
            return null;
        }
        CfDonationGuideLanguageVo guideLanguageVo = new CfDonationGuideLanguageVo();
        List<CrowdFundingVerification> verificationList = verificationBiz.getByVerifyUserIds(
                Lists.newArrayList(userId), infoUuid);
        if (CollectionUtils.isEmpty(verificationList)) {
            log.info("verificationDetail is  null userId:{}", userId);
            return null;
        }
        guideLanguageVo.setGuideType(DonationGuideTypeEnum.HED_VERIFICATION.getType());
        return guideLanguageVo;
    }

    /**
     * data_cf_donate_cnt", "data_cf_share_case_ids","data_cf_donate_amount", "data_cf_last_donate_order_id"
     * 捐款用户
     *
     * @param dsDonationDataVo
     * @param userId
     * @return
     */
    private CfDonationGuideLanguageVo donationRecordDetail(DsDonationDataVo dsDonationDataVo, long userId,
                                                           CrowdfundingInfo crowdfundingInfo, int sharecount) {

        if (null == dsDonationDataVo || userId <= 0 || null == crowdfundingInfo) {
            return null;
        }

        CfDonationGuideLanguageVo guideLanguageVo = new CfDonationGuideLanguageVo();
        //首捐
        if (1 == dsDonationDataVo.getDonateCnt()) {
            guideLanguageVo.setGuideType(DonationGuideTypeEnum.FIRST_DONATION.getType());
            guideLanguageVo.setHelpFamilyNum(1);
            return guideLanguageVo;
        }

        //用户之前对其他案例有过转发
        if (dsDonationDataVo.getShareCaseNum() > 0 && dsDonationDataVo.getSharePosition() < 0) {
            guideLanguageVo.setGuideType(DonationGuideTypeEnum.SHARE_MORE_FUNDING.getType());
            guideLanguageVo.setSharedFamilyNum(dsDonationDataVo.getShareCaseNum());
            return guideLanguageVo;
        }

        //用户之前转发过改案例
        if (dsDonationDataVo.getSharePosition() > 0) {
            guideLanguageVo.setGuideType(DonationGuideTypeEnum.SHARE_THIS_FUNDING.getType());
            guideLanguageVo.setShareFundingOrder(dsDonationDataVo.getSharePosition());
            guideLanguageVo.setFundraiserAccount(crowdfundingInfo.getAmount() / 100);
            return guideLanguageVo;
        }

        //复捐用户
        if (dsDonationDataVo.getDonateCnt() >= 2) {
            guideLanguageVo.setGuideType(DonationGuideTypeEnum.HAS_MORE_DONATION.getType());
            guideLanguageVo.setDonationAccount(Double.valueOf(dsDonationDataVo.getDonateAmount()).intValue());
            //案例数有统计
            guideLanguageVo.setHelpFamilyNum(dsDonationDataVo.getDonateCaseNum());
            guideLanguageVo.setSharedFamilyNum(dsDonationDataVo.getShareCaseNum());
            return guideLanguageVo;
        }
        return null;
    }

    /**
     * 1度好友>用户对该案例有过证实>2度好友>用户是首捐>用户之前对其他案例有过转发>用户对当前案例有过转发>用户是复捐
     */
    private enum DonationGuideTypeEnum {
        FIRST_DV_FRIEND(1, "1度好友"),
        HED_VERIFICATION(2, "用户对该案例有过证实"),
        SECOND_DV_FRIEND(3, "2度好友"),
        FIRST_DONATION(4, "用户是首捐"),
        SHARE_MORE_FUNDING(5, "用户之前对其他案例有过转发"),
        SHARE_THIS_FUNDING(7, "用户对当前案例有过转发"),
        HAS_MORE_DONATION(6, "用户是复捐");
        private int type;
        private String desc;

        DonationGuideTypeEnum(int type, String desc) {
            this.type = type;
            this.desc = desc;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }
    }
}
