package com.shuidihuzhu.cf.service.crowdfunding.casematerial.caseAuditListPage.impl;

import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.CfFirstApproveBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CfRefuseReasonMsgBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingAuthorBiz;
import com.shuidihuzhu.cf.enums.crowdfunding.CrowdfundingInfoDataStatusTypeEnum;
import com.shuidihuzhu.cf.enums.crowdfunding.UserIdentityType;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingAuthor;
import com.shuidihuzhu.cf.service.crowdfunding.casematerial.caseAuditListPage.ICfMaterialRejectListService;
import com.shuidihuzhu.cf.vo.v5.CfMaterialAuditListView;
import com.shuidihuzhu.cf.vo.v5.CfMaterialFieldName;
import com.shuidihuzhu.cf.vo.v5.CfMaterialParam;
import com.shuidihuzhu.cf.vo.v5.MaterialAuditEntry;
import com.shuidihuzhu.cf.vo.v5.MaterialModifySuggestType;
import com.shuidihuzhu.cf.vo.v5.MaterialRejectPositionType;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
@Service
public class CfPatientRejectListService implements ICfMaterialRejectListService {

    @Autowired
    private CfFirstApproveBiz firstApproveBiz;
    @Autowired
    private CfRefuseReasonMsgBiz reasonMsgBiz;
    @Autowired
    private CrowdfundingAuthorBiz authorBiz;

    @Override
    public List<CfMaterialAuditListView.AuditModifyEntry> getRejectModifyEntry(CfMaterialParam param) {

        List<CfMaterialAuditListView.AuditModifyEntry> allEntry = Lists.newArrayList();
        CfFirsApproveMaterial firstApprove = firstApproveBiz.getByInfoUuid(param.getInfoUuid());

        if (firstApprove == null || MapUtils.isEmpty(param.getRejectDetails())) {
            return allEntry;
        }
        boolean idCard = firstApprove.getPatientIdType() == UserIdentityType.identity.getCode();

        int entryStatus = getEntryStatus(param.getMaterialAuditStatus());
        int suggestStatus = getEntrySuggestStatus(param.getMaterialAuditStatus());
        Set<Integer> suggestCodes = getSuggestCodes(param.getSuggestViews());

        CrowdfundingAuthor author = authorBiz.get(param.getCaseId());
        // 判断是否是身份验证
        if (author != null && author.getFaceIdResult() == 20) {
            return getPatientAuthEntry(entryStatus);
        }

        if (hasSuggest(suggestCodes)) {
            return getSuggestEntry(param, suggestCodes, suggestStatus, idCard);
        }

        if (param.getRejectDetails().containsKey(MaterialRejectPositionType.PATIENT_AUTH.getCode())) {
            return getPatientAuthEntry(entryStatus);
        }

        IdCardModifyType modifyType = getRejectType(param.getRejectDetails());
        switch (modifyType) {
            case ID_CARD:
            case BORN_CARD:
                return getRightEntry(param, idCard, modifyType, entryStatus);
            case BOTH:
                return idCard ? getPatientIdCardEntry(param.getRejectDetails(), entryStatus) :
                        getPatientBornCardEntry(param.getRejectDetails(), entryStatus);
            default:
                break;
        }

        return allEntry;
    }

    private List<CfMaterialAuditListView.AuditModifyEntry> getPatientIdCardEntry(Map<Integer, Set<String>> rejectDetails,
                                                                                     int entryStatus) {

        List<CfMaterialAuditListView.AuditModifyEntry> entryList = Lists.newArrayList();
        if (rejectDetails.containsKey(MaterialRejectPositionType.PATIENT_ID_CARD_PHOTO.getCode())) {

            CfMaterialAuditListView.AuditModifyEntry curEntry = new CfMaterialAuditListView.AuditModifyEntry(
                    MaterialAuditEntry.PATIENT_ID_CARD_PHOTO, entryStatus);
            curEntry.setAllFields(Lists.newArrayList(getFieldItem(CfMaterialFieldName.patientIdCardPhoto, entryStatus)));
            entryList.add(curEntry);
        }

        if (rejectDetails.containsKey(MaterialRejectPositionType.PATIENT_FRONT_CERTIFICATE.getCode())) {
            CfMaterialAuditListView.AuditModifyEntry curEntry = new CfMaterialAuditListView.AuditModifyEntry(
                    MaterialAuditEntry.PATIENT_ID_CARD, entryStatus);
            curEntry.setAllFields(Lists.newArrayList(getFieldItem(CfMaterialFieldName.patientIdCard, entryStatus)));
            entryList.add(curEntry);
        }

        return entryList;
    }

    private List<CfMaterialAuditListView.AuditModifyEntry> getRightEntry(CfMaterialParam param,
                                                                         boolean idCard,
                                                                         IdCardModifyType modifyType,
                                                                         int entryStatus) {
        if (idCard && modifyType == IdCardModifyType.ID_CARD) {
            return getPatientIdCardEntry(param.getRejectDetails(), entryStatus);
        }

        if (!idCard && modifyType == IdCardModifyType.BORN_CARD) {
            return getPatientBornCardEntry(param.getRejectDetails(), entryStatus);
        }

//        reasonMsgBiz.updatePayeeCaseRefuseMsg(param.getInfoUuid(), CrowdfundingInfoDataStatusTypeEnum.PATIENT_INFO_SUBMIT,
//                MaterialRejectPositionType.PATIENT_AUTH);
        return getPatientAuthEntry(entryStatus);
    }

    private List<CfMaterialAuditListView.AuditModifyEntry> getPatientAuthEntry(int entryStatus) {
        List<CfMaterialAuditListView.AuditModifyEntry> allEntry = Lists.newArrayList();

        CfMaterialAuditListView.AuditModifyEntry modifyEntry = new CfMaterialAuditListView.AuditModifyEntry(
                MaterialAuditEntry.PATIENT_AUTH, entryStatus);
        modifyEntry.setAllFields(Lists.newArrayList(
                new CfMaterialAuditListView.FieldItemStatus(CfMaterialFieldName.patientRepeatVerif, entryStatus)));
        allEntry.add(modifyEntry);

        return allEntry;
    }

    private List<CfMaterialAuditListView.AuditModifyEntry> getPatientBornCardEntry(Map<Integer, Set<String>> rejectDetails,
                                                                                       int entryStatus) {

        List<CfMaterialAuditListView.AuditModifyEntry> entryList = Lists.newArrayList();
        if (rejectDetails.containsKey(MaterialRejectPositionType.PATIENT_BORN_CARD_PHOTO.getCode())) {
            CfMaterialAuditListView.AuditModifyEntry curEntry = new CfMaterialAuditListView.AuditModifyEntry(
                    MaterialAuditEntry.PATIENT_BORN_CARD_PHOTO, entryStatus);
            curEntry.setAllFields(Lists.newArrayList(getFieldItem(CfMaterialFieldName.patientBornCardPhoto, entryStatus)));
            entryList.add(curEntry);
        }

        if (rejectDetails.containsKey(MaterialRejectPositionType.PATIENT_FRONT_BORN_CARD.getCode())) {
            CfMaterialAuditListView.AuditModifyEntry curEntry = new CfMaterialAuditListView.AuditModifyEntry(
                    MaterialAuditEntry.PATIENT_BORN_CARD, entryStatus);

            curEntry.setAllFields(Lists.newArrayList(getFieldItem(CfMaterialFieldName.patientBornCard, entryStatus)));
            entryList.add(curEntry);
        }
        return entryList;
    }

    private boolean hasSuggest(Set<Integer> suggestCodes) {
        return suggestCodes.contains(MaterialModifySuggestType.PATIENT_BORN_CARD_PHOTO.getCode())
                || suggestCodes.contains(MaterialModifySuggestType.PATIENT_ID_CARD_PHOTO.getCode())
                || suggestCodes.contains(MaterialModifySuggestType.PATIENT_FACE_RECOGNITION.getCode());
    }

    private IdCardModifyType getRejectType(Map<Integer, Set<String>> rejectDetails) {

        boolean rejectIdCard = rejectDetails.containsKey(MaterialRejectPositionType.PATIENT_ID_CARD_PHOTO.getCode())
                || rejectDetails.containsKey(MaterialRejectPositionType.PATIENT_FRONT_CERTIFICATE.getCode());

        boolean rejectBornCard = rejectDetails.containsKey(MaterialRejectPositionType.PATIENT_BORN_CARD_PHOTO.getCode())
                || rejectDetails.containsKey(MaterialRejectPositionType.PATIENT_FRONT_BORN_CARD.getCode());

        if (!rejectIdCard && !rejectBornCard) {
            return IdCardModifyType.NO;
        }
        if (rejectIdCard && rejectBornCard) {
            return IdCardModifyType.BOTH;
        }

        return rejectIdCard ? IdCardModifyType.ID_CARD : IdCardModifyType.BORN_CARD;
    }

    private List<CfMaterialAuditListView.AuditModifyEntry> getSuggestEntry(CfMaterialParam param,
                                                                           Set<Integer> suggestCodes,
                                                                           int suggestStatus,
                                                                           boolean idCard) {
        CfMaterialAuditListView.AuditModifyEntry modifyEntry = new CfMaterialAuditListView.AuditModifyEntry(
                MaterialAuditEntry.PATIENT_AUTH_SUGGEST, suggestStatus);
        modifyEntry.setAllFields(Lists.newArrayList(getSuggestFieldItem(suggestCodes, suggestStatus, idCard)));

        return Lists.newArrayList(modifyEntry);
    }

    private List<CfMaterialAuditListView.ModifySuggest> getModifySuggestList(List<CfMaterialAuditListView.ModifySuggest> suggestViews,
                                                                             boolean idCard) {

        if (suggestViews.size() == 1) {
            return suggestViews;
        }

        List<CfMaterialAuditListView.ModifySuggest> result = Lists.newArrayList();
        for (CfMaterialAuditListView.ModifySuggest suggest : suggestViews) {
            if (suggestMatchIdType(suggest, idCard)) {
                result.add(suggest);
                break;
            }
        }

        return result;
    }

    private boolean suggestMatchIdType(CfMaterialAuditListView.ModifySuggest suggest,
                                      boolean idCard) {

        return (idCard && suggest.getSuggestCode() == MaterialModifySuggestType.PATIENT_ID_CARD_PHOTO.getCode())
                || (!idCard && suggest.getSuggestCode() == MaterialModifySuggestType.PATIENT_BORN_CARD_PHOTO.getCode());
    }

    private List<CfMaterialAuditListView.FieldItemStatus> getSuggestFieldItem(Set<Integer> suggestCodes,
                                                                        int suggestStatus,
                                                                        boolean idCard) {

        List<CfMaterialAuditListView.FieldItemStatus> fieldResult = Lists.newArrayList();

        if (suggestCodes.contains(MaterialModifySuggestType.PATIENT_ID_CARD_PHOTO.getCode())) {
            fieldResult.add(getFieldItem(CfMaterialFieldName.idCardPhoto, suggestStatus));
        }
        if (suggestCodes.contains(MaterialModifySuggestType.PATIENT_BORN_CARD_PHOTO.getCode())) {
            fieldResult.add(getFieldItem(CfMaterialFieldName.bornCardPhoto, suggestStatus));
        }
        if (suggestCodes.contains(MaterialModifySuggestType.PATIENT_FACE_RECOGNITION)) {
            fieldResult.add(getFieldItem(CfMaterialFieldName.faceRecognition, suggestStatus));
        }

        return fieldResult;
    }

    private CfMaterialAuditListView.FieldItemStatus getFieldItem(String content, int status) {
        return new CfMaterialAuditListView.FieldItemStatus(content, status);
    }

    @Getter
    private enum IdCardModifyType {
        NO("无"),
        ID_CARD("身份证"),
        BORN_CARD("出生证"),
        BOTH("身份证和出生证");

        IdCardModifyType(String desc) {
            this.desc = desc;
        }

        String desc;
    }
}
