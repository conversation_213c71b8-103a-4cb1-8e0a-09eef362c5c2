package com.shuidihuzhu.cf.service.crowdfunding;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shuidihuzhu.cf.biz.crowdfunding.CfFirstApproveBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoPayeeBiz;
import com.shuidihuzhu.cf.biz.crowdfunding.CrowdfundingInfoSimpleBiz;
import com.shuidihuzhu.cf.model.crowdfunding.CfFirsApproveMaterial;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfo;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingInfoPayee;
import com.shuidihuzhu.cf.model.crowdfunding.UserRealInfo;
import com.shuidihuzhu.cipher.ShuidiCipher;
import com.shuidihuzhu.client.baseservice.alarm.v1.AlarmClient;
import com.shuidihuzhu.client.dataservice.bi.v1.BiApiClient;
import com.shuidihuzhu.data.analytics.javasdk.core.Analytics;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @time 2019/8/4 下午4:44
 * @desc
 */
@Slf4j
@Service
public class RepeatRaiserPayeeTraceService {

    @Autowired
    private CfFirstApproveBiz cfFirstApproveBiz;

    @Autowired
    private CrowdfundingInfoPayeeBiz crowdfundingInfoPayeeBiz;
    @Autowired
    private PlatformEsService esService;



    List<Long> whiteUserIds = Lists.newArrayList(
            2073836l,97l,20470332l,1300181l,156119672l,538973216l,69143972l,388454299l,789175529l,
            352439482l,39507617l,74772830l,68316471l, 496409811l,615632347l,233655468l,165270067l,176410177l,
            352436316l,201599608l,677045300l,575986719l,196687422l,352440548l,216112878l,788200842l,508844101l,
            772350025l,733041806l,812178652l);

    @Deprecated
    public void repeatPayeeTrace(CrowdfundingInfo cfInfo){
//        if(whiteUserIds.contains(cfInfo.getUserId())){
//            return;
//        }
//
//
//        String infoUuid = cfInfo.getInfoId();
//
//        CrowdfundingInfoPayee payeeInfo = crowdfundingInfoPayeeBiz.getByInfoUuid(infoUuid);
//        if(Objects.isNull(payeeInfo) || StringUtils.isEmpty(payeeInfo.getIdCard())){
//            return;
//        }
//
//        CfFirsApproveMaterial material = cfFirstApproveBiz.getByInfoId(cfInfo.getId());
//        if(Objects.isNull(material) || UserIdentityType.birth.getCode() == material.getPatientIdType()){
//            return;
//        }
//
//        String patientCryptoIdcard = material.getPatientCryptoIdcard();
//
////        SearchDto searchDto = new SearchDto();
////        searchDto.setQuerySql(buildPayeeSql(payeeInfo.getIdCard()));
////        Response response = biApiClient.esQueryCustom(searchDto);
////
////        if(Objects.isNull(response) || 0 != response.getCode()){
////            log.info("RepeatRaiserPayeeTraceService es查询失败 payee code:{},idCard:{}", Objects.nonNull(response) ? response.getCode() : -1, payeeInfo.getIdCard());
////            return;
////        }
//
//        List<Map<String, Object>> list = esService.getQueryResultFromEs("shuidi_crowdfunding_crowdfunding_info_payee_alias",
//                buildPayeeQuery(payeeInfo.getIdCard()));
//        if(CollectionUtils.isEmpty(list)){
//            log.info("RepeatRaiserPayeeTraceService es查询结果为空,idCard:{}", payeeInfo.getIdCard());
//            return;
//        }
//
//        Map<String, CfInfoSimpleModel> infoUuidMaps = Maps.newHashMap();
//        for (Map<String, Object> map : list){
//            String infoId = (String) map.get("info_uuid");
//            if(infoUuid.equals(infoId)){
//                continue;
//            }
//
//            Integer caseId = (Integer) map.get("case_id");
//
//            CfFirsApproveMaterial otherMaterial = cfFirstApproveBiz.getByInfoId(Objects.nonNull(caseId) ? caseId : 0);
//            if(Objects.isNull(otherMaterial) || UserIdentityType.birth.getCode() == material.getPatientIdType()){
//                continue;
//            }
//
//            String otherPatientIdcard = otherMaterial.getPatientCryptoIdcard();
//
//            CfInfoSimpleModel simpleModel = crowdfundingInfoSimpleBiz.getFundingInfo(infoId);
//            if(Objects.isNull(simpleModel)){
//                continue;
//            }
//            simpleModel.setChannel(null);
//            simpleModel.setCreateTime(null);
//            if(!StringUtils.upperCase(shuidiCipher.decrypt(patientCryptoIdcard)).equals(StringUtils.upperCase(shuidiCipher.decrypt(otherPatientIdcard))) && !infoUuidMaps.containsKey(infoId)){
//                infoUuidMaps.put(infoId, simpleModel);
//            }
//        }
//
//        if(MapUtils.isEmpty(infoUuidMaps)){
//            return;
//        }
//
//        UserInfoModel userInfoModel = accountService.getUserInfoByMobile(shuidiCipher.decrypt(payeeInfo.getMobile()));
//        long payeeUserId = Objects.nonNull(userInfoModel) ? userInfoModel.getUserId() : 0;
//
//        Collection<CfInfoSimpleModel> infoUuids = infoUuidMaps.values();
//
//        Map<String, Object> ext = new HashMap<>();
//        ext.put(CrowdfundingConstant.INFO_ID_KEY, cfInfo.getId());
//        ext.put(CrowdfundingConstant.CASE_ID_KEY, cfInfo.getInfoId());
//        ext.put(CrowdfundingConstant.USER_ID, cfInfo.getUserId());
//        ext.put(CrowdfundingConstant.PAYEE_NAME, payeeInfo.getName());
//        ext.put(CrowdfundingConstant.PAYEE_USER_ID, payeeUserId);
//        ext.put(CrowdfundingConstant.PAYEE_IDENTITY, payeeInfo.getIdCard());
//        ext.put(CrowdfundingConstant.CASS_ID_LIST, infoUuids);
//        ext.put(CrowdfundingConstant.REPEAT_SIZE, infoUuids.size());
//
//
//        if(environment.acceptsProfiles("production")) {
//            log.info("RepeatRaiserPayeeTraceService repeatPayeeTrace ext:{}", ext);
//
//            for (CfInfoSimpleModel simpleModel : infoUuids) {
//                simpleModel.setInfoId(null);
//            }
//
//            CfPayeeRepeatInfo cfPayeeRepeatInfo = new CfPayeeRepeatInfo();
//            cfPayeeRepeatInfo.setRepeatCount(infoUuids.size());
//            cfPayeeRepeatInfo.setCaseId(cfInfo.getId());
//            cfPayeeRepeatInfo.setUserId(cfInfo.getUserId());
//            cfPayeeRepeatInfo.setPayeeName(payeeInfo.getName());
//            cfPayeeRepeatInfo.setPayeeIdCard(shuidiCipher.decrypt(payeeInfo.getIdCard()));
//            cfPayeeRepeatInfo.setPayeeUserId(payeeUserId);
//            cfPayeeRepeatInfo.setSimpleModels(infoUuids);
//
//            String content = JSON.toJSONString(cfPayeeRepeatInfo);
//            log.info("收款人重复msg:{}", content);
//            alarmClient.sendByGroup("wx-alarm-prod-*************", "【收款人重复】：\n" + JsonFormartUtil.JsonFormart(content));
//        }
    }
    @Deprecated
    public void repeatRaiserTrace(CrowdfundingInfo cfInfo){
//        if(whiteUserIds.contains(cfInfo.getUserId())){
//            return;
//        }
//
//        CfFirsApproveMaterial material = cfFirstApproveBiz.getByInfoId(cfInfo.getId());
//        if(Objects.isNull(material)){
//            return;
//        }
//
//        String raiserIdentity = StringUtils.isNotEmpty(material.getSelfCryptoIdcard()) ? material.getSelfCryptoIdcard() : "";
//        String raiserName = StringUtils.isNotEmpty(material.getSelfRealName()) ? material.getSelfRealName() : "";
//        if((StringUtils.isEmpty(raiserIdentity) || StringUtils.isEmpty(raiserName)) && UserRelTypeEnum.SELF.getValue() == material.getUserRelationType()){
//            raiserName = material.getPatientRealName();
//            raiserIdentity = material.getPatientCryptoIdcard();
//        }
//        String patientIdentity = material.getPatientCryptoIdcard();
//
////        SearchDto searchDto = new SearchDto();
////        searchDto.setQuerySql(buildRaiserSql(raiserIdentity));
////        Response response = biApiClient.esQueryCustom(searchDto);
////
////        if(Objects.isNull(response) || 0 != response.getCode()){
////            log.info("RepeatRaiserPayeeTraceService es查询失败 raiser code:{},idCard:{}", Objects.nonNull(response) ? response.getCode() : -1, raiserIdentity);
////            return;
////        }
//
//        List<Map<String, Object>> list = esService.getQueryResultFromEs("shuidi_crowdfunding_cf_first_approve_material_alias",
//                buildRaiseQuery(raiserIdentity));
//        if(CollectionUtils.isEmpty(list)){
//            log.info("RepeatRaiserPayeeTraceService es查询结果为空 idCard:{}", raiserIdentity);
//            return;
//        }
//
//        Map<String, CfInfoSimpleModel> infoUuidMaps = Maps.newHashMap();
//        for (Map<String, Object> map : list){
//            String infoId = (String)map.get("info_uuid");
//            if(cfInfo.getInfoId().equals(infoId)){
//                continue;
//            }
//
//            CfInfoSimpleModel simpleModel = crowdfundingInfoSimpleBiz.getFundingInfo(infoId);
//            if(Objects.isNull(simpleModel)){
//                continue;
//            }
//            simpleModel.setChannel(null);
//            simpleModel.setCreateTime(null);
//            String otherPatientIdentity = StringUtils.isEmpty((String)map.get("patient_crypto_idcard")) ? ""
//                    : (String)map.get("patient_crypto_idcard");
//
//            if(!StringUtils.upperCase(shuidiCipher.decrypt(otherPatientIdentity)).equals(StringUtils.upperCase(shuidiCipher.decrypt(patientIdentity))) && !infoUuidMaps.containsKey(infoId)){
//                infoUuidMaps.put(infoId, simpleModel);
//            }
//        }
//
//        if(MapUtils.isEmpty(infoUuidMaps)){
//            return;
//        }
//
//        Collection<CfInfoSimpleModel> infoUuids = infoUuidMaps.values();
//        if(environment.acceptsProfiles("production")){
//            for (CfInfoSimpleModel simpleModel : infoUuids){
//                simpleModel.setInfoId(null);
//            }
//
//            CfRaiserRepeatInfo cfRaiserRepeatInfo = new CfRaiserRepeatInfo();
//            cfRaiserRepeatInfo.setRepeatCount(infoUuids.size());
//            cfRaiserRepeatInfo.setCaseId(cfInfo.getId());
//            cfRaiserRepeatInfo.setUserId(cfInfo.getUserId());
//            cfRaiserRepeatInfo.setRaiserName(raiserName);
//            cfRaiserRepeatInfo.setRaiserIdCard(shuidiCipher.decrypt(raiserIdentity));
//            cfRaiserRepeatInfo.setSimpleModels(infoUuids);
//
//            String content = JSON.toJSONString(cfRaiserRepeatInfo);
//            log.info("发起人重复msg:{}", content);
//            alarmClient.sendByGroup("wx-alarm-prod-*************", "【发起人重复】：\n" + JsonFormartUtil.JsonFormart(content));
//        }
    }

    private BoolQueryBuilder buildPayeeQuery(String idCard) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

        boolQuery.must(QueryBuilders.termQuery("id_card", idCard));

        return boolQuery;
    }

    private BoolQueryBuilder buildRaiseQuery(String idCard) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

        boolQuery.should(QueryBuilders.termQuery("self_crypto_idcard", idCard));
        boolQuery.should(QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("self_crypto_idcard", ""))
                .must(QueryBuilders.termQuery("patient_crypto_idcard", idCard)));

        return boolQuery;
    }

    public void testPlatformEs(int caseId, String param) {
        CfFirsApproveMaterial material = cfFirstApproveBiz.getByInfoId(caseId);
        if (StringUtils.isBlank(material.getSelfRealName())) {
            material.setSelfRealName(material.getPatientRealName());
            material.setSelfCryptoIdcard(material.getPatientCryptoIdcard());
        }
        List<CfFirsApproveMaterial> result1 = esService.queryCaseByRaiser(material);
        List<CfFirsApproveMaterial> result2 = esService.queryCaseByAuthor(material);
        CrowdfundingInfoPayee infoPayee = crowdfundingInfoPayeeBiz.getByInfoUuid(material.getInfoUuid());

        List<CrowdfundingInfoPayee> result3 = Lists.newArrayList();
        if (infoPayee != null) {
            result3 = esService.queryCaseByPayee(infoPayee);
        }

        List<UserRealInfo> result4 = esService.queryUserRealInfo(JSON.parseObject(param, UserRealInfo.class));
        log.info("测试es 返回结果的 result1:{} result2:{} result3:{} result4:{}", result1.size(), result2.size(),
                result3.size(), result4.size());

    }

}
