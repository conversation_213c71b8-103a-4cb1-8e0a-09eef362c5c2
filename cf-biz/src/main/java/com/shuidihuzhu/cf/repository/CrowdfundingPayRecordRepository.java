package com.shuidihuzhu.cf.repository;

import com.shuidihuzhu.cf.dao.tdsql.TdCrowdfundingPayRecordDao;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingPayRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/13  15:24
 */
@Service
@RefreshScope
public class CrowdfundingPayRecordRepository {

    @Autowired
    private TdCrowdfundingPayRecordDao tdCrowdfundingPayRecordDao;

    public List<CrowdfundingPayRecord> getAllPaySuccessByIdAndTime(long id, Date beginTime, Date endTime, int size) {
        return tdCrowdfundingPayRecordDao.getAllPaySuccessByIdAndTime(id, beginTime, endTime, size);
    }

    public CrowdfundingPayRecord selectByOrderId(long orderId) {
        return tdCrowdfundingPayRecordDao.selectByOrderId(orderId);
    }

}
