package com.shuidihuzhu.cf.repository;

import com.shuidihuzhu.cf.dao.tdsql.TdCrowdfundingOrderDao;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingOrder;
import com.shuidihuzhu.cf.model.crowdfunding.OrderStatisticsPo;
import com.shuidihuzhu.cf.model.crowdfunding.vo.CfCountSumVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/5/13  15:48
 */
@Service
@RefreshScope
public class CrowdfundingOrderRepository {

    @Autowired
    private TdCrowdfundingOrderDao tdCrowdfundingOrderDao;

    public CfCountSumVo selectCountByMin(Timestamp begin, Timestamp end, Integer payStatus) {
        return tdCrowdfundingOrderDao.selectCountByMin(begin, end, payStatus);
    }

    public List<CrowdfundingOrder> getListBetweenTime(Timestamp start, Timestamp end, int offset, int limit) {
        return tdCrowdfundingOrderDao.getListBetweenTime(start, end, offset, limit);
    }


    public List<CrowdfundingOrder> getByOffset(long offset, int limit) {
        return tdCrowdfundingOrderDao.getByOffset(offset, limit);
    }


    public int getOrderCountBetweenTime(Timestamp start, Timestamp end) {
        return tdCrowdfundingOrderDao.getOrderCountBetweenTime(start, end);
    }

    public Timestamp getLatestOrderTime() {
        return tdCrowdfundingOrderDao.getLatestOrderTime();
    }

    public List<CrowdfundingOrder> selectByPayTimeLimit(Timestamp begin, Timestamp end, int start, int size) {
        return tdCrowdfundingOrderDao.selectByPayTimeLimit(begin, end, start, size);
    }


    public List<CrowdfundingOrder> getValidPayedSuccessListByPayTimeLimit(Timestamp begin, Timestamp end, Long id, int size) {
        return tdCrowdfundingOrderDao.getValidPayedSuccessListByPayTimeLimit(begin, end, id, size);
    }

    public List<CrowdfundingOrder> selectByUserIdAndThirdType(Long userId, Integer caseId, Integer thirdType) {
        return tdCrowdfundingOrderDao.selectByUserIdAndThirdType(userId, caseId, thirdType);
    }


    public List<CrowdfundingOrder> findCrowdfundingOrder(Long userId, String startTime, String endTime) {
        return tdCrowdfundingOrderDao.findCrowdfundingOrder(userId, startTime, endTime);
    }

    public List<Map<String, Object>> getCountByTime(String startTime, String endTime) {
        return tdCrowdfundingOrderDao.getCountByTime(startTime, endTime);
    }

    public List<OrderStatisticsPo> getCountByTimeNew(String startTime, String endTime) {
        return tdCrowdfundingOrderDao.getCountByTimeNew(startTime, endTime);
    }

    public List<CrowdfundingOrder> getNoPayListByDate(Date startDate, Date endDate, Long id, Integer limit) {
        return tdCrowdfundingOrderDao.getNoPayListByDate(startDate, endDate, id, limit);
    }

    public CrowdfundingOrder getCrowdfundingOrderById(Long id) {
        return tdCrowdfundingOrderDao.getCrowdfundingOrderById(id);
    }

    public CrowdfundingOrder getCrowdfundingOrderByCode(String code) {
        return tdCrowdfundingOrderDao.getCrowdfundingOrderByCode(code);
    }

    public List<CrowdfundingOrder> getCrowdfundingOrderByActivity(List<Integer> caseIds, long activityId) {
        return tdCrowdfundingOrderDao.getCrowdfundingOrderByActivity(caseIds, activityId);
    }

}
