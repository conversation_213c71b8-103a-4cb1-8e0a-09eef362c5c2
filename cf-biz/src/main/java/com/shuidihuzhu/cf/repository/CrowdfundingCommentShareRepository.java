package com.shuidihuzhu.cf.repository;

import com.shuidihuzhu.cf.dao.tdsql.TdCrowdfundingCommentShareDao;
import com.shuidihuzhu.cf.model.crowdfunding.CrowdfundingComment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/13  15:40
 */
@Service
@RefreshScope
public class CrowdfundingCommentShareRepository {

    @Autowired
    private TdCrowdfundingCommentShareDao tdCrowdfundingCommentShareDao;

    public List<CrowdfundingComment> getReplyByParentList(Integer type, List<Long> orderIdList, int limit) {
        return tdCrowdfundingCommentShareDao.getReplyByParentList(type, orderIdList, limit);
    }

}
