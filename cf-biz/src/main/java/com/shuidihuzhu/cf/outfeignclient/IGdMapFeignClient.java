package com.shuidihuzhu.cf.outfeignclient;

import com.shuidihuzhu.client.util.feignlog.EnableFeignLog;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "cf-api",
        url="https://restapi.amap.com",
        configuration = GdMapFeignClientConfiguaration.class,
        fallback = IGdMapFeignClient.GdMapFeignClientFallback.class)
public interface IGdMapFeignClient {
    /**
     *
     * @param key : 应用秘钥
     * @param keywords : 查询关键词 eg:安康市中心医院北院区
     * @param types : 查询POI类型 eg: 医疗保健服务
     * @param city : 城市名，可填：城市中文、中文全拼、citycode或adcode eg: 安康市
     * @param output : 返回数据格式类型 可选值：JSON，XML
     * @param offset : 每页记录数据 最小值1，最大值:25
     * @param page : 当前页数 最小值1
     * @param extensions : 返回结果控制
     * @return
     */
    @EnableFeignLog
    @GetMapping(path = "/v3/place/text")
    String getPoiByKeyword(@RequestParam("key") String key,
                           @RequestParam("keywords") String keywords,
                           @RequestParam("types") String types,
                           @RequestParam("city")String city,
                           @RequestParam("output")String output,
                           @RequestParam("offset") int offset,
                           @RequestParam("page")int page,
                           @RequestParam("extensions") String extensions
                           );

    /**
     * 获得附近位置
     * @param key: 应用秘钥
     * @param keywords: 查询关键词 eg:安康市中心医院北院区
     * @param types : 查询POI类型 eg: 医疗保健服务
     * @param city : 城市名，可填：城市中文、中文全拼、citycode或adcode eg: 安康市
     * @param radius : 查询半径 取值范围:0-50000。规则：大于50000按默认值，单位：米
     * @param sortrule : 规定返回结果的排序规则。按距离排序：distance；综合排序：weight
     * @param offset : 每页记录数据 最小值1，最大值:25
     * @param page  : 当前页数 最小值1
     * @return
     */
    @EnableFeignLog
    @GetMapping(path = "/v3/place/around")
    String getPoiByKeyword(@RequestParam("key") String key,
                           @RequestParam(value = "location") String location,
                           @RequestParam(value = "keywords",required = false) String keywords,
                           @RequestParam("types") String types,
                           @RequestParam(value = "city" ,required = false)String city,
                           @RequestParam("radius") int radius,
                           @RequestParam(value = "sortrule",required = false) String sortrule,
                           @RequestParam("offset") int offset,
                           @RequestParam("page")int page);

    /**
     *
     * @param key
     * @param location 经度和纬度用","分割，经度在前，纬度在后，经纬度小数点后不得超过6位。多个坐标对之间用”|”进行分隔最多支持40对坐标。
     * @param coordsys 可选值： gps; mapbar; baidu; autonavi(不进行转换)
     * @return
     */
    @EnableFeignLog
    @GetMapping(path = "/v3/assistant/coordinate/convert")
    String convert(@RequestParam("key") String key,
                   @RequestParam("locations") String location,
                   @RequestParam("coordsys") String coordsys);


    /**
     *
     * @param key
     * @param location 经度和纬度用","分割，经度在前，纬度在后，经纬度小数点后不得超过6位。多个坐标对之间用”|”进行分隔最多支持40对坐标。
     * @return
     */
    @EnableFeignLog
    @GetMapping(path = "/v3/geocode/regeo")
    String regeo(@RequestParam("key") String key,
                 @RequestParam(value = "location") String location);

    @EnableFeignLog
    @GetMapping(path = "/v3/geocode/geo")
    String geo(@RequestParam("key") String key,
               @RequestParam("address") String address,
               @RequestParam("city") String city);



    class GdMapFeignClientFallback implements IGdMapFeignClient{

        @Override
        public String getPoiByKeyword(String key, String keywords, String types, String city, String output, int offset, int page, String extensions) {
            return "";
        }

        @Override
        public String getPoiByKeyword(String key, String location, String keywords, String types, String city, int radius, String sortrule, int offset, int page) {
            return "";
        }

        @Override
        public String convert(String key, String location, String coordsys) {
            return "";
        }

        @Override
        public String regeo(String key, String location) {
            return "";
        }

        @Override
        public String geo(String key, String address, String city) {
            return "";
        }
    }
}

